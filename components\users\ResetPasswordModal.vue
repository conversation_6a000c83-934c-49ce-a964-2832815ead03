<template>
    <n-modal :maskClosable="false">
        <div class="w-11/12 md:w-[400px] bg-bg-2 rounded-2xl">
            <div class="p-6 flex justify-between items-center">
                <div class="text-base text-text-1 font-semibold">
                    {{ t("PROFILE_ACCOUNT_RESET_PASSWORD") }}
                </div>
                <IconsClose class="text-2xl text-text-1 cursor-pointer select-none" @click="handleClose" />
            </div>
            <div class="px-6 py-4 text-center">
                <div class="pb-6 relative">
                    <div class="form-item !border-none">
                        <input
                            type="email"
                            maxlength="100"
                            readonly
                            placeholder="<EMAIL>"
                            class="h-full border-none outline-none w-full bg-transparent text-sm font-medium dark:!text-dark-active-text/70 !text-dark-bg"
                            :value="user.email"
                        />
                    </div>
                </div>

                <div class="pb-6 relative flex items-center gap-4" :style="{ '--error-msg': `'${forms.validateCode.error}'` }" :class="{ 'check-error': forms.validateCode.status == 'error' }">
                    <div class="form-item flex-1">
                        <input
                            type="text"
                            :placeholder="t('PROFILE_ACCOUNT_VERIFY_CODE')"
                            class="!caret-primary h-full border-none outline-none w-full bg-transparent dark:placeholder:text-dark-active-text/40 placeholder:text-dark-bg/40 text-sm font-medium dark:!text-dark-active-text/70 !text-dark-bg"
                            v-model="forms.validateCode.value"
                            @input="checkValue('validateCode')"
                            @blur="checkValue('validateCode')"
                        />
                    </div>
                    <Button rounded="lg" type="primary" :disabled="isDisabled" @click="getVerifiCode">
                        <n-countdown v-if="isDisabled" ref="countdown" :duration="60000" :active="true" :render="renderCountdown" :on-finish="resetCountdown" />
                        <span v-else class="flex items-center gap-1.5">
                            <IconsKey class="text-xl" />
                            {{ t("PROFILE_ACCOUNT_GET_CODE") }}
                        </span>
                    </Button>
                </div>

                <div class="pb-6 relative" :style="{ '--error-msg': `'${forms.password.error}'` }" :class="{ 'check-error': forms.password.status == 'error' }">
                    <div class="form-item">
                        <input
                            :type="isSecret(forms.password.notPwd)"
                            :placeholder="t('PROFILE_ACCOUNT_NEW_PASSWORD')"
                            maxlength="32"
                            class="caret-primary h-full border-none outline-none w-full bg-transparent dark:placeholder:text-dark-active-text/40 placeholder:text-dark-bg/40 text-sm font-medium dark:!text-dark-active-text/70 !text-dark-bg"
                            v-model="forms.password.value"
                            @input="checkValue('password')"
                            @blur="checkValue('password')"
                        />
                        <span class="text-text-3 opacity-70 cursor-pointer hover:opacity-100" @click="forms.password.notPwd = !forms.password.notPwd">
                            <n-icon v-if="forms.password.notPwd" size="20">
                                <IconsNoEye />
                            </n-icon>
                            <n-icon v-else size="20">
                                <IconsEye />
                            </n-icon>
                        </span>
                    </div>
                </div>
                <div class="pb-6 relative" :style="{ '--error-msg': `'${forms.aginPassword.error}'` }" :class="{ 'check-error': forms.aginPassword.status == 'error' }">
                    <div class="form-item">
                        <input
                            :type="isSecret(forms.aginPassword.notPwd)"
                            :placeholder="t('PROFILE_ACCOUNT_CONFIRM_PASSWORD')"
                            maxlength="32"
                            class="caret-primary h-full border-none outline-none w-full bg-transparent dark:placeholder:text-dark-active-text/40 placeholder:text-dark-bg/40 text-sm font-medium dark:!text-dark-active-text/70 !text-dark-bg"
                            v-model="forms.aginPassword.value"
                            @input="checkValue('aginPassword')"
                            @blur="checkValue('aginPassword')"
                        />
                        <span class="text-text-3 opacity-70 cursor-pointer hover:opacity-100" @click="forms.aginPassword.notPwd = !forms.aginPassword.notPwd">
                            <n-icon size="20">
                                <IconsNoEye v-if="forms.aginPassword.notPwd" />
                                <IconsEye v-else />
                            </n-icon>
                        </span>
                    </div>
                </div>
            </div>
            <div class="py-4 px-6 flex justify-end items-center gap-3">
                <Button type="secondary" class="min-w-[108px]" @click="handleClose">
                    {{ $t("COMMON_BTN_CANCEL") }}
                </Button>
                <Button :loading="subLoading" class="min-w-[108px]" type="primary" @click="handleForgot">
                    <span v-if="!subLoading">{{ t("CONFIG_BASE_RESET_BTN") }}</span>
                </Button>
            </div>
        </div>
    </n-modal>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { ref, computed } from "vue";
import { strToMd5 } from "@/utils/tools.js";
import { useClearUserInfo } from "@/hook/updateAccount";
import { resetPassword, sendCodeByReset } from "@/api";
import { useUserProfile } from "@/stores/index.js";
import { storeToRefs } from "pinia";
const localePath = useLocalePath();

const emits = defineEmits(["update:show"]);

const userProfileStore = useUserProfile();
const { user } = storeToRefs(userProfileStore);

const isDisabled = ref(false);
const isLoading = ref(false); // 验证码专用
const subLoading = ref(false); // 提交专用
const isSecret = computed(() => {
    return (notPwd) => (notPwd ? "text" : "password");
});

// 密碼校验正则
const checkPassword = (password) => {
    const formItem = forms.value.aginPassword;
    if (formItem.value) {
        const results = formItem.check(formItem.value);
        formItem.status = results.res ? "checked" : "error";
        formItem.error = results.error;
    }

    // 检查基本要求：长度和允许的字符
    // const basePattern = /^[a-zA-Z0-9_\-\.@]{6,}$/;
    let error = "";
    if (password.length < 6) {
        error = t("PROFILE_ACCOUNT_INVALID_PASSWORD");
        return {
            res: false,
            error,
        };
    }
    return {
        res: true,
        error: "",
    };
};
// 確認密碼校验正则
const checkConfirmPassword = (val) => {
    if (val.length >= 6 && val == forms.value.password.value) {
        return {
            res: true,
            error: "",
        };
    }
    return {
        res: false,
        error: t("PROFILE_ACCOUNT_NO_PASSWORD"),
    };
};
// 验证码校验正则
const checkValidateCode = (verifiCode) => {
    const verifiCodeReg = /^[0-9]{4}$/;
    const res = verifiCodeReg.test(verifiCode);
    return {
        res,
        error: res ? "" : t("PROFILE_ACCOUNT_INVALID_VERIFY_CODE"),
    };
};
// 重置倒计时
const countdown = ref(null);
const resetCountdown = () => {
    countdown.value.reset();
    isDisabled.value = false;
    isLoading.value = false;
};
const renderCountdown = ({ seconds }) => {
    if (seconds == 0) {
        return "60 s";
    }
    return `${String(seconds).padStart(2, "0")} s`;
};

const defaultForm = () => {
    return {
        validateCode: {
            value: "",
            error: "",
            status: "",
            check: checkValidateCode,
        },
        password: {
            value: "",
            error: "",
            status: "",
            check: checkPassword,
        },
        aginPassword: {
            value: "",
            error: "",
            status: "",
            check: checkConfirmPassword,
        },
    };
};
// 註冊表单
const forms = ref(defaultForm());

const checkValue = (key) => {
    const formItem = forms.value[key];
    let { res, error } = formItem.check(formItem.value);
    if (res) {
        formItem.status = "checked";
        formItem.error = "";
    } else {
        formItem.status = "error";
        formItem.error = error;
    }
    forms.value[key] = { ...formItem };
    return res;
};
// 获取验证码
const getVerifiCode = async () => {
    if (isLoading.value) {
        return;
    }
    isLoading.value = true;
    let account = user.value.email;
    const { status, data, message } = await sendCodeByReset({ account });
    isLoading.value = false;
    if (status == 0) {
        isDisabled.value = true;
        openToast.success(message);
        return;
    }
    openToast.error(message);
};
// 忘记密碼
const handleForgot = async () => {
    if (subLoading.value) {
        return;
    }
    let k = 0;
    let req = {};
    for (const key in forms.value) {
        const formItem = forms.value[key];
        req[key] = formItem.value;
        let { res, error } = formItem.check(formItem.value);
        if (!res) {
            formItem.status = "error";
            formItem.error = error;
            k += 1;
        } else {
            formItem.status = "checked";
            formItem.error = "";
        }
        forms.value[key] = { ...formItem };
    }
    if (k !== 0) {
        return false;
    }
    subLoading.value = true;
    req.password = strToMd5(req.password);
    req.account = user.value.email;
    delete req.aginPassword;
    let { status, data, message } = await resetPassword(req);
    subLoading.value = false;

    if (status === 0) {
        openToast.success(t("TOAST_RESET_PASSWORD_SUCCESS"));
        emits("update:show", false);
        useClearUserInfo();
        await navigateTo(localePath("/account/login"));
        return false;
    }
    openToast.error(message);
};

const handleClose = () => {
    forms.value = defaultForm();
    if (isDisabled.value) resetCountdown();
    emits("update:show", false);
};
</script>

<style lang="scss" scoped>
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-transition: background-color 5000s ease-in-out 0s;
    transition: background-color 5000s ease-in-out 0s;
    background-color: transparent !important;
    -webkit-text-fill-color: #fff !important;
    /* 其他样式 */
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* 隐藏Firefox中的步进器 */
input[type="number"] {
    -moz-appearance: textfield;
}
.check-error {
    &::after {
        content: var(--error-msg);
        display: block;
        position: absolute;
        bottom: 6px;
        left: 0;
        right: 0;
        font-size: 12px;
        color: #fb5151;
        text-align: left;
        animation: duran 0.3s ease-in;
    }
    .form-item {
        border-color: #fb5151;
    }
}
@keyframes duran {
    0% {
        transform: translateX(0px);
    }
    70% {
        transform: translateX(6px);
    }
    100% {
        transform: translateX(0px);
    }
}
.form-item {
    @apply rounded-lg h-10 px-4 flex items-center bg-fill-ipt-1 focus-within:border-primary gap-1;
}
</style>
