<template>
    <div class="text-xs dark:text-color-c8">
        <div class="flex items-center overflow-hidden">
            <div class="flex items-center gap-2 cursor-pointer w-full" :class="{ 'max-w-32': comment.firstCommentId }" @click="useToCommunityHome(comment.ownerAcc.userId)">
                <n-avatar class="shrink-0" v-if="comment.ownerAcc.userAvatarUrl" round :size="28" :src="comment.ownerAcc.userAvatarUrl" />
                <n-icon class="shrink-0" v-else round :size="28">
                    <IconsPerson />
                </n-icon>
                <span class="text-sm text-nowrap overflow-hidden whitespace-normal text-ellipsis">{{ comment.ownerAcc.userName }}</span>
            </div>
            <div v-if="comment.firstCommentId" class="flex items-center max-w-40 overflow-hidden">
                <IconsCaretDown class="-rotate-90 text-2xl shrink-0" />
                <span class="text-sm overflow-hidden whitespace-nowrap text-ellipsis cursor-pointer" @click="useToCommunityHome(comment.targetAcc.userId)">{{ comment.targetAcc.userName }}</span>
            </div>
        </div>
        <div class="mt-3 break-all">
            <span v-if="comment.deleted" class="opacity-80">{{ t("COMMUNITY_DEL_COMMENT_TXT") }}</span>
            <span v-else-if="comment.reported" class="opacity-80">{{ t("COMMUNITY_REPORT_COMMENT_TXT") }}</span>
            <span v-else>{{ comment.content }}</span>
        </div>
        <div class="mt-2 flex items-center gap-5">
            <span class="opacity-70">{{ formatTime() }}</span>
            <div class="ml-auto flex items-center gap-5">
                <n-dropdown v-if="commentMoreActionList.length > 0" :options="commentMoreActionList" placement="top" class="explore-more-dropdown" trigger="click" @select="checkMoreAction">
                    <div class="cursor-pointer rounded-full text-base w-8 h-8 flex items-center justify-center text-text-4 hover:text-text-2 hover:bg-fill-wd-1">
                        <IconsHorMore />
                    </div>
                </n-dropdown>
                <div
                    v-if="!comment.deleted && !comment.reported"
                    class="cursor-pointer rounded-full text-base w-8 h-8 flex items-center justify-center text-text-4 hover:text-text-2 hover:bg-fill-wd-1"
                    @click="handleReplyToCurrentComment"
                >
                    <IconsNoticeComments />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import LangText from "@/components/LangText.vue";
import { IconReport, Dele, Alert } from "@/icons/index.js";
import { useReportContent, useToCommunityHome } from "@/hook/updateAccount";
import { NIcon } from "naive-ui";
import { formatCommentDate } from "@/utils/tools";
import { delCommentChilde, delCommentMain } from "@/api";

const emits = defineEmits(["reply", "hiddenSelf"]);
const props = defineProps({
    comment: {
        type: Object,
    },
    selfUserId: {
        type: [String, Number],
    },
});
const renderIcon = (icon, props) => {
    return () => {
        return h(
            NIcon,
            { size: 20, ...props },
            {
                default: () => h(icon),
            }
        );
    };
};
const commentMoreActionList = computed(() => {
    if (props.selfUserId != props.comment.ownerAcc?.userId && !props.comment.deleted && !props.comment.reported) {
        return [
            {
                label: () => h(LangText, { labelKey: "REPORT", class: "text-text-drop-5" }),
                key: "REPORT",
                icon: renderIcon(Alert, { class: "text-text-drop-5" }),
                props: {
                    class: "danger-bg",
                },
            },
        ];
    }

    if (props.selfUserId == props.comment.ownerAcc?.userId && !props.comment.deleted) {
        return [
            {
                label: () => h(LangText, { labelKey: "TOOLBAR_DELETE" }),
                key: "TOOLBAR_DELETE",
                icon: renderIcon(Dele, { class: "text-text-drop-5" }),
                props: {
                    class: "danger-bg",
                },
            },
        ];
    }
    return [];
});
const checkMoreAction = async (command) => {
    if (props.comment.deleted || props.comment.reported) {
        return;
    }
    //举报 -评论
    if (command === "REPORT") {
        const { fileId, id } = props.comment;
        const status = await useReportContent({ type: "COMMENT", commFileId: fileId, commentId: id });
        status === "confirm" && emits("hiddenSelf", "report");
        return;
    }
    // 删除 -评论
    if (command === "TOOLBAR_DELETE") {
        const { showMessage } = useModal();
        showMessage({
            style: { width: "380px" },
            cancelBtn: t("COMMON_BTN_CANCEL"),
            confirmBtn: t("TOOLBAR_DELETE"),
            content: h("div", null, t("COMMUNITY_DEL_COMMENT")),
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_CONFIRM"),
        })
            .then(dleCommunityComment)
            .catch(() => {});
    }
};
//获取当前格式化时间
const formatTime = () => {
    if (props.comment?.createTime) {
        const { diff, unit, units, label } = formatCommentDate(props.comment.createTime);
        if (label) {
            return label;
        }
        const time = Math.max(diff, 1);
        if (time === 1) {
            return t(unit, { time });
        }
        return t(units, { time });
    }
    return "";
};

//确认删除评论
const dleCommunityComment = async () => {
    let delCallback = delCommentChilde;
    const param = {
        commentId: props.comment.id,
    };
    if (!props.comment.firstCommentId) {
        delCallback = delCommentMain;
        param.commFileId = props.comment.fileId;
    }
    const { status, message } = await delCallback(param);
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    emits("hiddenSelf", "delete");
};

// 回复 当前评论
const handleReplyToCurrentComment = () => {
    if (props.comment.deleted || props.comment.reported) {
        return;
    }
    emits("reply", { ...props.comment });
};
</script>

<style lang="scss" scoped>
:global(.explore-more-dropdown) {
    @apply min-w-40 border border-solid border-border-t-1 bg-bg-6;
    padding: 8px !important;
}
:global(.explore-more-dropdown .n-dropdown-option-body) {
    @apply px-0;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before) {
    left: 0 !important;
    right: 0 !important;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending.danger-bg::before) {
    background-color: var(--p-fill-drop-5);
}
</style>
