<template>
    <div class="w-full flex justify-between h-full overflow-hidden items-center bg-bg-7 relative">
        <!-- 左侧面板 -->
        <div class="h-full bg-bg-2 w-[360px] hidden lg:block p-4 border-r border-border-t-2 pt-0">
            <n-tabs type="line" animated :style="tabsLineStyle" class="h-full">
                <!-- 图片生成 PC-->
                <n-tab-pane name="Image" :tab="t('CREATE.DISPLAY_TYPE_IMAGE')" class="flex h-full flex-col justify-between">
                    <!-- 生图输入框 -->
                    <div class="flex-1 flex flex-col gap-4 overflow-y-auto overflow-x-hidden no-scroll pb-4 !pr-0">
                        <div class="flex flex-col no-scroll bg-fill-ipt-1 rounded-lg">
                            <!-- 输入提示词 -->
                            <CreateInput :value="createStore.prompt" :maxRows="14" :placeholder="modelPlaceholder || t('SHORT_DRAW_WHAT')" @change="handleSetPrompt" @keyDown="handleKeyDown" />
                            <div class="w-full flex items-center h-10 mb-2 justify-between p-2 pt-4">
                                <div class="flex items-center gap-2">
                                    <!-- 提示词翻译 -->
                                    <CreateTranslator :value="createStore.prompt" :disabled="disabledTranslator" @change="handleSetPrompt" @translated="trackEvents('auto_translate')" />
                                    <!-- 提示词增强 -->
                                    <CreatePromptEnhancer
                                        :value="createStore.prompt"
                                        :disabled="disabledEnhanceAndTranslate"
                                        @change="handleSetPrompt"
                                        @enhanced="trackEvents('enhance')"
                                        :showError="showEnhanceError"
                                    />
                                </div>
                                <!-- 已经选中的图像参考 -->
                                <CreateReferenceImageSelectedRefers />
                            </div>
                        </div>
                        <!-- 图生图 -->
                        <CreateReferenceImage @changeModel="handleSetModelId" v-if="!isIpad" />
                        <!-- 模型选择 -->
                        <ClientOnly>
                            <CreateModelPanel :modelId="createStore.model_id" v-model:showNewTag="showNewTag" @change="handleSetModelId" @forbid="handleModelForbid">
                                <template #trigger>
                                    <div class="group w-full">
                                        <div class="w-full flex items-center text-text-4 text-sm mb-2 gap-1">
                                            <span>{{ $t("CONFIG_BASE_MODEL") }}</span>
                                            <IconsHelp
                                                class="text-text-4 cursor-pointer select-none hover:text-text-2 hidden group-hover:block"
                                                @click.stop.prevent="showFeatureModal = !showFeatureModal"
                                            />
                                        </div>
                                        <div
                                            class="model-trigger__wrapper w-full cursor-pointer h-12 p-2 bg-fill-wd-1 relative hover:bg-fill-wd-2 transition-all duration-[250] rounded-lg flex items-center g-node-model gap-2 group text-sm font-medium"
                                            v-if="createStore.model_id"
                                        >
                                            <img v-if="realSelectedItem.icon" :src="realSelectedItem.icon || defaultModel.icon" class="w-8 h-8 rounded-lg overflow-hidden shrink-0" />
                                            <span class="text-text-2 group-hover:text-text-1">{{ realSelectedItem.label }}</span>
                                            <img src="@/assets/images/subscribe/icon_fo_all_member.webp" class="size-4" alt="" v-if="realSelectedItem.isVipModel" />
                                            <CreateModelPanelNewModelPreview v-if="showNewTag && isCreatePage" v-model:showNewTag="showNewTag" />
                                        </div>
                                    </div>
                                </template>
                            </CreateModelPanel>
                        </ClientOnly>
                        <!-- 图像比例 -->
                        <CreateResolution
                            :defaultCheckRatioId="createStore.defaultCheckRatioId"
                            :modelId="createStore.model_id"
                            :disabled="disableResolutionChange"
                            :showAuto="disableResolutionChange"
                            @change="handleSetRatioId"
                        />
                        <!-- 生图张数 -->
                        <CreateBatchSizeSelect :batch_size="createStore.batch_size" :disabled="disabledBatchSizeChange" @change="handleSetBatchSize" />
                        <!-- 高级设置 cfg steps等 -->
                        <CreateAdvancedSettings />
                    </div>
                    <!-- 参数解析按钮 -->
                    <Button v-if="isParseMode" type="primary" class="w-full rounded-lg" @click="loadParseParam">
                        <span>{{ t("APPLY_PARAM") }}</span>
                    </Button>
                    <!-- 生图按钮 Pc-->
                    <n-tooltip placement="top-end" trigger="hover" :delay="100" :show-arrow="false" raw v-else-if="!currentTaskQueue.isLoading">
                        <template #trigger>
                            <Button type="primary" class="w-full rounded-lg" size="large" @click="handleCreateGenerator(createStore.generateConfig)">
                                {{ $t("CREATE.GENERATE") }}
                                <SubscribeExpendConst :feature-type="lumenCostKey" :num="createStore.batch_size" class="ml-3" />
                            </Button>
                        </template>

                        <div class="tips-box">
                            <span>{{ t("ESTIMATE_COST_LUMENS", lumenCostKey == "FLUX_1_DEV" ? 2 : 1) }}</span>
                            <div>{{ t("GEN_SHORTCUTS", { shortcuts: shortcut ? "Enter" : "Ctrl + Enter" }) }}</div>
                        </div>
                    </n-tooltip>
                    <Button v-else class="w-full rounded-lg" type="primary" :disabled="true" :loading="true"> </Button>
                </n-tab-pane>
                <!-- 视频生成 待开启 -->
                <!-- <n-tab-pane name="Video" tab="Video"> Video </n-tab-pane> -->
            </n-tabs>
        </div>

        <!-- 头 + 生图结果 -->
        <div class="flex flex-col flex-1 h-full w-full overflow-hidden justify-center items-center">
            <!-- 顶部 搜索 & 剩余Lumens PC -->
            <div class="w-full h-[64px] justify-end py-4 hidden lg:flex px-4 lg:px-6">
                <div class="flex items-center gap-3 text-text-2">
                    <!-- 搜索 -->
                    <div class="h-10 flex rounded-3xl items-center justify-end overflow-hidden bg-fill-btn-1">
                        <transition name="width">
                            <div :class="{ 'w-0': !showSearch, 'w-52 px-4 ': showSearch }">
                                <input class="bg-transparent outline-none min-w-0 w-full h-10" maxlength="50" :placeholder="t('COMMON_SEARCH_PLACEHOLDER')" v-model="searchWords" />
                            </div>
                        </transition>
                        <div class="shrink-0" @click="handleTriggerSearch">
                            <IconsSearch class="size-5 mx-2.5 cursor-pointer" />
                        </div>
                    </div>
                    <div class="rounded-full bg-fill-wd-1 h-10 pl-2 pr-3 flex items-center gap-1.5 cursor-pointer" @click="handleOpenLumenModal">
                        <IconsGradientLumenRound class="size-6" />
                        <!-- 剩余lumen  -->
                        <span class="ml-1.5 font-medium">{{ subscribeStore.remainTotalLumens }}</span>
                    </div>
                </div>
            </div>
            <!-- 剩余Lumens H5——————————————————  由于放到panel中无法定位到顶部 所以单独放置在外层-->
            <div class="rounded-full h-10 pl-2 pr-3 items-center gap-1.5 absolute right-3 top-3 text-text-2 lg:hidden flex cursor-pointer z-[20]" @click="handleOpenLumenModal">
                <IconsGradientLumenRound class="size-6" />
                <span class="ml-1.5 font-medium">{{ subscribeStore.remainTotalLumens }}</span>
            </div>
            <!-- 顶部 生图设置 H5———————————————— -->
            <div class="w-full block lg:!hidden px-4">
                <n-tabs type="line" animated :style="tabsLineStyle">
                    <n-tab-pane name="Image" :tab="t('CREATE.DISPLAY_TYPE_IMAGE')" class="flex flex-col justify-between">
                        <CreateRelaxLimit :isShow="showRelaxTips" class="lg:hidden mb-3" />
                        <div class="flex flex-col no-scroll bg-fill-ipt-1 rounded-lg">
                            <CreateInput
                                :value="createStore.prompt"
                                :placeholder="modelPlaceholder || t('SHORT_DRAW_WHAT')"
                                :minRows="3"
                                :maxRows="3"
                                @change="handleSetPrompt"
                                @keyDown="handleKeyDown"
                            />
                            <div class="w-full flex items-center h-10 mb-2 justify-between p-2 pt-4">
                                <div class="flex items-center gap-2">
                                    <CreateTranslator :value="createStore.prompt" :disabled="disabledTranslator" @change="handleSetPrompt" @translated="trackEvents('auto_translate')" />
                                    <CreatePromptEnhancer
                                        :value="createStore.prompt"
                                        :disabled="disabledEnhanceAndTranslate"
                                        @change="handleSetPrompt"
                                        :showError="showEnhanceError"
                                        @enhanced="trackEvents('enhance')"
                                    />
                                </div>
                                <!-- 已经选中的图像参考 -->
                                <CreateReferenceImageSelectedRefers />
                            </div>
                        </div>
                        <div class="w-full flex items-center justify-between mt-3 pb-2">
                            <div class="flex items-center gap-3">
                                <CreateSettingsSetH5
                                    :disableResolutionChange="disableResolutionChange"
                                    :realSelectedItem="realSelectedItem"
                                    :disabledBatchSizeChange="disabledBatchSizeChange"
                                    @setConfigs="handleSetConfigs"
                                />
                                <CreateReferenceImage class="block lg:hidden" @changeModel="handleSetModelId" v-if="isIpad" />
                            </div>
                            <!-- 生图按钮 H5—————————— -->
                            <div class="w-[138px]">
                                <!-- 参数解析按钮 -->
                                <Button v-if="isParseMode" type="primary" class="!w-full rounded-lg" @click="loadParseParam">
                                    <span>{{ t("APPLY_PARAM") }}</span>
                                </Button>
                                <!-- 生图按钮 H5—————————— -->
                                <Button type="primary" class="!w-full rounded-lg" size="large" @click="handleCreateGenerator(createStore.generateConfig)" v-else-if="!currentTaskQueue.isLoading">
                                    <IconsGen class="size-5 font-medium" />
                                    <SubscribeExpendConst :feature-type="lumenCostKey" :num="createStore.batch_size" class="ml-3" />
                                </Button>
                                <!-- loading 状态 -->
                                <Button v-else class="!w-full rounded-lg" type="primary" :disabled="true" :loading="true"> </Button>
                            </div>
                        </div>
                    </n-tab-pane>
                    <!-- 视频生成 待开启 -->
                    <!-- <n-tab-pane name="Video" tab="Video"> Video </n-tab-pane> -->
                </n-tabs>
            </div>
            <CreateRelaxLimit :isShow="showRelaxTips" class="!w-fit hidden lg:flex" />
            <CreateCreateHistory :keyWords="searchWords" @rerun="handleCreateGenerator" @clearSearch="searchWords = ''" />
        </div>
        <CreateModelPanelModelFeatureModal v-model:show="showFeatureModal" />
    </div>
</template>
<script setup>
import { KEEPALIVE_PAGES, MjModelId, defaultModel, REFER_TYPES } from "@/utils/constant.js";
import { tabsLineStyle } from "@/utils/constant-style.js";
import { getFuncNameByModelId, isNamiyaModel } from "@/utils/tools.js";
import { useCurrentTaskQueue, useCreateStore, useSupportModelList } from "@/stores/create";
import { useSubscribeModal, useSubPermission, useBuyLumenModal } from "@/hook/subscribe";
import { useGoToCreate, useGetModelInfo, useCheckMaxTask, useCommonLimitValidate, longTaskDialog, useUpdateLumen, usePromptController } from "@/hook/create";
import { useGlobalError } from "@/hook/error.js";
import { useAppendPreloadTask } from "@/hook/create.js";
import { Alert } from "@/icons/index.js";
import BusinessModal from "@/components/business/modal/index.vue";
import { imageGenerator } from "@/api";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
import { useSubscribeStore } from "@/stores/subscribe";
import { useUserProfile } from "@/stores";
const { isIpad } = storeToRefs(useThemeStore());
const { toCreate } = useGoToCreate();

const { t } = useI18n({ useScope: "global" });
const updateSeo = () => {
    useSeoMeta({
        title: () => t("SEO_META.SEO_CREATE_TITLE"),
        ogTitle: () => t("SEO_META.SEO_CREATE_TITLE"),
        description: () => t("SEO_META.SEO_CREATE_DESC"),
        ogDescription: () => t("SEO_META.SEO_CREATE_DESC"),
    });
};
const userProfile = useUserProfile();
const subscribeStore = useSubscribeStore();
const showFeatureModal = ref(false);

const showNewTag = ref(false); //是否有模型上新
const { checkPermissionNotModal } = useSubPermission();
const createStore = useCreateStore();
defineOptions({
    name: KEEPALIVE_PAGES.IMAGE_CREATE_WEB,
});
const { openModal: openSubModal } = useSubscribeModal(); // 引入订阅弹窗
const { showMessage } = useModal();

const currentTaskQueue = useCurrentTaskQueue();

const lumenCostKey = computed(() => getFuncNameByModelId({ model_id: createStore.model_id }));
const searchWords = ref("");
const showSearch = ref(false);

//禁用翻译
const disabledTranslator = computed(() => {
    return !createStore.prompt || createStore.prompt?.length > 600;
});
//禁用增强
const disabledEnhanceAndTranslate = computed(() => {
    return !createStore.prompt || createStore.prompt?.length > 600 || isNamiyaModel({ model_id: createStore.model_id });
});

const showEnhanceError = computed(() => !isNamiyaModel({ model_id: createStore.model_id }));
//输入关键字进行搜索 搜索动作在create-history组件中处理
const handleTriggerSearch = () => {
    showSearch.value = !showSearch.value;
    if (!showSearch.value && searchWords.value) {
        searchWords.value = "";
    }
    trackEvents("search");
};
//h5设置生图参数
const handleSetConfigs = (params) => {
    const { type, payload } = params;
    if (!type) return;
    switch (type) {
        case "setModelId":
            handleSetModelId(payload);
            break;
        case "modelForbid":
            handleModelForbid(payload);
            break;
        case "setResolution":
            handleSetRatioId(payload);
            break;
        case "setBatchSize":
            handleSetBatchSize(payload);
            break;
    }
};

//更新提示词
const handleSetPrompt = (value) => {
    createStore.setPrompt(value);
};
//解析m模型参数
const getItem = (obj = {}) => {
    const { width, height, seed, steps, cfg, scheduler, negative_prompt, sampler_name, model_ability } = obj;
    let anime = null;
    if (model_ability?.animeStyleControl) {
        anime = model_ability.animeStyleControl;
    }
    let newParams = { width, height, seed, steps, cfg, scheduler, negative_prompt, sampler_name, anime };
    return newParams;
};
//使用模型默认参数 覆盖当前表单
const resetModelConfig = (modelId) => {
    const models = useGetModelInfo(modelId);
    const newModelConf = getItem(models.defaultParams);
    const { initDefaultConfig = {} } = getStaticModelById(modelId) || {};
    createStore.setResolution(createStore.resolution);

    createStore.setGenerateConfig({
        ...initDefaultConfig,
        ...newModelConf,
    });
};
//更新选中模型
const handleSetModelId = (value = defaultModel.id) => {
    //重置部分参数
    resetModelConfig(value);
    //切换模型 清空图生图参考方式
    createStore.setGenerateConfig({
        model_id: value,
        style_list: [],
        image_control_list: [],
        mainCategory: "",
        subCategory: "",
    });
    createStore.setSelectedRefers([]);
};

//  真实选中的模型
const realSelectedItem = computed(() => useGetModelInfo(createStore.model_id));

//更新生图比例
const referImageResolution = ref(); //用于存储图生图参考图片宽高的临时变量
// 禁用比例选择 满足一下两个条件 不可手动选择生图比例
const disableResolutionChange = computed(() => {
    const refer_list = createStore.selectedRefers.filter((item) => item.displayType === REFER_TYPES.IMAGE_REFER);
    const control_list = createStore.selectedRefers.filter((item) => item.displayType === REFER_TYPES.IMAGE_CONTROL);
    const c1 = refer_list.find((item) => item.style === "contentRefer"); //条件一： 存在contentRefer的图像参考方式
    const c2 = control_list[0]; //条件二： 存在图像控制方式

    if (c1 || c2) {
        referImageResolution.value = {
            width: c1?.width || c2?.width,
            height: c1?.height || c2?.height,
        };
    }
    return c1 || c2 ? true : false;
});
const handleSetRatioId = (value) => {
    createStore.setResolution(value);
};
//更新生图张数

const disabledBatchSizeChange = computed(() => {
    const modelInfo = {
        model_id: createStore.model_id,
    };
    return isMjModel(modelInfo) || isFluxKontextModel(modelInfo);
});
const handleSetBatchSize = (value) => {
    createStore.setBatchSize(value);
};

//当前模型不可使用
const handleModelForbid = (forbidAction) => {
    console.log("这个用户不能用这个模型需要升级会员");
    if (forbidAction === "modal") {
        openSubModal();
    } else {
        handleSetModelId(defaultModel.id);
    }
};

//是否展示 加载参数 按钮
const parseInfo = ref({});
const isParseMode = computed(() => {
    let res = parsePrompt(createStore.prompt);
    parseInfo.value = res;
    return res;
});

//格式化生图参数
const formatConf = (conf) => {
    const { modelList } = useSupportModelList();
    let { model_id, resolution, batch_size, scheduler, sampler_name, cfg, steps, seed, negative_prompt, prompt, anime, highPixels = false, mainCategory, subCategory } = conf;
    if (!isPicLumenArtV1({ model_id })) {
        highPixels = false;
    }
    let { width, height } = resolution;
    const { defaultParams = {} } = modelList.find((item) => item.value === model_id) || {};
    if (seed == -1) {
        seed = Math.floor(Math.random() * MAX_SEED) + 1;
    }
    let { denoise } = defaultParams;
    const newConf = {
        highPixels,
        model_id,
        prompt,
        negative_prompt,
        resolution: {
            width: Number(width),
            height: Number(height),
            batch_size,
        },
        model_ability: {
            anime_style_control: anime || null,
        },
        seed,
        steps: steps || 0,
        cfg: cfg || 0,
        sampler_name: sampler_name || defaultParams.sampler_name,
        scheduler: scheduler || defaultParams.scheduler,
        //扩展参数(高清修复需要使用)
        denoise,
    };
    if (mainCategory && subCategory) {
        newConf.mainCategory = mainCategory;
        newConf.subCategory = subCategory;
    }
    return newConf;
};

//提交生图任务
const { showError } = useGlobalError();
const { checkLimit } = useCommonLimitValidate();
const submitTask = async (conf, longTask = false) => {
    const { clearPromptInput } = createStore;
    const isAllowedToCreate = await checkLimit();
    if (!isAllowedToCreate) return;
    //检查Seed 是否合规
    if (!isValidSeed(conf.seed)) {
        showMessage({
            style: { width: "400px" },
            showCancel: false,
            confirmBtn: t("SHORT_CONFIRM_BTN"),
            content: h("div", null, t("SEED_ERROR_CONTENT")),
            icon: h(Alert, { class: "text-error text-3xl" }),
            title: t("DIALOG_TITLE_INV_VALUE"),
        });
        return;
    }
    if (!longTask) {
        //提示词处理 pony v6
        const { prompt } = formatPonyV6Prompt(conf, "input");
        conf.prompt = prompt;
    }

    // 埋点
    trackEvents(`resolution#ratio=${conf.resolution?.width / conf.resolution?.height}#resolution=${conf.resolution?.width}*${conf.resolution?.height}`);
    trackEvents(`image_count=${conf.resolution?.batch_size}`);
    createStore.selectedRefers?.length && trackEvents(`reference_image_popup_image_ref_count=${createStore.selectedRefers?.length || 0}`);
    createStore.subCategory && trackEvents(`reference_image_popup_style=${createStore.subCategory}`);

    // 并发已满 提交预载
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus) {
        let featuresTypeKey = PRELOAD_TASK_TYPE.TEXT_TO_PICTURE;
        const style_list = conf.multi_img2img_info?.style_list || [];
        const image_control_list = conf.img_control_info?.style_list || [];
        if (style_list.length > 0 || image_control_list.length > 0) {
            featuresTypeKey = PRELOAD_TASK_TYPE.PICTURE_TO_PICTURE;
        }
        useAppendPreloadTask({ genParameters: conf }, featuresTypeKey);
        clearPromptInput();
        return;
    }
    currentTaskQueue.updateReqStatus(true);
    toCreate(false);
    try {
        conf.continueCreate = longTask;
        const { data, status, message } = await imageGenerator(conf);
        clearPromptInput();
        currentTaskQueue.updateReqStatus(false);
        if (status === ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR) {
            showError(ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR);
            return;
        }
        // 耗时任务 二次确认 重新提交
        if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
            const hasReSubmit = await longTaskDialog();
            hasReSubmit && submitTask(conf, true);
            return;
        }

        if (status !== 0) {
            showError(status, {
                triggerEl: isFluxDevModel(conf) ? SUB_EL.FLUX_1_DEV : "other",
            });
            return;
        }
        let originCreate = "create";
        const referList = conf.multi_img2img_info?.style_list || [];
        if (referList.length > 0) {
            originCreate = "picCreate";
        }
        const list = mergeArraysById(currentTaskQueue.taskQueue, [
            {
                ...conf,
                originCreate,
                status: "pending",
                fastHour: data.fastHour,
                markId: data.markId,
                index: data.index,
                createTimestamp: getCurrentISO8601Time(),
            },
        ]);
        currentTaskQueue.updateTaskQueue(list);
    } catch (error) {
        openToast.error(error.message || t("TOAST_GEN_ERROR_CONTENT"));
        currentTaskQueue.updateReqStatus(false);
    }
};

//手动触发生图(普通生图)
const handleCreateGenerator = async (generateParams) => {
    const { prompt, model_id } = generateParams;
    let modelInfo = { model_id };
    if (!prompt && (isMjModel(modelInfo) || isFluxKontextModel(modelInfo))) {
        return openToast.error(t("TOAST_MJ_PROMPT_EMPTY_ERROR"));
    }
    trackEvents("generate");
    if (isNamiyaModel({ model_id }) && !prompt) {
        generateParams.prompt = modelPlaceholder.value;
    }
    searchWords.value = "";
    const conf = formatConf(generateParams);
    //如果有content refer和 image control 需要将生图比例中的resolution替换为选中的参考图片的比例
    if (disableResolutionChange.value) {
        conf.resolution = {
            ...conf.resolution,
            ...referImageResolution.value,
        };
    }
    const final = { ...generateParams, ...conf };
    submitTask(final);
};

const localePath = useLocalePath();
//解析生图参数
const loadParseParam = async () => {
    let res = { ...parseInfo.value };
    const isMember = await checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
    if (!isVipModel({ model_id: res.model_id }) || isMember) return startParse(res);
    trackEvents("member_model_popup_show");
    showMessage({
        style: { width: "500px", padding: 0 },
        showCancel: false,
        showConfirm: false,
        content: h(BusinessModal, {
            base: {
                title: t("CREATE.VIP_ONLY_MODEL.TITLE"),
                description: t("CREATE.VIP_ONLY_MODEL.DESC"),
                cancelText: t("MODEL_SWITCH_1"),
                confirmText: t("SUBSCRIBE_UPGRADE"),
            },
        }),
    })
        .then(async () => {
            trackEvents("member_model_popup_upgrade");
            const subscribePage = localePath("/user/subscribe");
            await navigateTo({ path: subscribePage });
        })
        .catch(() => {
            startParse(res);
            createStore.setModel(defaultModel.id);
        });
};
/**
 * 对传入参数及逆行解析并更新createStore中的生图参数
 * @param res
 */
const startParse = (res) => {
    trackEvents("load_params");
    const hasAllow = checkPermissionNotModal(SUBSCRIBE_PERMISSION.IMAGE_BATCH_MAX, res.batch_size);
    const targetModel = useGetModelInfo(res.model_id);
    const { defaultParams = {} } = targetModel;
    const { batch_size } = createStore.generateConfig;
    res = {
        ...res,
        ...targetModel?.initDefaultConfig,
    };

    if (!res.batch_size) {
        res.batch_size = batch_size;
    }
    if (!hasAllow) {
        res.batch_size = Math.min(2, res.batch_size);
    }
    // 原参数
    let allOriginalParams = {
        ...createStore.generateConfig,
        ...res,
    };
    createStore.setGenerateConfig({
        ...allOriginalParams,
        style_list: [],
        image_control_list: [],
        mainCategory: "",
        subCategory: "",
    });
    createStore.setSelectedRefers([]);
    const { cfg: defCfg = null, steps: defSteps = null, negative_prompt: defNegativePrompt = null } = defaultParams;
    if (isMjModel({ model_id: res.model_id }) || isMjModel({ model_id: createStore.model_id })) {
        createStore.setGenerateConfig({
            cfg: defCfg,
            steps: defSteps,
        });
    }
    if (isFluxSeries({ model_id: res.model_id })) {
        if (!isFluxDevModel({ model_id: res.model_id })) {
            createStore.setGenerateConfig({
                cfg: defCfg,
                steps: defSteps,
            });
        }
        createStore.setGenerateConfig({
            negative_prompt: defNegativePrompt,
        });
    }
};

//是否展示 relax 订阅弹窗
const showRelaxTips = computed(() => {
    const isVip = checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
    return createStore.taskIsRelaxMode && !isVip;
});

const shortcut = computed(() => {
    return userProfile.userConfig.shortcutKey === "enter";
});
//Ctrl/Cmd + Enter command 快捷键提交任务
const handleKeyDown = (event) => {
    // 只按enter提交任务
    if (shortcut.value && event.key === "Enter" && !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey) {
        event.preventDefault();
        handleCreateGenerator(createStore.generateConfig);
        return;
    }
    //组合键提交任务
    if (!shortcut.value && (event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        handleCreateGenerator(createStore.generateConfig);
    }
};

// 判断当前模型是否使用随机提示词
const modelPlaceholder = ref("");
const { getRandomPrompt } = usePromptController();
watch(
    () => createStore.model_id,
    (value) => {
        modelPlaceholder.value = getRandomPrompt(value);
    },
    {
        immediate: true,
    }
);

onMounted(() => {
    useUpdateLumen();
});
const isCreatePage = ref(true);

onActivated(async () => {
    nextTick(updateSeo);
    useUpdateLumen();

    isCreatePage.value = true;
});
onDeactivated(() => {
    isCreatePage.value = false;
});
onDeactivated(() => {});

const { openModal: openLumenModal } = useBuyLumenModal();
const handleOpenLumenModal = () => {
    trackEvents("lumens_balance");
    openLumenModal();
};

// 埋点
const trackEvents = (el, event = "") => {
    try {
        window.trackEvent(event || "Create", { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};
</script>
<style scoped lang="scss">
::v-deep(.n-tabs-pane-wrapper) {
    height: 100%;
}
</style>
