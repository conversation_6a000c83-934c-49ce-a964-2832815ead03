<template>
  <n-checkbox :on-update:checked="switchDefConfirm">
    <slot></slot>
  </n-checkbox>
</template>

<script setup>
const emit = defineEmits(['update:checked'])
const switchDefConfirm = (state) => {
  emit('update:checked', state)
}
</script> 

<style lang="scss" scoped>
/* Add your custom styles here */
::v-deep(.n-checkbox.n-checkbox--checked .n-checkbox-box,){
  background-color: #8961FF !important ; // Change the color as per your requirement
}
</style>