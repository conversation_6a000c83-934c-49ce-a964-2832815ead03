<template>
    <div class="w-full">
        <div class="form-item relative pb-7 w-full mt-2" :style="{ '--err': `'${t(errorTips.email)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center pl-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsEmail />
                </n-icon>

                <n-input class="text-dark-active-text" type="email" maxlength="100" :placeholder="t('ACC_SIGN_IN_EMAIL')" v-model:value="config.email" @change="checkEmail" />
            </div>
        </div>
        <div class="form-item relative pb-7 w-full" :style="{ '--err': `'${t(errorTips.password)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center px-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsLock />
                </n-icon>
                <n-input
                    class="text-dark-active-text"
                    :type="inputType"
                    maxlength="32"
                    :placeholder="t('ACC_SIGN_IN_PWD')"
                    v-model:value="config.password"
                    @keydown.enter="handleSignIn"
                    @change="checkPassword"
                />

                <n-icon size="24" @click="displayPwd = !displayPwd" class="cursor-pointer hover:text-dark-active-text">
                    <IconsNoEye v-if="displayPwd" />
                    <IconsEye v-else />
                </n-icon>
            </div>
        </div>
        <div class="text-xs flex items-center justify-between w-full">
            <div class="flex items-center gap-2 cursor-pointer" @click="handleRememberMe">
                <div class="border border-solid border-white/40 w-4 h-4 rounded-sm flex items-center justify-center" :class="{ 'bg-primary !border-primary': hasRemember }">
                    <n-icon size="14" class="text-dark-active-text" v-if="hasRemember">
                        <IconsSuccess />
                    </n-icon>
                </div>
                <span class="opacity-60 hover:opacity-100">{{ t("ACC_SIGN_IN_REMEMBER") }}</span>
            </div>
            <div class="opacity-60 underline cursor-pointer hover:opacity-100" @click="toFeature('ForgotComp')">
                {{ t("ACC_SIGN_IN_FORGOT") }}
            </div>
        </div>

        <div class="mt-8 w-full">
            <Button type="primary" class="h-10 w-full" rounded="xl" block :disabled="subLoading" :loading="subLoading" @click="handleSignIn">
                <span class="text-dark-active-text" v-if="!subLoading">{{ t("ACC_SIGN_IN_TITLE") }}</span>
            </Button>
        </div>
        <div class="mt-4 w-full text-end text-xs opacity-60 cursor-pointer hover:opacity-100" @click="toFeature('SignUpComp')">
            <span>{{ t("ACC_SIGN_IN_NO_ACC") }}</span>
            <span class="underline ml-1">{{ t("ACC_SIGN_IN_SIGN_UP") }}</span>
        </div>
    </div>
</template>

<script setup>
import { useSignInByEmail } from "@/hook/updateAccount";
import { strToMd5, validateEmail } from "@/utils/tools.js";
const { requestSignIn } = useSignInByEmail();
import { NIcon } from "naive-ui";

const props = defineProps(["btnStyle", "hasRemember"]);
const { t } = useI18n({ useScope: "global" });
const emit = defineEmits(["changeComponent", "update:hasRemember"]);
const toFeature = (key) => {
    emit("changeComponent", key);
    // 忘记密码
    if (key === "ForgotComp") {
        window.trackEvent("Sign", { el: "forgot_password" });
    }
};

const displayPwd = ref(false);
const subLoading = ref(false);

// 基础信息
const config = ref({
    email: "",
    password: "",
});
// 错误提示
const errorTips = ref({
    email: "NULL_CONTENT",
    password: "NULL_CONTENT",
});
const inputType = computed(() => {
    return displayPwd.value ? "text" : "password";
});

// 邮箱校验
const checkEmail = () => {
    const email = config.value.email;
    // console.log(email, '<<<<')
    if (validateEmail(email)) {
        errorTips.value.email = "NULL_CONTENT";
        return true;
    }
    errorTips.value.email = "PROFILE_ACCOUNT_INVALID_EMAIL";
    return false;
};
//密碼校验
const checkPassword = () => {
    const val = config.value.password;
    if (val.length >= 6) {
        errorTips.value.password = "NULL_CONTENT";
        return true;
    }
    errorTips.value.password = "PROFILE_ACCOUNT_INVALID_PASSWORD";
    return false;
};

// 点击记住我复选框
const handleRememberMe = () => {
    emit("update:hasRemember", !props.hasRemember);
    window.trackEvent("Sign", { el: "remember_me" });
};

//登录
const handleSignIn = async () => {
    window.trackEvent("Sign", { el: "sign_in_button" });
    try {
        const t1 = checkEmail();
        const t2 = checkPassword();
        if (!t1 || !t2 || subLoading.value) {
            return;
        }
        subLoading.value = true;
        const storageType = props.hasRemember ? "localStorage" : "sessionStorage";
        const reqBody = {
            account: config.value.email,
            password: strToMd5(config.value.password),
        };
        const { success, err } = await requestSignIn(reqBody, storageType);
        subLoading.value = false;
        if (!success) {
            openToast.error(err);
            return;
        }
    } catch (error) {
        subLoading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.form-item::after {
    @apply absolute text-xs text-error top-10 mt-0.5 left-0 block scale-90 origin-top-left;
    content: var(--err, "");
}
</style>
