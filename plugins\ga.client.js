import { browserInfo, loadScript } from "@/utils/tools";

export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.hook("app:mounted", () => {
        window.trackEvent = () => {};
        const { gaMeasureId, version } = useRuntimeConfig().public;
        if (!gaMeasureId) return;

        loadScript(`https://www.googletagmanager.com/gtag/js?id=${gaMeasureId}`)
            .then(() => {
                window.dataLayer = window.dataLayer || [];
                function gtag() {
                    dataLayer.push(arguments);
                }
                window.gtag = gtag;

                gtag("js", new Date());
                gtag("config", gaMeasureId, {
                    send_page_view: true,
                });

                window.trackEvent = function (eventname, { ps = "", el = "" }) {
                    try {
                        if (!eventname) return;
                        let { loginName } = useUserProfile().user;
                        if (loginName) {
                            loginName = loginName.replace("@", "#");
                        }
                        console.log(loginName, "------------------->>>>>>>>>>>");
                        const br = browserInfo();
                        gtag("event", eventname, {
                            uid_custom: loginName,
                            pv: version,
                            br,
                            ps,
                            el,
                        });
                    } catch (error) {
                        console.error("trackEvent error:", error);
                    }
                };
            })
            .catch((err) => {
                console.warn("GA script load failed:", err);
            });
    });
});
