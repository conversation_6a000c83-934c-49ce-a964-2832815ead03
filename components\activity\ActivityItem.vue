<template>
    <NuxtLinkLocale :to="'/activity/' + activity.id" class="relative block rounded-xl overflow-hidden bg-bg-2 cursor-pointer hover:shadow-lg transition-all duration-300">
        <div class="h-[253px] flex justify-center w-full">
            <img class="w-full h-full object-cover" loading="lazy" :src="activity.cover" />
        </div>
        <div class="pt-3 p-4">
            <div class="flex flex-col gap-y-[2px] mb-3">
                <n-ellipsis :line-clamp="1" class="text-base font-medium text-text-2">{{ activity.title }}</n-ellipsis>
            </div>
            <div class="flex gap-x-2">
                <div v-if="tags.length" class="flex flex-1 gap-x-2 overflow-hidden">
                    <ActivityReward :tags="tags" />
                </div>
                <ActivityLabel class="flex-shrink-0" :icon="IconFire" :text="numberFormat(activity.imageNum)" />
            </div>
        </div>
        <ActivityStatus :activity="activity" />
    </NuxtLinkLocale>
</template>

<script setup>
import { numberFormat } from "@/utils/tools";

import ActivityStatus from "@/components/activity/ActivityStatus.vue";
import ActivityLabel from "@/components/activity/ActivityLabel.vue";
import ActivityReward from "@/components/activity/ActivityReward.vue";
import IconFire from "@/components/icons/Fire.vue";

const props = defineProps({
    activity: {
        type: Object,
        default: () => ({}),
    },
});

const tags = computed(() => {
    try {
        return JSON.parse(props.activity.tags) || [];
    } catch {
        return [];
    }
    // JSON.parse(props.activity.tags)
});
</script>
