<template>
  <!--使用ref属性给图片元素命名为imageRef-->
  <img ref="imageRef" class="w-full h-full object-contain " :src="imageSrc">
</template>

<script setup>
import Cropper from 'cropperjs';
import "cropperjs/dist/cropper.css";
const props = defineProps({
  //图片地址
  imageSrc: {
    type: String,
    required: true
  },
  //aspectRatio:置裁剪框为固定的宽高比
  aspectRatio: {
    type: Number,
    default: NaN,
  },
  //viewMode: 视图控制
  viewMode: {
    type: Number,
    default: 1,
  },
  //autoCropArea: 设置裁剪区域占图片的大小 值为 0-1 默认 0.8 表示 80%的区域
  autoCropArea: {
    type: Number,
    default: 0.8,
  },
  minWidth: {
    type: Number,
    default: 128,
  },
  minHeight: {
    type: Number,
    default: 128,
  },
})
//绑定图片的dom对象
const imageRef = ref(null)
let cropper = null;
//使用Cropper构造函数创建裁剪器实例，并将图片元素和一些裁剪选项传入
onMounted(() => {
  cropper = new Cropper(imageRef.value, {
    aspectRatio: props.aspectRatio,
    viewMode: props.viewMode,
    autoCropArea: props.autoCropArea,
    minCropBoxWidth: props.minWidth,
    minCropBoxHeight: props.minHeight,
    dragMode: 'move',
    background: false,
    highlight: false,
  });
});

// 将实例暴露给父组件。
const getCropper = () => cropper;
defineExpose({ getCropper })
//销毁
onBeforeUnmount(() => {
  cropper && cropper.destroy()
})
</script>
