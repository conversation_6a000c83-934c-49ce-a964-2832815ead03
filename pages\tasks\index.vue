<template>
    <MobileHeader :title="t('TASKS_CENTER_TITLE')" />

    <div class="h-full bg-bg-1 overflow-hidden font-medium p-6 overflow-y-auto scroll-container">
        <div class="max-w-[1200px] mx-auto">
            <n-spin :show="pageLoading">
                <template #icon>
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </template>
                <h1 class="text-center mt-8 font-semibold text-2.5xl text-text-1">{{ $t("TASKS_CENTER_TITLE") }}</h1>
                <div class="flex justify-end my-2 text-base dark:text-dark-tag-bg">
                    <div
                        @click="handleModal()"
                        class="flex gap-x-1.5 items-center justify-center h-10 px-4 text-sm font-medium text-primary-6 hover:bg-fill-btn-6 active:bg-fill-btn-7 rounded-full cursor-pointer"
                    >
                        <n-icon size="20">
                            <IconsLog />
                        </n-icon>
                        <span>{{ $t("TASKS_CENTER_SEE_REWARD_RECORD") }}</span>
                    </div>
                </div>
                <!-- 曝光率Banner -->
                <NuxtLinkLocale v-if="isHaveFirstBuySub" to="/user/subscribe" class="relative w-full min-h-16 px-5 py-4 overflow-hidden rounded-xl flex items-center gap-3 group">
                    <img class="absolute inset-0 size-full object-cover object-center pointer-events-none" src="@/assets/images/vip/img_user_vip_rewards_bg.webp" alt="VIP promotion background" />

                    <div class="relative z-10 flex items-center marker: gap-2 w-full">
                        <n-icon :size="32" class="shrink-0">
                            <IconsDiamondForAllMember />
                        </n-icon>

                        <template v-if="!isMobile">
                            <p class="flex-1 text-sm font-medium">{{ $t("TASK_BANNER.TITLE_PC") }}</p>
                            <div
                                class="shrink-0 rounded-full md:min-w-24 px-4 h-10 py-2 bg-gradient-to-r from-[#FF8C00] via-[#6EB75C] to-[#00A7C8] text-white text-sm font-medium hover:shadow-lg cursor-pointer transition-opacity"
                            >
                                <span class="hidden md:block">{{ $t("TASK_BANNER.BTN") }}</span>
                            </div>
                        </template>
                        <template v-else>
                            <n-marquee class="flex-1 text-sm font-medium">{{ $t("TASK_BANNER.TITLE_MO") }}</n-marquee>
                            <n-icon :size="20" class="block md:hidden"> <IconsSuccess /> </n-icon>
                        </template>
                    </div>
                </NuxtLinkLocale>

                <div class="mt-14">
                    <div class="mb-4">
                        <p class="text-text-4 text-sm font-medium">{{ $t("TASKS_CENTER_DAILY_REWARD") }}</p>
                    </div>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <template v-for="(item, index) in taskStore.dailyRewardsTasks" :key="index.taskId">
                            <Reward :data="item" />
                        </template>
                    </div>
                </div>
                <div class="mt-6">
                    <div class="mb-4">
                        <p class="text-text-4 text-sm font-medium">{{ $t("TASKS_CENTER_ACHIEVEMENT_REWARD") }}</p>
                    </div>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <RewardStep :data="taskStore.likesTasks" />
                        <RewardStep :data="taskStore.followTasks" />
                        <template v-for="(item, index) in taskStore.achievementRewardTasks" :key="index.taskId">
                            <Reward :data="item" />
                        </template>
                    </div>
                </div>
            </n-spin>
        </div>
    </div>
    <RewardRecordModal v-model:visible="modelVisible" />
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
import { useSubscribeStore } from "@/stores/subscribe.js";
const subscribeStore = useSubscribeStore();

const { isHaveFirstBuySub } = storeToRefs(subscribeStore);

const { t } = useI18n({ useScope: "global" });

useSeoMeta({
    title: () => t("SEO_META.SEO_TASK_TITLE"),
    ogTitle: () => t("SEO_META.SEO_TASK_TITLE"),
    description: () => t("SEO_META.SEO_TASK_DESC"),
    ogDescription: () => t("SEO_META.SEO_TASK_DESC"),
});
import { useTaskStore } from "@/stores/task";

import Reward from "@/components/task/Reward.vue";
import RewardStep from "@/components/task/RewardStep.vue";
import RewardRecordModal from "@/components/task/RewardRecordModal.vue";
import IconsDiamondForAllMember from "@/components/icons/DiamondForAllMember.vue";

const taskStore = useTaskStore();
const pageLoading = ref(false);
const { isMobile } = storeToRefs(useThemeStore());

onMounted(() => {
    // todo 更新每日任务，设计提供一个弹窗，在弹窗里面触发调用
    // resetTaskApi()
    taskStore.trackEvent("rewards_center");
    pageInit();
});

const pageInit = async () => {
    pageLoading.value = true;
    await taskStore.getTaskList().finally(() => {
        pageLoading.value = false;
    });
};

const modelVisible = ref(false);

const handleModal = () => {
    taskStore.trackEvent("rewards_history");
    modelVisible.value = true;
};
</script>

<style lang="scss" scoped></style>
