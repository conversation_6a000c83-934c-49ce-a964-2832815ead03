<template>
    <n-input
        type="textarea"
        ref="textareaRef"
        class="min-h-[88px]"
        :class="[isIpad ? 'h5-input' : 'pc-input']"
        :placeholder="placeholder"
        :bordered="false"
        :maxlength="2500"
        :rows="1"
        v-model:value="myValue"
        :autosize="{ minRows: minRows, maxRows: maxRows }"
        :on-update:value="handleSetPrompt"
        @keydown.stop="handleKeyDown"
    />
</template>
<script setup>
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isIpad } = storeToRefs(useThemeStore());

const emit = defineEmits(["update:value", "change", "keyDown"]);
const props = defineProps({
    value: {
        type: String,
        default: "",
    },
    placeholder: {
        type: String,
        default: "",
    },
    minRows: {
        type: Number,
        default: 4,
    },
    maxRows: {
        type: Number,
        default: 8,
    },
});
const myValue = computed({
    get() {
        return props.value;
    },
    set(value) {
        emit("update:value", value);
    },
});
const handleSetPrompt = (value) => {
    myValue.value = value;
    emit("change", value);
};

const handleKeyDown = (e) => {
    emit("keyDown", e);
};

onMounted(() => {
    myValue.value = props.value;
});
</script>
<style scoped lang="scss">
.h5-input {
    ::v-deep(.n-scrollbar-rail) {
        display: none !important;
        width: 0 !important;
    }
}
.pc-input {
    ::v-deep(.n-scrollbar-rail__scrollbar) {
        --n-scrollbar-color: var(--p-fill-wd-3) !important;
        --n-scrollbar-color-hover: var(--p-fill-wd-4) !important;
    }
}
</style>
