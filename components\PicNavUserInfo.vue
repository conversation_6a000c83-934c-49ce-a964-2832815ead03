<!--
 * @Author: HuangQS
 * @Description: <PERSON><PERSON><PERSON><PERSON>底部，用户信息头像组件 功能列表
 * @Date: 2025-07-24 10:23:57
 * @LastEditors: <PERSON><PERSON><PERSON><EMAIL>
 * @LastEditTime: 2025-08-08 15:49:04
-->
<template>
    <div class="p-4 border-t border-solid bg-bg-2 border-border-1">
        <n-popover
            :show="showUserPopover"
            :show-arrow="false"
            :placement="isFold ? 'right' : 'top'"
            class="shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)] rounded-2xl"
            raw
            trigger="click"
        >
            <template #trigger>
                <div
                    ref="popoverTriggerRef"
                    :class="[
                        showUserPopover && !isFold ? '!bg-fill-btn-3' : '',
                        isFold
                            ? 'flex-col-reverse gap-4 h-full p-2 bg-transparent border-none hover:bg-transparent'
                            : 'flex-row h-12 px-2 bg-fill-btn-1 border border-solid border-border-t-1 rounded-full hover:bg-fill-btn-2',
                    ]"
                    class="bg-fill-btn-1 border border-solid border-border-t-1 px-2 h-12 rounded-full flex items-center justify-between gap-1.5 hover:bg-fill-btn-2 cursor-pointer transition-all"
                    @click="handleOpenUserPopover"
                >
                    <ClientOnly>
                        <UsersAvatar
                            :src="userProfile?.user?.avatarUrl"
                            :icon-size="28"
                            class="!shrink-0 flex items-center !size-11 !text-text-3"
                            :class="{
                                'bg-fill-opt-1 p-2 rounded-full hover:!bg-fill-btn-2 transition-all duration-[250]': isFold,
                            }"
                        />

                        <div class="flex-1 truncate text-sm text-text-2 font-medium select-none" :class="{ '!hidden': isFold }">{{ userProfile.user?.userName || "" }}</div>
                        <SysNotice :offset="isFold ? [-6, -2] : [0, -8]">
                            <div
                                class="rounded-full p-1.5 hover:bg-fill-opt-2 transition-all duration-[250]"
                                :class="{
                                    'bg-fill-opt-1 p-3 rounded-full hover:!bg-fill-btn-2': isFold,
                                }"
                                @click.stop="handleOption('MESSAGE')"
                            >
                                <IconsNotice class="text-xl text-text-3" :class="'!text-text-1' && showUserPopover" />
                            </div>
                        </SysNotice>
                    </ClientOnly>
                </div>
            </template>
            <div v-if="!isMobile" ref="popoverContentRef" class="p-4 rounded-2xl border border-solid border-border-t-1 bg-bg-6 w-[420px]">
                <!-- 头像 关注 -->
                <div class="flex flex-col w-full items-center py-3">
                    <UsersAvatar :src="userProfile?.user?.avatarUrl" :icon-size="40" class="flex items-center cursor-pointer select-none" @click="handleOption('COMMUNITY')" />
                    <div class="flex items-center justify-center mt-3 gap-1" @click="handleOption('COMMUNITY')">
                        <span class="text-text-2 text-base cursor-pointer">{{ userProfile?.user?.userName || "" }}</span>
                        <div class="size-4 rounded-full text-text-4 transition-colors cursor-pointer flex items-center justify-center">
                            <IconsArrowRight class="text-base" />
                        </div>
                    </div>

                    <!-- 喜欢点赞关注 -->
                    <ul class="flex items-center justify-center w-full text-center py-3 cursor-pointer gap-3" @click="handleOption('COMMUNITY')">
                        <li class="flex gap-1 items-baseline justify-center">
                            <span class="text-xs text-text-3 cursor-pointer select-none">{{ userPoint.likes }}</span>
                            <span class="text-xs text-text-4 select-none">{{ $t("SHORT_LIKES") }}</span>
                        </li>
                        <li class="w-px h-3 bg-border-t-2"></li>
                        <li class="flex gap-1 items-baseline justify-center">
                            <span class="text-xs text-text-3 cursor-pointer select-none">{{ userPoint.focus }}</span>
                            <span class="text-xs text-text-4 select-none">{{ $t("COMMUNITY_FOLLOWING") }}</span>
                        </li>
                        <li class="w-px h-3 bg-border-t-2"></li>
                        <li class="flex gap-1 items-baseline justify-center">
                            <span class="text-xs text-text-3 cursor-pointer select-none">{{ userPoint.fans }}</span>
                            <span class="text-xs text-text-4 select-none">{{ $t("COMMUNITY_FANS") }}</span>
                        </li>
                    </ul>
                </div>

                <!-- 升级卡片 -->
                <template v-if="currVipInfo">
                    <div class="card-container flex items-center relative w-full h-16 rounded-lg overflow-hidden cursor-pointer" @click="handleOption('SUBSCRIBE')">
                        <div class="absolute top-0 right-0 bottom-0 left-0 z-10 flex items-center gap-2 px-3">
                            <n-icon size="32" class="shrink-0">
                                <component :is="currVipInfo.vipIcon" />
                            </n-icon>
                            <div class="flex-1">
                                <div class="flex items-center gap-1">
                                    <span class="text-[rgba(45,43,52,1)] text-sm font-semibold">{{ t("PICNAV_USERINFO.VIP_BOX.ACTIVE_PLAN") }}</span>
                                    <div class="h-4">
                                        <img :src="currVipInfo.targetImage" class="h-full object-cover pointer-events-none" />
                                    </div>
                                </div>
                                <div class="text-[rgba(45,43,52,0.8)] text-[10px] font-normal">{{ currVipInfo.content }}</div>
                            </div>

                            <div v-if="currVipInfo.type != 'pro'" class="px-3 h-8 bg-white rounded-2xl flex items-center justify-center shrink-0 hover:shadow-lg">
                                <span class="text-[rgba(45,43,52,1)] font-medium">{{ t("PICNAV_USERINFO.VIP_BOX.UPGRADE_BTN") }}</span>
                            </div>
                        </div>

                        <!-- 背景图 -->
                        <div class="absolute top-0 right-0 bottom-0 left-0 z-0">
                            <img :src="currVipInfo.bgImage" class="size-full object-cover pointer-events-none" :alt="`VIP ${currVipInfo.type} background`" />
                        </div>
                        <div class="absolute inset-0 z-10 pointer-events-none scan-raster"></div>
                    </div>
                </template>

                <!-- 会员 -->
                <div class="my-4 p-3 bg-bg-4 rounded-lg cursor-pointer select-none" @click="handleOption('SUBSCRIBE')">
                    <div class="flex items-center">
                        <div class="w-6 h-6 flex items-center justify-center rounded-full bg-primary-6 dark:bg-bg-6 mr-2">
                            <IconsLumenFill class="text-xs text-white" />
                        </div>
                        <span class="text-sm text-primary-6 font-semibold">{{ userPoint.lumenLeft }}</span>
                        <span class="text-sm text-text-3 ml-1">{{ $t("LUMEN_LEFT") }}</span>

                        <div class="flex ml-auto">
                            <n-button
                                :bordered="false"
                                @click="trackEvents('Personal_Popup', 'personal_popup_purchase')"
                                class="h-8 !bg-primary rounded-full text-xs font-medium !text-white px-3 w-full min-w-20"
                                type="primary"
                            >
                                {{ $t("PURCHASE") }}
                            </n-button>
                        </div>
                    </div>
                    <n-tooltip :show-arrow="false" class="rounded-lg" placement="bottom" raw trigger="hover">
                        <template #trigger>
                            <ul class="flex items-center text-center py-2 mt-4">
                                <li class="flex-1">
                                    <div class="font-bebas text-xl text-text-2">{{ userPoint.free }}</div>
                                    <div class="text-xs text-text-3 mt-0.5">{{ $t("FREE") }}</div>
                                </li>
                                <li class="w-px h-6 bg-border-t-2"></li>
                                <li class="flex-1">
                                    <div class="font-bebas text-xl text-text-2">{{ userPoint.award }}</div>
                                    <div class="text-xs text-text-3 mt-0.5">{{ $t("AWARD") }}</div>
                                </li>
                                <li class="w-px h-6 bg-border-t-2"></li>
                                <li class="flex-1">
                                    <div class="flex items-center justify-center gap-1.5">
                                        <span class="font-bebas text-xl text-text-2">{{ userPoint.subscription }}</span>
                                        <div class="pb-0.5">
                                            <Pie :percent="userPoint.subUsedPercent" class="w-4 h-4" />
                                        </div>
                                    </div>
                                    <div class="text-xs text-text-3 mt-0.5">{{ $t("SUBSCRIPTION") }}</div>
                                </li>
                                <li class="w-px h-6 bg-border-t-2"></li>
                                <li class="flex-1">
                                    <div class="font-bebas text-xl text-text-2">{{ userPoint.purchased }}</div>
                                    <div class="text-xs text-text-3 mt-0.5">{{ $t("PURCHASED") }}</div>
                                </li>
                            </ul>
                        </template>
                        <div class="bg-bg-6 px-3 py-2 rounded-lg">
                            <div class="text-sm text-text-1 font-medium">{{ $t("USAGE_PRIORITY") }}</div>
                            <div class="text-sm text-text-2 font-medium mt-2">{{ $t("USAGE_DES") }}</div>
                        </div>
                    </n-tooltip>
                </div>

                <!-- 工具栏 -->
                <div class="grid grid-cols-3 gap-2">
                    <!-- Theme -->
                    <n-popover :show-arrow="false" :z-index="50" class="shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)] rounded-lg" placement="right" raw trigger="hover">
                        <template #trigger>
                            <div
                                class="rounded-lg border border-solid border-border-2 py-4 px-3 flex flex-col items-center gap-2 hover:bg-fill-opt-2 hover:border-transparent transition-all cursor-pointer select-none"
                            >
                                <component :is="themeIcon[theme.icon]" class="text-2xl text-text-2" />
                                <span class="text-sm text-text-2 font-medium">{{ $t(themeLabel) }}</span>
                            </div>
                        </template>
                        <div ref="popoverThemeRef" class="min-w-[184px] p-2 rounded-lg space-y-1 bg-bg-6 border border-solid border-border-t-1">
                            <div
                                v-for="(item, index) in themeOptions"
                                :key="index"
                                class="flex items-center gap-2 px-3 py-2 cursor-pointer select-none rounded-lg text-text-drop-1 hover:bg-fill-opt-2"
                                @click="handleOption('THEME', item.key)"
                            >
                                <component :is="themeIcon[item.icon]" class="text-2xl" />
                                <span>{{ t(item.label) }}</span>
                            </div>
                        </div>
                    </n-popover>
                    <!-- 多语言 -->
                    <div
                        class="rounded-lg border border-solid border-border-2 py-4 px-3 flex flex-col items-center gap-2 hover:bg-fill-opt-2 hover:border-transparent transition-all cursor-pointer select-none"
                        @click="handleOption('LANGUAGE')"
                    >
                        <IconsGlobal class="text-2xl text-text-2" />
                        <span class="text-sm text-text-2 font-medium">{{ languageStore.localeLangLabel }}</span>
                    </div>
                    <div
                        class="rounded-lg border border-solid border-border-2 py-4 px-3 flex flex-col items-center gap-2 hover:bg-fill-opt-2 hover:border-transparent transition-all cursor-pointer select-none"
                        @click="handleOption('CONTACTUS')"
                    >
                        <IconsFeedback class="text-2xl text-text-2" />
                        <span class="text-sm text-text-2 font-medium">{{ t("MENU_FEEDBACK") }}</span>
                    </div>
                    <div
                        class="rounded-lg border border-solid border-border-2 py-4 px-3 flex flex-col items-center gap-2 hover:bg-fill-opt-2 hover:border-transparent transition-all cursor-pointer select-none"
                        @click="handleOption('SETTING')"
                    >
                        <IconsSetting class="text-2xl text-text-2" />
                        <span class="text-sm text-text-2 font-medium">{{ t("SHORT_SETTING") }}</span>
                    </div>
                    <!-- More -->
                    <n-popover
                        :show-arrow="false"
                        :z-index="50"
                        class="shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)] rounded-lg"
                        placement="top-start"
                        raw
                        trigger="hover"
                    >
                        <template #trigger>
                            <div
                                class="rounded-lg border border-solid border-border-2 py-4 px-3 flex flex-col items-center gap-2 hover:bg-fill-opt-2 hover:border-transparent transition-all cursor-pointer select-none"
                            >
                                <IconsHorMore class="text-2xl text-text-2" />
                                <span class="text-sm text-text-2 font-medium">{{ $t("MORE") }}</span>
                            </div>
                        </template>
                        <div class="min-w-[184px] p-2 rounded-lg space-y-1 bg-bg-6 border border-solid border-border-t-1 text-text-drop-1">
                            <div class="flex items-center gap-2 px-3 py-2 cursor-pointer select-none rounded-[4px] hover:bg-fill-opt-2" @click="handleOption('COMMUNITY')">
                                <n-icon :size="20">
                                    <IconsMenuCommunity class="text-text-2" />
                                </n-icon>
                                <span>{{ t("SHORT_COMMUNITY") }}</span>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-2 cursor-pointer select-none rounded-[4px] hover:bg-fill-opt-2" @click="handleOption('TUTORIAL')">
                                <n-icon :size="20">
                                    <IconsMenuTutorial class="text-text-2" />
                                </n-icon>
                                <span>{{ t("MENU_TUTORIAL") }}</span>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-2 cursor-pointer select-none rounded-[4px] hover:bg-fill-opt-2" @click="handleOption('IMPORT_FILE')">
                                <n-icon :size="20">
                                    <IconsUpload class="text-text-2" />
                                </n-icon>
                                <span>{{ t("IMPORT_FILE_TITLE") }}</span>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-2 cursor-pointer select-none rounded-[4px] hover:bg-fill-opt-2" @click="handleOption('LUMENS_RECORDS')">
                                <n-icon :size="20">
                                    <IconRecords class="text-text-2" />
                                </n-icon>
                                <span>{{ t("LUMENS_RECORDS.TITLE") }}</span>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-2 cursor-pointer select-none rounded-[4px] hover:bg-fill-opt-2" @click="handleOption('FAQ')">
                                <n-icon :size="20">
                                    <IconsHelp class=" " />
                                </n-icon>
                                <span>FAQ</span>
                            </div>
                        </div>
                    </n-popover>
                    <div
                        class="rounded-lg border border-solid border-border-2 py-4 px-3 flex flex-col items-center gap-2 hover:bg-fill-opt-2 hover:border-transparent transition-all cursor-pointer select-none"
                        @click="handleOption('LOGOUT')"
                    >
                        <IconsMenuLogout class="text-2xl text-danger-6" />
                        <span class="text-sm text-danger-6 font-medium">{{ t("LOG_OUT") }}</span>
                    </div>
                </div>
            </div>
        </n-popover>

        <!-- 用户反馈 -->
        <ContactUs ref="contactusRef" :show-trigger="false" />

        <!-- 多语言 -->
        <LanguageModel ref="languageRef" />

        <CustomImportFile v-if="showImportFile" v-model:show="showImportFile" />
    </div>
</template>
<script setup>
import { useLogout } from "@/hook/updateAccount";
import { numberFormat } from "@/utils/tools.js";
import { useUserProfile, useForceUpdatePageState, useSubscribeStore, useUnreadMessage } from "@/stores";
import { useThemeStore, useCurrentLang } from "@/stores/system-config";
import { storeToRefs } from "pinia";
import { IconExplore, IconCreate, IconHistory, IconTools, IconActivity, IconNotice, Moon, Sun, IconSystem } from "@/icons/index.js";
import { useTheme } from "@/hook/system.js";
import { reqUserCommunityAndLumenInfo } from "@/api/index.js";
import ContactUs from "@/components/ContactUs.vue";
import LanguageModel from "@/components/LanguageModel.vue";
import CustomImportFile from "@/components/CustomImportFile.vue";
import IconVipBasic from "@/components/icons/VipBasic.vue";
import IconVipStandard from "@/components/icons/VipStandard.vue";
import UsersAvatar from "@/components/users/Avatar.vue";
import IconVipPro from "@/components/icons/VipPro.vue";

// 背景图
import bgBasic from "@/assets/images/vip/img_user_vip_card_bg_basic.webp";
import bgStandard from "@/assets/images/vip/img_user_vip_card_bg_standard.webp";
import bgPro from "@/assets/images/vip/img_user_vip_card_bg_pro.webp";
import targetBasic from "@/assets/images/vip/img_user_vip_card_target_basic.webp";
import targetStandard from "@/assets/images/vip/img_user_vip_card_target_standard.webp";
import targetPro from "@/assets/images/vip/img_user_vip_card_target_pro.webp";

const { t } = useI18n({ useScope: "global" });

import VipIcon from "@/components/subscribe/VipIcon.vue";
import { SUB_EL } from "@/utils/constant.js";
import SysNotice from "@/components/SysNotice.vue";
import IconRecords from "@/components/icons/Records.vue";
import VipTrial from "@/components/userPromotion/views/VipTrial.vue";
import Pie from "@/components/subscribe/Pie.vue";
import { useCurrentTheme } from "@/stores/system-config";
const subscribeStore = useSubscribeStore();
const { isMobile } = storeToRefs(useThemeStore());
const { themeOptions, theme, themeLabel, handleSelect } = useTheme();
const { vipInfo, isPaymentVip } = storeToRefs(subscribeStore); // 会员相关
import { SUBSCRIBE_TYPE } from "@/utils/constant";
import { markRaw } from "vue";
const localePath = useLocalePath();

const props = defineProps({
    isFold: {
        type: Boolean,
        default: true,
    },
});

const vipMapping = ref({
    [SUBSCRIBE_TYPE.BASIC]: {
        // basic
        type: SUBSCRIBE_TYPE.BASIC,
        vipIcon: markRaw(IconVipBasic),
        content: t("PICNAV_USERINFO.VIP_BOX.BASIC"),
        showUpGrade: true,
        bgImage: bgBasic,
        targetImage: targetBasic,
    },
    [SUBSCRIBE_TYPE.STANDARD]: {
        // standard
        type: SUBSCRIBE_TYPE.STANDARD,
        vipIcon: markRaw(IconVipStandard),
        content: t("PICNAV_USERINFO.VIP_BOX.STANDARD"),
        showUpGrade: false,
        bgImage: bgStandard,
        targetImage: targetStandard,
    },
    [SUBSCRIBE_TYPE.PRO]: {
        // pro
        type: SUBSCRIBE_TYPE.PRO,
        vipIcon: markRaw(IconVipPro),
        content: t("PICNAV_USERINFO.VIP_BOX.PRO"),
        showUpGrade: false,
        bgImage: bgPro,
        targetImage: targetPro,
    },
});

const currVipInfo = computed(() => {
    const mapping = vipMapping.value;
    return Object.values(mapping).find((item) => item.type === vipInfo.value.plan) || mapping[SUBSCRIBE_TYPE.BASIC];
});

const contactusRef = ref();
const languageRef = ref();

const userProfile = useUserProfile();
const languageStore = useCurrentLang();

const showUserPopover = ref(false);
const popoverTriggerRef = ref();
const popoverContentRef = ref();
const popoverThemeRef = ref();
const showImportFile = ref(false); // 导入文件

const themeIcon = { Moon, Sun, IconSystem }; // 主题设置
const { userConfig, user } = storeToRefs(userProfile);

/**
 * @function clickOutSideRef - 用于初始化 clickOutSide 点击事件
 * @param targetElArray - 目标元素列表
 * @param precondition - 响应式前置条件对象
 * @param {Function} clickOutsideCallback - 点击到区域外时的回调函数
 */
const clickOutSideFn = (targetElArray, precondition, clickOutsideCallback) => {
    const clickHandler = ({ target }) => {
        if (!precondition.value) return;
        const isInside = targetElArray.some((el) => el.value?.contains(target)); // 点击里面为True 点击外面为False
        if (!isInside) clickOutsideCallback();
    };
    onMounted(() => {
        if (!targetElArray.length) return;
        document.addEventListener("click", clickHandler);
    });
    onBeforeUnmount(() => {
        document.removeEventListener("click", clickHandler);
    });
};

// 点击外部对应逻辑
clickOutSideFn([popoverTriggerRef, popoverContentRef, popoverThemeRef], showUserPopover, () => {
    showUserPopover.value = false;
});

const handleOpenUserPopover = () => {
    if (isMobile.value) return;

    showUserPopover.value = !showUserPopover.value;

    if (showUserPopover.value) {
        getUserCommunityAndLumenInfo();
    }

    trackEvents("Personal_Popup", "personal_popup");
};

const handleOption = async (type, value) => {
    switch (type) {
        case "MESSAGE":
            if (isMobile.value) return;
            await navigateTo(localePath("/message-center"));
            trackEvents("Message_Center", "nav_message_center=" + value);
            break;
        case "SUBSCRIBE":
            await navigateTo(localePath("/user/subscribe"));
            subscribeStore.setSubGaEvent(SUB_EL.PROFILE); // 将埋点事件置为空
            trackEvents("Personal_Popup", "personal_popup_hit_area");
            break;
        case "SETTING":
            await navigateTo(localePath("/account"));
            trackEvents("APP_SETTING", "setting");
            break;
        case "COMMUNITY":
            await navigateTo(localePath(`/user/${user.value.userId}`));
            trackEvents("Personal_Popup", "personal_popup_community");
            break;
        case "USERINFO":
            await navigateTo(localePath("/account"));
            break;
        case "LUMENS_RECORDS":
            await navigateTo(localePath("/user/records"));
            break;
        case "LOGOUT":
            useLogout();
            break;
        case "THEME":
            handleSelect(value);
            break;
        case "LANGUAGE":
            languageRef.value.handleOpenLanguageModal(true);
            break;
        case "CONTACTUS":
            contactusRef.value.changeShowPopover(true);
            trackEvents("Personal_Popup", "personal_popup_feedback");
            break;
        case "TUTORIAL":
            window.open("/tutorial", "_blank");
            trackEvents("Personal_Popup", "personal_popup_tutorial");
            break;
        case "IMPORT_FILE":
            showImportFile.value = true;
            trackEvents("Personal_Popup", "personal_popup_import_file");
            break;
        case "FAQ":
            window.open("/faq", "_blank");
            trackEvents("Personal_Popup", "personal_popup_faq");
            break;
    }
    showUserPopover.value = false;
};

// 用户相关点数
const userPoint = reactive({
    focus: "-",
    fans: "-",
    likes: "-",
    lumenLeft: "-",
    free: "-",
    award: "-",
    subscription: "-",
    subUsedPercent: 0,
    purchased: "-",
});

const getUserCommunityAndLumenInfo = async () => {
    try {
        const res = await reqUserCommunityAndLumenInfo();
        if (res.status !== 0) {
            console.error("Error fetching user community and lumen info:", res);
            return;
        }
        const { commUser, userLumens } = res.data;
        const { fansNums, likeNums, followNums } = commUser;
        const { vipLumens, leftDailyLumens, leftVipLumens, leftRechargeLumens, leftGiftLumens, leftTotalLumens } = userLumens;

        const rechargeUsed = vipLumens - leftVipLumens; // 已使用数
        let subUsedPercent;
        if (vipLumens !== 0) {
            subUsedPercent = (rechargeUsed / vipLumens) * 100; // 计算已使用百分比
        } else {
            subUsedPercent = 100; // 没有Lumen显示已用尽
        }

        Object.assign(userPoint, {
            focus: numberFormat(followNums),
            fans: numberFormat(fansNums),
            likes: numberFormat(likeNums),
            lumenLeft: numberFormat(leftTotalLumens),
            free: numberFormat(leftDailyLumens),
            award: numberFormat(leftGiftLumens),
            subscription: numberFormat(leftVipLumens),
            purchased: numberFormat(leftRechargeLumens),
            subUsedPercent,
        });
    } catch (error) {
        console.error("Unexpected error:", error.message);
    }
};

// 触发Google事件
const trackEvents = (event, el) => {
    try {
        window.trackEvent(event, { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};
</script>
<style lang="scss" scoped>
.scan-raster {
    background: transparent;
    transition: background 0.3s;
}

.card-container:hover .scan-raster {
    background: linear-gradient(70deg, transparent 0%, rgba(255, 255, 255, 0.3) 49%, transparent 50%);
    background-size: 300% 100%;
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% {
        background-position: 100% 0%;
    }
    100% {
        background-position: -200% 0%;
    }
}
</style>
