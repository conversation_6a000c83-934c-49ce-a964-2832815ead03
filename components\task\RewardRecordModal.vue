<template>
    <n-modal v-model:show="visible" draggable>
        <div class="w-1/3 md:min-w-[880px] md:max-h-[600px] flex flex-col rounded-2xl bg-bg-2">
            <div class="relative py-6 mb-4 flex justify-between items-center px-6">
                <h2 class="text-xl font-bold text-text-1">{{ t("TASKS_CENTER_RECORD_TITLE") }}</h2>
                <n-icon size="24" class="cursor-pointer text-text-1 hover:text-primary-5" @click="visible = false">
                    <IconsClose />
                </n-icon>
            </div>
            <div class="px-6 rounded-lg pb-6 overflow-hidden h-[474px]">
                <div v-if="showList.length == 0 && !isLoading" class="flex flex-col items-center gap-4 justify-center h-full">
                    <img class="dark:block hidden" src="@/assets/images/task/empty_dark.svg" alt="" />
                    <img class="dark:hidden block" src="@/assets/images/task/empty_light.svg" alt="" />
                    <span class="mt-4 opacity-70"> {{ t("TASKS_CENTER_RECORD_EMPTY") }} </span>
                </div>
                <div v-else class="scroll-view relative rounded-lg max-h-full border border-solid border-border-1 border-b-0 overflow-y-auto" @scroll="scrollLoadMore">
                    <div class="text-sm font-medium border-b border-solid text-text-1 border-border-1 rounded-t-lg bg-bg-2 grid grid-cols-7 items-center z-[1] sticky top-0">
                        <div class="p-4 h-full col-span-3">{{ t("TASKS_CENTER_TASK_NAME") }}</div>
                        <div class="p-4 h-full col-span-2 border-l border-solid border-border-1">{{ t("TASKS_CENTER_TASK_REWARD") }}</div>
                        <div class="p-4 h-full col-span-2 border-l border-solid border-border-1">{{ t("TASKS_CENTER_CLAIM_TIME") }}</div>
                    </div>
                    <div v-for="(item, index) in showList" :key="item.index" class="border-b border-solid text-sm font-medium border-border-1 grid grid-cols-7 text-text-3 items-center">
                        <div class="p-4 h-full col-span-3">{{ t(TASK_STATE_MAP[item.taskId]?.i18nKey) }}</div>
                        <div class="p-4 h-full col-span-2 flex items-center text-primary-6 border-l border-solid border-border-1">
                            <label class="text-base">{{ item.taskReward }}</label>
                            <n-icon size="16">
                                <IconsLumenFill />
                            </n-icon>
                        </div>
                        <div class="p-4 h-full col-span-2 border-l border-solid border-border-1">{{ formatDate(item.createTime) }}</div>
                    </div>

                    <div v-if="isLoading" class="sticky left-0 bottom-0 right-0 py-10 flex items-center justify-center">
                        <n-icon size="32" class="text-primary">
                            <IconsSpinLoading />
                        </n-icon>
                    </div>
                </div>
            </div>
        </div>
    </n-modal>
</template>
<script setup>
import { ref, watch, watchEffect } from "vue";

import { debounce, isScrolledToBottom, formatDate } from "@/utils/tools";

import { TASK_STATE_MAP } from "@/constants/taskCenter";
import { getTaskRewardLogApi } from "@/api/task";

const { t } = useI18n({ useScope: "global" });
const visible = defineModel("visible");

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});
watch(visible, (newVal) => {
    if (newVal) {
        pageInit();
    }
});

const page = ref({
    pageSize: 20,
    rewardLogId: "",
    // pageNum: 1
});
const isPageEnd = ref(false);
const isLoading = ref(false);
const tableData = ref([]);

//滚动加载更多
let virtualRefScrollTop = 0;
const scrollLoadMore = debounce(async (e) => {
    const isBottom = isScrolledToBottom(e.target, 100);
    virtualRefScrollTop = e.target.scrollTop;
    //获取元素滚动的高度
    if (isBottom) {
        loadMore();
    }
}, 100);

const loadMore = () => {
    if (isPageEnd.value || isLoading.value) {
        return;
    }
    page.value.rewardLogId = tableData.value[tableData.value.length - 1]?.id || "";
    getData();
};

function pageInit() {
    page.value.rewardLogId = "";
    getData();
}
const webTaskIds = computed(() => {
    return Object.keys(TASK_STATE_MAP);
});
async function getData() {
    isLoading.value = true;
    const { data } = await getTaskRewardLogApi({ ...page.value }).finally(() => {
        isLoading.value = false;
    });
    const newArray = data?.resultList || [];
    const resultList = newArray.map((item) => {
        if (!webTaskIds.value.includes(item.taskId)) {
            item.hidden = true;
        }
        return item;
    });

    if (resultList.length < page.value.pageSize - 1) {
        isPageEnd.value = true;
    }
    if (!page.value.rewardLogId) {
        tableData.value = resultList;
    } else {
        tableData.value.push(...resultList);
    }
}
const showList = computed(() => {
    return tableData.value.filter((item) => !item.hidden);
});
</script>

<style lang="scss" scoped>
.scroll-view {
    /* 整个滚动条 */
    &::-webkit-scrollbar {
        width: 10px; /* 垂直滚动条宽度 */
        height: 10px; /* 水平滚动条高度 */
    }
    /* 滚动条轨道 */
    &::-webkit-scrollbar-track {
        background: transparent;
        @apply rounded-lg;
    }
    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
        @apply rounded-lg bg-fill-wd-2;
    }
    &::-webkit-scrollbar-thumb:hover {
        @apply rounded-lg bg-fill-wd-3;
    }
}
</style>
