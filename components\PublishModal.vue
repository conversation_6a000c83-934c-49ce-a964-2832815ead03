<template>
    <div class="flex flex-col relative">
        <!-- 右上角关闭按钮 -->

        <!-- PC端关闭 -->
        <div class="hidden md:flex absolute top-0 right-0 size-8 rounded-full items-center justify-center cursor-pointer hover:bg-fill-wd-1" @click="handleCancel">
            <IconsClose class="size-4 text-text-2" />
        </div>
        <div class="absolute md:hidden top-0 right-0 size-8 flex items-center justify-center" @click="handleCancel">
            <n-icon>
                <IconsClose class="size-5 text-text-4" />
            </n-icon>
        </div>

        <div class="flex flex-col items-center gap-2 mt-2 mb:mt-0 pb-6">
            <n-icon :size="40" class="text-success-6">
                <IconsCommunities />
            </n-icon>

            <div class="font-bold text-base">{{ t("COMMUNITY_PUBLISH") }}</div>
        </div>

        <div class="flex w-full gap-6 mt-4 select-none" id="select-container">
            <div class="hidden md:flex size-[330px] shrink-0 aspect-square dark:bg-dark-bg-2 bg-neutral-200 rounded-lg overflow-hidden">
                <img :src="base.imgUrl" class="w-full h-full object-contain" />
            </div>
            <div class="flex w-full  md:w-[478px] flex-col text-center gap-4">
                <div class="conf-item-box text-xs">
                    <CustomInput ref="customInput" v-model:value="info.brief" :placeholder="t('COMMUNITY_PUBLISH_PLACEHOLDER')" />
                </div>
                <div class="flex flex-wrap gap-2 overflow-hidden">
                    <span class="text-xs text-primary-6 hover:text-primary-5 cursor-pointer font-medium" @click="chooseTag(item)" v-for="item in communityHotTags" :key="item">#{{ item }}</span>
                </div>
                <div v-if="false" class="mt-4 flex gap-4 overflow-hidden">
                    <div class="flex shrink-0">{{ t("COMMUNITY_SHOW_PROMPT_TITLE") }}</div>
                    <n-select
                        :render-tag="renderSingleSelectTag"
                        class="conf-item-box mr-4"
                        v-model:value="info.publicType"
                        :options="publicTypeOptions"
                        style="--n-font-size: 12px; --n-text-color: inherit"
                    >
                        <template #arrow>
                            <IconsCaretDown />
                        </template>
                    </n-select>
                </div>
                <div class="flex flex-col gap-2 overflow-hidden">
                    <span class="flex shrink-0">{{ t("ACTIVITY.JOIN_ACTIVITY") }}</span>

                    <n-select
                        class="conf-item-box mr-4 overflow-hidden"
                        v-model:value="info.activityId"
                        :options="activityList"
                        label-field="title"
                        value-field="id"
                        :placeholder="t('SELECT_PLACEHOLDER')"
                        :clearable="true"
                        to="#select-container"
                        style="--n-font-size: 12px; --n-text-color: inherit"
                    >
                        <template #arrow>
                            <IconsCaretDown />
                        </template>
                        <template #empty>
                            <div class="text-xs font-medium text-text-drop-1 py-2 cursor-pointer">
                                {{ t("COMMUNITY_PUBLISH_EMPTY") }}
                            </div>
                        </template>
                    </n-select>
                </div>

                <div class="text-warning-6 flex items-start gap-2 text-sm leading-[22px] text-left">
                    <n-icon :size="20"> <IconsAlert /> </n-icon>
                    <span>{{ t("COMMUNITY_PUBLISH_TIPS") }}</span>
                </div>
            </div>
        </div>
        <div class="mt-7 col-span-2 flex justify-end gap-2">
            <n-button :bordered="false" class="w-[120px] rounded-[20px] px-4 h-10 text-text-2 bg-fill-btn-1 hover:!bg-fill-btn-3" @click="handleCancel">
                {{ t("COMMON_BTN_CANCEL") }}
            </n-button>
            <n-button v-if="dailyPostPicLimit.limit > 0" :bordered="false" class="w-[120px] btn rounded-[20px] px-4 h-10 !text-white" :loading="submitLoading" @click="handleSubmit">
                {{ t("COMMUNITY_POST") }}
            </n-button>
            <!-- <n-button v-else :disabled="true" :bordered="false" class="btn rounded-lg px-10 h-10 !text-dark-active-text hover:!text-dark-active-text" :loading="submitLoading" @click="handleSubmit">
                {{ t("COMMUNITY_POST") }}
            </n-button> -->

            <PicPopover placement="top" v-else>
                <template #trigger>
                    <n-button :bordered="false" :disabled="true" class="w-[120px] btn rounded-[20px] px-4 h-10 !text-white">
                        <span>{{ t("COMMUNITY_POST") }}</span>
                        <n-icon class="ml-1">
                            <IconsAlert />
                        </n-icon>
                    </n-button>
                </template>
                <div>
                    {{ t("COMMUNITY_PUBLIC_LIMIT") }}
                </div>
            </PicPopover>
        </div>
    </div>
</template>

<script setup>
import { publishToCommunity } from "@/api";
import { getPostActivityListApi } from "@/api/activity";
import { useMaintainPostCount } from "@/hook/updateAccount";
import { useDailyPostPicLimit } from "@/stores";
const maintainPostCount = useMaintainPostCount();
const dailyPostPicLimit = useDailyPostPicLimit();
const userProfileStore = useUserProfile();
const { userConfig } = storeToRefs(userProfileStore);
const props = defineProps({
    base: {
        type: Object,
        required: true,
    },
});
const emits = defineEmits(["confirm", "cancel"]);
const info = ref({
    publicType: "everyone",
    brief: "",
});
import { t } from "@/utils/i18n-util";
import { communityHotTags } from "@/utils/tools";

// 社区-输入评论
const publicTypeOptions = [
    { label: t("COMMUNITY_PUBLISH_PROMPT_EVERYONE"), value: "everyone" },
    { label: t("COMMUNITY_PUBLISH_PROMPT_SELF"), value: "myself" },
];

const renderSingleSelectTag = ({ option }) => {
    return h("span", { class: ["text-xs"] }, option.label);
};
//发布
const submitLoading = ref(false);
const handleSubmit = async () => {
    if (submitLoading.value) {
        return;
    }
    submitLoading.value = true;
    const { imgUrl, ...params } = props.base;

    // 用户发布时prompt是否隐藏,在设置页中配置
    let publicType = "";
    // 用户已选择参加活动 则默认隐藏prompt
    if (info.value.activityId) {
        publicType = "myself";
    }
    // 不存在活动
    else {
        publicType = userConfig.value.hiddenPromptWhenUploadImg ? "myself" : "everyone";
    }

    const requestParams = {
        ...info.value,
        ...params,
        publicType,
    };

    const { status, message } = await publishToCommunity(requestParams);
    submitLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    maintainPostCount.updateCount();
    emits("confirm", true);
};
//取消
const handleCancel = () => {
    emits("cancel", false);
};
//选择 tag
const customInput = ref(null);
const chooseTag = (tag) => {
    customInput.value.insertHighlightText(`#${tag}`);
};
onMounted(() => {
    maintainPostCount.asyncUpdateLimit();
    getPostActivityList();
});

const activityList = ref([]);
async function getPostActivityList() {
    const res = await getPostActivityListApi();
    if (res.status === 0) {
        activityList.value = res.data;
    }
}
</script>

<style lang="scss" scoped>
.conf-item-box {
    @apply min-h-10 rounded-lg bg-fill-ipt-1 text-left;
}

.btn {
    @apply bg-primary focus:bg-primary;
    position: relative;
    overflow: hidden;
    &:hover {
        background: #7b57e5 !important;
    }
    &:active {
        background: #8961ff !important;
    }
}
</style>
