<template>
    <div class="skeleton-wrapper w-full text-text-1 shimmer relative flex items-center justify-center">
        <n-icon class="flicker" size="36" v-if="!noIcon">
            <IconsPiclumen />
        </n-icon>
    </div>
</template>
<script setup>
defineProps({
    noIcon: {
        type: Boolean,
        default: false,
    },
});
</script>
<style scoped lang="scss">
// .skeleton-wrapper {
//     min-height: 72px;
// }
@keyframes shimmer-left-to-right {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.shimmer::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background-size: 200% 100%; /* 增加背景的尺寸，使动画更平滑 */
    background-repeat: no-repeat;
    animation: shimmer-left-to-right 3s infinite ease;
    pointer-events: none;
}

@keyframes flicker-opacity {
    0%,
    100% {
        opacity: 0.9;
    }
    50% {
        opacity: 0.35;
    }
}

.shimmer {
    position: relative;
    overflow: hidden;
    &::after {
        background-image: linear-gradient(120deg, transparent, rgba(0, 0, 0, 0.12), transparent);
    }
}
.dark .shimmer::after {
    background-image: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.12), transparent);
}

.flicker {
    animation: flicker-opacity 1.6s infinite ease-in-out;
}
</style>
