<script setup>
import { SOCIAL_PLATFORM } from "@/utils/constant.js";
import IconDiscord from "@/components/icons/Discord.vue";
import IconDiscordFill from "@/components/icons/DiscordFill.vue";
import IconFacebook from "@/components/icons/Facebook.vue";
import IconFacebookFill from "@/components/icons/FacebookFill.vue";
import IconX from "@/components/icons/X.vue";
import IconApple from "@/components/icons/AppleNormal.vue";
import IconGooglePlay from "@/components/icons/GooglePlay.vue";
import IconGooglePlayFill from "@/components/icons/GooglePlayFill.vue";
import IconYoutube from "@/components/icons/Youtube.vue";
import IconYoutubeFill from "@/components/icons/YoutubeFill.vue";

defineProps({
    social: { type: Array, default: () => [] },
});

const socialMedia = {
    discord: { normal: IconDiscord, fill: IconDiscordFill, link: SOCIAL_PLATFORM.DISCORD.link, eveName: "discord" },
    facebook: { normal: IconFacebook, fill: IconFacebookFill, link: SOCIAL_PLATFORM.FACEBOOK.link, eveName: "facebook" },
    twitter: { normal: IconX, fill: IconX, link: SOCIAL_PLATFORM.TWITTER.link, eveName: "twitter" },
    apple: { normal: IconApple, fill: IconApple, link: SOCIAL_PLATFORM.APPLE.link, eveName: "download_apple" },
    google: { normal: IconGooglePlay, fill: IconGooglePlayFill, link: SOCIAL_PLATFORM.GOOGLE.link, eveName: "download_android" },
    youtube: { normal: IconYoutube, fill: IconYoutubeFill, link: SOCIAL_PLATFORM.YOUTUBE.link, eveName: "youtube" },
};

// 触发Google事件
const trackEvents = (event, el) => {
    try {
        window.trackEvent(event, { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};

const handleClick = (iconName) => {
    window.open(socialMedia[iconName].link, "_blank");
    trackEvents("Social_Media", socialMedia[iconName].eveName);
};
</script>

<template>
    <div class="relative">
        <div class="absolute top-0 right-0 bottom-0 left-0" v-bind="$attrs">
            <template v-for="iconName in social" :key="iconName">
                <component :is="socialMedia[iconName]?.normal" class="cursor-pointer text-text-4" @click.stop="handleClick(iconName)" />
            </template>
        </div>
        <div class="absolute top-0 right-0 bottom-0 left-0" v-bind="$attrs">
            <template v-for="iconName in social" :key="iconName">
                <component :is="socialMedia[iconName]?.fill" class="cursor-pointer opacity-0 hover:opacity-100 transition-all text-text-1" @click.stop="handleClick(iconName)" />
            </template>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
