<template>
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_8052_36937" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="1" y="2" width="14" height="12">
            <path
                d="M14.5105 5.96258C14.9916 6.76442 14.8529 7.79261 14.1764 8.43828L9.38094 13.0158C8.60809 13.7535 7.39189 13.7535 6.61904 13.0158L1.82354 8.43828C1.14712 7.79261 1.00839 6.76442 1.4895 5.96258L3.08405 3.30499C3.4455 2.70258 4.09651 2.33398 4.79904 2.33398H11.2009C11.9035 2.33398 12.5545 2.70258 12.9159 3.30499L14.5105 5.96258Z"
                fill="black"
            />
        </mask>
        <g mask="url(#mask0_8052_36937)">
            <g filter="url(#filter0_f_8052_36937)">
                <g clip-path="url(#paint0_angular_8052_36937_clip_path)" data-figma-skip-parse="true">
                    <g transform="matrix(-0.00666667 0.00466667 -0.00466667 -0.00666667 7.99997 7.33398)">
                        <foreignObject x="-1483.22" y="-1483.22" width="2966.44" height="2966.44"
                            ><div
                                xmlns="http://www.w3.org/1999/xhtml"
                                style="
                                    background: conic-gradient(
                                        from 90deg,
                                        rgba(0, 164, 193, 1) 0deg,
                                        rgba(255, 140, 0, 1) 61.2deg,
                                        rgba(255, 149, 0, 1) 118.8deg,
                                        rgba(242, 195, 63, 1) 180deg,
                                        rgba(113, 198, 213, 1) 241.2deg,
                                        rgba(50, 155, 173, 1) 298.8deg,
                                        rgba(0, 164, 193, 1) 360deg
                                    );
                                    height: 100%;
                                    width: 100%;
                                    opacity: 1;
                                "
                            ></div
                        ></foreignObject>
                    </g>
                </g>
                <rect
                    x="-3.05176e-05"
                    y="-0.666016"
                    width="16"
                    height="16"
                    data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.55000001192092896,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.17000001668930054},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.58431375026702881,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.33000004291534424},{&#34;color&#34;:{&#34;r&#34;:0.94999998807907104,&#34;g&#34;:0.76673227548599243,&#34;b&#34;:0.25059053301811218,&#34;a&#34;:1.0},&#34;position&#34;:0.50},{&#34;color&#34;:{&#34;r&#34;:0.44313725829124451,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.83529412746429443,&#34;a&#34;:1.0},&#34;position&#34;:0.66999995708465576},{&#34;color&#34;:{&#34;r&#34;:0.19719998538494110,&#34;g&#34;:0.60809361934661865,&#34;b&#34;:0.68000000715255737,&#34;a&#34;:1.0},&#34;position&#34;:0.82999998331069946},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.64419054985046387,&#34;b&#34;:0.75999999046325684,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.55000001192092896,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.17000001668930054},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.58431375026702881,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.33000004291534424},{&#34;color&#34;:{&#34;r&#34;:0.94999998807907104,&#34;g&#34;:0.76673227548599243,&#34;b&#34;:0.25059053301811218,&#34;a&#34;:1.0},&#34;position&#34;:0.50},{&#34;color&#34;:{&#34;r&#34;:0.44313725829124451,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.83529412746429443,&#34;a&#34;:1.0},&#34;position&#34;:0.66999995708465576},{&#34;color&#34;:{&#34;r&#34;:0.19719998538494110,&#34;g&#34;:0.60809361934661865,&#34;b&#34;:0.68000000715255737,&#34;a&#34;:1.0},&#34;position&#34;:0.82999998331069946},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.64419054985046387,&#34;b&#34;:0.75999999046325684,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-13.333338737487793,&#34;m01&#34;:-9.3333358764648438,&#34;m02&#34;:19.333309173583984,&#34;m10&#34;:9.3333368301391602,&#34;m11&#34;:-13.333337783813477,&#34;m12&#34;:9.3339834213256836},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
                />
            </g>
        </g>
        <path d="M10.3333 8.66602L7.99999 10.9993L5.66666 8.66602" stroke="white" stroke-opacity="0.8" stroke-linecap="round" stroke-linejoin="round" />
        <defs>
            <filter id="filter0_f_8052_36937" x="-2.6667" y="-3.33268" width="21.3333" height="21.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="1.33333" result="effect1_foregroundBlur_8052_36937" />
            </filter>
            <clipPath id="paint0_angular_8052_36937_clip_path"><rect x="-3.05176e-05" y="-0.666016" width="16" height="16" /></clipPath>
        </defs>
    </svg>
</template>
