<template>
    <div v-if="rankState" class="absolute top-2 left-2 py-1 px-2 flex items-center gap-1 rounded-full bg-fill-dd-4 text-[#e3e3e3] backdrop-blur-lg">
        <img :src="rankState.icon" class="w-6 h-6" />
        <span class="text-base font-semibold" :class="[colorClass]"> {{ rankState.levelName }}</span>
    </div>
</template>
<script setup>
import { computed } from "vue";
const colorMap = {
    1: "text-[#FFD731]",
    2: "text-[#DDC3FF]",
    3: "text-[#FBC499]",
    default: "text-[#E3E3E3]",
};
const props = defineProps({
    activity: {
        type: Object,
        default: () => {},
    },
    prizeList: {
        type: Array,
        default: () => [],
    },
});

const colorClass = computed(() => {
    const level = rankState.value?.level;
    return colorMap[level] || colorMap.default;
});
const rankState = computed(() => {
    const prizeLevel = props.activity?.prizeLevel;
    if (prizeLevel) {
        return props.prizeList.find((item) => item.level === prizeLevel);
    }
    return null;
});
</script>
