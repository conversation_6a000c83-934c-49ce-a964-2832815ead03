<script setup>
import { I18nT, useI18n } from "vue-i18n";
const { t } = useI18n({ useScope: "global" });
useSeoMeta({
    title: () => "Free AI Image Generator for AI Art Creation - PicLumen",
    ogTitle: () => "Free AI Image Generator for AI Art Creation - PicLumen",
});
import { reqBilling, reqBiSavePayLog, reqUnCancelAutoSubscribe } from "@/api";

import { useBuyLumenModal, useCancelAutoSubscriptionModal, useCancelUpgradeSubscriptionModal, useRequestLoadingModal, useSubscribeErrorModal, useUpgradePlanModal } from "@/hook/subscribe.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { storeToRefs } from "pinia";
import { getPlatformVipToWeight } from "@/utils/tools.js";
import { round56 } from "@/utils/subscribeTools.js";
import { BILLING_TYPE, PRICE_MAPPING, SUBSCRIBE_PARAMS, SUBSCRIBE_TYPE, SUBSCRIBE_CALLBACK_PARAMS, PLATFORM_TYPE, VIP_WEIGHT, SUB_EL } from "@/utils/constant.js";

import IconVipBasic from "@/components/icons/VipBasic.vue";
import IconVipStandard from "@/components/icons/VipStandard.vue";
import IconVipPro from "@/components/icons/VipPro.vue";
import PaymentMethodModal from "@/components/PaymentMethod.vue";
const { showMessage } = useModal();

import MobileHeader from "@/components/mobile/header/MobileHeader.vue";

import { useThemeStore } from "@/stores/system-config";
const { isMobile } = storeToRefs(useThemeStore());

const subscribeStore = useSubscribeStore();
const { vipInfo, isFreeVip, isGiftVip, isPaymentVip, formatExpireDate, formatRefreshDate, subList, subGaEvent, isHaveFirstBuySub } = storeToRefs(subscribeStore);
const isVipInfoLoaded = computed(() => !vipInfo.value.getInfoLoading); // vipInfo加载完成
const isNotSubscribeVip = computed(() => isFreeVip.value || !isPaymentVip.value); // 不是付费订阅会员
await subscribeStore.getSubList();
await subscribeStore.getLumenNum();
onMounted(async () => {
    // 此接口有一个Loading状态，等待上面接口结束
    checkPaymentStatus();
});

const allowFreeTry = computed(() => isHaveFirstBuySub.value);

watchEffect(() => {
    // 不是付费用户要重置状态
    if (isNotSubscribeVip.value) {
        subscribeStore.setVipInfo({ refreshDate: "", refreshPlan: "" });
    } else {
        subscribeStore.getRefreshOn();
    }
});

// 当前是paypal 平台
const paypalPayment = computed(() => {
    const webSubList = getPlatformVipToWeight(subList.value, PLATFORM_TYPE.WEB);
    if (webSubList.length === 0) {
        return false;
    }
    return webSubList[0].vipPlatform === "paypal";
});

/**
 * 检查支付状态
 */
const route = useRoute();
const { openModal: openRequestLoading } = useRequestLoadingModal();
const checkPaymentStatus = () => {
    if (!("type" in route.query)) return;

    let subscriptionTemp = sessionStorage.getItem("subscriptionTemp"); // 获取点击订阅是缓存的订阅信息
    let lumenTemp = sessionStorage.getItem("lumenTemp"); // 获取点击订阅是缓存的订阅信息
    if (!subscriptionTemp && !lumenTemp) {
        const url = new URL(window.location); // 创建一个 URL 对象
        url.searchParams.delete("type"); // 删除 type 参数
        url.searchParams.delete("action"); // 删除 type 参数
        url.searchParams.delete("paymentMethod"); // 删除 type 参数
        window.history.replaceState({}, "", url.href); // 替换当前的 URL 但不刷新页面
        return;
    }

    const type = route.query.type;
    const action = route.query.action;
    const paymentMethod = route.query.paymentMethod || "stripe";

    if (type === SUBSCRIBE_CALLBACK_PARAMS.PLAN.type) {
        openRequestLoading({ loopType: action === "upgradePlan" ? "upgradePlan" : "subPlan", paymentMethod });
        return;
    }
    if (type === SUBSCRIBE_CALLBACK_PARAMS.LUMEN.type) {
        openRequestLoading({ loopType: "buyLumen" });
    }
};

// 会员权益
const benefitsList = {
    [SUBSCRIBE_TYPE.BASIC]: {
        name: "SUBSCRIBE_PLAN_BASIC",
        style: "basic-plan",
        icon: "IconVipBasic",
        list: ["SUBSCRIBE_BENEFITS_1", "SUBSCRIBE_BENEFITS_2", "SUBSCRIBE_BENEFITS_3"],
    },
    [SUBSCRIBE_TYPE.STANDARD]: {
        name: "SUBSCRIBE_PLAN_STANDARD",
        style: "standard-plan",
        icon: "IconVipStandard",
        list: [
            "SUBSCRIBE_BENEFITS_1",
            "SUBSCRIBE_BENEFITS_19",
            "SUBSCRIBE_BENEFITS_3",
            "SUBSCRIBE_BENEFITS_5",
            "SUBSCRIBE_BENEFITS_6",
            "SUBSCRIBE_BENEFITS_7",
            "SUBSCRIBE_BENEFITS_8",
            "SUBSCRIBE_BENEFITS_20",
        ],
    },
    [SUBSCRIBE_TYPE.PRO]: {
        name: "SUBSCRIBE_PLAN_PRO",
        style: "pro-plan",
        icon: "IconVipPro",
        list: [
            "SUBSCRIBE_BENEFITS_1",
            "SUBSCRIBE_BENEFITS_21",
            "SUBSCRIBE_BENEFITS_3",
            "SUBSCRIBE_BENEFITS_5",
            "SUBSCRIBE_BENEFITS_10",
            "SUBSCRIBE_BENEFITS_11",
            "SUBSCRIBE_BENEFITS_12",
            "SUBSCRIBE_BENEFITS_8",
            "SUBSCRIBE_BENEFITS_20",
        ],
    },
};
const vipIconComp = { IconVipBasic, IconVipStandard, IconVipPro };

// const currPlanData = computed(() => benefitsList[vipInfo.value.plan]);
// const currPlanStyle = computed(() => currPlanData.value.style);
// const currPlanName = computed(() => currPlanData.value.name);
// const currPlanList = computed(() => currPlanData.value.list);
// const currPlanIcon = computed(() => vipIconComp[currPlanData.value.icon]);
const vipPlan = computed(() => vipInfo.value.plan);

// 显示骨架屏
const isShowVipInfoSkeletonOnce = ref(true); // 只显示一次骨架屏
const isShowVipInfoSkeleton = computed(() => isShowVipInfoSkeletonOnce.value && (vipInfo.value.getInfoLoading || vipInfo.value.getLumenLoading || vipInfo.value.getRefreshLoading));
watchEffect(() => {
    if (!isShowVipInfoSkeleton.value) isShowVipInfoSkeletonOnce.value = false;
});

const defaultText = "-";
// 格式化Lumen显示
const formatLumenText = (text) => (vipInfo.value.getLumenLoading ? defaultText : text);
// 格式化刷新时间
const isShowRefreshDate = computed(() => formatRefreshDate.value && vipInfo.value.refreshPlan);
const formatRefreshPlanName = computed(() => {
    if (vipInfo.value.refreshPlan === SUBSCRIBE_TYPE.STANDARD) return "SUBSCRIBE_PLAN_STANDARD";
    if (vipInfo.value.refreshPlan === SUBSCRIBE_TYPE.PRO) return "SUBSCRIBE_PLAN_PRO";
    return "";
});

// 格式化账单周期
const formatBillingPeriod = computed(() => {
    let str = defaultText;
    switch (vipInfo.value.billingPeriod) {
        case BILLING_TYPE.MONTHLY:
            str = "Monthly";
            break;
        case BILLING_TYPE.YEARLY:
            str = "Annual";
            break;
    }
    return vipInfo.value.getInfoLoading ? defaultText : str;
});

const currentRenewPrice = computed(() => {
    const { billingPeriod, renewPrice } = vipInfo.value;
    const price = Number(renewPrice) / (billingPeriod === "year" ? 12 : 1);
    return round56(price);
});
// 格式化价格显示
const formatPriceText = computed(() => {
    if (isFreeVip.value) return defaultText;
    return vipInfo.value.getInfoLoading ? defaultText : `$${currentRenewPrice.value} / mo`;
});
// 格式化过期时间显示
const formatExpireDateText = computed(() => {
    return {
        label: vipInfo.value.getInfoLoading ? defaultText : formatExpireDate.value.shortStr,
        labelKey: formatExpireDate.value.shortStrKey,
    };
});
const { openModal: buyOpenModal } = useBuyLumenModal();
const { openModal: openUpgradeModal } = useUpgradePlanModal();
const { showErrorModal, showInfoModal } = useSubscribeErrorModal();
/**
 * 购买Lumen
 */
const handleBuyMoreLumen = (gaEl) => {
    showVipModal.value = false; // 关闭当前订阅弹窗
    if (gaEl === "manage_popup_buy_lumen") {
        subscribeStore.setBuyLumenGaEvent("manage_subscription");
    } else {
        subscribeStore.setBuyLumenGaEvent("subscribe_purchase");
    }
    buyOpenModal();
    customTrackEvent(gaEl || "manage_popup_buy_lumen");
};

const customTrackEvent = (el) => {
    window.trackEvent("Subscribe", { el });
};
/**
 * 查看发票
 */
const invoicesLoading = ref(false);
const handleViewInvoices = () => {
    customTrackEvent("manage_popup_view_invoice");
    if (isFreeVip.value || invoicesLoading.value) return;
    invoicesLoading.value = true;
    let blankWindow = window.open("about:blank");
    reqBilling({ returnUrl: SUBSCRIBE_PARAMS.SUCCESS_CALLBACK() })
        .then((res) => {
            if (res.status === 0) {
                blankWindow.location.href = res.data; // 跳转到目标链接
            } else {
                showInfoModal("SUBSCRIBE_GET_BILLING_ERROR");
                if (blankWindow) blankWindow.close();
            }
        })
        .catch((err) => {
            showInfoModal("SUBSCRIBE_GET_BILLING_ERROR");
            if (blankWindow) blankWindow.close();
        })
        .finally(() => {
            invoicesLoading.value = false;
        });
};

/**
 * 当前是否不处于取消状态
 */
const notCancelSubPlan = computed(() => {
    // 返回根据平台权重排序后的订阅列表
    const webSubList = getPlatformVipToWeight(subList.value, PLATFORM_TYPE.WEB);
    if (webSubList.length === 0) {
        return false;
    }
    return webSubList[0].autoRenewStatus === 1; // 自动续订状态（1 表示启用，0 表示关闭）
});

const { openModal: openCanSubModal } = useCancelAutoSubscriptionModal();
const handleCancelSubscribe = () => {
    customTrackEvent("manage_popup_cancel_plan");
    if (isFreeVip.value) return;
    showVipModal.value = false; // 关闭当前订阅弹窗
    openCanSubModal();
};

/**
 * 取消取消自动续费
 */
const unCancelLoading = ref(false);
const handleUnCancelPlan = () => {
    customTrackEvent("manage_popup_uncancel");
    showVipModal.value = false; // 关闭当前订阅弹窗
    unCancelLoading.value = true;
    reqUnCancelAutoSubscribe()
        .then(async (res) => {
            if (res.status === 0) {
                await subscribeStore.getSubList();
            } else {
                showErrorModal("SUBSCRIBE_EX_OPERATION");
            }
        })
        .catch((err) => {
            showErrorModal("SUBSCRIBE_EX_OPERATION");
        })
        .finally(() => {
            unCancelLoading.value = false;
        });
};

/**
 * 取消升级订阅
 */
const { openModal: openCancelUpgradeSubModal } = useCancelUpgradeSubscriptionModal();
const handleCancelUpgradePlan = () => {
    customTrackEvent("manage_popup_cancel_change");
    if (isFreeVip.value) return;
    showVipModal.value = false; // 关闭当前订阅弹窗
    openCancelUpgradeSubModal();
};

//计费周期类型
const currBilling = ref(0);
const subscribePlanList = [
    {
        billing: "SUBSCRIBE_TAB_YEAR",
        unit: BILLING_TYPE.YEARLY,
    },
    {
        billing: "SUBSCRIBE_TAB_MONTH",
        unit: BILLING_TYPE.MONTHLY,
    },
];

const tabSlider = ref();
const handleTabChange = (tabIndex) => {
    currBilling.value = tabIndex;
    tabSlider.value.style.transform = `translateX(${tabIndex}00%)`;
};

const lumenCostList = [
    [
        { type: "text", name: "SUBSCRIBE_COST_TABLE_1_1" },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_2_1" },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_3_1" },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_4_1" },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_1" },
        { type: "text", name: "Primo" },
        { type: "text", name: "FLUX.1 Kontext" },
        { type: "text", name: "Namiya" },
    ],
    [
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_2" },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 1 },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 1 },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 1 },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 3 },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 3 },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 12 },
        { type: "text", name: "SUBSCRIBE_COST_TABLE_5_3", i18nCount: 1 },
    ],
];

const vipBenefits = ref([
    {
        type: "plan",
        iconName: "IconVipBasic",
        vipLevelStyle: "",
        plan: SUBSCRIBE_TYPE.BASIC,
        planAlias: "SUBSCRIBE_PLAN_BASIC",
        originalPrice: 0,
        currentPrice: 0,
        expirationDate: "SUBSCRIBE_UNIT_MONTH",
        billedDesc: "",
        desc: "SUBSCRIBE_BASIC_DESC",
        sale: "",
        showBtn: false,
        btnLoading: false,
        list: [
            { type: "text", name: "VIP_BENEFITS_1", i18nKey: "VIP_BENEFITS_10_LUMENS", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_3_BASIC", i18nKey: "VIP_BENEFITS_LIMIT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_BATCH_GEN", i18nCount: 2, i18nKey: "VIP_BENEFITS_BATCH_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_10", i18nCount: 500, i18nKey: "VIP_BENEFITS_10_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_4", i18nKey: "VIP_BENEFITS_4_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_EDIT_TOOLS", desc: "", showHelp: false },
        ],
    },

    {
        type: "plan",
        iconName: "IconVipStandard",
        vipLevelStyle: "standard-style",
        plan: SUBSCRIBE_TYPE.STANDARD,
        planAlias: "SUBSCRIBE_PLAN_STANDARD",
        originalPrice: 0,
        currentPrice: 0,
        expirationDate: "SUBSCRIBE_UNIT_MONTH",
        billedDesc: "",
        desc: "SUBSCRIBE_STANDARD_DESC",
        sale: "50%",
        showBtn: true,
        btnLoading: false,
        list: [
            { type: "text", name: "VIP_BENEFITS_1", i18nKey: "VIP_BENEFITS_10_LUMENS", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_2", i18nCount: "2,000", desc: "", showHelp: false, helpTitle: "SUBSCRIBE_COST_TABLE_TITLE", helpAlign: "left", helpList: lumenCostList },
            { type: "text", name: "VIP_BENEFITS_3", i18nKey: "VIP_BENEFITS_UNLIMIT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_DISCOUNT", i18nKey: "VIP_BENEFITS_DISCOUNT_10", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_6", i18nCount: 5, desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_7", i18nCount: 2, desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_FULL_HISTORY", i18nKey: "VIP_BENEFITS_FULL_HISTORY_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_10", i18nKey: "VIP_BENEFITS_10_COUNT", i18nCount: "5,000", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_BATCH_GEN", i18nCount: 4, i18nKey: "VIP_BENEFITS_BATCH_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_EDIT_TOOLS", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_8", desc: "", showHelp: false },
        ],
    },
    {
        type: "plan",
        iconName: "IconVipPro",
        vipLevelStyle: "pro-style",
        plan: SUBSCRIBE_TYPE.PRO,
        planAlias: "SUBSCRIBE_PLAN_PRO",
        originalPrice: 0,
        currentPrice: 0,
        expirationDate: "SUBSCRIBE_UNIT_MONTH",
        billedDesc: "",
        desc: "SUBSCRIBE_PRO_DESC",
        sale: "50%",
        showBtn: true,
        btnLoading: false,
        list: [
            { type: "text", name: "VIP_BENEFITS_1", i18nKey: "VIP_BENEFITS_10_LUMENS", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_2", i18nCount: "5,000", desc: "", showHelp: false, helpTitle: "SUBSCRIBE_COST_TABLE_TITLE", helpAlign: "left", helpList: lumenCostList },
            { type: "text", name: "VIP_BENEFITS_3", i18nKey: "VIP_BENEFITS_UNLIMIT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_DISCOUNT", i18nKey: "VIP_BENEFITS_DISCOUNT_20", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_6", i18nCount: 10, desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_7", i18nCount: 5, desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_FULL_HISTORY", i18nKey: "VIP_BENEFITS_FULL_HISTORY_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_10", i18nKey: "VIP_BENEFITS_10_COUNT", i18nCount: "50,000", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_BATCH_GEN", i18nCount: 4, i18nKey: "VIP_BENEFITS_BATCH_COUNT", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_EDIT_TOOLS", desc: "", showHelp: false },
            { type: "text", name: "VIP_BENEFITS_8", desc: "", showHelp: false },
        ],
    },
]);

const vipBenefitsList = computed(() => {
    return vipBenefits.value.map((item) => {
        const billing = subscribePlanList[currBilling.value];

        const promotion = subscribeStore.getUserPromotionByUnit(item.plan, billing.unit); // 聚合来自服务端的金额
        const prices = { ...PRICE_MAPPING[billing.unit][item.plan], ...promotion };

        prices.currentPrice = prices.CURRENT;
        prices.originalPrice = prices.ORIGINAL;
        prices.sale = prices.SALE;
        //当前满足首购 优惠
        // if (allowFreeTry.value) {
        //     prices.sale = prices.FIRST_SALE;
        //     prices.currentPrice = prices.FIRST_SUB || 0;
        // }
        Object.assign(item, { ...prices });
        return item;
    });
});
//当前是试用期会员
const isTrialVip = computed(() => {
    return subList.value[0]?.trial;
});

/**
 * 1. 只存在IOS订阅不显示按钮
 * 2. IOS会员等级比我高不显示订阅按钮
 */
const multiSubAllowShow = computed(() => {
    const existOtherSub = subList.value.some((item) => PLATFORM_TYPE.OTHER.includes(item.vipPlatform)); // 存在非WEB订阅
    const existWebSub = subList.value.some((item) => PLATFORM_TYPE.WEB.includes(item.vipPlatform)); // 存在WEB订阅
    // 只存在非WEB订阅,不显示按钮
    if (existOtherSub && !existWebSub) {
        return false;
    }
    // IOS与WEB订阅都存在
    if (existOtherSub && existWebSub) {
        const iosSubInfo = subList.value.find((item) => PLATFORM_TYPE.OTHER.includes(item.vipPlatform));
        const webSubInfo = subList.value.find((item) => PLATFORM_TYPE.WEB.includes(item.vipPlatform));
        const iosVipWeight = VIP_WEIGHT[iosSubInfo.planLevel] + VIP_WEIGHT[iosSubInfo.priceInterval];
        const webVipWeight = VIP_WEIGHT[webSubInfo.planLevel] + VIP_WEIGHT[webSubInfo.priceInterval];
        return iosVipWeight <= webVipWeight; // ios订阅订阅等级比web小或等于显示按钮
    }
    return true;
});
const isShowBtn = (item, type) => {
    const billing = subscribePlanList[currBilling.value];

    // 激活状态升降级去当前付费订阅等级最高的显示
    let curBilling = BILLING_TYPE.MONTHLY,
        curPlan = SUBSCRIBE_TYPE.BASIC;
    const webSubList = getPlatformVipToWeight(subList.value, PLATFORM_TYPE.WEB);
    if (webSubList.length) {
        curBilling = webSubList[0].priceInterval;
        curPlan = webSubList[0].planLevel;
    }

    const v1 = curBilling === billing.unit; // 判断当前订阅炸账单是月订阅还是年订阅
    const v2 = curPlan === item.plan; // 判断当前是订阅计划不是当前项
    //假如当前是paypal 支付 且 状态是取消订阅状态，则无法进行订阅/升降级操作，不展示按钮
    if (!notCancelSubPlan.value && paypalPayment.value) {
        return false;
    }
    const v3 = isShowRefreshDate.value && paypalPayment.value; // 是否展示升降级按钮： 假如是paypal 支付方式，且存在将来计划，则不展示
    if (type === "subscribe") {
        if (v3) {
            return false;
        }
        return item.showBtn && !(v1 && v2) && multiSubAllowShow.value;
    }
    if (type === "active") {
        return item.showBtn && v1 && v2 && multiSubAllowShow.value;
    }
};

// Down 或 Upgrade 权重计算
const isUpgradeByWeight = (item) => {
    const currVipWeight = VIP_WEIGHT[vipInfo.value.billingPeriod] + VIP_WEIGHT[vipInfo.value.plan];
    const itemWeight = VIP_WEIGHT[item.plan] + VIP_WEIGHT[subscribePlanList[currBilling.value].unit];
    return itemWeight >= currVipWeight;
};
const showSubscribeBtnText = (item) => {
    if (isNotSubscribeVip.value) return "SUBSCRIBE_BUTTON_TEXT";
    return isUpgradeByWeight(item) ? "SUBSCRIBE_UPGRADE_PLAN" : "SUBSCRIBE_DOWNGRADE_PLAN";
};

// 事件名称映射
const gaEventMap = {
    [BILLING_TYPE.YEARLY]: {
        [SUBSCRIBE_TYPE.STANDARD]: "standard_year_sub", // 年标准会员订阅按钮点击事件
        [SUBSCRIBE_TYPE.PRO]: "pro_year_sub", // 年高级会员订阅按钮点击事件
    },
    [BILLING_TYPE.MONTHLY]: {
        [SUBSCRIBE_TYPE.STANDARD]: "standard_month_sub", // 月标准会员订阅按钮点击事件
        [SUBSCRIBE_TYPE.PRO]: "pro_month_sub", // 月高级会员订阅按钮点击事件
    },
};

/* 点击订阅相关逻辑 */
const clickPlan = ref(SUBSCRIBE_TYPE.BASIC);
const subscribeLoading = ref(false);
const handleSubscribe = async (index, item) => {
    // const colIndex = Math.floor(index / totalRow); // 0:Basic 1:Standard 2:Pro
    if (subscribeLoading.value) return;

    // 来自其他页面
    const fromSource = route?.query?.from;
    if (fromSource) {
        // 周年庆卡片
        if (fromSource === "anniversary_card") {
            customTrackEvent("subscribe_from=anniversary_left_corner");
        }
    }

    const itemPlan = item.plan;
    const billing = subscribePlanList[currBilling.value];
    if (isNotSubscribeVip.value) {
        const trackEl = `${itemPlan}_${billing.unit}_sub`;
        customTrackEvent(trackEl);
        // 添加后端埋点
        const source = subGaEvent.value || SUB_EL.OTHER;
        let priceType = gaEventMap[billing.unit][itemPlan];
        reqBiSavePayLog({ source, priceType, paymentChannel: "stripe" }).catch((err) => {
            console.error(err.message);
        });

        //3天试用专用埋点
        if (allowFreeTry.value) {
            priceType = priceType.replace("sub", "trial");
        }
        // 埋点
        customTrackEvent(priceType);
        subscribeLoading.value = true;

        // 查询支付通道
        const paymentChannels = await subscribeStore.getPaymentChannels();
        if (paymentChannels.length === 0) {
            subscribeLoading.value = false;
            //没有获取到支付通道
            //TODO
            showErrorModal("SUBSCRIBE_NO_PAY_CHANNELS");
            return;
        }

        const onlyOneChannel = paymentChannels.length === 1; // 是否只有一个支付通道
        // 免费订阅 或 没有有效订阅
        clickPlan.value = item.plan;
        const requestBody = {
            style: { width: "560px", padding: 0, opacity: onlyOneChannel ? 0 : 1 },
            showCancel: false,
            showConfirm: false,
            content: h(PaymentMethodModal, {
                params: { product: itemPlan, priceInterval: billing.unit },
                source,
                priceType,
                price: item.currentPrice,
                allowFreeTry: allowFreeTry.value,
            }),
        };
        try {
            const res = await showMessage(requestBody);
            if (res.code !== 0) {
                clickPlan.value = SUBSCRIBE_TYPE.BASIC;
                showErrorModal("SUBSCRIBE_EX_TEXT", res);
            }
        } catch (error) {
            clickPlan.value = SUBSCRIBE_TYPE.BASIC;
            if (error !== "cancel") {
                showErrorModal("SUBSCRIBE_EX_TEXT", { code: 500 });
            }
        } finally {
            subscribeLoading.value = false;
        }
    } else {
        const isUpgrade = isUpgradeByWeight(item);
        openUpgradeModal({ currSub: { subscribeType: itemPlan, billingType: billing.unit }, isUpgrade });
    }
};

/* 常见问题相关 */
const currFaq = ref([]);
const faqList = [
    { title: "SUBSCRIBE_FAQ_1_TITLE", desc: "SUBSCRIBE_FAQ_1_DESC" },
    { title: "SUBSCRIBE_FAQ_2_TITLE", desc: "SUBSCRIBE_FAQ_2_DESC" },
    { title: "SUBSCRIBE_FAQ_3_TITLE", desc: "SUBSCRIBE_FAQ_3_DESC" },
    { title: "SUBSCRIBE_FAQ_4_TITLE", desc: "SUBSCRIBE_FAQ_4_DESC" },
    { title: "SUBSCRIBE_FAQ_5_TITLE", desc: "SUBSCRIBE_FAQ_5_DESC" },
    { title: "SUBSCRIBE_FAQ_6_TITLE", desc: "SUBSCRIBE_FAQ_6_DESC", i18nKey: "COMMON_SERVICE_EMAIL" },
    { title: "SUBSCRIBE_FAQ_7_TITLE", desc: "SUBSCRIBE_FAQ_7_DESC" },
    { title: "SUBSCRIBE_FAQ_8_TITLE", desc: "SUBSCRIBE_FAQ_8_DESC" },
    { title: "SUBSCRIBE_FAQ_9_TITLE", desc: "SUBSCRIBE_FAQ_9_DESC" },
    { title: "SUBSCRIBE_FAQ_10_TITLE", desc: "SUBSCRIBE_FAQ_10_DESC" },
    { title: "SUBSCRIBE_FAQ_11_TITLE", desc: "SUBSCRIBE_FAQ_11_DESC" },
    { title: "SUBSCRIBE_FAQ_12_TITLE", desc: "SUBSCRIBE_FAQ_12_DESC" },
    { title: "SUBSCRIBE_FAQ_13_TITLE", desc: "SUBSCRIBE_FAQ_13_DESC" },
    { title: "SUBSCRIBE_FAQ_14_TITLE", desc: "SUBSCRIBE_FAQ_14_DESC", i18nKey: "SUBSCRIBE_FAQ_14_DESC_LIGHT" },
    { title: "SUBSCRIBE_FAQ_16_TITLE", desc: "SUBSCRIBE_FAQ_16_DESC" },
    { title: "SUBSCRIBE_FAQ_17_TITLE", desc: "SUBSCRIBE_FAQ_17_DESC" },
    { title: "SUBSCRIBE_FAQ_18_TITLE", desc: "SUBSCRIBE_FAQ_18_DESC" },
    { title: "SUBSCRIBE_FAQ_19_TITLE", desc: "SUBSCRIBE_FAQ_19_DESC" },
    { title: "SUBSCRIBE_FAQ_20_TITLE", desc: "SUBSCRIBE_FAQ_20_DESC" },
];
const handleFaqChange = (index) => {
    const hasIndex = currFaq.value.findIndex((item) => item === index);
    if (hasIndex > -1) {
        currFaq.value.splice(hasIndex, 1);
    } else {
        currFaq.value.push(index);
    }
};
const i18nValue = computed(() => {
    return ({ i18nCount, i18nKey }) => {
        if (i18nKey) {
            return t(i18nKey, { count: i18nCount });
        }
        if (i18nCount) {
            return i18nCount;
        }

        return "";
    };
});
//购买的单价和当前展示的单价是否一致，一致则 展示active
const samePrice = computed(() => {
    return (price) => Number(currentRenewPrice.value) === Number(price);
});
//当前vip弹窗
const showVipModal = ref(false);
const handleShowVipModal = () => {
    showVipModal.value = true;
    customTrackEvent("manage_subscription");
};
//价格格式化
const priceFormat = (price) => {
    return price ? `$${Number(price).toFixed(2)}` : "$0";
};
</script>

<template>
    <div class="w-full h-full">
        <MobileHeader v-if="isMobile" :title="t('MENU_SUBSCRIBE')" :customBackPath="'/image/create'" />
        <div class="h-full bg-bg-1 p-6 pt-5 md:pt-20 overflow-y-auto scroll-container">
            <h1 class="hidden md:block text-2.5xl font-semibold text-text-1 text-center">{{ $t("MENU_SUBSCRIBE") }}</h1>
            <div class="mt-2 text-center text-text-4 text-sm md:text-base">
                <i18n-t keypath="SUBSCRIBE_TITLE_DESC">
                    <template v-slot:count>
                        <span class="cursor-pointer text-primary-6 ml-1" @click="handleShowVipModal">{{ i18nValue({ i18nKey: "SUBSCRIBE_TITLE_DESC_HIGH_LIGHT" }) }}</span>
                    </template>
                </i18n-t>
            </div>

            <div class="mt-4 md:mt-[72px] max-w-[1200px] mx-auto">
                <!-- 订阅列表 -->
                <div class="flex items-center justify-center relative flex-wrap max-md:gap-4">
                    <div class="h-11 bg-fill-tab-4 rounded-full p-0.5 w-60">
                        <div class="h-full flex justify-center relative gap-0.5 text-text-4">
                            <div ref="tabSlider" class="w-1/2 rounded-full transition-transform duration-500 bg-fill-tab-3 absolute left-0 top-0 bottom-0"></div>
                            <div
                                v-for="(tabItem, tabIndex) in subscribePlanList"
                                :key="tabIndex"
                                :class="{ 'text-text-1': currBilling === tabIndex }"
                                class="w-1/2 h-full flex justify-center items-center rounded-full text-sm font-medium cursor-pointer select-none transition-colors duration-500 relative"
                                @click="handleTabChange(tabIndex)"
                            >
                                <span>{{ $t(tabItem.billing) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="md:absolute right-0">
                        <Button type="tertiary" @click="handleBuyMoreLumen('purchase')">
                            <div class="flex items-center gap-1.5">
                                <IconsLumenFill class="text-xl" />
                                <span>{{ $t("SUBSCRIBE_PURCHASE_LUMEN") }}</span>
                            </div>
                        </Button>
                    </div>
                </div>

                <div class="mt-10 grid xl:grid-cols-3 gap-6">
                    <div
                        v-for="(item, index) in vipBenefitsList"
                        :key="item.plan"
                        class="rounded-2xl border border-solid border-border-t-1 p-6 bg-bg-2 relative !bg-origin-border !bg-no-repeat"
                        :class="[item.vipLevelStyle, index == 0 && 'hidden md:block ']"
                    >
                        <SaleBadge v-if="item.sale" class="sale-bar-style" :is-translate-y="false">
                            <span>{{ $t("SUBSCRIBE_PRICE_OFF", { sale: item.sale }) }}</span>
                        </SaleBadge>

                        <div class="flex flex-col justify-center items-center">
                            <n-icon size="40">
                                <component :is="vipIconComp[item.iconName]"></component>
                            </n-icon>
                            <span class="mt-2 text-xl font-semibold text-text-2 bg-clip-text plan-name-style">{{ $t(item.planAlias) }}</span>

                            <div class="py-6">
                                <div class="flex items-center justify-center gap-2">
                                    <span v-if="item.originalPrice && !isNaN(item.originalPrice) && item.originalPrice !== item.currentPrice" class="text-sm font-normal text-text-3 line-through">
                                        ${{ item.originalPrice }}
                                    </span>
                                    <span class="text-2.5xl font-semibold text-text-1">{{ priceFormat(item.currentPrice) }}</span>
                                    <span class="text-text-4">{{ $t(item.expirationDate) }}</span>
                                </div>
                                <div class="text-text-4 text-center" :class="{ 'text-transparent pointer-events-none': item.plan === SUBSCRIBE_TYPE.BASIC }">{{ $t("SUBSCRIBE_TAX") }}</div>
                            </div>
                        </div>

                        <div class="mb-10 h-11 relative">
                            <div
                                v-if="
                                    index != 0 &&
                                    isHaveFirstBuySub &&
                                    (isShowBtn(item, 'subscribe') ||
                                        (isShowBtn(item, 'active') && isTrialVip) ||
                                        (isShowBtn(item, 'active') && !isTrialVip && samePrice(item.currentPrice)) ||
                                        (isFreeVip && item.currentPrice == 0))
                                "
                                class="absolute top-[-9px] right-0 h-[18px] px-2 py-2 z-10 flex items-center justify-center rounded-tr-xl rounded-bl-xl bg-gradient-to-r from-[rgb(144,19,254)] to-[rgb(103,17,242,1)] cursor-default"
                            >
                                <div class="text-xs font-medium text-white f">{{ t("SUBSCRIBE_NEW_USER_EXCLUSIVE") }}</div>
                            </div>

                            <Button
                                v-if="isShowBtn(item, 'subscribe')"
                                type="primary"
                                block
                                rounded="lg"
                                size="large"
                                :disabled="clickPlan !== item.plan && subscribeLoading"
                                :loading="clickPlan === item.plan && subscribeLoading"
                                class="w-full"
                                @click="handleSubscribe(index, item)"
                            >
                                {{ allowFreeTry ? $t("SUBSCRIBE_FREE_TRIAL", { count: 3 }) : $t(showSubscribeBtnText(item)) }}
                            </Button>
                            <Button v-if="isShowBtn(item, 'active') && isTrialVip" type="primary" block disabled rounded="lg" class="w-full" size="large">
                                {{ $t("SUBSCRIBE_IN_TRIAL") }}
                            </Button>
                            <Button v-if="isShowBtn(item, 'active') && !isTrialVip && samePrice(item.currentPrice)" type="secondary" block disabled rounded="lg" class="w-full" size="large">
                                {{ $t("SUBSCRIBE_ACTIVE") }}
                            </Button>
                            <Button v-if="isFreeVip && item.currentPrice == 0" type="secondary" block disabled rounded="lg" class="w-full" size="large">
                                {{ $t("SUBSCRIBE_ACTIVE") }}
                            </Button>
                        </div>

                        <div class="flex flex-col gap-4">
                            <div class="flex gap-2" v-for="(inner, index) in item.list" :key="index">
                                <IconsSuccess class="shrink-0 text-xl text-success-6" />
                                <div class="text-sm font-medium text-text-3">
                                    <i18n-t :keypath="inner.name">
                                        <template v-slot:count>
                                            <span class="text-primary-6 mx-0.5">{{ i18nValue(inner) }}</span>
                                        </template>
                                    </i18n-t>
                                </div>

                                <div v-if="inner.showHelp" class="w-5 flex items-center">
                                    <n-popover :show-arrow="false" :to="false" class="rounded-lg" placement="left" raw trigger="click">
                                        <template #trigger>
                                            <IconsHelp class="text-xl text-text-5 hover:text-text-2 cursor-pointer mb-auto" />
                                        </template>
                                        <div
                                            class="min-w-[432px] bg-bg-6 rounded-lg p-4 border border-solid border-border-t-1 shadow-[0px_4px_8px_-2px_rgba(0, 0, 0, 0.12),_0px_8px_24px_0px_rgba(0, 0, 0, 0.16)]"
                                        >
                                            <div class="text-base font-medium text-text-1 pb-4">{{ $t(inner.helpTitle) }}</div>
                                            <div class="flex justify-between border border-solid border-border-t-2 rounded text-text-3">
                                                <div v-for="(helpColsItem, helpColsIndex) in inner.helpList" :key="helpColsIndex" class="w-full">
                                                    <div
                                                        v-for="(helpItem, helpIndex) in helpColsItem"
                                                        :key="helpIndex"
                                                        :class="[helpColsIndex !== 0 ? 'text-center justify-center ' : 'border-r', helpIndex !== 0 ? 'border-t' : '']"
                                                        class="h-12 px-3 flex items-center border-solid border-border-t-2"
                                                    >
                                                        <div
                                                            v-if="helpItem.type === 'text'"
                                                            :class="[helpIndex === 0 ? 'text-text-1' : '']"
                                                            class="inline-block min-h-6 text-sm font-medium leading-5.5 whitespace-nowrap"
                                                        >
                                                            <i18n-t :keypath="helpItem.name" tag="div" class="flex">
                                                                <template v-slot:count>
                                                                    <span class="text-primary-6 flex items-center gap-0.5 text-sm font-medium">
                                                                        {{ helpItem.i18nCount }}
                                                                        <n-icon size="16"><IconsLumenFill /></n-icon>
                                                                    </span>
                                                                </template>
                                                            </i18n-t>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </n-popover>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 常见问题解答 -->
                <div class="text-base md:text-xl mt-6 md:mt-[72px] text-text-1 text-center font-semibold">{{ $t("SUBSCRIBE_FAQ_TITLE") }}</div>
                <div class="space-y-4 font-medium text-sm mt-5 md:mt-10">
                    <div v-for="(item, index) in faqList" :key="index" @click="handleFaqChange(index)" class="p-4 bg-bg-2 rounded-lg cursor-pointer select-none border border-solid border-border-t-1">
                        <div class="flex justify-between items-center">
                            <span class="text-base text-text-2" :class="{ '!text-text-1': currFaq.includes(index) }">{{ $t(item.title) }}</span>
                            <IconsArrowLine :class="[currFaq.includes(index) ? 'rotate-0' : '-rotate-90']" class="w-6 h-6 text-text-5 transition-all" />
                        </div>
                        <div :class="{ '!max-h-[1000px]': currFaq.includes(index) }" class="text-text-3 transition-all duration-200 overflow-hidden max-h-0" @click.stop>
                            <div class="pt-4 whitespace-pre-wrap select-text md:pr-12">
                                <i18n-t :keypath="item.desc">
                                    <template v-slot:count>
                                        <span class="text-primary-6 ml-1">{{ i18nValue({ i18nKey: item.i18nKey }) }}</span>
                                    </template>
                                </i18n-t>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <n-modal v-model:show="showVipModal" transform-origin="center">
                <div class="bg-bg-2 rounded-2xl py-4 border border-solid border-border-1 w-[500px] max-md:w-11/12">
                    <!-- 标题 -->
                    <div class="flex justify-between items-center pb-4 pl-6 pr-4">
                        <n-skeleton v-if="isShowVipInfoSkeleton" height="32px" round />
                        <template v-else>
                            <div class="flex items-center gap-2">
                                <div class="text-base text-text-1 font-semibold">{{ $t("SUBSCRIBE_ACTIVE_PLAN_TEXT") }}:</div>
                                <div v-show="isVipInfoLoaded" class="text-xs font-medium gap-1 py-0.5 border-2 border-solid border-transparent rounded-full relative flex items-center">
                                    <VipLevel :plan="vipPlan" />
                                </div>
                                <!-- <span class="text-white text-xl font-semibold">{{ $t(currPlanName) }}</span> -->
                                <IconsSpinLoading v-show="!isVipInfoLoaded" class="w-5 h-5 text-primary" />
                            </div>

                            <div
                                class="w-8 h-8 rounded-full flex items-center justify-center hover:bg-fill-wd-1 text-text-4 hover:text-text-2 cursor-pointer transition-colors"
                                @click="showVipModal = false"
                            >
                                <IconsClose class="text-xl" />
                            </div>
                        </template>
                    </div>

                    <!-- 会员信息 -->
                    <div class="px-6 pt-4">
                        <n-skeleton v-if="isShowVipInfoSkeleton" class="flex-1 rounded-2xl" height="152px" />
                        <template v-else>
                            <template v-if="!isFreeVip && multiSubAllowShow">
                                <div class="flex items-center w-full">
                                    <div class="text-text-2 text-base font-medium">{{ $t("SUBSCRIBE_BILLING") }}</div>
                                    <div class="flex items-center gap-2 ml-auto">
                                        <template v-if="!isNotSubscribeVip && multiSubAllowShow && !isGiftVip">
                                            <!-- 发票 -->
                                            <IconsSpinLoading v-if="invoicesLoading" class="w-5 h-5 text-primary" />
                                            <Button v-if="!paypalPayment && !invoicesLoading" @click="handleViewInvoices" type="secondary" size="small">{{ $t("SUBSCRIBE_VIEW_INVOICES") }} </Button>
                                            <!-- 升级未来订阅后不显示恢复自动订阅按钮 -->
                                            <template v-if="!notCancelSubPlan && !isShowRefreshDate">
                                                <IconsSpinLoading v-if="unCancelLoading" class="w-5 h-5 text-primary" />
                                                <Button v-if="!paypalPayment && !unCancelLoading" @click="handleUnCancelPlan" type="tertiary" size="small">{{ $t("SUBSCRIBE_UNCANCEL_PLAN") }}</Button>
                                            </template>
                                        </template>
                                        <!-- 取消未来计划 -->
                                        <Button v-show="!paypalPayment && isShowRefreshDate" @click="handleCancelUpgradePlan" type="secondary" size="small"
                                            >{{ $t("SUBSCRIBE_CANCEL_CHANGE") }}
                                        </Button>
                                        <!-- 取消自动续费 -->
                                        <Button v-show="notCancelSubPlan" @click="handleCancelSubscribe" type="secondary" size="small">{{ $t("SUBSCRIBE_CANCEL_PLAN") }} </Button>
                                    </div>
                                </div>

                                <!-- 会员订阅信息 -->
                                <div v-if="!isGiftVip" class="mt-4 space-y-2 text-text-4 font-medium">
                                    <div class="flex items-center justify-between">
                                        <span>{{ $t("SUBSCRIBE_PRICE") }}</span>
                                        <span class="text-text-2">{{ formatPriceText }}</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span>{{ $t("SUBSCRIBE_PERIOD") }}</span>
                                        <span class="text-text-2">{{ formatBillingPeriod }}</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span>{{ $t("SUBSCRIBE_RENEWED_ON") }}</span>
                                        <span class="text-text-2">
                                            {{ !notCancelSubPlan && !isShowRefreshDate ? "-" : formatRefreshDate || formatExpireDateText.label || $t(formatExpireDateText.labelKey) }}
                                            {{ isShowRefreshDate ? "(" + $t(formatRefreshPlanName) + ")" : "" }}
                                        </span>
                                    </div>

                                    <div class="flex items-center justify-between" v-if="!notCancelSubPlan">
                                        <span>{{ $t("SUBSCRIBE_CANCELLING_ON") }}</span>
                                        <span class="text-text-2">{{ formatExpireDateText.label || $t(formatExpireDateText.labelKey) }}</span>
                                    </div>
                                </div>

                                <!-- 只有赠送会员时显示 -->
                                <template v-if="isGiftVip">
                                    <div class="flex items-center justify-between mt-2 text-text-4 font-medium">
                                        <span>{{ $t("SUBSCRIBE_RENEWED_ON") }}</span>
                                        <span class="text-text-2"> {{ formatExpireDateText.label || $t(formatExpireDateText.labelKey) }} </span>
                                    </div>
                                </template>
                                <div class="my-6 border-t border-dashed border-border-2"></div>
                            </template>

                            <div class="text-sm font-medium space-y-2 text-text-4">
                                <div class="text-base text-text-2">{{ $t("SUBSCRIBE_LUMEN") }}</div>

                                <div class="flex items-center justify-between">
                                    <span> {{ $t("SUBSCRIBE_DAILY") }}</span>
                                    <span class="text-text-2"> {{ formatLumenText(`${vipInfo.freeLumenLeft} / ${vipInfo.freeLumenTotal}`) }} </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span> {{ $t("SUBSCRIBE_INCLUDED") }}</span>
                                    <div class="text-text-2 flex items-center gap-1">
                                        <span>{{ formatLumenText(`${vipInfo.planLumenLeft} / ${vipInfo.planLumenTotal}`) }}</span>
                                        <span v-if="isTrialVip" class="text-info-6">({{ t("SUBSCRIBE_TRIAL_BOUNS") }}) </span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span> {{ $t("PURCHASED") }}</span>
                                    <span class="text-text-2"> {{ formatLumenText(vipInfo.awardedLumenLeft) }} </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span> {{ $t("AWARDED") }}</span>
                                    <span class="text-text-2"> {{ formatLumenText(vipInfo.giftLumenLeft) }} </span>
                                </div>
                            </div>
                            <Button type="tertiary" block size="large" rounded="lg" class="w-full mt-6" @click="handleBuyMoreLumen('manage_popup_buy_lumen')">{{ $t("SUBSCRIBE_BUY_MORE") }}</Button>
                        </template>
                    </div>
                </div>
            </n-modal>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.standard-style {
    background: linear-gradient(180deg, rgba(173, 243, 255, 0.08) 0%, rgba(173, 243, 255, 0) 100%), var(--p-bg-2, #121216);
    .plan-name-style {
        background-image: linear-gradient(90deg, #71c6d5 0%, #2d9aad 100%);
    }
    .sale-bar-style {
        background: linear-gradient(90deg, #00c19a 0.17%, #20b761 100%);
    }
}
.pro-style {
    background: linear-gradient(180deg, rgba(255, 185, 79, 0.08) 0%, rgba(255, 185, 79, 0) 100%), var(--p-bg-2, #121216);
    .plan-name-style {
        background-image: linear-gradient(90deg, #fa0 0%, #ff6c3f 100%);
    }
    .sale-bar-style {
        background: linear-gradient(270deg, #ff3700 0%, #ff9417 99.83%);
    }
}
.standard-style,
.pro-style {
    .plan-name-style {
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .sale-bar-style {
        border-radius: 2px 16px;
    }
}

.basic-plan {
    @apply border-[#BCBCBC];
    background: linear-gradient(90deg, #7c7c7c 0%, #bcbcbc 100%);
}

.standard-plan {
    @apply border-[#3CEA85];
    background: linear-gradient(180deg, #f3ff14 0%, #adf3ff00 100%);
}

.pro-plan {
    @apply border-[#FFDD34];
    background: linear-gradient(90deg, #fb6842 0%, #ffd500 100%);
}
</style>
