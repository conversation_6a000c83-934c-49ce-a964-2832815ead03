<!--
 * @Author: HuangQS
 * @Description: Explore页面 Header中的筛选组件
 * @Date: 2025-06-18 10:44:35
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-16 20:21:48
-->

<template>
    <template v-if="isMobile">
        <!-- 移动端 -->
        <div class="mo-wrapper">
            <!-- 左 -->
            <!-- onMoSearchBtnClick -->
            <div class="search-box" @click="onMoSearchBtnClick" :class="isMoSearchBtnClick ? 'active' : 'close'">
                <n-icon size="24" class="text-text-2 shrink-0"> <IconsSearch /> </n-icon>

                <div class="input-content">
                    <template v-if="isMoSearchBtnClick">
                        <input
                            :disabled="isLoading"
                            class="text-text-1 bg-transparent outline-none w-full h-full ml-2"
                            maxlength="50"
                            :placeholder="t('COMMON_SEARCH_PLACEHOLDER')"
                            v-model="searchParam.vagueKey"
                            @keyup.enter="searchFn(0)"
                            @input="searchFn(2000)"
                            @click="trackEvents('Community', 'search_click')"
                        />

                        <div class="p-1 flex items-center justify-center bg-fill-opt-1 rounded-full" @click.stop="moClearSearchKey">
                            <n-icon size="18" class="text-text-2">
                                <IconsClose />
                            </n-icon>
                        </div>
                    </template>
                </div>
            </div>

            <!-- 中 -->
            <div v-if="!isMoSearchBtnClick" class="filter-bar justify-center">
                <div class="filter-item" :class="{ 'filter-item-active': searchParam.collationName === item.value }" v-for="item in filterList" :key="item.value" @click="changeFilter(item.value)">
                    <span>{{ t(item.label, 2) }}</span>
                </div>
            </div>
            <!-- 右 -->
            <div v-if="!isMoSearchBtnClick" class="drop-box" @click.stop="isMoDropBtnClick = !isMoDropBtnClick" :class="isMoDropBtnClick ? 'active' : 'close'">
                <n-tooltip ref="tagModal" style="--n-box-shadow: none" placement="bottom" trigger="click" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <n-icon size="24" class="text-text-4">
                            <IconsArrowLine />
                        </n-icon>
                    </template>
                </n-tooltip>
            </div>

            <template v-if="isMoDropBtnClick">
                <div class="absolute left-0 right-0" :style="{ top: `${absTopHeihgt}px` }">
                    <div class="tag-items-box">
                        <div v-for="item in tagTypes" :key="item.value" @click="changeTag(item.value)" class="item" :class="{ 'active-tag ': searchParam.tags === item.value }">
                            <span>{{ item.label }}</span>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </template>

    <template v-else>
        <!-- 网页端 -->
        <div class="pc-wrapper items-start h-11 relative">
            <!-- 右侧下拉组件 -->
            <n-tooltip ref="tagModal" style="--n-box-shadow: none" placement="bottom" trigger="click" :delay="100" :show-arrow="false" raw>
                <template #trigger>
                    <div @click="triggerTagGroup" class="tags-trigger-btn">
                        <n-icon size="24" class="transition-transform" :class="{ 'rotate-180': tagGroupExpanded }">
                            <IconsArrowLine />
                        </n-icon>
                        <div v-if="showDot" class="w-3 h-3 rounded-full bg-primary-6 top-0 right-0 absolute border-2 border-solid border-bg-2"></div>
                    </div>
                </template>

                <div class="p-4 bg-bg-6 rounded-2xl border border-solid border-border-t-1 flex flex-wrap flex-1 gap-2 max-w-[496px] shadow-sm">
                    <div
                        v-for="item in tagTypes.slice(splitTagIndex + 1)"
                        :key="item.value"
                        @click="changeTag(item.value)"
                        class="tag-item"
                        :class="{ 'active-tag ': searchParam.tags === item.value }"
                    >
                        <span>{{ item.label }}</span>
                    </div>
                </div>
            </n-tooltip>

            <!-- 搜索框 -->
            <div class="search-box shadow-box focus-within:!border-primary">
                <n-icon size="24" class="text-text-4">
                    <IconsSearch />
                </n-icon>
                <input
                    :disabled="isLoading"
                    class="bg-transparent outline-none min-w-0 w-52 h-full"
                    maxlength="50"
                    :placeholder="t('COMMON_SEARCH_PLACEHOLDER')"
                    v-model="searchParam.vagueKey"
                    @keyup.enter="searchFn(0)"
                    @input="searchFn(2000)"
                    @click="trackEvents('Community', 'search_click')"
                />
                <n-icon v-show="!!searchParam.vagueKey" size="18" class="cursor-pointer absolute right-1" @click="clearSearchKey">
                    <IconsClose />
                </n-icon>
            </div>

            <!-- 三选一TAB -->
            <div class="filter-bar">
                <div class="filter-item" :class="{ 'filter-item-active': searchParam.collationName === item.value }" v-for="item in filterList" :key="item.value" @click="changeFilter(item.value)">
                    <span>{{ t(item.label, 2) }}</span>
                </div>
            </div>

            <div class="flex flex-wrap flex-1 gap-2">
                <div v-for="item in tagTypes" :key="item.value" @click="changeTag(item.value)" class="tag-item" :class="{ 'active-tag ': searchParam.tags === item.value }">
                    <span>{{ item.label }}</span>
                </div>
            </div>
        </div>
    </template>
</template>
<script setup>
import { useThemeStore } from "@/stores/system-config";
import { toRefs } from "vue";
import useWindowResize from "@/hook/windowResize";
import { storeToRefs } from "pinia";

const route = useRoute();
const router = useRouter();

let timeout = null;
import { communityHotTags } from "@/utils/tools";
const windowResize = useWindowResize();

const { t } = useI18n({ useScope: "global" });

const { isMobile } = storeToRefs(useThemeStore());
const tagGroupExpanded = ref(false);
const tagModal = ref(null);

const props = defineProps({
    searchParam: {
        type: Object,
        default: () => ({
            collationName: "",
            tags: "",
            vagueKey: "",
        }),
    },
    isLoading: {
        type: Boolean,
        default: false,
    },
    absTopHeihgt: {
        type: Number,
        default: 0,
    },
});

//列表排序方式
const filterList = [
    { label: "COMMON_SORT_FEATURED", value: "Featured" },
    { label: "COMMUNITY_TAG_TRENDING", value: "Trending" },
    { label: "COMMUNITY_TAG_NEW", value: "New" },
    // { label: "SHORT_LIKES", value: "Likes" },
];

const { searchParam, isLoading } = toRefs(props);

const emit = defineEmits([
    "whenChangeTag", // 当变更tag 清除列表数据
    "whenSearch", //前往搜索
]);

onMounted(() => {
    const tab = route.query.tab;
    const tabIndex = filterList.findIndex((item) => item.value === tab);
    if (tabIndex > -1) {
        searchParam.value.collationName = tab;
    }
    handleSearch();
});

// 触发Google事件
const trackEvents = (event, el) => {
    try {
        window.trackEvent(event, { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};

const triggerTagGroup = () => {
    tagGroupExpanded.value = !tagGroupExpanded.value;
};

const clearSearchKey = () => {
    searchParam.value.vagueKey = "";
    handleSearch();
};
const moClearSearchKey = () => {
    isMoSearchBtnClick.value = false;

    if (searchParam.value.vagueKey) {
        clearSearchKey();
    }
};

const searchFn = (t = 2000) => {
    timeout && clearTimeout(timeout);
    timeout = setTimeout(() => {
        handleSearch();
    }, t);
};

//修改tag
const changeTag = (tag) => {
    tagModal.value.setShow(false);
    tagGroupExpanded.value = false;
    isMoDropBtnClick.value = false;
    if (isLoading.value) return;

    if (searchParam.value.tags === tag) {
        searchParam.value.tags = "";
    } else {
        searchParam.value.tags = tag;
        trackEvents("Community", `tag=${tag}`);
    }
    // dataList.value = [];
    emit("whenChangeTag", []);
    handleSearch();
};
const localePath = useLocalePath();
const changeFilter = async (v) => {
    trackEvents("Community", `tab=${v}`);
    if (isLoading.value) return;
    if (searchParam.value.collationName === v) return;

    await navigateTo({ path: localePath("/community/explore"), query: { tab: v } });
    searchParam.value.collationName = v;
    isMoDropBtnClick.value = false;
    emit("whenChangeTag", []);
    handleSearch();
};

//关键词模糊匹配 在此处请求数据
const handleSearch = () => {
    if (isLoading.value) return;

    emit("whenSearch");
};

const formateTags = (str = "") => {
    return str
        .split("_") // 按下划线分割字符串
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 首字母大写，其余小写
        .join(" "); // 用空格连接
};
//计算tag分割索引
const splitTagIndex = computed(() => {
    if (windowResize.width.value) {
        return findTopRightTagIndex();
    }
    return 0;
});

//查询指定className 最右上角的tag
const findTopRightTagIndex = () => {
    let topRightTagIndex = -1;
    if (import.meta.client) {
        const elements = document.querySelectorAll(".tag-item");
        let minY = Infinity;
        let maxX = -Infinity;

        elements.forEach((element, index) => {
            const rect = element.getBoundingClientRect();
            const y = rect.top;
            const x = rect.left;
            if (y < minY || (y === minY && x > maxX)) {
                minY = y;
                maxX = x;
                topRightTagIndex = index;
            }
        });
    }
    return topRightTagIndex;
};

const tagTypes = [...communityHotTags.map((item) => ({ label: formateTags(item), value: item }))];
const showDot = computed(() => tagTypes.findIndex((item) => searchParam.value.tags === item.value) > splitTagIndex.value);
const isMoSearchBtnClick = ref(false); // 搜索按钮被点击
const isMoDropBtnClick = ref(false); // 下拉按钮被点击

// 移动端搜索按钮被点击
const onMoSearchBtnClick = () => {
    isMoSearchBtnClick.value = true;
    isMoDropBtnClick.value = false;
};
</script>
<style lang="scss" scoped>
.mo-wrapper {
    @apply flex  w-full h-[60px]  items-center    bg-bg-1 px-3;

    .search-box {
        @apply border border-bg-1 shrink-0;
        @apply h-11 items-center px-4;

        &.active {
            @apply border border-border-2;
            @apply flex justify-start rounded-[22px] w-full;
            @apply bg-fill-opt-1;

            animation: alternate open-anim 0.3s ease-in-out forwards;
            @keyframes open-anim {
                0% {
                    width: 44px;
                }
                100% {
                    width: 100%;
                }
            }
        }

        &.close {
            @apply flex size-11  justify-center rounded-full;

            animation: alternate close-anim 0.3s ease-in-out forwards;
            @keyframes close-anim {
                0% {
                    width: 100%;
                }
                100% {
                    // width: 0;
                }
            }
        }

        .input-content {
            @apply flex flex-1 items-center;
        }
    }

    .filter-bar {
        @apply flex flex-1 items-center overflow-x-auto   text-text-4  gap-4;
        .filter-item {
            @apply h-full flex items-center justify-center rounded-full  cursor-pointer relative z-10  min-w-16 px-4;
        }
        .filter-item.filter-item-active {
            @apply text-text-1;
        }

        /* 隐藏滚动条但保留功能 */
        scrollbar-width: none;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .drop-box {
        @apply size-11 flex items-center justify-center rounded-full;

        &.active {
            @apply rotate-180;
            animation: rotate-anim-active 0.3s ease-in-out forwards;
            @keyframes rotate-anim-active {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    @apply bg-fill-btn-1;
                    transform: rotate(180deg);
                }
            }
        }
        &.close {
            animation: rotate-anim-close 0.3s ease-in-out forwards;
            @keyframes rotate-anim-close {
                0% {
                    @apply bg-fill-btn-1;
                    transform: rotate(180deg);
                }
                100% {
                    transform: rotate(0deg);
                }
            }
        }
    }
    .tag-items-box {
        @apply flex flex-wrap flex-1 gap-2;
        @apply bg-fill-gradient-10 max-h-[45vh] overflow-y-auto  p-2.5 gap-2;
        @apply rounded-bl-2xl rounded-br-2xl;

        .item {
            @apply bg-fill-opt-1 px-3 py-2 text-text-3;
            @apply rounded-3xl;
        }

        .active-tag {
            @apply bg-fill-opt-3 text-text-tab-7;
        }
    }
}

.pc-wrapper {
    @apply flex flex-wrap gap-2 pr-12 overflow-hidden;

    .tags-trigger-btn {
        @apply h-11 w-11 rounded-full bg-fill-tab-5 text-text-tab-5 cursor-pointer absolute right-0 top-0  flex items-center justify-center;
    }

    .search-box {
        @apply h-11 backdrop-blur-sm bg-fill-ipt-1 relative pr-6  rounded-full p-2.5 text-sm flex items-center gap-1.5  border border-solid border-white/0 dark:text-dark-text/70;
    }
    .search-box {
        @apply h-11 backdrop-blur-sm bg-fill-ipt-1 relative pr-6  rounded-full p-2.5 text-sm flex items-center gap-1.5  border border-solid border-white/0 dark:text-dark-text/70;
    }
    .filter-bar {
        @apply flex items-center justify-between relative bg-fill-tab-4 h-11 rounded-full p-0.5 overflow-hidden gap-1 text-text-4;
        .filter-item {
            @apply h-full flex items-center justify-center rounded-full shrink-0 cursor-pointer relative z-10 hover:text-text-1 px-4 min-w-24;
        }
        .filter-item.filter-item-active {
            @apply bg-fill-tab-3 text-text-1;
        }
    }
}

.tag-item {
    @apply flex items-center justify-center rounded-full text-sm h-11 px-4 bg-fill-tab-5 text-text-tab-5 cursor-pointer hover:bg-fill-tab-6 hover:text-text-tab-6;
    &.active-tag {
        @apply bg-fill-tab-7 text-text-tab-7;
    }
}
</style>
