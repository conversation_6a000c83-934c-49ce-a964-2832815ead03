<template>
    <div class="absolute top-0 left-0 right-0 bottom-0 pointer-events-none bg-fill-t-3 z-[9999]" v-if="isDragging"></div>
    <div
        id="container"
        ref="containerRef"
        class="operation-bar-box transition-all ease-linear duration-200 pb-20"
        :class="{ 'cursor-crosshair': selectable && status !== 'loading' }"
        @scroll="handleScrollCore"
        @mousedown="recordStartPoint"
    >
        <!-- 虚拟滚动高度占位 -->
        <div :style="{ height: totalHeight + 'px' }" class="relative transition-all ease-linear duration-200 h-full" v-if="status !== 'loading'"></div>
        <BoundingBox
            class="min-h-[90vh]"
            :style="{ height: totalHeight + 'px' }"
            v-if="selectable"
            @onMove="getSelectedItem"
            @onEnd="handleDragEnd"
            @onUp="shouldClear"
            @onBoundaryCrossed="handleAutoScroll"
            :disabled="disabled || !items.length"
        />
        <!-- 渲染图片项 -->
        <!-- v-longPress="() => handleItemLongPress(item)" -->
        <template v-if="status === 'loading' && !visibleItems.length">
            <slot name="skeleton">
                <DefaultSkeleton :perRow="perRow" />
            </slot>
        </template>
        <template v-else>
            <div
                v-for="item in visibleItems"
                v-show="item.visible"
                :key="item[imageKey]"
                class="image-item group transition-all ease-linear duration-200 bg-bg-2 rounded-lg"
                :class="{ ' !cursor-pointer': itemClickable }"
                :style="{
                    top: item.top + 'px',
                    left: item.left + 'px',
                    width: itemWidth + 'px',
                    height: itemWidth + 'px',
                }"
                @click="handleItemClick(item)"
            >
                <!-- 此组件当前仅支持 正方形item 且宽高目前仅支持通过layoutType和perRow(每行元素个数)决定 -->
                <slot name="item" :item="item">
                    <div class="image-item-img" :class="itemClass">
                        <img
                            loading="lazy"
                            :draggable="selectedIds.includes(item[imageKey])"
                            @mousedown="handleProxyMove($event, selectedIds.includes(item[imageKey]))"
                            @mouseup="handleImageDragEnd"
                            @dragstart="handleDragStart($event, item)"
                            @dragend="handleImageDragEnd"
                            :src="item[imageKey] || ''"
                            class="w-full h-full transition-all ease-linear rounded-lg"
                            :class="[fillType === 'EqualHeight' ? 'object-contain' : 'object-cover', selectedIds.includes(item[imageKey]) ? 'scale-75 ' : '']"
                        />
                    </div>
                </slot>
                <slot name="item-extra" :item="item">
                    <YileCheckbox
                        v-if="selectable"
                        :model-value="selectedIds.includes(item[imageKey])"
                        @change="handleItemSelect($event, item)"
                        size="20"
                        class="absolute top-2 right-2 hidden group-hover:block"
                        :class="{ '!flex': selectedIds.includes(item[imageKey]) }"
                    />
                </slot>
            </div>

            <!-- 条件渲染分组标题 -->
            <div v-for="(header, hIdx) in visibleHeaders" v-show="header.visible" :key="'header-' + hIdx" class="date-header" :style="{ top: header.top + 'px' }">
                <!-- header中的内容 默认以group-key进行展示 高度通过headerHeight控制 虽然能自定义内部展示的内容 但是无法根据自定义内容的高度进行调整 -->
                <slot v-if="showHeader" name="header" :header="header">
                    <span>{{ header.needI18n ? $t(header.key) : header.key }}</span>
                </slot>
            </div>
        </template>

        <slot name="footer" />
        <div ref="dragPreviewRef" class="absolute w-12 h-12 rounded-md pointer-events-none top-1/2 opacity-0 -z-10">
            <div
                class="absolute top-0 right-0 text-xs font-semibold max-w-max min-w-6 h-5 border-2 border-solid z-[100] border-dark-active-text bg-red-600 rounded-full flex items-center justify-center text-dark-active-text"
            >
                <span class="px-1">{{ selectedIds.length }}</span>
            </div>
            <div class="w-full h-full absolute top-0 left-0 right-0 z-2">
                <img
                    class="images-for-drag w-full h-full origin-center absolute top-0 left-0 right-0 bottom-0 rounded-lg object-cover"
                    :src="dragItem[imageKey]"
                    v-for="(dragItem, index) in imagesForDrag"
                    :key="`drag-${dragItem[imageKey]}`"
                />
            </div>
        </div>
    </div>
</template>
<script setup>
import BoundingBox from "@/components/business/bounding-box/index.vue";
import YileCheckbox from "@/components/common/checkbox/index.vue";
import DefaultSkeleton from "@/components/business/virtual-scroll/DefaultSkeleton.vue";

import { debounce } from "@/utils/tools";
import { computed, nextTick, onActivated, onDeactivated } from "vue";

const emit = defineEmits(["scrollToLower", "itemClick", "itemLongPress", "bounding-select"]);
const props = defineProps({
    items: {
        type: Array,
        default: () => [],
    },
    groupKey: {
        type: String,
        default: "",
    },
    imageKey: {
        //这个key决定了图片内容能否被正常渲染
        type: String,
        required: true,
    },
    // 是否开启橡皮筋多选模式
    selectable: {
        type: Boolean,
        default: true,
    },
    // 图片可点击
    itemClickable: {
        type: Boolean,
        default: true,
    },
    showHeader: {
        type: Boolean,
        default: false,
    },
    fillType: {
        type: String,
        default: "Square",
    },
    headerHeight: {
        type: Number,
        default: 0,
    },
    gap: {
        type: Number,
        default: 4,
    },
    layoutType: {
        type: String,
        default: "Medium",
    },
    itemClass: {
        type: String,
        default: "",
    },
    perRow: {
        type: Number,
        default: 10,
    },
    status: {
        type: String,
        default: "loading",
    },
});

const buffer = computed(() => (props.layoutType === "Small" ? 100 : props.layoutType === "Medium" ? 150 : 200));

const handleItemClick = (item) => {
    isCheck = true;
    emit("itemClick", item);
};

//代理移动事件，防止移动和 框选事件冲突
const handleProxyMove = (event, existed) => {
    if (existed) {
        disabled.value = true;
        event.stopPropagation();
    }
};
onActivated(() => {
    if (!scrollOffsetTop.value) return (skipResize = false);
    scrollTo({
        top: scrollOffsetTop.value,
        height: totalHeight.value,
    });
});

onDeactivated(() => {
    skipResize = true;
});
const disabled = ref(false);
const isDragging = ref(false);
const dragPreviewRef = ref(null);
const cover = ref("");
const handleDragStart = (event, item) => {
    const hasSelected = selectedIds.value.includes(item[props.imageKey]);
    if (!hasSelected) return;
    cover.value = event.target.getAttribute("src") || item.renderUrl;
    disabled.value = true;
    isDragging.value = true;
    dragPreviewRef.value.style.opacity = "1";
    event.dataTransfer.setDragImage(dragPreviewRef.value, 30, 30); // 中心点为 (50,50)
};
const handleImageDragEnd = () => {
    disabled.value = false;
    isDragging.value = false;
    dragPreviewRef.value.style.opacity = "0";
};

const handleItemLongPress = (item) => {
    emit("itemLongPress", item);
};

// Reactive state
const containerRef = ref(null);
const totalHeight = ref(0);
const visibleItems = ref([]);
const visibleHeaders = ref([]);

const groupByGroupKey = (flatData) => {
    const map = new Map();
    for (const item of flatData) {
        const key = item[props.groupKey];
        if (!map.has(key)) map.set(key, []);
        map.get(key).push(item);
    }
    return Array.from(map.entries()).map(([key, items]) => ({ key, items, needI18n: items[0]?.needI18n ?? false }));
};

// 计算属性：是否需要分组
const shouldGroup = computed(() => props.groupKey);

let groupedData = {};
const allItemPositions = ref([]); // 全局 item 位置信息数组

const calculateLayout = () => {
    const positions = []; // 临时收集位置信息
    let currentY = 0;
    const layoutCache = [];

    // 分组模式
    if (shouldGroup.value) {
        groupedData.value = groupByGroupKey(props.items);
        Object.values(groupedData.value).forEach((group) => {
            const rows = Math.ceil(group.items.length / props.perRow);
            const groupHeight = props.headerHeight + rows * (itemWidth.value + props.gap);
            layoutCache.push({
                date: group.date,
                items: group.items,
                startY: currentY,
                height: groupHeight,
                key: group.key,
                needI18n: group.needI18n,
            });

            // 生成每个 item 的位置信息
            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < props.perRow; col++) {
                    const index = row * props.perRow + col;
                    if (index >= group.items.length) break;
                    const item = group.items[index];
                    const top = currentY + props.headerHeight + row * (itemWidth.value + props.gap);
                    const left = col * (itemWidth.value + props.gap) + 8;
                    positions.push({
                        id: item[props.imageKey],
                        top,
                        left,
                        width: itemWidth.value,
                        height: itemWidth.value,
                        item, // 保存原始数据，方便回传
                    });
                }
            }
            currentY += groupHeight;
        });
    } else {
        // 平铺模式
        const rows = Math.ceil(props.items.length / props.perRow);
        const totalHeightValue = rows * (itemWidth.value + props.gap);

        layoutCache.push({
            items: props.items,
            startY: 0,
            height: totalHeightValue,
        });

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < props.perRow; col++) {
                const index = row * props.perRow + col;
                if (index >= props.items.length) break;
                const item = props.items[index];
                const top = props.headerHeight + row * (itemWidth.value + props.gap);
                const left = col * (itemWidth.value + props.gap) + 8;
                positions.push({
                    id: item[props.imageKey],
                    top,
                    left,
                    width: itemWidth.value,
                    height: itemWidth.value,
                    item,
                });
            }
        }
        currentY = totalHeightValue;
    }

    totalHeight.value = currentY;
    allItemPositions.value = positions; // 保存全局位置
    return layoutCache;
};

const calculateVisibleElements = (scrollTop, containerHeight) => {
    const layoutCache = layoutCacheRef.value;
    const buffer = itemWidth.value * 2; // 缩小 buffer
    const renderStartY = scrollTop - buffer;
    const renderEndY = scrollTop + containerHeight + buffer;
    const visibleItemsMap = new Map();
    const visibleHeadersMap = new Map();

    for (const layout of layoutCache) {
        const layoutOffset = shouldGroup.value ? props.headerHeight : 0;
        const groupEndY = layout.startY + layout.height;

        // 如果该项完全不在可视区域内，就跳过计算
        if (groupEndY < renderStartY || layout.startY > renderEndY) continue;

        // 更新可见标题
        if (shouldGroup.value) {
            const headerVisible = layout.startY <= renderEndY && layout.startY + props.headerHeight >= renderStartY;
            if (headerVisible) {
                visibleHeadersMap.set(layout.key, {
                    key: layout.key,
                    top: layout.startY,
                    needI18n: layout.needI18n,
                    visible: true,
                });
            }
        }

        // 计算当前可见的行
        const startRow = Math.max(0, Math.floor((renderStartY - layout.startY - layoutOffset) / (itemWidth.value + props.gap)));
        const endRow = Math.min(Math.ceil(layout.items.length / props.perRow) - 1, Math.ceil((renderEndY - layout.startY - layoutOffset) / (itemWidth.value + props.gap)));
        // 优化渲染区域，只计算可见区域
        for (let row = startRow; row <= endRow; row++) {
            for (let col = 0; col < props.perRow; col++) {
                const index = row * props.perRow + col;
                if (index >= layout.items.length) break;
                const item = layout.items[index];
                const top = layout.startY + layoutOffset + row * (itemWidth.value + props.gap);
                const left = col * (itemWidth.value + props.gap) + 8;

                visibleItemsMap.set(item[props.imageKey], {
                    ...item,
                    top,
                    left,
                    width: itemWidth.value,
                    height: itemWidth.value,
                    visible: true,
                });
            }
        }
    }

    return {
        visibleItems: Array.from(visibleItemsMap.values()),
        visibleHeaders: Array.from(visibleHeadersMap.values()),
    };
};
const scrollOffsetTop = ref(0);
let layoutCacheRef = ref([]);
// Scroll handler
const handleScrollCore = async () => {
    if (!containerRef.value || skipResize) return;
    const scrollTop = containerRef.value.scrollTop;
    const containerHeight = containerRef.value.clientHeight;

    // Check if we need to load more data
    const nearBottom = scrollTop + containerHeight >= totalHeight.value - buffer.value;
    scrollOffsetTop.value = scrollTop;
    if (nearBottom) {
        if (scrollTop) {
            emit("scrollToLower");
        }
    }

    // Calculate visible elements
    layoutCacheRef.value = calculateLayout();
    const { visibleItems: newVisibleItems, visibleHeaders: newVisibleHeaders } = calculateVisibleElements(scrollTop, containerHeight);
    visibleItems.value = newVisibleItems;
    visibleHeaders.value = newVisibleHeaders;
    if (totalHeight.value < containerHeight) {
        emit("scrollToLower");
        console.log("还不够一屏");
    }
};

let itemWidth = ref(118);

//计算每一个item的宽度
const getItemWidth = (containerWidth = 1080) => {
    const exactWidth = containerWidth - 16;
    const ratio = 1 / props.perRow;
    const finalItemWidth = (exactWidth - (props.perRow - 1) * props.gap) * ratio;
    return finalItemWidth;
};
let skipResize = false;
const handleResize = debounce((immediate = false) => {
    if (skipResize) return;
    const container = containerRef.value;
    if (!container) return;
    const width = container.clientWidth;
    itemWidth.value = getItemWidth(width);
    handleScrollCore();
    // (immediate ? handleScrollCore : debounce(handleScrollCore, 300))();
}, 50);
watch(
    () => [props.layoutType, props.perRow],
    () => {
        layoutCacheRef.value = calculateLayout();
        handleResize(true);
    }
);
let isShiftPressed = false;
const handleKeyDown = (e) => {
    if (e.key === "Shift") {
        isShiftPressed = true;
    }
};

const handleKeyUp = (e) => {
    if (e.key === "Shift") {
        isShiftPressed = false;
    }
};
let observer;
onMounted(() => {
    skipResize = false;
    observer = new ResizeObserver((entries) => {
        handleResize(); // 只有宽度变化时才触发
    });

    if (containerRef.value) {
        observer.observe(containerRef.value);
    }
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);

    // 初始化一次
    handleResize();
});

onBeforeUnmount(() => {
    observer.disconnect();
    document.removeEventListener("keydown", handleKeyDown);
    document.removeEventListener("keyup", handleKeyUp);
    observer.disconnect();
});
const reProcessData = () => {
    groupedData = groupByGroupKey(props.items);
    handleScrollCore();
};

watch(
    () => props.items,
    () => {
        layoutCacheRef.value = calculateLayout();
        reProcessData();
    },
    { immediate: true, deep: true }
);

// --- 选中逻辑 ---
const allSelectedItems = ref([]); // 最终选中结果
const currentSelectedItems = ref([]); //本次框选的选中结果
const imagesForDrag = computed(() => {
    let arr = [...allSelectedItems.value];
    if (cover.value && arr.length > 1) {
        let newItem = {};
        newItem[props.imageKey] = cover.value;
        let length = arr.length || 0;
        arr[length] = newItem;
    }
    return arr.length > 3 ? arr?.slice(-3) : arr;
});
const getSelectedItem = (box) => {
    const selectedItems = [];
    for (const pos of allItemPositions.value) {
        const left = pos.left;
        const top = pos.top;
        const right = left + pos.width;
        const bottom = top + pos.height;

        const isIntersecting = !(box.left > right || box.left + box.width < left || box.top > bottom || box.top + box.height < top);

        if (isIntersecting) {
            selectedItems.push(pos.item);
        }
    }
    // 实时更新当前操作的选中集
    currentSelectedItems.value = selectedItems;
};
const symmetricDifference = (arr1, arr2) => {
    // 使用 Set 去重，并且确保元素是唯一的
    const set1 = new Set(arr1.map((item) => item[props.imageKey]));
    const set2 = new Set(arr2.map((item) => item[props.imageKey]));

    // 找到 arr1 中不在 arr2 中的元素
    const diff1 = arr1.filter((item) => !set2.has(item[props.imageKey]));
    // 找到 arr2 中不在 arr1 中的元素
    const diff2 = arr2.filter((item) => !set1.has(item[props.imageKey]));

    // 返回去重后的对称补集
    return [...diff1, ...diff2];
};

const handleDragEnd = ({ e }) => {
    if (!currentSelectedItems.value.length) return;
    if (isShiftPressed) {
        const result = symmetricDifference(allSelectedItems.value, currentSelectedItems.value);
        allSelectedItems.value = result;
    } else {
        allSelectedItems.value = [...new Map([...allSelectedItems.value, ...currentSelectedItems.value].map((item) => [item[props.imageKey], item])).values()];
    }
    emit("bounding-select", allSelectedItems.value);
    currentSelectedItems.value = [];
};

const handleAutoScroll = (type) => {
    let top = scrollOffsetTop.value;
    const dpi = window.devicePixelRatio;
    let step = props.layoutType === "Small" ? 8 : props.layoutType === "Medium" ? 12 : 16;
    step = dpi * step;
    type === "top" ? (top -= step) : (top += step);
    scrollTo({
        top,
    });
};
let point = null;
const recordStartPoint = (e) => {
    point = { x: e.clientX, y: e.clientY };
};
let isCheck = false; //是否按住checkbox
const shouldClear = (e) => {
    setTimeout(() => {
        if (isCheck) return (isCheck = false);
        let newPoint = { x: e.clientX, y: e.clientY };
        if (Math.abs(point?.x - newPoint.x) <= 5 || Math.abs(point?.y - newPoint.y) <= 5) {
            clearSelect();
        }
    }, 50);
};
//动态同步页面选中状态
const selectedIds = computed(() => {
    if (isShiftPressed) {
        const arr1 = allSelectedItems.value;
        const arr2 = currentSelectedItems.value;
        const result = symmetricDifference(arr1, arr2);
        return result.map((item) => item[props.imageKey]);
    } else {
        let result = [...new Set([...allSelectedItems.value, ...currentSelectedItems.value])];
        return result.map((item) => item[props.imageKey]);
    }
});
const handleItemSelect = (checked, item) => {
    isCheck = true;
    const index = allSelectedItems.value.findIndex((i) => i[props.imageKey] === item[props.imageKey]);
    if (index >= 0) {
        allSelectedItems.value.splice(index, 1);
    } else {
        allSelectedItems.value.push(item);
    }
    console.log(allSelectedItems.value, "after");
    emit("bounding-select", allSelectedItems.value);
};

const clearSelect = () => {
    allSelectedItems.value = [];
    console.log("clearSelect");
    currentSelectedItems.value = [];
    emit("bounding-select", []);
};

const getScrollTop = () => {
    return {
        top: scrollOffsetTop.value,
        height: totalHeight.value,
    };
};
const scrollTo = (scrollInfo) => {
    const { top, height } = scrollInfo;
    if (top) {
        skipResize = true;
    }
    let newTop = Math.max(Math.min(top, totalHeight.value + 300 - containerRef.value.clientHeight), 0);
    if (height) {
        totalHeight.value = height;
    }

    nextTick(() => {
        if (!containerRef.value) return;
        // 1. 先滚动到指定位置
        containerRef.value.scrollTo({
            top: newTop,
        });
        scrollOffsetTop.value = newTop;

        // 2. 然后单独刷新 "可见元素"，而不是重新算布局！
        const containerHeight = containerRef.value.clientHeight;
        const { visibleItems: newVisibleItems, visibleHeaders: newVisibleHeaders } = calculateVisibleElements(top, containerHeight);

        visibleItems.value = newVisibleItems;
        visibleHeaders.value = newVisibleHeaders;
        // 3. 恢复 skipResize 标志
        setTimeout(() => {
            skipResize = false;
        }, 200);
    });
};

defineExpose({
    clearSelect,
    reProcessData,
    getScrollTop,
    scrollTo,
});
</script>

<style scoped>
#container {
    height: 100%;
    max-height: 100vh;
    overflow-y: auto;
    position: relative;
    -webkit-overflow-scrolling: touch;
}

.date-header {
    @apply text-text-2  h-10 text-sm font-medium absolute w-full z-10 flex items-center px-4;
}

.image-item {
    position: absolute;
    will-change: transform, top, left;
    transition: transform 0.3s ease, top 0.3s ease, left 0.3s ease;
}

.image-item-img {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.images-for-drag {
    &:nth-child(3n + 1) {
        rotate: 0deg;
    }
    &:nth-child(3n + 2) {
        rotate: -15deg;
    }

    &:nth-child(3n + 3) {
        rotate: 15deg;
    }
}
</style>
