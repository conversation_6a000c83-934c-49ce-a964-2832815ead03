<template>
    <div>
        <DynamicScroller :items="messageList" :min-item-size="160" :emit-update="true" keyField="id" tabindex="1" class="h-full md:pr-3 scroll-container" @scroll="pageScroll">
            <template #default="{ item, index, active }">
                <DynamicScrollerItem :item="item" :active="active" :size-dependencies="[item.message]" :data-index="index" :data-active="active" class="pb-4">
                    <div :key="item.id" @click="handleMessage(item)" class="rounded-2xl flex flex-col gap-2 p-4 pl-6 bg-transparent hover:bg-fill-wd-1 cursor-pointer">
                        <div class="font-medium flex items-center gap-2 text-text-2">
                            <NoReadDot v-if="!item.read" />
                            <p class="font-medium max-w-full !line-clamp-1">{{ item.title }}</p>
                        </div>
                        <div class="!line-clamp-2 text-text-3">
                            {{ item.introduction }}
                        </div>
                        <div class="text-xs text-text-4">{{ item.actionTime }}</div>
                    </div>
                </DynamicScrollerItem>
            </template>
            <template #after>
                <div v-if="loading" class="flex justify-center py-10">
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </div>
                <div v-if="noMore && messageList.length > 0" class="flex justify-center py-10">
                    <div class="pt-4 px-2 border-t border-solid border-border-1 text-text-4">There's no more</div>
                </div>
            </template>
        </DynamicScroller>
        <div v-if="noMore && messageList.length === 0" class="absolute left-0 top-0 right-0 h-full flex items-center justify-center flex-col z-10">
            <img src="@/assets/images/notice_empty.webp" class="w-36 aspect-square hidden dark:block" />
            <img src="@/assets/images/notice_empty_light.webp" class="w-36 aspect-square dark:hidden" />
            <div class="mt-4 text-text-4">{{ t("MESSAGE_CENTER_EMPTY") }}</div>
        </div>
        <n-modal v-model:show="currentNotice.showModal">
            <div class="w-[880px] rounded-2xl bg-bg-2 p-6 overflow-hidden border border-solid border-border-1">
                <div class="flex items-center justify-between pb-8 font-semibold">
                    <span class="text-text-1">{{ t("MESSAGE_CENTER_UPDATE") }} </span>
                    <n-icon @click="currentNotice.showModal = false" class="cursor-pointer text-text-4 hover:text-text-2" size="20"> <IconsClose /> </n-icon>
                </div>
                <n-scrollbar style="max-height: 488px" class="pr-4">
                    <h2 class="text-base font-medium text-text-2">{{ currentNotice.title }}</h2>
                    <pre class="mt-4 text-text-3 whitespace-pre-wrap">{{ currentNotice.details }}</pre>
                </n-scrollbar>
            </div>
        </n-modal>
    </div>
</template>

<script setup>
import { formatDate, debounce, isScrolledToBottom } from "@/utils/tools";
import { getSysUpdateMessage } from "@/api";
import { useUnreadMessage } from "@/stores";

import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import NoReadDot from "@/components/message/NoReadDot.vue";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
const { t } = useI18n({ useScope: "global" });
const unreadMessages = useUnreadMessage();

const messageList = ref([]);
const loading = ref(false);
const noMore = ref(false);
//加载数据
const pageSize = 30;
let lastSysUpdateId = null;
const handleLoad = async () => {
    if (loading.value || noMore.value) return;
    loading.value = true;
    const { status, data, message } = await getSysUpdateMessage({ pageSize, lastSysUpdateId });
    loading.value = false;

    if (status !== 0) {
        openToast.error(message);
        return;
    }
    lastSysUpdateId = data.lastId;
    const newArray = data?.resultList || [];
    if (!data.lastId || newArray.length < pageSize) {
        noMore.value = true;
    }
    const list = newArray.map((item) => {
        item.actionTime = formatDate(item.publishTime);
        return item;
    });
    messageList.value.push(...list);
};
handleLoad();
const pageScroll = debounce((e) => {
    const isBottom = isScrolledToBottom(e.target);
    if (!isBottom) {
        return;
    }
    handleLoad();
}, 60);
//阅读信息
let fetchLoading = false;
const messageType = "nsysUpdateNums";
const readMessage = async ({ id, read }) => {
    if (fetchLoading || read) return;
    fetchLoading = true;
    const res = await unreadMessages.updateNoticeReadStatus(id, messageType);
    fetchLoading = false;
    if (!res) {
        return;
    }
    const index = messageList.value.findIndex((item) => item.id === id);
    messageList.value[index].read = true;
};
const currentNotice = ref({
    showModal: false,
});
const handleMessage = (item) => {
    readMessage(item);
    currentNotice.value = { ...item, showModal: true };
};
const readAll = () => {
    messageList.value.forEach((item) => {
        item.read = true;
    });
    console.log("父组件点击了全部已读", messageList.value);
};
defineExpose({
    readAll,
});
</script>

<style lang="scss" scoped></style>
