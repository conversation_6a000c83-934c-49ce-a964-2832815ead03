<template>
    <n-badge :value="unreadMessages.unReadCount" :max="99" color="#F76965">
        <slot>
            <NuxtLinkLocale class="w-11 h-11 rounded-full dark:bg-dark-bg-3 bg-neutral-200 flex items-center justify-center" to="/message-center" @click="trackEvent">
                <n-icon size="24">
                    <IconsNotice />
                </n-icon>
            </NuxtLinkLocale>
        </slot>
    </n-badge>
</template>

<script setup>
import { loopUnreadNotice } from "@/api";
import { useUnreadMessage } from "@/stores";
import { useGetRemoteModelList } from "@/hook/create";
const unreadMessages = useUnreadMessage();

const trackEvent = () => {
    window.trackEvent("Message_Center", { el: `nav_message_center` });
};

let looping = false;
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
const fetchUnreadMessages = async () => {
    if (!looping) {
        return;
    }
    try {
        const { status, data } = await loopUnreadNotice();
        if (status === 0 && !!data) {
            unreadMessages.updateUnreadList(data);
            // 同步新的模型列表
            data.currentResourcesVersion && useGetRemoteModelList(Number(data.currentResourcesVersion));
            //同步新的活动
            unreadMessages.updateNewActivityCount(data.commActivityNums);
            await sleep(1000 * 60); // 1分钟刷新一次
            fetchUnreadMessages();
        }
    } catch (error) {
        console.error(error);
        looping = false; // 关闭轮询
    }
};

onMounted(() => {
    looping = true; // 开启轮询
    fetchUnreadMessages(); // 初始加载时立即获取一次
});
onBeforeUnmount(() => {
    looping = false; // 关闭轮询
});
</script>
