<script setup>
import { useCurrentLang } from "@/stores/system-config.js";
import { DEFAULT_LANG_LIST as langList } from "@/utils/constant.js";
import { storeToRefs } from "pinia";

const { setLocale } = useI18n({ inheritLocale: true, useScope: "global" });
const currentLang = useCurrentLang();
const { lang } = storeToRefs(currentLang);

const showLanguageModal = ref(false);
const handleOpenLanguageModal = (status) => {
    showLanguageModal.value = status;
    if (status) currentLocal.value = lang.value;
};

const currentLocal = ref("en");
const handleOkLanguage = () => {
    window.trackEvent("Personal_Popup", { el: `personal_popup_language=${currentLocal.value}` });
    setLocale(currentLocal.value);
    currentLang.setLang(currentLocal.value);
    handleOpenLanguageModal(false);
};

onMounted(() => {
    currentLocal.value = lang.value;
    setLocale(lang.value);
});

defineExpose({ handleOpenLanguageModal });
</script>

<template>
    <n-modal v-model:show="showLanguageModal" :maskClosable="false">
        <div class="w-[400px] bg-bg-2 rounded-2xl">
            <div class="p-6 flex justify-between items-center">
                <div class="text-base text-text-1 font-semibold">{{ $t("LANGUAGE") }}</div>
                <IconsClose class="text-2xl text-text-1 cursor-pointer select-none" @click="handleOpenLanguageModal(false)" />
            </div>
            <n-scrollbar style="max-height: 65vh" class="px-2 py-4 space-y-1">
                <div
                    v-for="item in langList"
                    :key="item.key"
                    class="min-h-10 flex justify-between items-center py-2 px-3 hover:bg-fill-opt-2 rounded transition-all cursor-pointer select-none"
                    @click="currentLocal = item.key"
                >
                    <span class="text-sm text-text-2 font-medium">{{ item.label }}</span>
                    <IconsSuccess v-show="item.key === currentLocal" class="text-2xl text-text-2" />
                </div>
            </n-scrollbar>
            <div class="py-4 px-6 flex justify-end items-center gap-3">
                <button class="h-10 px-4 rounded-full text-sm text-text-2 font-medium bg-fill-btn-1 min-w-[108px] hover:bg-fill-btn-2 transition-all" @click="handleOpenLanguageModal(false)">
                    {{ $t("COMMON_BTN_CANCEL") }}
                </button>
                <button class="h-10 px-4 rounded-full text-sm text-text-white font-medium bg-primary-6 min-w-[108px] hover:bg-primary-5 transition-all" @click="handleOkLanguage">
                    {{ $t("COMMON_BTN_OK") }}
                </button>
            </div>
        </div>
    </n-modal>
</template>

<style lang="scss" scoped></style>
