<template>
    <div>
        <div class="h-8 flex items-center justify-between">
            <span class="ml-2 text-base font-semibold">{{ t("PROFILE_ACCOUNT_BIO") }}</span>
            <div class="rounded-full w-8 h-8 flex items-center justify-center text-text-4 hover:bg-fill-wd-1 hover:text-text-2 cursor-pointer" @click="emits('cancel')">
                <n-icon size="20">
                    <IconsClose />
                </n-icon>
            </div>
        </div>
        <div class="mt-4 bg-fill-ipt-1 rounded-lg border border-solid border-border-ipt-4 mx-2">
            <n-input type="textarea" placeholder="Your bio..." class="h-auto" :bordered="false" v-model:value="userBio" :maxlength="250" round :rows="5" :autosize="{ minRows: 5, maxRows: 6 }" />
        </div>
        <div class="mt-2 text-text-4">{{ t("PROFILE_ACCOUNT_CHARTS_LIMIT") }}</div>

        <div class="mt-10 flex justify-end gap-3 mx-2 pb-2">
            <Button :bordered="false" size="medium" type="secondary" class="min-w-[120px]" @click="emits('cancel')">
                {{ t("COMMON_BTN_CANCEL") }}
            </Button>
            <Button :bordered="false" size="medium" :disabled="hasDisable" class="min-w-[120px]" type="primary" @click="handleSub">
                {{ t("COMMON_BTN_SAVE") }}
            </Button>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
const props = defineProps(["info"]);
const userBio = ref(props.info || "");
const emits = defineEmits(["cancel", "confirm"]);
const hasDisable = computed(() => {
    return userBio.value.trim().length === 0 || userBio.value.trim().length > 250;
});
const handleSub = () => {
    userBio.value = userBio.value.trim();
    emits("confirm", userBio.value);
};
</script>
