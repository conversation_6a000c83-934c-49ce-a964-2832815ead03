import http from "./http";

/**
 * 获取活动列表
 * @param {*} data
 * @returns
 */
export const getActivityListApi = (data) =>
  http({
    url: "/comm-activity/activity-list",
    method: "post",
    data,
    isForm: true,
  });
/**
 * 获取投稿中活动列表
 * @param {*} data
 * @returns
 */
export const getPostActivityListApi = (data) =>
  http({
    url: "/comm-activity/post-list",
    method: "post",
    data,
    isForm: true,
  });
/**
 * 获取活动列表
 * @param {*} data
 * @returns
 */
export const getActivityDetailApi = (data) => {
  return http({
    url: "/comm-activity/activity-detail",
    method: "post",
    data,
    isForm: true,
  });
};
// 获取活动所有作品
export const getActivityImagesApi = (params) =>
  http({
    url: "/comm-activity/page-search",
    method: "get",
    params,
  });
// 获取活动获奖作品
export const getActivityWinImagesApi = (params) =>
  http({
    url: "/comm-activity/win-search",
    method: "get",
    params,
  });
// 获取活动用户作品
export const getActivityUserImagesApi = (params) =>
  http({
    url: "/comm-activity/user-activity-posts-list",
    method: "get",
    params,
  });
// 获取活动奖章  所有活动获奖作品匹配展示
export const getActivityPrizeListApi = () =>
  http({
    url: "/comm-activity/prize-list",
    method: "post",
  });
// 删除活动已投稿作品
export const deleteActivityImgApi = (data) =>
  http({
    url: "/comm-activity/delete-activity-img",
    method: "post",
    data,
    isForm: true,
  });
// 提交 活动投稿作品
export const submitActivityImgApi = (data) =>
  http({
    url: "/comm-activity/public-activity-img",
    method: "post",
    data,
  });
// 提交 未发布到社区的作品
export const getActivityNotPublishApi = (params) =>
  http({
    url: "/comm-activity/not-publish-img-list",
    method: "get",
    params,
  });
