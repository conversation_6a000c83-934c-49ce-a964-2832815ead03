<template>
    <div v-if="show" class="fixed z-[9999] left-0 top-0 right-0 bottom-0 bg-black/50 flex items-center justify-center">
        <div class="modal-content text-base font-bold -translate-y-10 border border-solid border-white/20" :style="style">
            <div class="flex gap-2.5 items-center">
                <div class="dark:text-dark-active-text text-black">
                    <slot name="icon">
                        <div class="w-8 h-8 rounded-full bg-red-600 flex items-center justify-center shrink-0">
                            <n-icon size="24" class="text-dark-active-text">
                                <IconsClose />
                            </n-icon>
                        </div>
                    </slot>
                </div>

                <div class="text-black dark:text-dark-text">
                    <slot name="title">
                        <p class="leading-6">{{ t("TOAST_TITLE_WARNING") }}</p>
                    </slot>
                </div>
            </div>
            <div class="leading-6 text-sm font-medium mt-3 text-black dark:text-dark-text">
                <slot></slot>
            </div>

            <div class="mt-7 flex justify-end gap-2.5">
                <n-button
                    v-if="showCancel"
                    class="px-5 h-10 rounded gap-1 !duration-150 text-dark-active-text dark:!bg-white/5 !bg-black/20 hover:!bg-black/35 hover:dark:!bg-white/30 hover:!text-dark-active-text text-sm"
                    :bordered="false"
                    @click="handleCancel"
                >
                    <slot name="cancel-btn">{{ t("COMMON_BTN_CANCEL") }}</slot>
                </n-button>

                <n-button
                    v-if="showConfirm"
                    class="px-5 h-10 rounded gap-1 !duration-150 text-dark-active-text primary-btn hover:!text-dark-active-text text-sm"
                    :bordered="false"
                    :loading="reqStatus"
                    @click="handleOk"
                >
                    <slot name="confirm-btn">{{ t("COMMON_BTN_CONTINUE") }}</slot>
                </n-button>
            </div>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
const props = defineProps({
    show: {
        type: Boolean,
        required: true,
    },
    showCancel: {
        type: Boolean,
        default: true,
    },
    showConfirm: {
        type: Boolean,
        default: true,
    },
    style: {
        type: Object,
        default: () => ({}),
    },
});

watch(
    () => props.show,
    (val) => {
        if (val) {
            reqStatus.value = false;
        }
    }
);
const emits = defineEmits(["ok", "update:show"]);
const reqStatus = ref(false);

const handleOk = () => {
    reqStatus.value = true;
    emits("ok");
};
const handleCancel = () => {
    emits("update:show", false);
};
</script>

<style lang="scss" scoped>
.modal-content {
    @apply rounded-xl overflow-hidden p-6 dark:bg-dark-bg bg-white;
    width: 474px;
    box-shadow: 0px 20px 50px 0px rgba(0, 0, 0, 0.1);
}
.primary-btn {
    background: #8961ff !important;
    &:hover {
        background: #7b57e5 !important;
    }
    &:active {
        background: #6a4bc6 !important;
    }
}
</style>
