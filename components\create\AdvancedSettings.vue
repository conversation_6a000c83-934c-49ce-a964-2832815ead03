<template>
    <!-- Pc 侧边 tool-tip -->
    <n-tooltip
        v-if="!isIpad"
        ref="modelTooltipRef"
        v-model:show="isOpen"
        :show-arrow="false"
        class="rounded-lg shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)]"
        placement="right"
        raw
        trigger="click"
    >
        <template #trigger>
            <div
                class="flex w-full items-center gap-2 p-2 bg-fill-wd-1 rounded-lg text-text-2 font-medium hover:text-text-1 cursor-pointer hover:!bg-fill-wd-2 transition-all duration-[250]"
                :class="{
                    '!text-text-1 !bg-fill-wd-2': isOpen,
                }"
            >
                <span class="p-2">
                    <IconsControl class="size-4" />
                </span>
                <span>{{ $t("CONFIG_ADVANCE_TITLE") }}</span>
            </div>
        </template>

        <div class="tooltip-container gap-4 w-[360px] !rounded-2xl">
            <p class="text-text-4 text-sm mb-4">{{ $t("CONFIG_ADVANCE_TITLE") }}</p>
            <div class="flex flex-col gap-6 advanced-max-height-inner">
                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <!-- cfg -->
                            <span class="text-text-2 text-sm">Guidance Scale</span>
                            <n-tooltip placement="right" trigger="click" :show-arrow="false" raw ref="toolTipsRef_1">
                                <template #trigger>
                                    <n-icon size="20" class="advance-tips-icon">
                                        <IconsHelp />
                                    </n-icon>
                                </template>
                                <div class="tips-box">
                                    <span>{{ t("CONFIG_ADVANCE_CFG_TIPS") }}</span>
                                </div>
                            </n-tooltip>
                        </div>
                        <Button type="textSecondary" size="small" class="min-w-16" :disabled="disabledFn('cfg')" @click="resetConfByField('cfg')">{{ t("CONFIG_BASE_RESET_BTN") }}</Button>
                    </div>
                    <div class="mt-2">
                        <CommonSlider
                            :modelValue="createStore.cfg && createStore.cfg >= 0 ? createStore.cfg : 0"
                            :step="0.1"
                            :max="30"
                            :min="disabledFn('cfg') ? 0 : 1"
                            :disabled="disabledFn('cfg')"
                            @input="handleSetCfg"
                        />
                    </div>
                </div>
                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <!-- steps -->
                            <span class="text-text-2 text-sm">Steps</span>
                            <n-tooltip placement="right" trigger="click" :show-arrow="false" raw ref="toolTipsRef_2">
                                <template #trigger>
                                    <n-icon size="20" class="advance-tips-icon">
                                        <IconsHelp />
                                    </n-icon>
                                </template>
                                <div class="tips-box">
                                    <span>{{ t("CONFIG_ADVANCE_STEPS_TIPS") }}</span>
                                </div>
                            </n-tooltip>
                        </div>
                        <Button type="textSecondary" size="small" class="min-w-16" :disabled="disabledFn('steps')" @click="resetConfByField('steps')">{{ t("CONFIG_BASE_RESET_BTN") }}</Button>
                    </div>
                    <div class="mt-2">
                        <CommonSlider
                            :modelValue="createStore.steps && createStore.steps >= 0 ? createStore.steps : 0"
                            :step="1"
                            :max="maxSteps"
                            :min="disabledFn('steps') ? 0 : 1"
                            :disabled="disabledFn('steps')"
                            @input="handleSetSteps"
                        />
                    </div>
                </div>
                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <!-- seed -->
                            <span class="text-text-2 text-sm">Seed</span>
                            <n-tooltip placement="right" trigger="click" :show-arrow="false" raw>
                                <template #trigger>
                                    <n-icon size="20" class="advance-tips-icon">
                                        <IconsHelp />
                                    </n-icon>
                                </template>
                                <div class="tips-box">
                                    <div>{{ t("CONFIG_ADVANCE_SEED_TIPS") }}</div>
                                </div>
                            </n-tooltip>
                        </div>
                        <n-switch v-model:value="switchOn" size="large" :style="switchPrimaryStyle"></n-switch>
                    </div>
                    <div class="mt-2 flex items-center gap-3">
                        <!-- seed输入框 仅开启状态展示 -->
                        <div v-show="switchOn" class="seed-input w-full overflow-hidden rounded-lg bg-fill-ipt-1 border border-border-ipt-1 border-solid" :class="{ '!border-red-500': isInvalid }">
                            <n-input
                                class="h-10 flex-1 bg-transparent outline-none border-none px-3 text-sm w-full min-w-0"
                                :value="createStore.seed && createStore.seed == -1 ? '' : createStore.seed"
                                :placeholder="t('CONFIG_ADVANCE_SEED_LANDSCAPE')"
                                maxlength="11"
                                :min="1"
                                :max="MAX_SEED"
                                :on-update:value="handleSeedChange"
                                :show-button="false"
                            />
                        </div>
                    </div>
                </div>

                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <!-- 负向提示词 与提示词保持一致 最大长度2500 -->
                            <span class="text-text-2 text-sm">{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>
                            <n-tooltip placement="right" trigger="click" :show-arrow="false" raw>
                                <template #trigger>
                                    <n-icon size="20" class="advance-tips-icon">
                                        <IconsHelp />
                                    </n-icon>
                                </template>
                                <div class="tips-box">
                                    <div>{{ t("CONFIG_BASE_NEGATIVE_PROMPT_TIPS") }}</div>
                                </div>
                            </n-tooltip>
                        </div>
                        <Button type="textSecondary" size="small" class="min-w-16" :disabled="disabledFn('negative_prompt')" @click="resetConfByField('negative_prompt')">{{
                            t("CONFIG_BASE_RESET_BTN")
                        }}</Button>
                    </div>

                    <div class="mt-2 rounded-lg bg-fill-ipt-1 border border-solid border-border-ipt-1 relative">
                        <n-input
                            type="textarea"
                            class="bg-transparent"
                            ref="negativePromptRef"
                            :bordered="false"
                            :disabled="disabledFn('negative_prompt')"
                            :placeholder="t('ENTER_ENTER_CONFIG_BASE_NEGATIVE_PROMPT')"
                            :value="createStore.negative_prompt"
                            :on-update:value="handleSetNegativePrompt"
                            :style="{
                                '--n-text-color': 'var(--p-text-2)',
                            }"
                            :maxlength="2500"
                            round
                            :autosize="{
                                minRows: 5,
                                maxRows: 5,
                            }"
                        />
                    </div>
                </div>

                <div
                    class="w-full flex items-center justify-center text-primary-6 h-11 hover:bg-fill-btn-6 rounded-lg font-medium cursor-pointer transition-all duration-[250]"
                    @click="saveUserDefaultSettings"
                >
                    {{ t("SHORT_SAVE_CONF") }}
                </div>
            </div>
        </div>
    </n-tooltip>
    <div
        class="w-full items-center gap-2 p-2 bg-fill-wd-1 rounded-lg text-text-2 font-medium hover:text-text-1 cursor-pointer hover:!bg-fill-wd-2 flex lg:hidden transition-all duration-[250]"
        :class="{
            '!text-text-1 !bg-fill-wd-2': isOpen,
        }"
        @click="isOpen = true"
    >
        <span class="p-2">
            <IconsControl class="size-4" />
        </span>
        <span>{{ $t("CONFIG_ADVANCE_TITLE") }}</span>
    </div>
    <!-- h5 底部弹窗 -->
    <CommonH5Popup v-model:show="isOpen">
        <div class="gap-4 w-full h-[76dvh]">
            <div class="w-full flex justify-between items-center mt-5 mb-2">
                <span class="rounded-full size-8 bg-fill-wd-1 hover:bg-fill-wd-2 flex items-center justify-center cursor-pointer" @click="isOpen = false">
                    <IconsArrowLeft />
                </span>
                <p class="text-text-1 font-medium text-base">{{ $t("CONFIG_ADVANCE_TITLE") }}</p>
                <span class="text-primary-6 font-medium cursor-pointer text-base" @click="isOpen = false">{{ $t("DONE") }}</span>
            </div>
            <div class="flex flex-col gap-6 advanced-max-height-inner">
                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <span class="text-text-2 text-sm">Guidance Scale</span>
                        </div>
                        <Button type="textSecondary" size="small" class="min-w-16" :disabled="disabledFn('cfg')" @click="resetConfByField('cfg')">{{ t("CONFIG_BASE_RESET_BTN") }}</Button>
                    </div>
                    <div class="mt-2">
                        <CommonSlider
                            :modelValue="createStore.cfg && createStore.cfg >= 0 ? createStore.cfg : 0"
                            :step="0.1"
                            :max="30"
                            :min="disabledFn('cfg') ? 0 : 1"
                            :disabled="disabledFn('cfg')"
                            @input="handleSetCfg"
                        />
                    </div>
                </div>
                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <span class="text-text-2 text-sm">Steps</span>
                        </div>
                        <Button type="textSecondary" size="small" class="min-w-16" :disabled="disabledFn('steps')" @click="resetConfByField('steps')">{{ t("CONFIG_BASE_RESET_BTN") }}</Button>
                    </div>
                    <div class="mt-2">
                        <CommonSlider
                            :modelValue="createStore.steps && createStore.steps >= 0 ? createStore.steps : 0"
                            :step="1"
                            :max="maxSteps"
                            :min="disabledFn('steps') ? 0 : 1"
                            :disabled="disabledFn('steps')"
                            @input="handleSetSteps"
                        />
                    </div>
                </div>
                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <span class="text-text-2 text-sm">Seed</span>
                        </div>
                        <n-switch v-model:value="switchOn" size="large" :style="switchPrimaryStyle"></n-switch>
                    </div>
                    <div class="mt-2 flex items-center gap-3">
                        <div v-show="switchOn" class="w-full overflow-hidden rounded-lg bg-fill-ipt-1 border border-border-ipt-1 border-solid" :class="{ '!border-red-500': isInvalid }">
                            <n-input
                                class="h-10 flex-1 bg-transparent outline-none border-none px-3 text-sm w-full min-w-0"
                                :value="createStore.seed && createStore.seed == -1 ? '' : createStore.seed"
                                :placeholder="t('CONFIG_ADVANCE_SEED_LANDSCAPE')"
                                maxlength="11"
                                :min="1"
                                :max="MAX_SEED"
                                :on-update:value="handleSeedChange"
                                :show-button="false"
                            />
                        </div>
                    </div>
                </div>

                <div class="advance-option">
                    <div class="advance-item">
                        <div class="advance-title">
                            <span class="text-text-2 text-sm">{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>
                        </div>
                        <Button type="textSecondary" size="small" class="min-w-16" :disabled="disabledFn('negative_prompt')" @click="resetConfByField('negative_prompt')">{{
                            t("CONFIG_BASE_RESET_BTN")
                        }}</Button>
                    </div>

                    <div class="mt-2 rounded-lg bg-fill-ipt-1 border border-solid border-border-ipt-1 relative">
                        <n-input
                            type="textarea"
                            class="bg-transparent"
                            ref="negativePromptRef"
                            :bordered="false"
                            :disabled="disabledFn('negative_prompt')"
                            :placeholder="t('ENTER_ENTER_CONFIG_BASE_NEGATIVE_PROMPT')"
                            :value="createStore.negative_prompt"
                            :on-update:value="handleSetNegativePrompt"
                            :style="{
                                '--n-text-color': 'var(--p-text-2)',
                            }"
                            :maxlength="2500"
                            round
                            :autosize="{
                                minRows: 5,
                                maxRows: 5,
                            }"
                        />
                    </div>
                </div>

                <div class="w-full flex items-center justify-center text-primary-6 h-11 bg-fill-btn-6 rounded-lg font-medium cursor-pointer" @click="saveUserDefaultSettings">
                    {{ t("SHORT_SAVE_CONF") }}
                </div>
            </div>
        </div>
    </CommonH5Popup>
</template>

<script setup>
import { isFluxSeries, isFluxDevModel, isMjModel, isFluxKreaModel } from "@/utils/tools";
import { MAX_SEED } from "@/utils/constant";
import { switchPrimaryStyle } from "@/utils/constant-style";
import { useCreateStore } from "@/stores/create";
import { useUserProfile } from "@/stores";
import { useGetModelInfo } from "@/hook/create";
import { Alert } from "@/icons/index.js";
import { NIcon } from "naive-ui";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isIpad } = storeToRefs(useThemeStore());

const userProfile = useUserProfile();
const { showMessage } = useModal();
const isOpen = ref(false);

watch(
    () => isOpen.value,
    (newVal) => {
        if (newVal) {
            window.trackEvent("Create", { el: "advance_setting" });
        }
        console.log("createStore.seed", createStore.seed);
        if (createStore.seed && createStore.seed != -1) {
            console.log("createStore.seed", createStore.seed);
            switchOn.value = true;
        } else {
            switchOn.value = false;
        }
    }
);
const createStore = useCreateStore();
const maxSteps = ref(60);
const disabledFn = (field = "") => {
    if (!field) return console.error("请传入字段名");
    const modelInfo = { model_id: createStore.model_id };
    switch (field) {
        case "cfg":
        case "steps":
            return (isFluxSeries(modelInfo) && !isFluxDevModel(modelInfo)) || isMjModel(modelInfo);
        case "negative_prompt":
            return isFluxSeries(modelInfo) || isFluxKreaModel(modelInfo);
        default:
            return;
    }
};
// seed 输入框检查
const handleSeedChange = (value) => {
    // 移除所有非数字字符
    value = value.replace(/[^0-9]/g, "");
    // 限制范围为 1 到 MAX_SEED
    if (value !== "" && (Number(value) < 1 || Number(value) > MAX_SEED)) {
        value = value.slice(0, -1); // 删除最后一个字符
    }
    createStore.setSeed(value);
};

const isInvalid = computed(() => {
    return !/^[1-9]\d{0,10}$/.test(createStore.seed);
});

const handleSetCfg = (value) => {
    createStore.setCfg(value);
};
const handleSetSteps = (value) => {
    createStore.setSteps(value);
};
const handleSetNegativePrompt = (value) => {
    createStore.setNegativePrompt(value);
};

//重置参数
const resetConfByField = (field) => {
    window.trackEvent("APP_ADVANCED_RESET", { el: `advanced_reset_btn=${field}` });
    const modelInfo = useGetModelInfo(createStore.model_id);
    const { defaultParams } = modelInfo;
    createStore.setGenerateConfig({
        [field]: defaultParams[field],
    });
};

const switchOn = ref(false); //seed的启用状态

watch(
    () => switchOn.value,
    (newVal) => {
        window.trackEvent("APP_ADVANCED_SEED", { el: `advanced_seed=${newVal ? "random" : "fixed"}` });
        if (!newVal) {
            createStore.setSeed(-1);
        }
    }
);
const saveUserDefaultSettings = async () => {
    const { model_id, steps, negative_prompt, cfg, sampler_name, scheduler, resolution, batch_size } = createStore.generateConfig;
    const model = useGetModelInfo(model_id);
    const res = await userProfile.syncUpdateUserConfig({
        model_id,
        steps,
        negative_prompt,
        cfg,
        size: `${resolution.width} x ${resolution.height}`,
        batch_size,
        sampler_name,
        scheduler,
    });
    if (!res) {
        return;
    }
    isOpen.value = false;
    showMessage({
        showCancel: false,
        style: { width: "420px" },
        confirmBtn: t("COMMON_BTN_OK"),
        content: h("div", null, [
            h("p", { class: "tracking-wider" }, t("SET_CONFIG_SUCCESS")),
            h("p", { class: "tracking-wider mt-3 flex gap-3" }, [h("span", { class: "font-bold" }, `Model:`), h("span", null, `${model.label}`)]),
            h("p", { class: "tracking-wider mt-3 flex gap-3" }, [h("span", { class: "font-bold" }, `Aspect ratio:`), h("span", null, `${resolution.label}`)]),
            h("p", { class: "tracking-wider mt-3 flex gap-3" }, [h("span", { class: "font-bold" }, `Image batch:`), h("span", null, `${batch_size}`)]),
            h("p", { class: "tracking-wider mt-3 flex gap-3" }, [h("span", { class: "font-bold" }, `Guidance scale:`), h("span", null, `${cfg || 0}`)]),
            h("p", { class: "tracking-wider mt-3 flex gap-3" }, [h("span", { class: "font-bold" }, `Steps:`), h("span", null, `${steps || 0}`)]),
        ]),
        icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_NOTICE"),
    });
};
</script>
<style lang="scss" scoped>
.advance-title {
    @apply flex gap-2 items-center text-text-2;
}
.advance-item {
    @apply flex items-center justify-between;
}
.advance-option {
    .advance-tips-icon {
        @apply hidden cursor-pointer text-text-4;
    }
    &:hover {
        .advance-tips-icon {
            @apply block;
            &:hover {
                @apply text-text-2;
            }
        }
    }
}
.seed-input {
    ::v-deep(.n-input-wrapper) {
        padding: 0;
    }
}
</style>
