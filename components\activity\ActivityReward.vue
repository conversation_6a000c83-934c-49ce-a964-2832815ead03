<template>
    <ActivityLabel v-for="(tag, index) in tags" :key="index" :icon="iconMap[tag.type]" :text="tag.title" class="flex-1 max-w-max" />
</template>

<script setup>
import ActivityLabel from "@/components/activity/ActivityLabel.vue";
import IconLumenFill from "@/components/icons/LumenFill.vue";
import IconGifts from "@/components/icons/Gifts.vue";
import IconSubscribe2 from "@/components/icons/Subscribe2.vue";
import IconMoney from "@/components/icons/Money.vue";
const iconMap = {
    lumens: IconLumenFill,
    gifts: IconGifts,
    subscribe: IconSubscribe2,
    money: IconMoney,
};

const props = defineProps({
    tags: {
        type: Object,
        default: () => [],
    },
});
</script>
