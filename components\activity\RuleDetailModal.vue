<template>
    <n-modal v-model:show="visible" draggable>
        <div class="w-1/3 md:min-w-[600px] h md:max-h-[600px] max-h-[600px] flex flex-col rounded-md bg-bg-2 pb-6">
            <div class="relative px-6 py-6 flex justify-between items-center">
                <h2 class="text-xl font-bold text-text-1">{{ t("ACTIVITY.AWARDS_AND_NOMINATIONS") }}</h2>
                <n-icon size="24" class="cursor-pointer hover:text-primary-5" @click="visible = false">
                    <IconsClose />
                </n-icon>
            </div>
            <pre class="detail-content px-6 pt-4 text-sm text-text-3 overflow-y-auto space-y-3 whitespace-pre-wrap" v-html="ruleDetail"></pre>
        </div>
    </n-modal>
</template>
<script setup>
defineProps({
    ruleDetail: {
        type: String,
        default: "",
    },
});
const { t } = useI18n({ useScope: "global" });
const visible = defineModel("visible");
</script>

<style lang="scss" scoped>
.detail-content * {
    color: var(--p-text-3) !important;
    text-wrap: wrap;
}
</style>
