<script setup>
const props = defineProps({
    percent: { type: Number, default: 100 }
});

/**
 * 将百分比转换为角度
 * @param {number} percentage - 百分比值（0到100之间）
 * @returns {number} - 对应的角度值（0到360之间）
 */
function percentageToAngle(percentage) {
    // 确保百分比在0到100之间
    const clampedPercentage = Math.max(0, Math.min(100, percentage));
    // 将百分比映射到0到360度
    return (clampedPercentage / 100) * 360;
}

const style = computed(() => {
    const angle = percentageToAngle(props.percent);
    return {
        background: `conic-gradient(var(--p-primary-3) ${angle}deg, var(--p-primary-6) ${angle}deg)`
    };
});
</script>

<template>
    <div :style="style" class="rounded-full"></div>
</template>