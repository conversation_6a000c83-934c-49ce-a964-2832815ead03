import http from "./http";

//联系我们
export const contactUs = (data) =>
    http({
        url: "/contacts/create",
        method: "post",
        data,
    });
//获取服务状态
export const getSysStatus = (data) =>
    http({
        url: "/common/hello",
        method: "get",
    });

//用户登出
export const userLogoutSys = (data) =>
    http({
        url: "/user/logout",
        method: "post",
    });
//获取用户信息
export const getUserByToken = (data) =>
    http({
        url: "/user/info",
        method: "get",
    });
//更新用户信息
export const updateUserProfile = (data) =>
    http({
        url: "/user/update-user-info",
        method: "post",
        data,
    });
//删除用户账户（v2）
export const delUserAccount = (params) =>
    http({
        url: "/user/account-deletion-v2",
        method: "post",
        params,
    });
//上传图片
export const uploadAvatar = (data) =>
    http({
        url: "/user/upload-user-avatar",
        method: "post",
        isForm: true,
        data,
    });
//检查昵称是否可用
export const checkNickname = (data) =>
    http({
        url: "/user/check-user-name",
        method: "post",
        data,
        isForm: true,
    });
//检查用户名是否可用
export const checkLoginName = (data) =>
    http({
        url: "/user/check-login-name",
        method: "post",
        data,
        isForm: true,
    });

// 查询用户lumen记录
export const requestLumenChangeRecord = (params) =>
    http({
        url: "/user/lumen-change-record",
        method: "get",
        params: params,
    });

// 分页查询当前用户支付记录展示
export const requestUserPayRecord = (params) =>
    http({
        url: "/user-pay-record/page-search-display",
        method: "get",
        params,
    });

//点赞
export const addLike = (data) =>
    http({
        url: "/img/add-like",
        method: "post",
        data,
        isForm: true,
    });
//点赞
export const cancelLike = (data) =>
    http({
        url: "/img/reduce-like",
        method: "post",
        data,
        isForm: true,
    });

//新用户註冊
export const createAccount = (data) =>
    http({
        url: "/user/register",
        method: "post",
        data,
    });
//第三方平台登录
export const loginByThird = (data) =>
    http({
        url: "/user/login-oauth",
        method: "post",
        data,
    });
//apple sign in
export const signInByAppleAuth = (data) =>
    http({
        url: "/user/apple/login",
        method: "post",
        data,
    });
//发送註冊验证码
export const sendCodeByRegister = (data) =>
    http({
        url: "/user/register-send-code",
        method: "post",
        isForm: true,
        data,
    });
//重置密碼
export const resetPassword = (data) =>
    http({
        url: "/user/reset-password",
        method: "post",
        isForm: true,
        data,
    });
//重置重置密碼验证码
export const sendCodeByReset = (data) =>
    http({
        url: "/user/reset-password-send-code",
        method: "post",
        isForm: true,
        data,
    });
//登录
export const loginSystem = (data) =>
    http({
        url: "/user/login",
        method: "post",
        isForm: true,
        data,
    });
//获取当前用户未完成任务
export const getTaskQueueByUser = () =>
    http({
        url: "/gen/task-queue",
        method: "post",
    });
//获取当前支持的模型
export const pullModelList = (data) =>
    http({
        url: "/gen/model/list",
        method: "get",
    });
//提交生图任务
export const imageGenerator = (data) =>
    http({
        url: "/gen/create",
        method: "post",
        data,
    });

//去背景（单个）
export const removeBackground = (data) =>
    http({
        url: "/gen/remove-background",
        method: "post",
        data,
    });
//去背景（创建任务）
export const batchRemoveBackground = (data) =>
    http({
        url: "/rmbg/saveRmbg",
        method: "post",
        data,
    });
//去背景（查询任务状态）
export const batchQueryBackground = (data) =>
    http({
        url: "/rmbg/rmbg-status",
        method: "post",
        data,
    });

//高清修复
export const hiresFix = (data) =>
    http({
        url: "/gen/hires-fix",
        method: "post",
        data,
    });
//查询个人历史记录
export const getHistoryTask = (data) =>
    http({
        url: "/img/gen-history/history-list",
        method: "post",
        isForm: true,
        data,
    });

//删除生成结果
export const deleteResult = (data) =>
    http({
        url: "/img/delete",
        method: "post",
        isForm: true,
        data,
    });
//查询 Explore 瀑布流
export const getShowCaseList = (data) =>
    http({
        url: "/img/explore-list",
        method: "post",
        isForm: true,
        data,
    });
//获取 Explore random
export const getExploreRandom = (data) =>
    http({
        url: "/img/random-list",
        method: "post",
        isForm: true,
        data,
    });
//获取任务详情(白名单/免登录可调用)
export const getTaskDetail = (data) =>
    http({
        url: "/img/img-detail",
        method: "post",
        isForm: true,
        data,
    });

//上传文件到相册
export const uploadAlbum = (data) =>
    http({
        url: "/album/upload-album-img",
        method: "post",
        isForm: true,
        data,
    });
//相册分页查询
export const queryAlbumByPage = (params) =>
    http({
        url: "/album/album-img-list",
        method: "get",
        params,
    });
//相册-删除上传的照片
export const delAlbumItemById = (data) =>
    http({
        url: "/album/delete-album-img",
        method: "post",
        isForm: true,
        data,
    });

//------------v1.2------------------
//局部重绘
export const genRepaint = (data) =>
    http({
        url: "/gen/local-redraw",
        method: "post",
        isForm: true,
        data,
    });
//局部重绘-新接口
export const genInpaint = (data) =>
    http({
        url: "/gen/inpaint",
        method: "post",
        data,
    });
//扩图
export const genExpand = (data) =>
    http({
        url: "/gen/enlarge-image",
        method: "post",
        data,
    });
// 线稿上色
export const lineArtColorize = (data) =>
    http({
        url: "/gen/lineRecolor",
        method: "post",
        data,
    });
// vary 图片微变
export const genVary = (data) =>
    http({
        url: "/gen/vary-image",
        method: "post",
        data,
    });
//查询用户生图统计数据
export const queryGenStatistics = (data) =>
    http({
        url: "/img/statistics-img-nums",
        method: "post",
        data,
    });
//用户主动取消任务
export const delTask = (data) =>
    http({
        url: "/task/cancelTask",
        method: "post",
        isForm: true,
        data,
    });
//轮询任务结果
export const pullTaskRes = (data) =>
    http({
        url: "/task/processTask",
        method: "post",
        isForm: true,
        data,
    });
// 同步服务器 状态
export const syncServerStatus = () =>
    http({
        url: "/common/hello",
        method: "get",
    });
// 查询更新维护时间
export const getMaintenanceTime = () =>
    http({
        url: "/common/suspension-time",
        method: "get",
    });

//举报用户生成的图片
export const reportUserGenContent = (data) =>
    http({
        url: "/img/add-report",
        method: "post",
        data,
    });
//直传COS 获取临时秘钥
export const getCosTmpAuth = (data) =>
    http({
        url: "/bucket/tmp-auth",
        method: "post",
        data,
    });
//直传COS 获取临时秘钥(小工具 去背景功能专用)
export const getCosTmpAuthForRembg = (data) =>
    http({
        url: "/rmbg/batch-rmbg-tmp-auth",
        method: "post",
        data,
    });
// 保存用户上传内容
export const saveCustomUpload = (data) =>
    http({
        url: "/custom-file/save",
        method: "post",
        data,
    });
//新增收藏夹
export const appendCollect = (data) =>
    http({
        url: "/collect/add-classify",
        method: "post",
        isForm: true,
        data,
    });
//设置收藏夹封面
export const updateCollectCover = (data) =>
    http({
        url: "/collect/set-cover",
        method: "post",
        isForm: true,
        data,
    });
//根据ID删除收藏夹
export const delCollectById = (data) =>
    http({
        url: "/collect/delete-classify",
        method: "post",
        isForm: true,
        data,
    });
//修改收藏夹
export const updateCollect = (data) =>
    http({
        url: "/collect/rename-classify",
        method: "post",
        isForm: true,
        data,
    });
//用户收藏夹列表
export const getCollect = () =>
    http({
        url: "/collect/classify-list",
        method: "get",
    });
//收藏图片
export const collectionImg = (data) =>
    http({
        url: "/collect/add",
        method: "post",
        isForm: true,
        data,
    });
//取消收藏
export const reduceCollectionImg = (data) =>
    http({
        url: "/collect/reduce",
        method: "post",
        isForm: true,
        data,
    });
//批量 取消收藏
export const batchReduceCollection = (data) =>
    http({
        url: "/collect/reduce-batch",
        method: "post",
        data,
    });
//批量 取消收藏 并删除图片
export const batchReduceDel = (data) =>
    http({
        url: "/collect/reduce-delete-batch",
        method: "post",
        data,
    });
//移动收藏夹
export const moveToCollections = (data) =>
    http({
        url: "/collect/move",
        method: "post",
        isForm: true,
        data,
    });
//批量移动收藏夹
export const batchMoveToCollections = (data) =>
    http({
        url: "/collect/move-batch",
        method: "post",
        data,
    });
//查询指定收藏夹下的图片
export const queryImgByCollect = (params) =>
    http({
        url: "/collect/list",
        method: "get",
        params,
    });
//查询指定收藏夹下的图片___新接口
export const queryImgByCollectNew = (params) =>
    http({
        url: "/collect/history-collect-list",
        method: "get",
        params,
    });
//批量收藏
export const batchSaveCollection = (data) =>
    http({
        url: "/collect/add-batch",
        method: "post",
        data,
    });
// 查询explore 社区数据
export const getCommunityPage = (params) =>
    http({
        url: "/comm-img/page-search-v2",
        method: "get",
        params,
    });
// 查询社区图片详情
export const getCommunityDetail = (params) =>
    http({
        url: "/comm-img/img-particular",
        method: "get",
        params,
    });
// 查询 用户是否对当前社区用户关注
export const queryIsFollow = (params) =>
    http({
        url: "/comm-follow/judge-follow",
        method: "get",
        params,
    });
// 关注指定用户
export const followUserById = (data) =>
    http({
        url: "/comm-follow/add-follow",
        method: "post",
        isForm: true,
        data,
    });
// 取消关注指定用户
export const unfollowUserById = (data) =>
    http({
        url: "/comm-follow/reduce-follow",
        method: "post",
        isForm: true,
        data,
    });
//查询自己关注的用户列表 或者 关注自己的粉丝列表
export const getFansOrFollowings = (params) =>
    http({
        url: "/comm-follow/select-follow",
        method: "get",
        params,
    });
//查询自己关注的用户列表 或者 关注自己的粉丝列表
export const getFansOrFollowingsV2 = (params) =>
    http({
        url: "/comm-follow/select-followV2",
        method: "get",
        params,
    });
//查询评论列表
export const queryCommentList = (params) =>
    http({
        url: "/comm-comment/select-comment",
        method: "get",
        params,
    });
// 发表评论
export const postComment = (data) =>
    http({
        url: "/comm-comment/add-comment-file",
        method: "post",
        isForm: true,
        data,
    });
// 回复评论
export const replyComment = (data) =>
    http({
        url: "/comm-comment/add-comment-comment",
        method: "post",
        isForm: true,
        data,
    });
//将图片发布到社区
export const publishToCommunity = (data) =>
    http({
        url: "/img/public-img",
        method: "post",
        isForm: true,
        data,
    });
//删除主评论
export const delCommentMain = (data) =>
    http({
        url: "/comm-comment/delete-comment-file",
        method: "post",
        isForm: true,
        data,
    });
//删除子评论
export const delCommentChilde = (data) =>
    http({
        url: "/comm-comment/delete-comment-comment",
        method: "post",
        isForm: true,
        data,
    });
//根据图片ID 对图片点赞
export const addLikeByFileId = (data) =>
    http({
        url: "/comm-like/add-like-file",
        method: "post",
        isForm: true,
        data,
    });
//根据图片ID 对图片取消点赞
export const reduceLikeByFileId = (data) =>
    http({
        url: "/comm-like/reduce-like-file",
        method: "post",
        isForm: true,
        data,
    });
//举报社区图片
export const reportCommunityGenContent = (data) =>
    http({
        url: "/comm-img-report/report-img",
        method: "post",
        isForm: true,
        data,
    });
//举报评论
export const reportComment = (data) =>
    http({
        url: "/comm-report/report-comment",
        method: "post",
        isForm: true,
        data,
    });
//根据ID查询社区个人主页数据
export const getCommunityPersonalById = (data) =>
    http({
        url: "/comm-user/select-user",
        method: "post",
        isForm: true,
        data,
    });
//根据ID查询社区个人主页公开的图片
export const getCommunityPersonalImgsById = (params) =>
    http({
        url: "/comm-img/page-personal-search",
        method: "get",
        params,
    });
//删除社区中的公开图片
export const delCommunityImgById = (data) =>
    http({
        url: "/comm-img/delete",
        method: "post",
        isForm: true,
        data,
    });
//修改社区图片提示词 可见性
export const updateCommunityPromptDisplay = (data) =>
    http({
        url: "/comm-img/view-change",
        method: "post",
        isForm: true,
        data,
    });
//获取 社区分享的图片基础信息
export const getPublishBaseInfoById = (params) =>
    http({
        url: "/comm-img/img-detail",
        method: "get",
        params,
    });
//获取 社区分享的图片的数量限制
export const getDailyPostPicLimit = () =>
    http({
        url: "/img/public-img-surplus-nums",
        method: "get",
    });
//获取个人历史记录(批量操作)
export const getHistoryList = (params) =>
    http({
        url: "/history/history-img-list",
        method: "get",
        params,
    });
//批量删除图片
export const batchDelImg = (data) =>
    http({
        url: "/img/batch-deletes-img",
        method: "post",
        data,
    });

// 获取支付渠道
export const getPayChannels = (data) =>
    http({
        url: "/common-resources/available-payment-channels",
        method: "get",
    });
// 发起订阅会话 --- paypal
export const reqSubscribeSessionByPaypal = (data) =>
    http({
        url: "/paypal/create-payment",
        method: "post",
        data,
    });

// 发起订阅会话 --- stripe
export const reqSubscribeSession = (data) =>
    http({
        url: "/stripe/pay/create-payment",
        method: "post",
        data,
    });
// 升级订阅 --- paypal
export const reqUpgradeSessionByPaypal = (data, type, opType) =>
    http({
        url: `/paypal/upgrade-downgrade?type=${type}&opType=${opType}`,
        method: "post",
        data,
    });
// 升级订阅 --- stripe
export const reqUpgradeSession = (data) =>
    http({
        url: "/stripe/pay/upgradeSubscription",
        method: "post",
        data,
    });
// 切换订阅 （未来）
export const reqChangeSession = (data) =>
    http({
        url: "/stripe/pay/changeSubscription",
        method: "post",
        data,
    });
// 获取会员权限列表
export const reqVipPermissionList = (data) =>
    http({
        url: "/vip/resource-list",
        method: "post",
        data,
    });
// 获取Lumen数量
export const reqLumenNum = (data) =>
    http({
        url: "/vip/lumens-message-detail",
        method: "get",
        data,
    });
// 轮询Lumen数量
export const reqPollLumenNum = (data) =>
    http({
        url: "/vip/lumens-recharge",
        method: "get",
        data,
    });
// 查询是否支持3天免费试用
export const freeTry3Day = (data) =>
    http({
        url: "/vip/trail-flag",
        method: "get",
        data,
    });
// 查询用户是否享受首充奖励
export const firstGift = (data) =>
    http({
        url: "/vip/cant-first-gift",
        method: "get",
        data,
    });
// 获取账单
export const reqBilling = (params) =>
    http({
        url: "/stripe/pay/billing",
        method: "get",
        params,
    });
// 获取用户订阅信息（简单信息）
export const reqUserVipInfo = (params) =>
    http({
        url: "/pay/common/vip-info-for-pay",
        method: "get",
        params,
    });

// 获取用户订阅信息（列表信息）
export const reqUserVipList = (data) =>
    http({
        url: "/pay/common/all-pay-vip-info",
        method: "get",
    });

// 查询用户优惠状态和折扣价格列表 例：PicNav中【老用户找回积分奖励】组件展示
export const requestUserPromotionStatus = (data) =>
    http({
        url: "/pay/common/user-promotion-status",
        method: "get",
    });

// 获取订阅刷新时间
export const reqSubscriptionSchedule = (data) =>
    http({
        url: "/stripe/pay/getSubscriptionSchedule",
        method: "post",
    });
// 取消自动订阅 --- paypal
export const reqCancelAutoSubscribeByPaypal = (data) =>
    http({
        url: "/paypal/cancel",
        method: "post",
        isForm: true,
    });
// 取消自动订阅
export const reqCancelAutoSubscribe = (data) =>
    http({
        url: "/stripe/pay/cancel-future",
        method: "post",
    });
// 恢复自动订阅
export const reqUnCancelAutoSubscribe = (data) =>
    http({
        url: "/stripe/pay/uncancel",
        method: "post",
    });
// 取消升级订阅
export const reqCancelUpgradeSubscribe = (data) =>
    http({
        url: "/stripe/pay/cancelSubscriptionSchedule",
        method: "post",
    });
// 获取用户社区信息与Lumen点数（这是一个聚合接口）
export const reqUserCommunityAndLumenInfo = (data) => http({ method: "get", url: "/user/select-user-detail" });

// 保存支付来源日志（后端埋点）
export const reqBiSavePayLog = (data) => http({ method: "post", url: "/pay/source/log/add", data });

// -------------------------批量和并发任务-------------------------
// 新增预载任务
export const addPreloadTask = (data) =>
    http({
        url: "/task/add-task-queue",
        method: "post",
        data,
    });
// 查询预载任务列表
export const getPreloadTasks = (params) =>
    http({
        url: "/task/select-task-queue",
        method: "get",
        params,
    });
// 删除预载任务
export const delPreloadTask = (data) =>
    http({
        url: "/task/delete-task-queue",
        method: "post",
        data,
    });
// 将预载任务提交到生图队列
export const submitPreloadTaskToExecute = (data) =>
    http({
        url: "/task/execute-task",
        method: "post",
        data,
        isForm: true,
    });
// 批量轮询生图结果
export const batchCheckExecuteResult = (data) =>
    http({
        url: "/task/batch-process-task",
        method: "post",
        data,
    });
// 图像反推提示词
export const image2TxtDescribe = (data) =>
    http({
        url: "/open/img-to-text",
        isForm: true,
        method: "post",
        data,
    });
// 语言翻译
export const translateText = (data) =>
    http({
        url: "/open/translate-to-language",
        method: "post",
        data,
    });
// 轮询- 未读通知
export const loopUnreadNotice = (params) =>
    http({
        url: "/comm-message/select-message-nums",
        method: "get",
        params,
    });
// 消息全部已读
export const dealReadAll = (data) =>
    http({
        url: "/comm-message/deal-read-all",
        method: "post",
        data,
    });
// 更新阅读状态
export const updateReadStatus = (data) =>
    http({
        url: "/comm-message/deal-read-message",
        method: "post",
        data,
    });
// 获取点赞通知列表
export const getLikesMessage = (params) =>
    http({
        url: "/comm-message/select-like-message",
        method: "get",
        params,
    });
// 获取公告通知列表
export const getSysMessage = (params) =>
    http({
        url: "/comm-message/select-activity-message",
        method: "get",
        params,
    });
// 获取评论通知列表
export const getCommentsMessage = (params) =>
    http({
        url: "/comm-message/select-comment-message",
        method: "get",
        params,
    });
// 获取平台私信列表
export const getPlatformMessage = (params) =>
    http({
        url: "/comm-message/select-platform-message",
        method: "get",
        params,
    });
// 获取系统更新知列表
export const getSysUpdateMessage = (params) =>
    http({
        url: "/comm-message/select-sysUpdate-message",
        method: "get",
        params,
    });
// 获取问卷
export const getQuestionnaire = (params) =>
    http({
        url: "/questionnaire/select-questionnaire-detail",
        method: "get",
        params,
    });
// 提交问卷答案
export const submitAnswer = (data) =>
    http({
        url: "/questionnaire/execute-answer",
        method: "post",
        data,
    });
// 图片控制 API -- 解析
export const imageDetectPost = (data) =>
    http({
        url: "/img-control/extract",
        isForm: true,
        method: "post",
        data,
    });
// 图片控制 API -- 新增
export const appendImageControl = (data) =>
    http({
        url: "/img-control/save",
        method: "post",
        data,
    });
// 图片控制 API -- 删除
export const delImageControl = (data) =>
    http({
        url: "/img-control/delete",
        isForm: true,
        method: "post",
        data,
    });
// 图片控制 API -- 查询
export const getImageControlByType = (data) =>
    http({
        url: "/img-control/select-list",
        isForm: true,
        method: "post",
        data,
    });
//-------------社区首页banner
export const getCommunityBanner = (params) =>
    http({
        url: "/comm-img/banner-img-list",
        method: "get",
        params,
    });
//社区首页操作记录排序分
export const communityBehaviorScore = (data) =>
    http({
        url: "/comm-img/share-remix",
        method: "post",
        isForm: true,
        data,
    });
//提交图片二次编辑
export const postImageEdit = (data) =>
    http({
        url: "/gen/edit",
        method: "post",
        data,
    });

// 上报 用户每日免费lumen弹窗控制
export const updateUserDailyLumenDailogControl = (params = { showDialog: false }) =>
    http({
        url: "/user/daily-lumen-dialog-control",
        method: "post",
        params, //这个接口特别处理
    });


export const getRemainLumens = (params) =>
    http({
        url: "/user/user-current-lumen",
        method: "get",
        params,
    });
