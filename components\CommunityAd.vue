<template>
    <div class="h-full relative bg-white rounded-xl overflow-hidden">
        <n-carousel :draggable="adList.length > 1" show-arrow class="carousel transform-gpu" autoplay>
            <div class="w-full h-full" v-for="(img, index) in adList" :key="img.id || index">
                <a :href="img.jumpUrl" target="_blank" rel="noopener noreferrer">
                    <img loading="lazy" class="w-full h-full object-cover cursor-pointer" :src="img.src" />
                </a>
            </div>

            <template #arrow="{ prev, next }">
                <div>
                    <div v-if="adList.length > 1" class="carousel-arrow left-3 rotate-90" @click="prev">
                        <n-icon size="20"><IconsArrowLine /></n-icon>
                    </div>
                    <div v-if="adList.length > 1" class="carousel-arrow right-3 -rotate-90" @click="next">
                        <n-icon size="20"><IconsArrowLine /></n-icon>
                    </div>
                </div>
            </template>
            <template #dots="{ total, currentIndex, to }">
                <ul class="absolute left-0 bottom-[7px] w-full justify-center items-center flex gap-x-1">
                    <li
                        v-for="index of total"
                        :key="index"
                        class="w-[6px] h-[6px] rounded-full cursor-pointer bg-text-t-5"
                        :class="{ '!bg-text-white !w-4': currentIndex === index - 1 }"
                        @click="to(index - 1)"
                    />
                </ul>
            </template>
        </n-carousel>
    </div>
</template>
<script setup>
defineProps({
    adList: {
        type: Array,
        default: () => [],
    },
});
</script>
<style lang="scss" scoped>
.carousel {
    @apply w-full bg-bg-1;
    .carousel-arrow {
        @apply absolute top-1/2 w-8 h-8 hidden justify-center items-center -translate-y-1/2 bg-fill-dd-3 backdrop-blur-lg rounded-full text-text-white cursor-pointer hover:bg-fill-dd-4;
    }
    &:hover {
        .carousel-arrow {
            @apply flex;
        }
    }
}
</style>
