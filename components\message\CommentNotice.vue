<template>
    <div>
        <DynamicScroller :items="messageList" :min-item-size="160" :emit-update="true" keyField="id" tabindex="1" class="h-full pr-3 scroll-container" @scroll="pageScroll">
            <template #default="{ item, index, active }">
                <DynamicScrollerItem :item="item" :active="active" :size-dependencies="[item.message]" :data-index="index" :data-active="active" class="pb-4">
                    <div :key="item.id" @click="handleReadMessage(item)" class="rounded-2xl p-4 flex gap-3 bg-transparent hover:bg-fill-wd-1 cursor-pointer w-full">
                        <div class="flex h-10 items-center gap-3">
                            <NoReadDot v-if="!item.read" />
                            <NuxtLinkLocale :to="`/community/profile/${item.ownerAcc.userId}`" @click.stop>
                                <img v-if="item.ownerAcc?.userAvatarUrl" :src="item.ownerAcc.userAvatarUrl" class="h-11 w-11 rounded-full overflow-hidden shrink-0" />
                                <n-icon v-else size="44" class="shrink-0 text-text-3">
                                    <IconsPerson />
                                </n-icon>
                            </NuxtLinkLocale>
                        </div>
                        <div class="font-normal flex-1 w-0">
                            <div class="flex gap-2 items-center">
                                <span class="text-nowrap text-ellipsis truncate max-w-[50%] text-sm font-medium text-text-2">
                                    {{ item.ownerAcc.userName }}
                                </span>
                                <span class="text-nowrap text-ellipsis truncate max-w-[40%] text-xs text-text-4">
                                    {{ t("MESSAGE_CENTER_COMMENTS_TITLE") }}
                                </span>
                            </div>
                            <div class="mt-1 text-xs text-text-4">{{ item.actionTime }}</div>
                            <div class="mt-2 !line-clamp-2 w-full text-text-2">{{ item.content }}</div>

                            <n-button :bordered="false" round class="w-32 mt-4 h-8 !bg-fill-btn-5 hover:!bg-fill-btn-6 !text-primary-6 text-xs" @click.stop="handleReplyTo(item)">
                                {{ t("MESSAGE_CENTER_COMMENTS_REPLY") }}
                            </n-button>
                        </div>
                        <img :src="item.miniThumbnailUrl" class="w-[72px] h-[72px] rounded-lg object-cover shrink-0" />
                    </div>
                </DynamicScrollerItem>
            </template>
            <template #after>
                <div v-if="loading" class="flex justify-center py-10">
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </div>
                <div v-if="noMore && messageList.length > 0" class="flex justify-center py-10">
                    <div class="pt-4 px-2 border-t border-solid text-text-4 border-border-1">There's no more</div>
                </div>
            </template>
        </DynamicScroller>
        <div v-if="noMore && messageList.length === 0" class="absolute left-0 top-0 right-0 h-full flex items-center justify-center flex-col z-10">
            <img src="@/assets/images/notice_empty.webp" class="w-36 aspect-square hidden dark:block" />
            <img src="@/assets/images/notice_empty_light.webp" class="w-36 aspect-square dark:hidden" />
            <div class="mt-4 text-text-4">{{ t("MESSAGE_CENTER_EMPTY") }}</div>
        </div>
        <n-modal v-model:show="currentNotice.showModal">
            <div class="w-[550px] rounded-2xl bg-bg-2 p-6 overflow-hidden border border-solid border-border-1">
                <div class="flex justify-between pb-8 font-semibold">
                    <span class="text-text-1">{{ t("MESSAGE_CENTER_COMMENTS_REPLY") }}</span>
                    <n-icon @click="currentNotice.showModal = false" class="cursor-pointer text-text-4 hover:text-text-2" size="20"> <IconsClose /> </n-icon>
                </div>
                <div class="bg-fill-ipt-1 text-text-2 rounded-xl">
                    <n-input
                        @keydown.stop="keyDownHandlePost"
                        class=""
                        style="--n-text-color: inherit"
                        :maxlength="100"
                        type="textarea"
                        :autofocus="true"
                        :placeholder="currentNotice.ownerAcc?.userName ? `Reply to @${currentNotice.ownerAcc.userName}` : t('COMMUNITY_COMMENT')"
                        v-model:value="currentNotice.replyContent"
                        round
                        :rows="5"
                        :autosize="{ minRows: 5, maxRows: 5 }"
                    />
                </div>

                <div class="mt-8 flex justify-end">
                    <Button :bordered="false" type="primary" :disabled="!currentNotice.replyContent" :loading="currentNotice.loading" round class="w-44 h-11" @click="submitComment">
                        <span>{{ t("MESSAGE_CENTER_COMMENTS_PUBLISH") }}</span>
                    </Button>
                </div>
            </div>
        </n-modal>
    </div>
</template>

<script setup>
import { formatDate, debounce, isScrolledToBottom } from "@/utils/tools";
import { getCommentsMessage, replyComment } from "@/api";
import { useUnreadMessage, useShareDataStore } from "@/stores";

import { trackLabels } from "@/common/trackEventLabel";
import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import NoReadDot from "@/components/message/NoReadDot.vue";

const { t } = useI18n({ useScope: "global" });
const localePath = useLocalePath();

const unreadMessages = useUnreadMessage();

const messageList = ref([]);
const loading = ref(false);
const noMore = ref(false);
//加载数据
const pageSize = 30;
let lastCommentId = null;
const handleLoad = async () => {
    if (loading.value || noMore.value) return;
    loading.value = true;
    const { status, data, message } = await getCommentsMessage({ pageSize, lastCommentId });
    loading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    lastCommentId = data.lastId;
    const newArray = data?.resultList || [];
    if (!data.lastId || newArray.length < pageSize) {
        noMore.value = true;
    }
    const list = newArray.map((item) => {
        item.actionTime = formatDate(item.createTime);
        return item;
    });
    messageList.value.push(...list);
    // messageList.value =   messageList.value.slice(0,3)
};
handleLoad();

const pageScroll = debounce((e) => {
    console.log("Page scroll", e);
    const isBottom = isScrolledToBottom(e.target);
    if (!isBottom) {
        return;
    }
    handleLoad();
}, 60);
//阅读信息
let fetchLoading = false;
const messageType = "ncommentNums";
const handleReadMessage = (item) => {
    readMessage(item);
    toCommunityDetail(item.fileId);
};
const readMessage = async ({ id, read }) => {
    if (fetchLoading || read) return;
    fetchLoading = true;
    const res = await unreadMessages.updateNoticeReadStatus(id, messageType);
    fetchLoading = false;
    if (!res) {
        return;
    }
    const index = messageList.value.findIndex((item) => item.id === id);
    messageList.value[index].read = true;
};

const currentNotice = ref({
    showModal: false,
});
const handleReplyTo = (item) => {
    readMessage(item);
    window.trackEvent("Message_Center", { el: trackLabels.replay });

    currentNotice.value = { ...item, showModal: true, loading: false };
};
const shareData = useShareDataStore();
const toCommunityDetail = async (id) => {
    shareData.setList([]);
    await navigateTo({ path: localePath(`/community/detail/${id}`) });
};
// ctrl + enter 组合键 发布评论
const keyDownHandlePost = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
        submitComment();
    }
};
const submitComment = async () => {
    const { loading, replyContent, fileId, firstCommentId, id } = currentNotice.value;
    window.trackEvent("Message_Center", { el: trackLabels.publish });
    if (loading) return;
    currentNotice.value.loading = true;
    const params = {
        commentId: id,
        firstCommentId: firstCommentId || id,
        commFileId: fileId,
        content: replyContent,
    };
    const { status, message } = await replyComment(params);
    currentNotice.value.loading = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    currentNotice.value = { showModal: false };
};
const readAll = () => {
    messageList.value.forEach((item) => {
        item.read = true;
    });
    console.log("父组件点击了全部已读", messageList.value);
};
defineExpose({
    readAll,
});
</script>

<style lang="scss" scoped></style>
