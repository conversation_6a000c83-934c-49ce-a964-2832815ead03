<template>
    <div class="w-full">
        <div class="form-item pb-7 relative w-full mt-2" v-if="!hasDisplayCode" :style="{ '--err': `'${t(errorTips.email)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center pl-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsEmail />
                </n-icon>
                <n-input class="text-dark-active-text" type="email" maxlength="100" v-model:value="config.email" :placeholder="t('ACC_SIGN_IN_EMAIL')" @change="checkEmail" />
            </div>
        </div>
        <div class="form-item pb-7 relative w-full" v-if="!hasDisplayCode" :style="{ '--err': `'${t(errorTips.password)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center px-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsLock />
                </n-icon>
                <n-input
                    class="text-dark-active-text"
                    :type="inputType"
                    maxlength="32"
                    v-model:value="config.password"
                    :placeholder="t('ACC_SIGN_IN_PWD')"
                    @keydown.enter="getValidateCode"
                    @change="checkPassword"
                />
                <n-icon size="24" @click="displayPwd = !displayPwd" class="cursor-pointer hover:text-dark-active-text">
                    <IconsNoEye v-if="displayPwd" />
                    <IconsEye v-else />
                </n-icon>
            </div>
        </div>
        <div v-else class="mt-4">
            <div class="text-xs mb-2 opacity-60">
                {{ $t("ACCOUNT_CURRENT", { email: desensitization }) }}
            </div>
            <div class="form-item pb-7 relative w-full flex gap-2" :style="{ '--err': `'${t(errorTips.validateCode)}'` }">
                <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center pl-4 text-dark-active-text/70">
                    <n-icon size="24">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 -960 960 960" width="1em" fill="currentColor">
                            <path
                                d="M280-240q-100 0-170-70T40-480q0-100 70-170t170-70q66 0 121 33t87 87h432v240h-80v120H600v-120H488q-32 54-87 87t-121 33Zm0-80q66 0 106-40.5t48-79.5h246v120h80v-120h80v-80H434q-8-39-48-79.5T280-640q-66 0-113 47t-47 113q0 66 47 113t113 47Zm0-80q33 0 56.5-23.5T360-480q0-33-23.5-56.5T280-560q-33 0-56.5 23.5T200-480q0 33 23.5 56.5T280-400Zm0-80Z"
                            />
                        </svg>
                    </n-icon>
                    <n-input
                        class="text-dark-active-text"
                        type="text"
                        :placeholder="t('PROFILE_ACCOUNT_VERIFY_CODE')"
                        maxlength="4"
                        v-model:value="config.validateCode"
                        @change="checkValidateCode"
                        @keydown.enter="handleRegister"
                    />
                </div>
                <Button class="flex-1" type="primary" rounded="xl" :disabled="isDisabled || isLoading" :bordered="false" :loading="isLoading" @click="getValidateCode">
                    <n-countdown v-if="isDisabled" ref="countdown" :duration="60000" :active="true" :render="renderCountdown" :on-finish="resetCountdown" />
                    <span v-else>{{ t("PROFILE_ACCOUNT_GET_CODE") }}</span>
                </Button>
            </div>
        </div>

        <div v-if="!hasDisplayCode" class="text-xs flex items-start gap-2 break-all w-full cursor-pointer" @click="isAgree = !isAgree">
            <span class="custom-checkbox" :class="{ 'bg-primary !border-primary': isAgree }">
                <n-icon size="14" v-if="isAgree">
                    <IconsSuccess />
                </n-icon>
            </span>
            <span class="cursor-pointer">
                <span class="opacity-60 hover:opacity-100">{{ t("ACC_SIGN_IN_AGREE") }} </span>
                <span class="opacity-80 hover:opacity-100 ml-1 underline" @click.stop="toWp('/terms-of-use')">{{ t("ACC_SIGN_IN_TERMS") }} </span>
                <span class="mx-1 cursor-default">&</span>
                <span class="opacity-80 hover:opacity-100 underline" @click.stop="toWp('/privacy-policy')">{{ t("ACC_SIGN_IN_POLICY") }}.</span>
            </span>
        </div>

        <div v-if="hasDisplayCode" class="text-xs flex items-start gap-2 break-all w-full cursor-pointer" @click="emit('update:hasRemember', !hasRemember)">
            <span class="custom-checkbox" :class="{ 'bg-primary !border-primary': hasRemember }">
                <n-icon size="14" v-if="hasRemember">
                    <IconsSuccess />
                </n-icon>
            </span>
            <span class="cursor-pointer opacity-60 hover:opacity-100">
                {{ t("ACC_SIGN_IN_REMEMBER") }}
            </span>
        </div>

        <div class="mt-8 w-full">
            <Button v-if="!hasDisplayCode" :disabled="isLoading" type="primary" class="h-10 w-full" block rounded="lg" :loading="isLoading" :bordered="false" @click="getValidateCode">
                <span class="text-dark-active-text" v-if="!isLoading">{{ t("ACC_SIGN_IN_SIGN_UP") }}</span>
            </Button>
            <Button v-else :disabled="subLoading" :loading="subLoading" type="primary" class="h-10 w-full" block rounded="lg" :bordered="false" @click="handleRegister">
                <span class="text-dark-active-text" v-if="!subLoading">{{ t("PROFILE_ACCOUNT_CONFIRM_EMAIL") }}</span>
            </Button>
        </div>
        <div class="mt-4 w-full text-end text-xs opacity-60 cursor-pointer hover:opacity-100" @click="toFeature('SignInComp')">
            <span>{{ t("ACC_SIGN_IN_READY_ACC") }}</span>
            <span class="underline ml-1">{{ t("ACC_SIGN_IN_TITLE") }}</span>
        </div>
    </div>
</template>

<script setup>
import { createAccount, sendCodeByRegister } from "@/api";
import { useSignInByEmail } from "@/hook/updateAccount";
const { requestSignIn } = useSignInByEmail();

const { t } = useI18n({ useScope: "global" });
import { Alert } from "@/icons/index.js";
import { NIcon } from "naive-ui";

const props = defineProps(["hasRemember"]);
import { strToMd5, validateEmail } from "@/utils/tools.js";
const emit = defineEmits(["changeComponent", "update:hasRemember"]);
const toFeature = (key) => {
    emit("changeComponent", key);
};
const toWp = (path) => {
    const url = window.location.origin + path;
    window.open(url, "_blank");
};

//  同意协议
const isAgree = ref(false);
//  loading
const isLoading = ref(false);
// 提交 loading
const subLoading = ref(false);
//禁用 验证码 button
const isDisabled = ref(false);
const countdown = ref(null);
const renderCountdown = ({ seconds }) => {
    if (seconds == 0) {
        return "60 s";
    }
    return `${String(seconds).padStart(2, "0")} s`;
};

//重置倒计时
const resetCountdown = () => {
    countdown.value.reset();
    isDisabled.value = false;
    isLoading.value = false;
};
const desensitization = computed(() => {
    try {
        let email = config.value.email;
        let lastIndex = email.lastIndexOf("@");
        return email.substring(0, 1) + "***@" + email.substring(lastIndex + 1, lastIndex + 2) + "***";
    } catch (error) {
        return email.substring(0, 1) + "***@***";
    }
});
const hasDisplayCode = ref(false);
const displayPwd = ref(false);
const config = ref({
    email: "",
    password: "",
    validateCode: "",
});
const errorTips = ref({
    email: "NULL_CONTENT",
    password: "NULL_CONTENT",
    validateCode: "NULL_CONTENT",
});

const inputType = computed(() => {
    return displayPwd.value ? "text" : "password";
});

// 邮箱校验
const checkEmail = () => {
    const email = config.value.email;
    // console.log(email, '<<<<')
    if (validateEmail(email)) {
        errorTips.value.email = "NULL_CONTENT";
        return true;
    }
    errorTips.value.email = "PROFILE_ACCOUNT_INVALID_EMAIL";
    return false;
};
//密碼校验
const checkPassword = () => {
    const val = config.value.password;
    if (val.length >= 6) {
        errorTips.value.password = "NULL_CONTENT";
        return true;
    }
    errorTips.value.password = "PROFILE_ACCOUNT_INVALID_PASSWORD";
    return false;
};

// 验证码校验正则
const checkValidateCode = () => {
    const code = config.value.validateCode;
    const codeReg = /^[0-9]{4}$/;
    if (codeReg.test(code)) {
        errorTips.value.validateCode = "NULL_CONTENT";
        return true;
    }
    errorTips.value.validateCode = "PROFILE_ACCOUNT_INVALID_VERIFY_CODE";
    return false;
};

//获取验证码
const getValidateCode = async () => {
    const t1 = checkEmail();
    const t2 = checkPassword();
    if (isLoading.value || isDisabled.value || !t1 || !t2) {
        return false;
    }
    const t3 = await alertAgree();
    if (!t3) {
        return false;
    }
    try {
        isLoading.value = true;
        let account = config.value.email;
        const { status, message } = await sendCodeByRegister({ account });
        isLoading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        isDisabled.value = true;
        hasDisplayCode.value = true;
    } catch (error) {
        isLoading.value = false;
    }
};
const alertAgree = () => {
    return new Promise((resolve, reject) => {
        const { showMessage, clearMessageBox } = useModal();
        if (!isAgree.value) {
            showMessage({
                style: { width: "420px", background: "#121314" },
                cancelBtn: t("COMMON_BTN_CANCEL"),
                confirmBtn: t("ACC_AGREE_BTN"),
                content: h("div", { class: ["text-sm text-dark-active-text/70 leading-6"] }, [
                    h("span", null, t("ACC_REG_CONTENT_1")),
                    h(
                        "span",
                        {
                            class: ["text-dark-active-text underline cursor-pointer underline-offset-4 opacity-70 hover:opacity-100 active:opacity-80 ml-1"],
                            onclick: () => toWp("/terms-of-use"),
                        },
                        t("ACC_SIGN_IN_TERMS")
                    ),
                    h("span", { class: ["mx-1.5"] }, "&"),
                    h(
                        "span",
                        {
                            class: ["text-dark-active-text underline cursor-pointer underline-offset-4 opacity-70 hover:opacity-100 active:opacity-80 ml-1"],
                            onclick: () => toWp("/privacy-policy"),
                        },
                        t("ACC_SIGN_IN_POLICY")
                    ),
                    h("span", { class: ["ml-1"] }, t("ACC_REG_CONTENT_2")),
                ]),
                icon: h(Alert, { class: "text-error text-2xl" }),
                title: h("span", { class: ["text-dark-active-text"] }, t("ACC_REG_TITLE")),
            })
                .then(() => {
                    isAgree.value = true;
                    resolve(true);
                })
                .catch(() => {
                    resolve(false);
                })
                .finally(() => {
                    clearMessageBox();
                });
        } else {
            resolve(true);
        }
    });
};

// 检测是否允许註冊
const checkAllowRegister = () => {
    return new Promise((resolve) => {
        const t1 = checkEmail();
        const t2 = checkPassword();
        const t3 = checkValidateCode();
        if (!t1 || !t2 || !t3) {
            resolve(false);
            return;
        }
        resolve(alertAgree());
    });
};

//註冊
const handleRegister = async () => {
    window.trackEvent("Sign", { el: "sign_up_button" });

    const hasAllow = await checkAllowRegister();
    if (!hasAllow || subLoading.value) {
        return;
    }
    try {
        const reqBody = {
            account: config.value.email,
            password: strToMd5(config.value.password),
            validateCode: config.value.validateCode,
        };
        subLoading.value = true;
        let { status, message } = await createAccount(reqBody);
        if (status != 0) {
            subLoading.value = false;
            openToast.error(message);
            return;
        }
        subLoading.value = true;
        const storageType = props.hasRemember ? "localStorage" : "sessionStorage";
        const { validateCode, ...signInReqBody } = reqBody;
        const { success, err } = await requestSignIn(signInReqBody, storageType);
        subLoading.value = false;
        if (!success) {
            openToast.error(err);
        }
    } catch (error) {
        subLoading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.form-item::after {
    @apply absolute text-xs text-error top-10 mt-0.5 left-0 block scale-90 origin-top-left;
    content: var(--err, "");
}
.custom-checkbox {
    @apply shrink-0 border border-solid cursor-pointer border-white/40 w-4 h-4 rounded-sm flex items-center justify-center;
}
</style>
