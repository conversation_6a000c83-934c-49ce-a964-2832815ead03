<template>
    <div
        @click="openFilePicker"
        @dragover.prevent="onDragOver"
        @dragleave="onDragLeave"
        @drop.prevent="onDrop"
        :class="[disabled ? '' : 'cursor-pointer']"
        class="rounded-md text-center transition-all w-full h-full"
    >
        <slot>
            <div class="w-full h-full flex flex-col items-center justify-center">
                <n-icon size="20"><IconsUpload class="text-text-2" :class="{ 'text-text-6': disabled }" /></n-icon>
                <div class="mt-3">
                    <div class="text-text-3 text-xs font-medium" :class="{ 'text-text-6': disabled }">{{ $t("FEATURE_SELF_UPLOAD_TITLE") }}</div>
                    <div class="mt-1 text-[10px] font-medium text-text-4 select-none" :class="{ 'text-text-6': disabled }" v-if="showAccept">
                        <span>{{ formattedAcceptString }}</span>
                        <span v-if="limit"> up to {{ limit }}MB</span>
                    </div>
                    <div v-if="draggable && dragHint" class="text-gray-500 mt-2 text-xs">
                        {{ $t(dragHint) }}
                    </div>
                </div>
            </div>
        </slot>

        <!-- 隐藏的文件选择输入框 -->
        <input ref="fileInput" type="file" @change="handleFileChange" :accept="accept" :multiple="multiple" style="display: none" />
    </div>
</template>

<script setup>
import { ref, computed } from "vue";

const emit = defineEmits(["before-file-change", "change", "error", "check-failed"]);

// 定义 props
const props = defineProps({
    disabled: { type: Boolean, default: false },
    accept: { type: String, default: ".png,.jpg,.jpeg,.webp" },
    dragHint: { type: String, default: "" }, //拖拽上传的提示文案
    multiple: { type: Boolean, default: false },
    eventName: { type: String, default: "" },
    limit: { type: Number, default: 20 }, // in MB
    maxPixels: { type: Number, default: 25000000 }, // in px
    oneTimeMax: { type: Number, default: 30, validator: (value) => value > 0 }, //单次上传最大个数
    draggable: { type: Boolean, default: true },
    showAccept: { type: Boolean, default: true },
});

const fileInput = ref(null);
const isDragging = ref(false);

// 触发文件选择框
const openFilePicker = () => {
    if (!props.disabled && fileInput.value) {
        fileInput.value.click();
        gaTrackEvent({ el: `upload_way=click` });
    } else {
        emit("disabled-click");
    }
};

// 格式化 accept 字符串
const formattedAcceptString = computed(() => {
    return props.accept
        .split(",")
        .map((ext) => ext.trim().replace(/^\./, "").toUpperCase())
        .join(", ");
});

// 处理文件选择后的事件
const handleFileChange = (event) => {
    emit("before-file-change");
    processFiles(event.target.files);
    fileInput.value.value = "";
};

// 处理拖拽进入
const onDragOver = () => {
    if (props.draggable && !props.disabled) {
        isDragging.value = true;
    }
};

// 处理拖拽离开
const onDragLeave = () => {
    if (props.draggable) {
        isDragging.value = false;
    }
};

// 处理拖拽释放
const onDrop = (event) => {
    if (props.draggable && !props.disabled) {
        isDragging.value = false;
        const fileInfo = {
            target: {
                files: event.dataTransfer.files,
            },
        };
        handleFileChange(fileInfo);
        gaTrackEvent({ el: `upload_way=drag` });
    }
};

const processFiles = async (fileList) => {
    if (!fileList || !fileList.length) {
        emit("error", "No files selected.");
        return;
    }
    const { limit, maxPixels, oneTimeMax } = props;
    const pixelsStr = maxPixels / 1000000;
    const files = Array.from(fileList); // 确保是数组
    gaTrackEvent({ el: `upload_amount=${files.length}` });
    const acceptFormats = props.accept.split(",").map((ext) => ext.trim().toLowerCase());
    let supportedFiles = [];
    const sizeExceededFiles = []; // 统一拼写
    const ratioExceededFiles = [];

    const checkImageRatio = (file) => {
        return new Promise((resolve) => {
            const img = new Image();
            const url = URL.createObjectURL(file);

            img.onload = () => {
                const pixels = img.width * img.height;
                // console.log(pixels);
                URL.revokeObjectURL(url);
                if (pixels > props.maxPixels) {
                    gaTrackEvent({ el: `upload_image_fail#pixel=${file.size / 1024 / 1024}` });
                } else {
                    gaTrackEvent({ el: `upload_image#pixel=${file.size / 1024 / 1024}` });
                }
                resolve(pixels > props.maxPixels); // 默认 2500 万像素
            };

            img.onerror = () => {
                URL.revokeObjectURL(url);
                resolve(false);
            };

            img.src = url;
        });
    };

    const checkResults = await Promise.all(
        files.map(async (file) => {
            const fileType = file.type?.toLowerCase() || "";
            const fileExtension = file.name.split(".").pop()?.toLowerCase() || "";
            gaTrackEvent({ el: `upload_type=${fileType}` });

            // 检查格式
            const isFormatValid = acceptFormats.includes(fileType) || acceptFormats.includes(`.${fileExtension}`);
            if (!isFormatValid) {
                emit("check-failed", {
                    type: "format",
                    limit,
                    maxPixels: pixelsStr,
                    oneTimeMax,
                    format: formattedAcceptString.value,
                });
                return { file, isValid: false };
            }

            // 检查大小
            if (props.limit > 0 && file.size > props.limit * 1024 * 1024) {
                gaTrackEvent({ el: `upload_image_fail#size=${file.size / 1024 / 1024}` });
                return { file, isSizeExceeded: true };
            } else {
                gaTrackEvent({ el: `upload_image#size=${file.size / 1024 / 1024}` });
            }

            // 检查像素（仅图片）
            if (fileType.startsWith("image/")) {
                const isRatioExceeded = await checkImageRatio(file);
                if (isRatioExceeded) {
                    return { file, isRatioExceeded: true };
                }
            }

            return { file, isValid: true };
        })
    );

    // 分类结果
    checkResults.forEach((result) => {
        if (result.isSizeExceeded) {
            sizeExceededFiles.push(result.file.name);
        } else if (result.isRatioExceeded) {
            ratioExceededFiles.push(result.file.name);
        } else if (result.isValid) {
            supportedFiles.push(result.file);
        }
    });
    // 触发事件
    if (sizeExceededFiles.length > 0) {
        emit("check-failed", {
            type: "size",
            limit,
            maxPixels: pixelsStr,
            oneTimeMax,
            files: sizeExceededFiles,
        });
    }

    if (ratioExceededFiles.length > 0) {
        emit("check-failed", {
            type: "ratio",
            limit,
            maxPixels: pixelsStr,
            oneTimeMax,
            files: sizeExceededFiles,
        });
    }

    if (supportedFiles.length > 0) {
        if (supportedFiles.length > props.oneTimeMax) {
            emit("check-failed", {
                type: "count",
                limit,
                maxPixels: pixelsStr,
                oneTimeMax,
                files: sizeExceededFiles,
            });
            supportedFiles = supportedFiles.slice(0, props.oneTimeMax);
        }
        emit("change", supportedFiles);
    }
};

defineExpose({ openFilePicker });

/**埋点 */
const gaTrackEvent = (options) => {
    if (!props.eventName) return;
    console.log("gaTrackEvent", options);
    window.trackEvent(props.eventName, options);
};
</script>

<style scoped>
/* 仅在允许拖拽时生效 */
.border-dashed {
    transition: border-color 0.3s;
}
</style>
