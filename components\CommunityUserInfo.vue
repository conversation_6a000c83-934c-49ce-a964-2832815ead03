<!--
 * @Author: HuangQS
 * @Description: 社区页、我的个人页 顶部用户信息组件
 * @Date: 2025-07-24 20:52:52
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-31 14:55:33
-->

<template>
    <!-- 裁剪头像、上传 组件 -->
    <CropModal ref="clipAvatarRef" />
    <!-- 头像 -->
    <UsersAvatar :src="avatarUrl" :icon-size="isMobile ? 80 : 96" @upload-click="handleUploadClick">
        <template v-if="who === 'other' && isLoaded" #rightbotom>
            <!-- 是他人信息，关注按钮 -->
            <template v-if="isFollowed">
                <div class="size-6 border-2 border-white dark:border-black bg-success-6 rounded-full flex items-center justify-center cursor-pointer" @click="handleFollowOtherClick">
                    <n-icon :size="18" class="text-white"> <IconsSuccess /> </n-icon>
                </div>
            </template>
            <template v-else>
                <div class="size-6 border-2 border-white dark:border-black bg-primary rounded-full flex items-center justify-center cursor-pointer" @click="handleFollowOtherClick">
                    <n-icon :size="18" class="text-white"> <IconsAdd /> </n-icon>
                </div>
            </template>
        </template>

        <!-- 头像上传 -->
        <template v-if="who === 'self' && isLoaded" #upload>
            <n-icon :size="24">
                <IconsUpload />
            </n-icon>
        </template>
    </UsersAvatar>

    <!-- 姓名区域 | VIP等级徽章 -->
    <div class="mt-3 md:mt-4 text-text-1 md:text-text-2 flex justify-center items-center gap-2" :class="isMobile && 'w-screen px-8'">
        <span class="text-xl md:text-2xl font-semibold truncate cursor-default">{{ isLoaded ? userName : "&nbsp" }}</span>
        <ClientOnly> <VipLevel v-if="isLoaded" :plan="vipPlan" /> </ClientOnly>
    </div>

    <!-- 个人介绍描述 -->
    <div class="mt-2 text-text-4 text-center break-words px-4 w-full md:max-w-[1200px] line-clamp-2">
        <span class="transition-colors" :class="who == 'self' && 'cursor-pointer hover:text-text-3'" @click="editCommunityInfo">{{ userDesc || t("COMMUNITY_EMPTY_DESC") }}</span>
    </div>

    <!-- 关注 点赞数 -->
    <div class="mt-3 md:mt-2 flex items-center justify-center cursor-default font-medium">
        <div class="hidden md:flex items-center gap-1 px-4 text-center">
            <span class="text-text-2 text-sm min-w-2">{{ isLoaded ? formatLike(posted) : 0 }}</span>
            <span class="text-text-4">{{ t("COMMUNITY_PUBLISHED") }}</span>
        </div>
        <div class="hidden md:flex divide-line" />
        <div class="flex items-center gap-1 px-4 div">
            <span class="text-text-2 text-sm min-w-2">{{ isLoaded ? formatLike(likes) : 0 }}</span>
            <span class="text-text-4">{{ t("SHORT_LIKES") }}</span>
        </div>
        <div class="divide-line" />
        <div class="flex items-center gap-1 px-4">
            <span class="text-text-2 text-sm min-w-2">{{ isLoaded ? formatLike(following) : 0 }}</span>
            <span class="text-text-4">{{ t("COMMUNITY_FOLLOWING") }}</span>
        </div>
        <div class="divide-line" />
        <div class="flex items-center gap-1 px-4">
            <span class="text-text-2 text-sm min-w-2">{{ isLoaded ? formatLike(followers) : 0 }}</span>
            <span class="text-text-4">{{ t("COMMUNITY_FANS") }}</span>
        </div>
    </div>
</template>
<script setup>
const { t } = useI18n({ useScope: "global" });
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
import UsersAvatar from "@/components/users/Avatar.vue";
import VipLevel from "@/components/VipLevel.vue";
import EditCommunityBio from "@/components/EditCommunityBio.vue";
import { useUserCommunity, useSubscribeStore, useUnreadMessage } from "@/stores";
import { getCommunityPersonalById, updateUserProfile } from "@/api";
import { debounce, isScrolledToBottom, formatNumber } from "@/utils/tools";

const emits = defineEmits([
    "onFollowOtherClick", //关注他人
]);

const userCommunity = useUserCommunity();

const { isMobile } = storeToRefs(useThemeStore());
const clipAvatarRef = ref(null);

const props = defineProps({
    isLoaded: { type: Boolean, default: false }, // 是否加载完成
    who: { type: String, default: "self" }, // "self" | "other"
    avatarUrl: { type: String, default: "" },
    userName: { type: String, default: "" },
    vipPlan: { type: String, default: "" }, // vip等级
    userDesc: { type: String, default: "" }, //用户个人介绍 昵称
    isFollowed: { type: Boolean, default: false }, // 是否被我关注中 (who==other时起效)
    // =====
    posted: { type: Number, default: 0 }, // 发布数
    likes: { type: Number, default: 0 }, // 喜欢数
    following: { type: Number, default: 0 }, // 关注数
    followers: { type: Number, default: 0 }, // 关注者
});

const { isLoaded, who, avatarUrl, userName, vipPlan, userDesc, isFollowed, posted, likes, following, followers } = toRefs(props);

const handleUploadClick = () => {
    clipAvatarRef.value.chooseLocalPic();
};

//更新个人备注
const editCommunityInfo = async () => {
    if (who.value != "self") return;

    const { showMessage } = useModal();
    let info = "";
    showMessage({
        style: { width: "500px", borderRadius: "16px" },
        showCancel: false,
        showConfirm: false,
        content: h(EditCommunityBio, {
            info: userDesc.value,
        }),
    }).then((introduction) => {
        updateBio(introduction);
    });
};

const updateBio = async (introduction) => {
    if (introduction === userCommunity.communityUser.introduction) {
        return;
    }
    const { status, message } = await updateUserProfile({ introduction });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    userCommunity.updateCommunityUser({ introduction });
};

const handleFollowOtherClick = () => {
    // 关注他人 按钮被点击
    emits("onFollowOtherClick");
};

const formatLike = computed(() => {
    return (num = 0) => formatNumber(num);
});
</script>
<style lang="scss" scoped>
.divide-line {
    width: 1px;
    height: 12px;
    background-color: var(--p-text-5);
}
</style>
