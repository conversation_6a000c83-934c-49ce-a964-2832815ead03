<template>
    <div class="flex flex-col h-screen">
        <BannerArea />
        <div class="flex min-h-0 box-border bg-bg-1 max-md:flex-wrap-reverse" :class="bannerAreaHeight == 0 ? 'flex-1 h-screen' : `flex-1 min-h-[calc(h-screen - ${bannerAreaHeight}px)]`">
            <div v-if="!isMobile" class="flex max-md:!hidden"><PicNav /></div>
            <div class="flex flex-col w-full h-full flex-1 relative" :class="isMobile ? 'overflow-hidden' : 'overflow-x-hidden '">
                <slot />

                <TabBar v-if="isShowTabBar" />
            </div>
        </div>
    </div>

    <!-- 每日领取lumen -->
    <client-only> <TaskModal /> </client-only>
</template>

<script setup>
import { useAsyncUserCount } from "@/hook//updateAccount";
import { useUserProfile } from "@/stores";
import TaskModal from "@/components/task/TaskModal.vue";
import { autoLoopQueryTaskResult, useUpdateLumen } from "@/hook/create";
import { useCurrentTaskQueue } from "@/stores/create";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
import { useGetRemoteModelList, useSetDefaultCreateConfig } from "@/hook/create";

const { locale } = useI18n({ inheritLocale: true, useScope: "global" });
const { isMobile, bannerAreaHeight } = storeToRefs(useThemeStore());

const ssrPagePaths = ref(["/community/explore"]);
const currentTaskQueue = useCurrentTaskQueue();
const { setCreateConfig } = useSetDefaultCreateConfig();
const isNeedSSR = computed(() => {
    // 如果是移动端，且当前路由在ssrPagePaths中，则需要开启SSR
    return ssrPagePaths.value.includes(route.path);
});

const mobileTabBarFilterPaths = computed(() => {
    const userProfile = useUserProfile();
    return [
        `/user/${userProfile.user.userId}`, //个人中心 Personal
        "/image/create",
        "/community/explore",
        "/m/collection", //gallery页
    ];
});

// 优化的路径匹配函数
const matchPath = (targetPath, patternPath) => {
    let cleanedTarget = targetPath.split("?")[0].split("#")[0]; // 移除查询参数和哈希
    const prefix = locale.value === "en" ? "" : `/${locale.value}`; // 添加语言前缀
    // 转换模式为正则表达式
    // 匹配动态参数
    // 匹配通配符
    let regexPattern = patternPath.replace(/\//g, "\\/").replace(/:\w+/g, "\\w+").replace(/\*/g, ".*");
    regexPattern = `${prefix}${regexPattern}`;
    const regex = new RegExp(`^${regexPattern}$`, "i");
    return regex.test(cleanedTarget);
};
const route = useRoute();
const isShowTabBar = computed(() => {
    if (!isMobile.value) return false;
    const currentPath = route.fullPath;
    return mobileTabBarFilterPaths.value.some((path) => {
        return matchPath(currentPath, path);
    });
});

onMounted(async () => {
    if (import.meta.client) {
        autoLoopQueryTaskResult();
        const userInfo = await useAsyncUserCount(); // 登录成功后 先获取模型列表 再并根据用户的默认设置 设置默认的生图参数
        await useGetRemoteModelList();
        setCreateConfig(userInfo);
    }
});

const allTaskInQueueLength = computed(() => {
    let len1 = currentTaskQueue.taskQueue?.length;
    let len2 = currentTaskQueue.preloadQueue?.length;
    return len1 + len2;
});

watch(
    () => allTaskInQueueLength.value,
    () => {
        useUpdateLumen();
    }
);
</script>
