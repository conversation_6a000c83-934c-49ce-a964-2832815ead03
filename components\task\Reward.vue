<template>
    <div class="flex p-6 items-center gap-x-3 bg-bg-2 rounded-2xl">
        <div class="reward-icon w-12 h-12 rounded-full">
            <img :src="icon" class="w-full h-full" />
        </div>
        <div class="flex-1 h-full flex flex-col justify-between">
            <div class="text-sm font-medium text-text-2 flex items-center gap-x-1">
                <div>{{ $t(currentTaskState.i18nKey) }}</div>
                <div v-if="data.taskId === '4'" class="flex items-center gap-x-[2px] text-text-4">
                    <n-icon size="13" class="text-text-4">
                        <IconsShareTwitter />
                    </n-icon>
                    <n-icon size="14">
                        <IconsSharePinterest />
                    </n-icon>
                    <n-icon size="14">
                        <IconsFacebookTask />
                    </n-icon>
                    <n-icon size="14">
                        <IconsShareReddit />
                    </n-icon>
                </div>
                <!-- 还有待完成的任务 -->
                <n-tooltip style="--n-box-shadow: none" v-if="tooltip" placement="top" trigger="hover" :delay="100" :show-arrow="false" :raw="true">
                    <template #trigger>
                        <n-icon size="14">
                            <IconsExclamationCircle />
                        </n-icon>
                    </template>
                    <div class="bg-bg-5 py-2 px-3 max-w-[320px] rounded-lg border-border-t-1 shadow-[0px_8px_24px_0px_rgba(0,0,0,0.16)] border text-text-3 text-sm">
                        <div>{{ $t(tooltip) }}</div>
                    </div>
                </n-tooltip>
            </div>
            <div class="flex text-base font-medium gap-x-[2px] items-center text-primary-6">
                <label class="text-base">{{ data.reward }}</label>
                <n-icon size="16">
                    <IconsLumenFill />
                </n-icon>
            </div>
        </div>
        <RewardButtons :tasks="[data]" />
    </div>
</template>
<script setup>
import { computed } from "vue";

import RewardButtons from "@/components/task/RewardButton.vue";
import { TASK_STATE_MAP } from "@/constants/taskCenter";

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});

/** 有的任务鼠标移上去会有提示 */
const tooltip = computed(() => {
    return (TASK_STATE_MAP[props.data.taskId]?.tooltip || "").trim();
});

const currentTaskState = computed(() => {
    return TASK_STATE_MAP[props.data.taskId];
});
const icon = computed(() => {
    return currentTaskState.value?.icon || "";
});
</script>

<style lang="scss" scoped></style>
