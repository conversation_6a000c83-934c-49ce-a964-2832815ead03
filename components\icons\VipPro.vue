<template>
    <!-- <svg fill="none" height="1em" version="1.1" viewBox="0 0 48 48" width="1em" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <mask id="master_svg0_281_8426" maskUnits="objectBoundingBox" style="mask-type: alpha">
                <g>
                    <rect fill="#FFFFFF" fill-opacity="1" height="48" rx="0" width="48" x="0" y="0" />
                </g>
            </mask>
            <linearGradient id="master_svg1_281_4462" x1="0.5" x2="0.5" y1="0" y2="1">
                <stop offset="0%" stop-color="#FFF3E1" stop-opacity="1" />
                <stop offset="100%" stop-color="#FFE1B4" stop-opacity="1" />
            </linearGradient>
            <linearGradient id="master_svg2_281_4440" x1="0.5" x2="0.5" y1="0" y2="1">
                <stop offset="0%" stop-color="#FFE29F" stop-opacity="1" />
                <stop offset="100%" stop-color="#FFAA00" stop-opacity="1" />
            </linearGradient>
            <filter
                id="master_svg3_281_8431"
                color-interpolation-filters="sRGB"
                filterUnits="objectBoundingBox"
                height="1.2000073244869807"
                width="1.1666671435051892"
                x="-0.08333357175259455"
                y="-0.10000366224349036"
            >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
                <feOffset dx="0" dy="0" />
                <feGaussianBlur stdDeviation="0.5" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0" />
                <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow" />
                <feBlend in="SourceGraphic" in2="effect1_dropShadow" mode="normal" result="shape" />
            </filter>
        </defs>
        <g mask="url(#master_svg0_281_8426)">
            <g>
                <path
                    d="M23.999999961853028,43.58999984741211C22.52399996185303,43.58999984741211,21.13399996185303,43.01399984741211,20.089999961853028,41.96799984741211C18.479999961853025,40.35799984741211,16.335999961853027,39.46999984741211,14.057999961853028,39.46999984741211C11.009999961853026,39.46999984741211,8.529999961853028,36.98999984741211,8.529999961853028,33.941999847412106C8.529999961853028,31.66199984741211,7.641999961853028,29.521999847412108,6.031999961853027,27.90999984741211C3.875999961853027,25.75399984741211,3.875999961853027,22.245999847412108,6.031999961853027,20.08999984741211C7.641999961853028,18.477999847412107,8.529999961853028,16.33799984741211,8.529999961853028,14.057999847412109C8.529999961853028,11.009999847412109,11.009999961853026,8.52999984741211,14.057999961853028,8.52999984741211C16.335999961853027,8.52999984741211,18.479999961853025,7.64199984741211,20.089999961853028,6.031999847412109C21.13399996185303,4.985999847412109,22.52399996185303,4.409999847412109,23.999999961853028,4.409999847412109C25.475999961853027,4.409999847412109,26.865999961853028,4.985999847412109,27.90999996185303,6.031999847412109C29.519999961853028,7.64199984741211,31.663999961853026,8.52999984741211,33.94199996185303,8.52999984741211C36.98999996185303,8.52999984741211,39.46999996185303,11.009999847412109,39.46999996185303,14.057999847412109C39.46999996185303,16.33799984741211,40.357999961853025,18.477999847412107,41.967999961853025,20.08999984741211C44.12399996185303,22.245999847412108,44.12399996185303,25.75399984741211,41.967999961853025,27.90999984741211C40.357999961853025,29.521999847412108,39.46999996185303,31.66199984741211,39.46999996185303,33.941999847412106C39.46999996185303,36.98999984741211,36.98999996185303,39.46999984741211,33.94199996185303,39.46999984741211C31.663999961853026,39.46999984741211,29.519999961853028,40.35799984741211,27.90999996185303,41.96799984741211C26.865999961853028,43.01399984741211,25.475999961853027,43.58999984741211,23.999999961853028,43.58999984741211C23.999999961853028,43.58999984741211,23.999999961853028,43.58999984741211,23.999999961853028,43.58999984741211Z"
                    fill="url(#master_svg1_281_4462)"
                    fill-opacity="1"
                />
            </g>
            <g>
                <path
                    d="M23.999999940872193,5.911999940872192C25.075999940872194,5.911999940872192,26.08799994087219,6.3299999408721925,26.84999994087219,7.091999940872192C28.743999940872193,8.985999940872192,31.261999940872194,10.029999940872193,33.94199994087219,10.029999940872193C36.16199994087219,10.029999940872193,37.96999994087219,11.837999940872193,37.96999994087219,14.057999940872193C37.96999994087219,16.737999940872193,39.01399994087219,19.255999940872194,40.907999940872195,21.149999940872192C42.47999994087219,22.72199994087219,42.47999994087219,25.277999940872192,40.907999940872195,26.84999994087219C39.01399994087219,28.743999940872193,37.96999994087219,31.261999940872194,37.96999994087219,33.94199994087219C37.96999994087219,36.16199994087219,36.16199994087219,37.96999994087219,33.94199994087219,37.96999994087219C31.261999940872194,37.96999994087219,28.743999940872193,39.01399994087219,26.84999994087219,40.907999940872195C26.08799994087219,41.669999940872195,25.075999940872194,42.087999940872194,23.999999940872193,42.087999940872194C22.923999940872193,42.087999940872194,21.911999940872192,41.669999940872195,21.149999940872192,40.907999940872195C19.255999940872194,39.01399994087219,16.737999940872193,37.96999994087219,14.057999940872193,37.96999994087219C11.837999940872193,37.96999994087219,10.029999940872193,36.16199994087219,10.029999940872193,33.94199994087219C10.029999940872193,31.261999940872194,8.985999940872192,28.743999940872193,7.091999940872192,26.84999994087219C5.519999940872193,25.277999940872192,5.519999940872193,22.72199994087219,7.091999940872192,21.149999940872192C8.985999940872192,19.255999940872194,10.029999940872193,16.737999940872193,10.029999940872193,14.057999940872193C10.029999940872193,11.837999940872193,11.837999940872193,10.029999940872193,14.057999940872193,10.029999940872193C16.737999940872193,10.029999940872193,19.255999940872194,8.985999940872192,21.149999940872192,7.091999940872192C21.911999940872192,6.3299999408721925,22.923999940872193,5.911999940872192,23.999999940872193,5.911999940872192ZM23.999999940872193,2.9119999408721924C22.20199994087219,2.9119999408721924,20.40199994087219,3.5979999408721923,19.02999994087219,4.969999940872192C19.02999994087219,4.969999940872192,19.02999994087219,4.969999940872192,19.02999994087219,4.969999940872192C17.711999940872193,6.287999940872192,15.923999940872193,7.029999940872193,14.057999940872193,7.029999940872193C10.175999940872192,7.029999940872193,7.029999940872193,10.175999940872192,7.029999940872193,14.057999940872193C7.029999940872193,14.057999940872193,7.029999940872193,14.057999940872193,7.029999940872193,14.057999940872193C7.029999940872193,15.923999940872193,6.287999940872192,17.711999940872193,4.969999940872192,19.02999994087219C4.969999940872192,19.02999994087219,4.969999940872192,19.02999994087219,4.969999940872192,19.02999994087219C2.2259999408721924,21.77399994087219,2.2259999408721924,26.225999940872192,4.969999940872192,28.969999940872192C4.969999940872192,28.969999940872192,4.969999940872192,28.969999940872192,4.969999940872192,28.969999940872192C6.287999940872192,30.287999940872194,7.029999940872193,32.075999940872194,7.029999940872193,33.94199994087219C7.029999940872193,33.94199994087219,7.029999940872193,33.94199994087219,7.029999940872193,33.94199994087219C7.029999940872193,37.82399994087219,10.175999940872192,40.96999994087219,14.057999940872193,40.96999994087219C15.923999940872193,40.96999994087219,17.711999940872193,41.71199994087219,19.02999994087219,43.029999940872194C20.40199994087219,44.401999940872194,22.20199994087219,45.087999940872194,23.999999940872193,45.087999940872194C25.79799994087219,45.087999940872194,27.597999940872192,44.401999940872194,28.969999940872192,43.029999940872194C30.287999940872194,41.71199994087219,32.075999940872194,40.96999994087219,33.94199994087219,40.96999994087219C37.82399994087219,40.96999994087219,40.96999994087219,37.82399994087219,40.96999994087219,33.94199994087219C40.96999994087219,33.94199994087219,40.96999994087219,33.94199994087219,40.96999994087219,33.94199994087219C40.96999994087219,32.075999940872194,41.71199994087219,30.287999940872194,43.029999940872194,28.969999940872192C43.029999940872194,28.969999940872192,43.029999940872194,28.969999940872192,43.029999940872194,28.969999940872192C45.773999940872194,26.225999940872192,45.773999940872194,21.77399994087219,43.029999940872194,19.02999994087219C43.029999940872194,19.02999994087219,43.029999940872194,19.02999994087219,43.029999940872194,19.02999994087219C41.71199994087219,17.711999940872193,40.96999994087219,15.923999940872193,40.96999994087219,14.057999940872193C40.96999994087219,14.057999940872193,40.96999994087219,14.057999940872193,40.96999994087219,14.057999940872193C40.96999994087219,10.175999940872192,37.82399994087219,7.029999940872193,33.94199994087219,7.029999940872193C32.075999940872194,7.029999940872193,30.287999940872194,6.287999940872192,28.969999940872192,4.969999940872192C28.969999940872192,4.969999940872192,28.969999940872192,4.969999940872192,28.969999940872192,4.969999940872192C27.597999940872192,3.5979999408721923,25.79799994087219,2.9119999408721924,23.999999940872193,2.9119999408721924C23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924C23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924Z"
                    fill="url(#master_svg2_281_4440)"
                    fill-opacity="1"
                />
            </g>
            <g>
                <g filter="url(#master_svg3_281_8431)" style="mix-blend-mode: overlay">
                    <path
                        d="M16.000000000000004,15.999526515603065L32,15.999526515603065L35.9996,21.999526515603065L35.999900000000004,21.999526515603065L35.99980000000001,21.999736515603065L35.999900000000004,21.999996515603065L35.999500000000005,21.999996515603065L23.997200000000003,35.99882651560307L12.000405376000003,21.999996515603065L12.000000000000004,21.999996515603065L12.000177362000004,21.999736515603065L12.000000000000004,21.999526515603065L12.000315323000004,21.999526515603065L16.000000000000004,15.999526515603065Z"
                        fill="#FFAB03"
                        fill-opacity="1"
                        fill-rule="evenodd"
                        style="mix-blend-mode: overlay"
                    />
                </g>
                <g>
                    <path
                        d="M23.997200000000003,35.9993147819519C23.997200000000003,35.9993147819519,12.000000000000004,22.000014781951904,12.000000000000004,22.000014781951904C12.000000000000004,22.000014781951904,35.999900000000004,22.000014781951904,35.999900000000004,22.000014781951904C35.999900000000004,22.000014781951904,23.997200000000003,35.9993147819519,23.997200000000003,35.9993147819519C23.997200000000003,35.9993147819519,23.997200000000003,35.9993147819519,23.997200000000003,35.9993147819519Z"
                        fill="#FFAB03"
                        fill-opacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M32.000000178813934,15.999996185302734C32.000000178813934,15.999996185302734,16.000000178813934,15.999996185302734,16.000000178813934,15.999996185302734C16.000000178813934,15.999996185302734,12.000000178813934,22.000466185302734,12.000000178813934,22.000466185302734C12.000000178813934,22.000466185302734,35.99990017881393,22.000466185302734,35.99990017881393,22.000466185302734C35.99990017881393,22.000466185302734,32.000000178813934,15.999996185302734,32.000000178813934,15.999996185302734C32.000000178813934,15.999996185302734,32.000000178813934,15.999996185302734,32.000000178813934,15.999996185302734Z"
                        fill="#FFC024"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M18,22.000246185302736C18,22.000246185302736,24,15.999996185302734,24,15.999996185302734C24,15.999996185302734,30,22.000246185302736,30,22.000246185302736C30,22.000246185302736,18,22.000246185302736,18,22.000246185302736C18,22.000246185302736,18,22.000246185302736,18,22.000246185302736Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M18,21.99998426437378C18,21.99998426437378,23.99731,35.99928426437378,23.99731,35.99928426437378C23.99731,35.99928426437378,30,21.99998426437378,30,21.99998426437378C30,21.99998426437378,18,21.99998426437378,18,21.99998426437378C18,21.99998426437378,18,21.99998426437378,18,21.99998426437378Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M16,15.999996185302734C16,15.999996185302734,18,22.000246185302736,18,22.000246185302736C18,22.000246185302736,12,22.000246185302736,12,22.000246185302736C12,22.000246185302736,16,15.999996185302734,16,15.999996185302734C16,15.999996185302734,16,15.999996185302734,16,15.999996185302734Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M32,15.968746185302734C32,15.968746185302734,30,21.999996185302734,30,21.999996185302734C30,21.999996185302734,36,21.999996185302734,36,21.999996185302734C36,21.999996185302734,32,15.968746185302734,32,15.968746185302734C32,15.968746185302734,32,15.968746185302734,32,15.968746185302734Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
            </g>
        </g>
    </svg> -->
    <svg v-if="uid" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path
            d="M29.0207 11.9237C29.9829 13.5274 29.7054 15.5838 28.3526 16.8751L18.7616 26.0301C17.2159 27.5056 14.7835 27.5056 13.2378 26.0301L3.64677 16.8751C2.29394 15.5838 2.01648 13.5274 2.9787 11.9237L6.1678 6.60852C6.89069 5.4037 8.19272 4.6665 9.59777 4.6665H22.4016C23.8066 4.6665 25.1087 5.4037 25.8316 6.60852L29.0207 11.9237Z"
            :fill="`url(#paint0_linear_pro_${uid})`"
        />
        <path d="M20.6663 17.3335L15.9997 22.0002L11.333 17.3335" stroke="#FEEED4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <defs>
            <linearGradient :id="`paint0_linear_pro_${uid}`" x1="1.33301" y1="4.6665" x2="30.6663" y2="28.6665" gradientUnits="userSpaceOnUse">
                <stop stop-color="#FECD34" />
                <stop offset="0.4" stop-color="#FF9500" />
                <stop offset="1" stop-color="#FF5F3F" />
            </linearGradient>
        </defs>
    </svg>
</template>

<script setup>
const uid = ref("");
onMounted(() => {
    uid.value = Math.random().toString(36).slice(2, 10);
});
</script>
<style scoped></style>
