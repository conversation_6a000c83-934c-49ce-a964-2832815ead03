<template>
    <div class="w-full relative">
        <Skeleton class="!w-[200px] my-4 h-5 rounded-[4px]" noIcon />
        <div class="w-full grid gap-1" :style="{ gridTemplateColumns: `repeat(${perRow},1fr)` }">
            <Skeleton v-for="i in perRow * 4" class="aspect-square rounded-lg" noIcon />
        </div>
        <Skeleton class="!w-[200px] my-4 h-5 rounded-[4px]" noIcon />
        <div class="w-full grid gap-1" :style="{ gridTemplateColumns: `repeat(${perRow},1fr)` }">
            <Skeleton v-for="i in perRow * 4" class="aspect-square rounded-lg" noIcon />
        </div>
    </div>
</template>
<script setup>
import Skeleton from "@/components/common/skeleton/index.vue";

defineProps({
    perRow: {
        type: Number,
        default: 4,
    },
});
</script>
