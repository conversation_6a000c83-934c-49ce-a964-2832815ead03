<template>
    <n-dropdown :options="shareActionOptions" trigger="click" placement="top" class="explore-more-dropdown" @select="checkShareAction">
        <slot />
    </n-dropdown>
</template>

<script setup>
import { useTaskStore } from "@/stores/task";
import { useRecordCommunityScore } from "@/hook/useCommon";
const { recordCommunityScore } = useRecordCommunityScore();
import { ShareFacebook, SharePinterest, ShareReddit, ShareTwitter } from "@/icons/index.js";

const { finishTaskShare } = useTaskStore();
const { t } = useI18n({ useScope: "global" });
import { NIcon } from "naive-ui";
import { copyToClipboard } from "@/utils/tools";
const emit = defineEmits(["share"]);
const props = defineProps({
    url: {
        type: String,
        default: "",
    },
    imgName: {
        type: String,
        default: "",
    },
    id: {
        type: String,
        default: "",
    },
    isSensitive: {
        type: Boolean,
        default: false,
    },
    gId: {
        type: String,
        default: "",
    },
    fileId: {
        type: String,
        default: "",
    },
    community: {
        type: Boolean,
        default: false,
    },
});
const renderIcon = (icon) => {
    return () => {
        return h(
            NIcon,
            { size: 20 },
            {
                default: () => h(icon),
            }
        );
    };
};
const actionOptionsList = [
    {
        label: "X",
        key: "Twitter",
        icon: renderIcon(ShareTwitter),
    },
    {
        label: "Pinterest",
        key: "Pinterest",
        icon: renderIcon(SharePinterest),
    },
    {
        label: "Facebook",
        key: "Facebook",
        icon: renderIcon(ShareFacebook),
    },
    {
        label: "Reddit",
        key: "Reddit",
        icon: renderIcon(ShareReddit),
    },
];

const shareActionOptions = computed(() => {
    return actionOptionsList.map((item) => ({ ...item, disabled: props.isSensitive }));
});

const shareConf = {
    facebookId: "61562588510786",
};

const checkShareAction = async (platform) => {
    emit("share", platform);
    window.trackEvent("APP_SHARE", { el: `share=${platform}` });
    let href = null;
    if (props.community) {
        href = encodeURIComponent(window.location.host + `/app/community/detail/${props.id}`);
        recordCommunityScore(props.fileId, "share");
    } else {
        href = encodeURIComponent(window.location.host + `/app/image/share?promptId=${props.id}&imageName=${props.imgName}&gId=${btoa(props.gId)}`);
    }
    const pageTitle = encodeURIComponent("Just made this awesome AI art with PicLumen - isn't it cool?");
    let link = "";
    switch (platform) {
        case "Facebook":
            link = `https://www.facebook.com/sharer/sharer.php?u=${href}&id=${shareConf.facebookId}`;
            break;
        case "Twitter":
            link = `https://twitter.com/intent/tweet?url=${href}&text=${pageTitle}&hashtags=PicLumen,PicLumenArt,PicLumenAI`;
            break;
        case "Pinterest":
            link = `https://pinterest.com/pin/create/button/?url=${href}&media=${props.url}&description=${pageTitle}`;
            break;
        case "Reddit":
            link = `https://reddit.com/submit?url=${href}&title=${pageTitle}`;
            break;
        default:
            link = href;
    }
    if (link === href) {
        link = decodeURIComponent(href);
        await copyToClipboard(link);
        openToast.success(t("TOAST_COPY_SUCCESS"));
        return;
    }
    finishTaskShare();
    openCenteredWindow(link, 600, 500);
};

const openCenteredWindow = (url, width, height) => {
    // 获取屏幕的宽度和高度
    let screenX = window.screenX || window.screenLeft;
    let screenY = window.screenY || window.screenTop;
    let outerWidth = window.outerWidth || window.innerWidth;
    let outerHeight = window.outerHeight || window.innerHeight;
    // 计算新窗口的左上角坐标
    let left = screenX + outerWidth / 2 - width / 2;
    let top = screenY + outerHeight / 2 - height / 2;
    // 弹出新窗口
    let newWindow = window.open(url, "_blank", "width=" + width + ", height=" + height + ", top=" + top + ", left=" + left);
    // 将新窗口聚焦
    if (newWindow) {
        newWindow.focus();
    }
};
</script>

<style lang="scss" scoped>
.share-item {
    @apply flex items-center justify-center dark:text-neutral-200 cursor-pointer h-10 w-10 rounded-md bg-black/5 dark:bg-dark-bg-2 transition-transform duration-200;
    &:hover {
        transform: translateY(-4px);
    }
}
:global(.explore-more-dropdown) {
    @apply min-w-40;
    padding: 8px !important;
}

:global(.explore-more-dropdown .n-dropdown-option-body) {
    @apply px-0;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before) {
    left: 0 !important;
    right: 0 !important;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending.danger-bg::before) {
    background-color: var(--p-fill-drop-5);
}
</style>
