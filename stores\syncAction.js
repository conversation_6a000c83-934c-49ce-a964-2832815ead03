import { defineStore } from 'pinia';

export const useSyncAction = defineStore('syncAction', () => {
  // 使用非响应式的 Map 存储事件和对应的回调函数 Set
  const events = new Map();

  /**
   * 订阅事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  function subscribe(event, callback) {
    if (!events.has(event)) {
      events.set(event, new Set());
    }
    const callbacks = events.get(event);
    callbacks.add(callback);
  }

  /**
   * 取消订阅事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  function unsubscribe(event, callback) {
    if (!events.has(event)) return;
    const callbacks = events.get(event);
    callbacks.delete(callback);
    if (callbacks.size === 0) {
      events.delete(event);
    }
  }

  /**
   * 发布事件
   * @param {string} event - 事件名称
   * @param {*} payload - 事件负载
   * @param {object} options
   */
  function publish(event, payload, options) {
    if (!events.has(event)) return;
    // 使用扩展运算符将 Set 转换为数组，确保回调的执行顺序和防止迭代时的修改
    const callbacks = Array.from(events.get(event));
    for (const callback of callbacks) {
      try {
        callback(payload, options);
      } catch (error) {
        console.error(`Error executing callback for event "${event}":`, error);
      }
    }
  }

  return {
    subscribe,
    unsubscribe,
    publish,
  };
});
