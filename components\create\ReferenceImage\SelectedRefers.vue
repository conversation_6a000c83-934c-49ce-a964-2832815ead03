<template>
    <!-- 已经选中的参考汇总 仅支持 更新和删除 -->
    <n-tooltip class="!rounded-2xl" :placement="isIpad ? 'bottom-end' : 'right'" v-model:show="show" trigger="click" raw :show-arrow="false" passive>
        <template #trigger>
            <div class="flex-1 flex items-center justify-end gap-2 relative" ref="allSelectRef">
                <div
                    class="size-8 relative cursor-pointer"
                    v-for="referItem in createStore.selectedRefers"
                    :key="referItem.id"
                    :class="{
                        'selected-border rounded-lg !p-[1px]': isCheckingItem.id === referItem.id,
                    }"
                    @click.stop="handleItemActive(referItem)"
                >
                    <img v-if="referItem.cover" :src="renderStaticImage(referItem.cover)" class="rounded-lg size-full object-cover" />
                    <img v-else :src="referItem.imgUrl || referItem.img_url" class="rounded-lg size-full object-cover" />
                    <div class="size-full selected-mask rounded-lg" :class="[isCheckingItem.id === referItem.id ? 'opacity-100' : 'opacity-0']"></div>
                </div>
            </div>
        </template>

        <ReferItem
            v-show="isCheckingItem.id"
            ref="selectedReferItemRef"
            class="z-[100] tips-box !w-[280px] !p-4 !pt-3 !rounded-2xl"
            :item="isCheckingItem"
            :supportReferList="createStore.supportReferList"
            @delete="handleDeleteItem"
            @update="handleUpdateItem"
        />
    </n-tooltip>
</template>
<script setup>
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isIpad } = storeToRefs(useThemeStore());

import ReferItem from "@/components/create/ReferenceImage/components/ReferItem.vue";
import { useCreateStore } from "@/stores/create";
import { renderStaticImage } from "@/utils/tools.js";

const createStore = useCreateStore();
const selectedReferItemRef = ref();
const isCheckingItem = ref({ id: "" }); //正在查看的图像参考
const show = ref(false);

watch(
    () => show.value,
    (newVal) => {
        if (!newVal) {
            isCheckingItem.value = { id: "" };
        }
    }
);
// 更新参考项
const handleUpdateItem = (updatedItem) => {
    const { id } = updatedItem;
    if (!id) console.error("id is null");
    // 找到 item 的索引
    const index = createStore.selectedRefers.findIndex((item) => item.id === id);
    if (index !== -1) {
        let list = [...createStore.selectedRefers];
        list?.splice(index, 1, updatedItem);
        nextTick(() => {
            createStore.updateReferStore(list);
        });
    }

    isCheckingItem.value = updatedItem;
};
// 删除参考项
const handleDeleteItem = (id) => {
    if (!id) console.error("id is null");
    if (id === isCheckingItem.value.id) {
        isCheckingItem.value = { id: "" };
        show.value = false;
    }
    let list = [...createStore.selectedRefers];
    let newList = list?.filter((item) => item.id !== id);
    nextTick(() => {
        createStore.updateReferStore(newList);
    });
};

const handleItemActive = (referItem) => {
    isCheckingItem.value = referItem;
    show.value = true;
};
</script>
