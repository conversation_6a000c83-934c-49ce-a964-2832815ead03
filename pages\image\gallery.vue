<template>
    <div class="h-full relative select-none font-medium">
        <div class="flex h-full" :class="{ 'opacity-0': showPreview }">
            <CollectionPanel v-model:collectId="currentCollectionId" :hasDisabled="hasDisabled" @handleMoveFiles="handleMoveFiles" ref="collectionPanelRef" />
            <div class="h-full relative pt-6 text-text-2 flex-1 overflow-hidden" @mousedown.stop="onMouseDown" @mousemove.stop="onMouseMove" @mouseup.stop="onMouseUp" @mouseleave.stop="onMouseLeave">
                <div class="absolute top-0 left-0 right-0 bottom-0 pointer-events-none bg-fill-t-3 z-[9999]" v-if="hasDisabled"></div>
                <div class="sticky top-0 z-50 flex items-center justify-end gap-4 pr-4 pb-4" @mousedown.stop @click.stop>
                    <div class="flex items-center gap-4 mr-auto">
                        <n-icon size="24">
                            <IconsUnorganized v-if="currentCollectionId === unorganizedId" />
                            <IconsBookMark v-else />
                        </n-icon>
                        <div class="text-base">
                            <div v-if="currentCollectionId === unorganizedId">{{ t("COLLECTION_UNORGANIZED") }}</div>
                            <div v-else class="max-w-44 truncate">{{ currentCollectionName }}</div>
                        </div>
                        <div v-if="show30DayTips">
                            <n-tooltip placement="bottom" trigger="hover" :show-arrow="false" raw class="rounded-lg overflow-hidden max-w-96" ref="keptRef">
                                <template #trigger>
                                    <IconsAlert class="text-info-6 text-2xl cursor-pointer" />
                                </template>

                                <div class="px-3 py-2 rounded-lg bg-bg-6 text-text-2 border border-solid border-border-t-1">
                                    <div>{{ t("HISTORY_DEL_AUTO_TIPS", { day: 30 }) }}</div>
                                </div>
                            </n-tooltip>
                        </div>
                    </div>

                    <div v-show="selectStack.length > 0 && !batchProcessing" class="flex items-center gap-4 h-10">
                        <div class="batch-action-btn" @click="clearSelectedItems">
                            <n-icon size="20">
                                <IconsClose />
                            </n-icon>
                            <span>{{ selectStack.length }} {{ t("SHORT_SELECTED") }}</span>
                        </div>
                        <div v-show="selectStack.length === 1 && currentCollectionId !== unorganizedId" class="batch-action-btn" @click="handleSetCover">
                            <n-tooltip trigger="hover">
                                <template #trigger>
                                    <n-icon size="20">
                                        <IconsCover />
                                    </n-icon>
                                </template>
                                <span>{{ t("COLLECTION_SET_COVER") }} </span>
                            </n-tooltip>
                            <span class="max-2xl:hidden">{{ t("COLLECTION_SET_COVER") }} </span>
                        </div>
                        <div v-if="currentCollectionId === unorganizedId" class="batch-action-btn" @click="deleteSelectedItems">
                            <n-tooltip trigger="hover">
                                <template #trigger>
                                    <n-icon size="20">
                                        <IconsDele />
                                    </n-icon>
                                </template>
                                <span>{{ t("TOOLBAR_DELETE") }}</span>
                            </n-tooltip>
                            <span class="max-2xl:hidden">{{ t("TOOLBAR_DELETE") }}</span>
                        </div>
                        <div v-if="currentCollectionId !== unorganizedId" class="batch-action-btn" @click="handleBatchUnCollection">
                            <n-tooltip trigger="hover">
                                <template #trigger>
                                    <n-icon size="20">
                                        <IconsUnFavorite />
                                    </n-icon>
                                </template>
                                <span>{{ t("COLLECTION_REMOVE") }}</span>
                            </n-tooltip>
                            <span class="max-2xl:hidden">{{ t("COLLECTION_REMOVE") }}</span>
                        </div>

                        <div v-if="currentCollectionId === unorganizedId" class="batch-action-btn" @click="handleBatchSaveCollection('Add')">
                            <n-tooltip trigger="hover">
                                <template #trigger>
                                    <n-icon size="20">
                                        <IconsFavorite />
                                    </n-icon>
                                </template>
                                <span>{{ t("MENU_COLLECTION_TIPS") }}</span>
                            </n-tooltip>
                            <span class="max-2xl:hidden">{{ t("MENU_COLLECTION_TIPS") }}</span>
                        </div>
                        <div v-else class="batch-action-btn" @click="handleBatchSaveCollection('Move')">
                            <n-tooltip trigger="hover">
                                <template #trigger>
                                    <n-icon size="20">
                                        <IconsMoveTo />
                                    </n-icon>
                                </template>
                                <span>{{ t("COLLECTION_MOVE_TITLE") }}</span>
                            </n-tooltip>
                            <span class="max-2xl:hidden">{{ t("COLLECTION_MOVE_TITLE") }}</span>
                        </div>
                        <div @click="handleBatchDownload">
                            <ClientOnly>
                                <BatchDown ref="batchDownLoadRef" @completed="stopBatchAction()" @updateIndex="updateBatchIndex" />
                            </ClientOnly>
                        </div>
                    </div>

                    <div v-show="batchProcessing" class="flex items-center gap-2 text-xs rounded-lg h-10 overflow-hidden mr-4 bg-bg-2 hover:text-text-opt-3">
                        <div class="p-2 flex items-center gap-2 pl-4">
                            <n-icon size="16" class="text-primary">
                                <IconsSpinLoading />
                            </n-icon>
                            <div class="opacity-70 flex items-center gap-1.5">
                                <span>{{ batchProgress }}</span>
                                <span>{{ t("IMAGE_GENERATE_PROCESS") }}</span>
                            </div>
                        </div>
                        <div @click="stopBatchAction" class="flex items-center gap-1 h-full border-l border-solid dark:border-dark-text px-4 cursor-pointer">
                            <n-icon size="18">
                                <IconsClose />
                            </n-icon>
                            <span>{{ t("COMMON_BTN_CANCEL") }}</span>
                        </div>
                    </div>

                    <n-popover trigger="click" placement="bottom" :show-arrow="false" raw class="rounded-lg" ref="sortPopoverRef">
                        <template #trigger>
                            <div
                                class="h-10 flex items-center p-2 gap-0.5 cursor-pointer rounded-lg bg-bg-2 hover:text-text-opt-3"
                                v-show="selectStack.length === 0"
                                @click="handleTrackFn('sort_btn')"
                            >
                                <n-icon size="20">
                                    <IconsChange />
                                </n-icon>
                                <n-icon class="text-text-4" size="20">
                                    <IconsArrowLine />
                                </n-icon>
                            </div>
                        </template>

                        <div class="min-w-48 p-2 rounded-lg flex flex-col gap-1 overflow-hidden bg-bg-6">
                            <div @click="changeSortType('Descending')" class="layout-bar-option">
                                <n-icon size="20" class="rotate-180">
                                    <IconsArrowTop />
                                </n-icon>
                                <span>{{ t("COLLECTION_ORDER_DESC") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="sortType === 'Descending'" />
                                </n-icon>
                            </div>
                            <div @click="changeSortType('Ascending')" class="layout-bar-option">
                                <n-icon size="20">
                                    <IconsArrowTop />
                                </n-icon>
                                <span>{{ t("COLLECTION_ORDER_ASC") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="sortType === 'Ascending'" />
                                </n-icon>
                            </div>
                        </div>
                    </n-popover>

                    <n-popover trigger="click" placement="bottom" :show-arrow="false" raw class="rounded-lg" ref="layoutPopoverRef">
                        <template #trigger>
                            <div
                                class="h-10 flex items-center p-2 gap-0.5 cursor-pointer rounded-lg bg-bg-2 hover:text-text-opt-3"
                                v-show="selectStack.length === 0"
                                @click="handleTrackFn('layout_btn')"
                            >
                                <n-icon size="20">
                                    <IconsEqualHeight v-if="chooseLayoutType === 'EqualHeight'" />
                                    <IconsSquare v-else />
                                </n-icon>
                                <n-icon class="text-text-4" size="20">
                                    <IconsArrowLine />
                                </n-icon>
                            </div>
                        </template>

                        <div class="min-w-48 p-2 rounded-lg flex flex-col gap-1 overflow-hidden bg-bg-6">
                            <div @click="handleSelectLayout('layout', 'EqualHeight')" class="layout-bar-option">
                                <n-icon size="20">
                                    <IconsEqualHeight />
                                </n-icon>
                                <span>{{ t("COLLECTION_LAYOUT_RIVER") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="chooseLayoutType === 'EqualHeight'" />
                                </n-icon>
                            </div>
                            <div @click="handleSelectLayout('layout', 'Square')" class="layout-bar-option">
                                <n-icon size="20">
                                    <IconsSquare />
                                </n-icon>
                                <span>{{ t("COLLECTION_LAYOUT_GRID") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="chooseLayoutType === 'Square'" />
                                </n-icon>
                            </div>

                            <div class="h-[1px] my-3 mx-2.5 bg-border-3"></div>

                            <div @click="handleSelectLayout('size', 'Small')" class="layout-bar-option">
                                <n-icon size="20">
                                    <IconsSizeSmall />
                                </n-icon>
                                <span>{{ t("COLLECTION_SIZE_SMALL") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="chooseLayoutSize === 'Small'" />
                                </n-icon>
                            </div>
                            <div @click="handleSelectLayout('size', 'Medium')" class="layout-bar-option">
                                <n-icon size="20">
                                    <IconsSizeMedium />
                                </n-icon>
                                <span>{{ t("COLLECTION_SIZE_MEDIUM") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="chooseLayoutSize === 'Medium'" />
                                </n-icon>
                            </div>
                            <div @click="handleSelectLayout('size', 'Large')" class="layout-bar-option">
                                <n-icon size="20">
                                    <IconsSizeLarge />
                                </n-icon>
                                <span>{{ t("COLLECTION_SIZE_LARGE") }}</span>
                                <n-icon size="20" class="ml-auto">
                                    <IconsSuccess v-show="chooseLayoutSize === 'Large'" />
                                </n-icon>
                            </div>
                        </div>
                    </n-popover>
                </div>

                <div ref="scrollWrapperRef" class="h-full">
                    <!-- 虚拟滚动区域  未收藏的图片列表-->
                    <VirtualScroll
                        ref="virtualScrollRef"
                        :items="dataFlatList"
                        :per-row="itemsPerRow"
                        :selectable="!showPreview"
                        group-key="createTimeStr"
                        :image-key="chooseLayoutSize === 'Large' ? 'largeUrl' : 'renderUrl'"
                        :layout-type="chooseLayoutSize"
                        show-header
                        :header-height="44"
                        :fill-type="chooseLayoutType"
                        :status="loadStatus"
                        @bounding-select="handleGetSelectedData"
                        @item-click="handlePreviewItem"
                        @scroll-to-lower="getPageList"
                    >
                        <!-- 加载状态 -->
                        <template #footer>
                            <LoadStatus :status="pageStatus" @load-more="getPageList" />
                        </template>
                    </VirtualScroll>
                </div>
            </div>
        </div>

        <div class="select-text">
            <ClientOnly>
                <Preview
                    ref="previewRef"
                    v-model:show="showPreview"
                    :total="dataFlatList.length"
                    :viewHigh="chooseLayoutSize === 'Large'"
                    :list="dataFlatList"
                    :isFavorite="currentCollectionId !== unorganizedId"
                    @delItem="deleteItem"
                    @loadMore="getPageList"
                    @unFavorite="handleUnFavorite"
                />
            </ClientOnly>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { useCurrentLang } from "@/stores/system-config";
import { getHistoryList, queryImgByCollectNew, batchDelImg, batchSaveCollection, deleteResult, batchReduceCollection, batchMoveToCollections, updateCollectCover, batchReduceDel } from "@/api";
import { processData, formatPonyV6Prompt } from "@/utils/tools";
import { getRelativeDateLabel } from "@/utils/timeFormat";
import useWindowResize from "@/hook/windowResize";
import { CloseRoundFill, Alert } from "@/icons/index.js";
import FavoriteModal from "@/components/FavoriteModal.vue";
import VirtualScroll from "@/components/business/virtual-scroll/index.vue";
import LoadStatus from "@/components/business/load-status/index.vue";
import { useUserProfile, useForceUpdatePageState, useShareCollect, useDefUnCollectionsConfirm } from "@/stores";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
import { useSyncAction } from "@/stores/syncAction";
import { useUpdateCloudStorage, useDeleteImageConfirm, useInitCollection } from "@/hook/updateAccount";
import { NIcon, NCheckbox } from "naive-ui";
import { SUB_EL, SUBSCRIBE_PERMISSION, KEEPALIVE_PAGES } from "@/utils/constant.js";
import CollectionPanel from "@/components/collections/CollectionPanel.vue";
import { nextTick, ref, watchEffect } from "vue";
defineOptions({
    name: KEEPALIVE_PAGES.GALLERY_WEB,
});
const userProfile = useUserProfile();
const updateSeo = () => {
    useSeoMeta({
        title: () => t("SEO_META.SEO_GALLERY_TITLE", { Username: userProfile.user.userName }),
        ogTitle: () => t("SEO_META.SEO_GALLERY_TITLE", { Username: userProfile.user.userName }),
        description: () => t("SEO_META.SEO_GALLERY_DESC"),
        ogDescription: () => t("SEO_META.SEO_GALLERY_DESC"),
    });
};

const { showMessage } = useModal();

const defUnCollectionsConfirm = useDefUnCollectionsConfirm();
//修改当前组件的预览状态
const forceUpdatePageState = useForceUpdatePageState();
const { checkPermission, checkPermissionNotModal } = useSubPermission();

// 同步图片操作(删除/修改等)
const syncAction = useSyncAction();

const hasDisabled = ref(false);
const loadStatus = ref("more");

//未整理的默认ID标识为-1
const unorganizedId = -1;
const currentCollectionId = ref(unorganizedId);

const collectionPanelRef = ref(null);
const { checkShowVipNotice } = useVipNotice();
const closePreview = () => {
    showPreview.value = false;
};

watch(
    () => forceUpdatePageState.key["/image/gallery"],
    () => {
        if (showPreview.value) {
            //HISTORY 页面 HISTORY 菜单时 关闭预览 滚动[不]置顶
            closePreview();
            return;
        }
        virtualScrollRef.value?.scrollTo({
            top: 0,
        });
    }
);

watch(
    () => currentCollectionId.value,
    () => {
        //请求数据
        pages.value.pageNum = 1;
        pages.value.maxPage = 1;
        dataFlatList.value = [];
        loadStatus.value = "more";
        clearSelectedItems();
        nextTick(() => {
            getPageList();
            virtualScrollRef.value?.scrollTo({
                top: 0,
            });
        });
    }
);

const pageStatus = computed(() => {
    if (loadStatus.value === "loading") {
        return "loading";
    }
    if (loadStatus.value === "noData" && dataFlatList.value.length === 0) {
        return "noData";
    }
    if (loadStatus.value === "noMore") {
        return "noMore";
    }
    return "more";
});

const show30DayTips = computed(() => {
    const t1 = currentCollectionId.value === unorganizedId;
    const t2 = !checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
    return t1 && t2;
});

const handleTrackFn = (el) => {
    window.trackEvent("Gallery", { el });
};

// 添加到收藏夹或者移动到收藏夹
const handleMoveFiles = ({ command, targetId }) => {
    const param = {};
    if (command === "Add") {
        param.classifyId = targetId;
    } else if (command === "Move") {
        param.classifyId = targetId;
        param.oldClassifyId = currentCollectionId.value;
    }
    moveOrAddBatchProcess(command, param);
};

const windowResize = useWindowResize();

const previewRef = ref(null);
const showPreview = ref(false);
// 引用滚动容器
// 配置参数
const itemsPerRow = ref(10); // 每行的元素数量

//获取page数据
const pages = ref({
    pageNum: 1,
    pageSize: 200,
    total: 1,
    maxPage: 1,
});

// 排序
const sortTypeDefault = "Descending";
const chooseLayoutTypeDefault = "Square";
const chooseLayoutSizeDefault = "Medium";

const sortPopoverRef = ref(null);
const sortType = ref(userProfile.userConfig.historyOrder || sortTypeDefault);
const changeSortType = (type, hasDef = false) => {
    sortType.value = type;
    sortPopoverRef.value.setShow(false);
    pages.value.pageNum = 1;
    clearSelectedItems();
    getPageList();
    !hasDef && handleTrackFn(`sort=${type}`);
};
//尺寸变更
const layoutPopoverRef = ref(null);
const chooseLayoutType = ref(userProfile.userConfig.historyType || chooseLayoutTypeDefault);
const chooseLayoutSize = ref(userProfile.userConfig.historyTypeSize || chooseLayoutSizeDefault);

const handleSelectLayout = (key, val) => {
    if (key === "size") {
        chooseLayoutSize.value = val;
        initConf();
    } else {
        chooseLayoutType.value = val;
    }
    layoutPopoverRef.value.setShow(false);
    syncQueryConf();
    handleTrackFn(`layout=${chooseLayoutType.value}#size=${chooseLayoutSize.value}`);
};

//数据组
const dataFlatList = ref([]);
const currentLang = useCurrentLang();
const processItem = (item) => {
    let genInfo = item.genInfo || item;
    const formatData = formatPonyV6Prompt(genInfo, "output");
    let timeStr = getRelativeDateLabel(item.createTime, currentLang.lang);
    const isNotDate = isNaN(Date.parse(timeStr));
    return {
        ...genInfo,
        ...item,
        prompt: formatData.prompt,
        sensitive: item.sensitiveMessage,
        createTimeStr: timeStr,
        needI18n: isNotDate,
        renderUrl: item.miniThumbnailUrl || item.highMiniUrl || item.thumbnailUrl || item.highThumbnailUrl || item.imgUrl,
        largeUrl: item.highMiniUrl || item.thumbnailUrl || item.highThumbnailUrl || item.imgUrl,
    };
};
const getPageList = async () => {
    if (loadStatus.value === "loading") {
        return false;
    }

    let { pageSize, pageNum, maxPage = 1 } = pages.value;
    if (pageNum > maxPage) {
        return false;
    }
    loadStatus.value = "loading";

    //默认请求历史记录
    let requestCallback = getHistoryList;
    const param = {
        ...pages.value,
        collationName: sortType.value,
        notCollection: true,
    };
    //请求收藏夹
    if (currentCollectionId.value !== unorganizedId) {
        requestCallback = queryImgByCollectNew;
        param.classifyId = currentCollectionId.value;
        delete param.notCollection;
    }
    try {
        const { data, status } = await requestCallback(param);
        if (currentCollectionId.value !== unorganizedId && currentCollectionId.value !== param.classifyId) {
            return;
        }
        if (status !== 0) {
            return;
        }
        if (!data) {
            return;
        }
        const newArray = data?.resultList || [];
        const arr = newArray.map((item) => {
            return processItem(item);
        });
        if (pageNum === 1) {
            dataFlatList.value = [...arr];
        } else {
            dataFlatList.value = [...dataFlatList.value, ...arr];
        }
        pages.value.total = data.total;
        pages.value.pageNum = pageNum + 1;
        pages.value.maxPage = Math.ceil(data.total / pageSize);
        syncQueryConf();
        // TODO: 延迟加载，避免骨架切换太快导致视觉闪烁
        // setTimeout(() => {
        loadStatus.value = !dataFlatList.value?.length ? "noData" : arr.length < pageSize ? "noMore" : "more";
        // }, 150);
        return Promise.resolve();
    } catch (error) {
        console.error(error, "error");
        return Promise.resolve();
    }
};

watch(
    () => windowResize.width.value,
    (v) => {
        nextTick(() => {
            if (hasActive.value) {
                console.log("init-conf");
                initConf();
            }
        });
    }
);

//初始化全局配置
const virtualScrollRef = ref();
const scrollWrapperRef = ref();

const initConf = () => {
    const virtualBox = scrollWrapperRef.value.clientWidth;
    const layoutWidth = virtualBox - 16;
    const size = chooseLayoutSize.value;
    let multiple = 1;
    if (size === "Small") {
        multiple *= 2;
    } else if (size === "Large") {
        multiple /= 1.3;
    }

    let base = 10;
    if (layoutWidth <= 1660) {
        base = 8;
    }
    if (layoutWidth <= 1440) {
        base = 6;
    }
    if (layoutWidth <= 1144) {
        base = 4;
    }
    if (layoutWidth <= 768) {
        base = 3;
    }
    if (layoutWidth <= 480) {
        base = 2;
    }

    itemsPerRow.value = Math.floor(Math.max(2, base * multiple));
};
// 行高列表与总行数

//同步查询配置
const syncQueryConf = async () => {
    //排序方式 不是已持久化的值
    const v1 = sortType.value !== userProfile.userConfig.historyOrder;
    //填充方式 不是已持久化的值
    const v2 = chooseLayoutType.value !== userProfile.userConfig.historyType;
    //展示尺寸 不是已持久化的值
    const v3 = chooseLayoutSize.value !== userProfile.userConfig.historyTypeSize;
    //任意 1个条件发生变化  update info
    if (v1 || v2 || v3) {
        const param = {
            historyOrder: sortType.value,
            historyType: chooseLayoutType.value,
            historyTypeSize: chooseLayoutSize.value,
        };
        userProfile.syncUpdateUserConfig(param);
    }
};

// 选中的全部图片
const selectStack = ref([]);

//清空选中项目
const clearSelectedItems = () => {
    selectStack.value = [];
    virtualScrollRef.value?.clearSelect();
};

//正在批量操作中
const batchProcessing = ref(false);
const isBatchDown = ref(false);
const batchRawList = ref([]);
const batchActionSize = 20;
const batchActionIndex = ref(0);
//批量操作 进度
const batchProgress = computed(() => {
    const totalCount = batchRawList.value.length;
    let progressCount = batchActionIndex.value;
    if (!isBatchDown.value) {
        progressCount = batchActionSize * (batchActionIndex.value + 1);
    }
    return `${Math.min(totalCount, progressCount)} / ${totalCount}`;
});
const handleBatch = async () => {
    if (batchProcessing.value) {
        return Promise.resolve(false);
    }
    if (selectStack.value.length === 0) {
        return Promise.resolve(false);
    }
    hasCancelPorcess.value = false;
    batchProcessing.value = true;
    batchRawList.value = [...selectStack.value];
    return Promise.resolve(true);
};
// 轮询 删除选中项目
// 休眠函数

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
const hasCancelPorcess = ref(false);
const deleteSelectedItems = async () => {
    if (selectStack.value.length === 0) {
        return;
    }
    handleTrackFn(`delete_image`);
    const delCheck = await useDeleteImageConfirm();
    if (!delCheck) {
        return;
    }
    handleTrackFn(`delete_count=${selectStack.value.length}`);
    const res = await handleBatch();
    if (!res) {
        return;
    }

    //轮询次数
    const loopCount = Math.ceil(batchRawList.value.length / batchActionSize);
    const list = batchRawList.value.map(({ imgName, promptId }) => ({ imgName, promptId }));
    const loopTask = async () => {
        const startIndex = batchActionIndex.value * batchActionSize;
        const chunkList = list.slice(startIndex, batchActionSize + startIndex);
        // requestCallback.chunkList = chunkList;

        const { status, message, data } = await batchDelImg(chunkList);
        if (status !== 0) {
            stopBatchAction();
            showMessage({
                style: { width: "420px" },
                showCancel: false,
                confirmBtn: t("COMMON_BTN_OK"),
                content: h("div", null, message),
                icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(CloseRoundFill) }),
                title: t("TOAST_TITLE_WARNING"),
            });
            return;
        }
        const toDelete = new Set(chunkList.map((item) => item.imgName));
        syncAction.publish("batchDelImg", toDelete);
        // //取消并收藏
        // if (currentCollectionId.value !== unorganizedId) {
        //     useUpdateCloudStorage(data);
        //     collectionPanelRef.value.updateCount({
        //         changeCount: chunkList.length, //变化数量
        //         fromId: requestCallback.classifyId,
        //         toId: null,
        //     });
        // }

        batchActionIndex.value++;
        if (batchActionIndex.value >= loopCount) {
            stopBatchAction();
            return;
        }
        await sleep(1000);
        if (hasCancelPorcess.value) {
            return;
        }
        loopTask();
    };
    loopTask();
};

const deleteBatchEmit = (toDelete) => {
    dataFlatList.value = dataFlatList.value.filter((item) => !toDelete.has(item.imgName));
    if (dataFlatList.value.length === 0) {
        closePreview();
        loadStatus.value = "noData";
        return;
    }
    if (showPreview.value) {
        previewRef.value.forceUpdateIndex(previewRef.value.getCUrrentIndex());
    }
};

// 批量解除收藏
const handleBatchUnCollection = async () => {
    handleTrackFn(`remove_from_collection`);
    if (!defUnCollectionsConfirm.isConfirm) {
        let hasChecked = false;
        const hasNext = await showMessage({
            style: { width: "480px" },
            confirmBtn: t("COLLECTION_UN_COLLECTION"),
            content: h("div", null, [
                h("p", null, t("COLLECTION_REMOVE_CONTENT")),
                h(
                    NCheckbox,
                    {
                        class: "mt-4",
                        style: {
                            "--n-color-checked": "#6904E9", // 修改选中时的颜色
                            "--n-border-checked": "1px solid #6904E9", // 修改选中时的颜色
                            "--n-border-focus": "1px solid #6904E9", // 修改选中时的颜色
                            "--n-box-shadow-focus": "none", // 修改选中时的颜色
                            "--n-border": "1px solid #6904E9", // 修改选中时的颜色
                        },
                        "on-update:checked": (val) => {
                            hasChecked = val;
                        },
                    },
                    {
                        default: () =>
                            h(
                                "span",
                                {
                                    class: " dark:text-dark-text",
                                },
                                t("COLLECTION_REMOVE_NOT_CONFIRM")
                            ),
                    }
                ),
            ]),
            icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_ATTEN"),
        })
            .then(() => {
                defUnCollectionsConfirm.setDefConfirm(hasChecked);
                return Promise.resolve(true);
            })
            .catch((_) => {
                return Promise.resolve(false);
            });

        if (!hasNext) {
            return;
        }
    }
    handleTrackFn(`remove_from_collection_count=${selectStack.value.length}`);
    if (selectStack.value.length === 0) {
        return;
    }
    const res = await handleBatch();
    if (!res) {
        return;
    }

    const param = { classifyId: currentCollectionId.value };
    //轮询次数
    const loopCount = Math.ceil(batchRawList.value.length / batchActionSize);
    const list = batchRawList.value.map(({ imgName, promptId }) => ({ fileName: imgName, promptId }));
    const loopTask = async () => {
        const startIndex = batchActionIndex.value * batchActionSize;
        const chunkList = list.slice(startIndex, batchActionSize + startIndex);
        param.collectBatchList = chunkList;
        const { status, message, data } = await batchReduceCollection(param);

        if (status !== 0) {
            stopBatchAction();
            showMessage({
                style: { width: "420px" },
                showCancel: false,
                confirmBtn: t("COMMON_BTN_OK"),
                content: h("div", null, message),
                icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(CloseRoundFill) }),
                title: t("TOAST_TITLE_WARNING"),
            })
                .then((_) => {})
                .catch((_) => {});
            return;
        }

        useUpdateCloudStorage(data);
        collectionPanelRef.value.updateCount({
            changeCount: chunkList.length, //变化数量
            fromId: param.classifyId,
            toId: null,
        });
        deleteBatchEmit(new Set(chunkList.map((item) => item.fileName)));
        batchActionIndex.value++;
        if (batchActionIndex.value >= loopCount) {
            stopBatchAction();
            return;
        }
        await sleep(1000);
        if (hasCancelPorcess.value) {
            return;
        }
        loopTask();
    };
    loopTask();
};

const shareCollect = useShareCollect();
//当前选中的收藏夹名称
const currentCollectionName = computed(() => (shareCollect.collectList || []).find((item) => item.id === currentCollectionId.value)?.collectName || "");
//批量保存收藏夹
const handleBatchSaveCollection = async (command) => {
    if (batchProcessing.value) {
        return;
    }
    const waitProcessCount = selectStack.value.length;
    if (waitProcessCount === 0) {
        return;
    }
    const param = {};
    const favoriteConf = {
        base: null,
        btn: t("ADD_BUTTON"),
        "onUpdate:base": (val) => Object.assign(param, val),
    };
    //假如是新增 则需要校验容量是否足够
    if (command === "Add") {
        handleTrackFn(`add_to_collection`);
        const useCloudCount = userProfile.user.usedCollectNum + waitProcessCount;
        const allowCollection = checkPermissionNotModal(SUBSCRIBE_PERMISSION.CLOUD_STORAGE_COUNT, useCloudCount);
        if (!allowCollection) {
            showMessage({
                showCancel: false,
                style: { width: "420px" },
                confirmBtn: t("COMMON_BTN_OK"),
                content: h("div", null, [h("p", { class: "tracking-wider" }, t("COLLECTION_STORAGE_LIMIT_MESSAGE"))]),
                icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
                title: t("DIALOG_TITLE_NOTICE"),
            });
            return;
        }
    } else {
        favoriteConf.title = t("COLLECTIONS_MOVE_TITLE");
        favoriteConf.subTitle = t("COLLECTIONS_MOVE_DESC");
        favoriteConf.btn = t("COLLECTIONS_MOVE_BTN");
        favoriteConf.filterId = currentCollectionId.value;
        param.oldClassifyId = currentCollectionId.value;
        handleTrackFn(`move_to_collection`);
    }
    const hasNext = await showMessage({
        style: { width: "420px", paddingLeft: "8px", paddingRight: "8px" },
        confirmBtn: favoriteConf.btn,
        content: h(FavoriteModal, favoriteConf),
    })
        .then((_) => {
            return Promise.resolve(true);
        })
        .catch((_) => {
            return Promise.resolve(false);
        });
    if (!hasNext || param.classifyId === param.oldClassifyId) {
        return;
    }

    moveOrAddBatchProcess(command, param);
};
//移动或者新增的批量处理
const moveOrAddBatchProcess = async (command, param) => {
    const res = await handleBatch();
    if (!res) {
        return;
    }
    let requestCallback = batchSaveCollection;
    if (command === "Move") {
        handleTrackFn(`move_to_collection_count=${selectStack.value.length}`);
        requestCallback = batchMoveToCollections;
    } else {
        handleTrackFn(`add_to_collection_count=${selectStack.value.length}`);
    }

    //轮询次数
    const loopCount = Math.ceil(batchRawList.value.length / batchActionSize);
    const pendingData = batchRawList.value.map(({ imgName, promptId }) => ({ fileName: imgName, promptId }));
    const loopTask = async () => {
        const startIndex = batchActionIndex.value * batchActionSize;
        const collectBatchList = pendingData.slice(startIndex, batchActionSize + startIndex);
        param.collectBatchList = collectBatchList;
        const { status, message, data } = await requestCallback(param);
        if (status !== 0) {
            stopBatchAction();
            showMessage({
                style: { width: "420px" },
                showCancel: false,
                confirmBtn: t("COMMON_BTN_OK"),
                content: h("div", null, message),
                icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(CloseRoundFill) }),
                title: t("TOAST_TITLE_WARNING"),
            });
            return;
        }
        useUpdateCloudStorage(data);
        collectionPanelRef.value.updateCount({
            changeCount: collectBatchList.length, //变化数量
            fromId: param.oldClassifyId,
            toId: param.classifyId,
        });
        deleteBatchEmit(new Set(collectBatchList.map((item) => item.fileName)));
        batchActionIndex.value++;
        if (batchActionIndex.value >= loopCount) {
            stopBatchAction();
            return;
        }
        await sleep(1000);
        if (hasCancelPorcess.value) {
            return;
        }
        loopTask();
    };
    loopTask();
};

//批量下载
const batchDownLoadRef = ref(null);

const handleBatchDownload = async () => {
    handleTrackFn(`download_image#download_count=${selectStack.value.length}`);
    const allowNext = await checkPermission(SUBSCRIBE_PERMISSION.BATCH_DOWNLOAD, { triggerEl: SUB_EL.BATCH_DOWNLOAD });
    if (!allowNext) {
        return;
    }
    const maxCount = 500;

    if (selectStack.value.length > maxCount) {
        showMessage({
            showCancel: false,
            style: { width: "420px" },
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, [h("p", { class: "tracking-wider" }, t("BATCH_DOWNLOAD_MAX_COUNT", { maxCount }))]),
            icon: h(NIcon, { size: 32, class: "text-danger-6" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_NOTICE"),
        });
        return;
    }
    const res = await handleBatch();
    if (!res) {
        return;
    }

    checkShowVipNotice(`batch_download`);

    isBatchDown.value = true;
    const downList = batchRawList.value.map(({ imgUrl, highThumbnailUrl, thumbnailUrl }) => ({ fileUrl: highThumbnailUrl || imgUrl, highThumbnailUrl: highThumbnailUrl || imgUrl }));
    batchDownLoadRef.value?.downloadZip(downList);
};
//下载时更新进度
const updateBatchIndex = (index) => {
    const total = batchRawList.value.length;
    batchActionIndex.value = Math.min(index, total);
};

// 批量操作完成 /中断
const stopBatchAction = () => {
    hasCancelPorcess.value = true;
    let index = batchActionIndex.value;
    batchActionIndex.value = 0;
    //下载操作单独处理,不清空选中状态
    if (isBatchDown.value) {
        batchDownLoadRef.value?.cancelDownload();
        isBatchDown.value = false;
        index = 0;
    }
    //先计算已经处理了多少(批量处理事事务，具有原子性,batchActionIndex.value是已经处理完成了的)
    const doneCount = Math.min(batchActionSize * index, batchRawList.value.length);
    // 全部处理完成
    if (doneCount === batchRawList.value.length) {
        clearSelectedItems();
    } else {
        selectStack.value.splice(0, doneCount);
    }
    batchProcessing.value = false;
};

const updateEmit = ({ promptId, updateKey, updateValue, imgName }) => {
    const index = dataFlatList.value.findIndex((item) => item.imgName === imgName);
    const current = dataFlatList.value[index];
    current[updateKey] = updateValue;
    dataFlatList.value.splice(index, 1, current);
    if (dataFlatList.value.length === 0) {
        loadState.value = "noData";
    }
    if (showPreview.value) {
        previewRef.value.forceUpdateIndex(index);
    }
};
const deleteItemEmit = ({ imgName }) => {
    if (currentCollectionId.value !== unorganizedId) {
        return;
    }
    dataFlatList.value = dataFlatList.value.filter((item) => item.imgName !== imgName);
    if (dataFlatList.value.length === 0) {
        loadState.value = "noData";
    }
    if (showPreview.value) {
        previewRef.value.forceUpdateIndex(previewRef.value.getCUrrentIndex());
    }
};
const handleGetSelectedData = (data) => {
    selectStack.value = data;
};
const handlePreviewItem = ({ imgName }) => {
    let index = dataFlatList.value.findIndex((item) => item.imgName === imgName);
    if (index === -1) return;
    showPreview.value = true;
    nextTick(() => {
        previewRef.value.forceUpdateIndex(index);
    });
};

//删除图片
const deleteItem = async (promptId, imgName) => {
    handleTrackFn("delete_count=1");
    const delCheck = await useDeleteImageConfirm();
    if (!delCheck) {
        return;
    }
    confirmDel(promptId, imgName);
};

const delLoading = ref(false);
const confirmDel = async (promptId, imgName) => {
    if (delLoading.value) {
        openToast.info(t("DEL_TIPS"));
        return;
    }
    delLoading.value = true;
    const task = dataFlatList.value.find((item) => item.imgName === imgName);
    const res = await handleDel(promptId, imgName, task.loginName);
    delLoading.value = false;
    if (!res) {
        return;
    }
    const toDelete = new Set([imgName]);
    syncAction.publish("batchDelImg", toDelete);
    useInitCollection();
};

const handleDel = async (promptId, imgName, loginName) => {
    return new Promise(async (resolve) => {
        let requestCallback = deleteResult;
        let param = { promptId, imgName, loginName };
        if (currentCollectionId.value !== unorganizedId) {
            requestCallback = batchReduceDel;
            param = {
                classifyId: currentCollectionId.value,
                collectBatchList: [{ fileName: imgName, promptId }],
            };
        }
        const { status, message } = await requestCallback(param);
        resolve(status === 0);
        if (status != 0) {
            openToast.error(message);
            return;
        }
        openToast.success(message);
    });
};

//解除单张图片收藏
const handleUnFavorite = async ({ imgName, promptId }) => {
    handleTrackFn(`uncollected`);
    if (!defUnCollectionsConfirm.isConfirm) {
        let hasChecked = false;
        const hasNext = await showMessage({
            style: { width: "480px" },
            confirmBtn: t("COLLECTION_UN_COLLECTION"),
            content: h("div", null, [
                h("p", null, t("COLLECTION_REMOVE_CONTENT")),
                h(
                    NCheckbox,
                    {
                        class: "mt-4",
                        style: {
                            "--n-color-checked": "#6904E9", // 修改选中时的颜色
                            "--n-border-checked": "1px solid #6904E9", // 修改选中时的颜色
                            "--n-border-focus": "1px solid #6904E9", // 修改选中时的颜色
                            "--n-box-shadow-focus": "none", // 修改选中时的颜色
                            "--n-border": "1px solid #6904E9", // 修改选中时的颜色
                        },
                        "on-update:checked": (val) => {
                            hasChecked = val;
                        },
                    },
                    {
                        default: () =>
                            h(
                                "span",
                                {
                                    class: " dark:text-dark-text",
                                },
                                t("COLLECTION_REMOVE_NOT_CONFIRM")
                            ),
                    }
                ),
            ]),
            icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_ATTEN"),
        })
            .then(() => {
                defUnCollectionsConfirm.setDefConfirm(hasChecked);
                return Promise.resolve(true);
            })
            .catch((_) => {
                return Promise.resolve(false);
            });

        if (!hasNext) {
            return;
        }
    }
    handleTrackFn(`uncollected=1`);

    const param = { classifyId: currentCollectionId.value };
    param.collectBatchList = [{ fileName: imgName, promptId }];
    const { status, message, data } = await batchReduceCollection(param);
    if (status !== 0) {
        showMessage({
            style: { width: "420px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, message),
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(CloseRoundFill) }),
            title: t("TOAST_TITLE_WARNING"),
        })
            .then((_) => {})
            .catch((_) => {});
        return;
    }

    useUpdateCloudStorage(data);
    collectionPanelRef.value.updateCount({
        changeCount: param.collectBatchList.length, //变化数量
        fromId: param.classifyId,
        toId: null,
    });
    deleteBatchEmit(new Set(param.collectBatchList.map((item) => item.fileName)));
};

//生图完成
const latestCompletedMission = (taskList) => {
    if (currentCollectionId.value !== unorganizedId) {
        return;
    }
    //获取前后20条数据，排除重复的
    const elements = new Set();
    (dataFlatList.value.slice(0, 20) || []).map((item) => elements.add(item.imgName));
    (dataFlatList.value.slice(-20) || []).map((item) => elements.add(item.imgName));
    //任务二维数组扁平化
    let list = taskList.reduce((acc, item) => {
        const { img_urls, genInfo, ...rest } = item;
        const flattenedList = (img_urls || []).map((listItem) => ({
            ...rest,
            ...listItem,
            isPublic: 0,
            createTime: rest.createTimestamp,
        }));
        return acc.concat(flattenedList);
    }, []);
    list = list.filter((item) => !elements.has(item.imgName));
    if (list.length === 0) {
        return;
    }
    list = list.map((i) => {
        return processItem(i);
    });
    //排序方式不一致，不进行数据拼接
    if (sortType.value !== sortTypeDefault) {
        appendData(list);
        return;
    }
    dataFlatList.value.unshift(...list);
    pages.value.total += list.length;
    virtualScrollRef.value?.reProcessData();
};

const appendData = (list) => {
    // 已经加载了最后 的数据了，如果未加载 则不进行数据的拼接
    const lastDataLoaded = dataFlatList.value.length === pages.value.total;
    if (!lastDataLoaded) {
        return;
    }

    dataFlatList.value.push(...list);
    virtualScrollRef.value?.reProcessData();
    pages.value.total += list.length;
};
//收藏指定的某一个文件
const collectedFileItem = ({ promptId, imgName }) => {
    // 只在未整理模式，删除指定图片
    if (currentCollectionId.value === unorganizedId) {
        deleteBatchEmit(new Set([imgName]));
    }
};

//设置收藏夹封面
const handleSetCover = async () => {
    handleTrackFn("set_as_cover");
    const { imgName } = selectStack.value.pop();
    const id = currentCollectionId.value;
    const { miniThumbnailUrl, highMiniUrl, thumbnailUrl, highThumbnailUrl, imgUrl } = dataFlatList.value.find((item) => item.imgName === imgName) || {};
    const cover = miniThumbnailUrl || highMiniUrl || thumbnailUrl || highThumbnailUrl || imgUrl;
    if (!cover) {
        return;
    }
    const { status, message } = await updateCollectCover({ id, cover });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    collectionPanelRef.value.updateItemCover(id, cover);
};
//过滤unorganized 被收藏的 图片
const hasActive = ref(true);
onActivated(() => {
    nextTick(updateSeo);
    hasActive.value = true;
    initConf();
});
onDeactivated(() => {
    hasActive.value = false;
});
const initPageData = async () => {
    await getPageList();
    nextTick(() => {
        syncAction.subscribe("latestSuccessfulMission", latestCompletedMission);
    });
};
//订阅的自定义导入成功刷新
const customImportFileReloadPage = () => {
    closePreview();
    changeSortType(sortType.value, true);
};
onMounted(async () => {
    syncAction.subscribe("updateImg", updateEmit);
    syncAction.subscribe("delImg", deleteItemEmit);
    syncAction.subscribe("batchDelImg", deleteBatchEmit);
    syncAction.subscribe("reloadPage", customImportFileReloadPage);
    syncAction.subscribe("collectedItem", collectedFileItem);
    syncAction.subscribe("closePreview", closePreview);
    if (import.meta.client) {
        initPageData();
    }
});
onBeforeUnmount(() => {
    syncAction.unsubscribe("updateImg", updateEmit);
    syncAction.unsubscribe("delImg", deleteItemEmit);
    syncAction.unsubscribe("batchDelImg", deleteBatchEmit);
    syncAction.unsubscribe("reloadPage", customImportFileReloadPage);
    syncAction.unsubscribe("collectedItem", collectedFileItem);
    syncAction.unsubscribe("latestSuccessfulMission", latestCompletedMission);
    syncAction.unsubscribe("closePreview", closePreview);
});
</script>

<style lang="scss" scoped>
.batch-action-btn {
    @apply flex items-center p-2 px-3 gap-2 cursor-pointer rounded-lg bg-bg-2 select-none text-text-2 hover:text-text-opt-3;
}
.layout-bar-option {
    @apply flex p-2 gap-2 items-center rounded-md overflow-hidden cursor-pointer text-text-2 hover:bg-fill-opt-2 hover:text-text-opt-2 select-none;
}
</style>
