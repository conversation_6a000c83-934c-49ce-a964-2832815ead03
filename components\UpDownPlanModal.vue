<script setup>
import { formatSubscribeParams } from "@/utils/tools.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { storeToRefs } from "pinia";
import { reqChangeSession, reqUpgradeSession, reqUpgradeSessionByPaypal } from "@/api";
import { usePurchaseSuccessModal, useRequestLoadingModal, useSubscribeErrorModal } from "@/hook/subscribe.js";
import { BILLING_TYPE, SUBSCRIBE_TYPE, SUBSCRIBE_CALLBACK_PARAMS, SUBSCRIBE_PARAMS } from "@/utils/constant.js";
import { I18nT, useI18n } from "vue-i18n";
const { t } = useI18n({ useScope: "global" });
const emits = defineEmits(["close"]);

const props = defineProps({
    currSub: {
        type: Object,
        default: () => ({
            billingType: BILLING_TYPE.MONTHLY,
            subscribeType: SUBSCRIBE_TYPE.BASIC,
        }),
    },
    isUpgrade: {
        type: Boolean,
        default: false,
    },
});

const subscribeStore = useSubscribeStore();
const { vipInfo, formatExpireDate, formatSubExpireDate } = storeToRefs(subscribeStore);

/**
 * 是否支持立即升级，用户当前订阅与预升级订阅的笛卡尔积
 *
 *                  ---- basic --- | -- standard -- | ---- pro--
 *                 | month | year  | month | year  | month | year
 * basic    |month | false | false | true  | true  | true  | true
 *          |year  | false | false | true  | true  | true  | true
 *          |month | false | false | false | true  | true  | true
 * standard |year  | false | false | false | false | false | true
 *          |month | false | false | false | false | false | true
 * pro      |year  | false | false | false | false | false | false
 */
const cartesian = [
    [false, false, true, true, true, true],
    [false, false, true, true, true, true],
    [false, false, false, true, true, true],
    [false, false, false, false, false, true],
    [false, false, false, false, false, true],
    [false, false, false, false, false, false],
];

/* 行列映射 */
const keyMapping = {
    basic_month: 0,
    basic_year: 1,
    standard_month: 2,
    standard_year: 3,
    pro_month: 4,
    pro_year: 5,
};

const isShowImmediately = computed(() => {
    console.log(vipInfo.value, "********************");
    const realVipInfo = subscribeStore.getHighestVipPermission();
    if (!realVipInfo) {
        return false;
    }
    const billing = props.currSub.billingType;
    const itemPlan = props.currSub.subscribeType;
    const vipKey = realVipInfo.planLevel + "_" + realVipInfo.priceInterval;
    const localKey = itemPlan + "_" + billing;
    const vipIndex = keyMapping[vipKey]; // 当前订阅类型对应 cartesian 的行
    const localIndex = keyMapping[localKey];
    return cartesian[vipIndex][localIndex];
});

const { showErrorModal } = useSubscribeErrorModal();
const { openModal: openRequestLoading } = useRequestLoadingModal();
const { openModal: openPurchaseSuccess } = usePurchaseSuccessModal();

const currUpgrade = ref("future");
const handleUpgradeChange = (index) => {
    if (upgradeLoading.value) return;
    currUpgrade.value = index;
};

const upgradeLoading = ref(false);
const changePlanFn = (isStripe) => {
    // ↓↓↓↓↓↓↓ 【Paypal 专属参数】 ↓↓↓↓↓↓↓
    //          type, opType
    // ↑↑↑↑↑↑↑ 【Paypal 专属参数】 ↑↑↑↑↑↑↑
    const type = props.isUpgrade ? "upgrade" : "downgrade";
    if (currUpgrade.value === "future") {
        const reqPromise = isStripe ? reqChangeSession : reqUpgradeSessionByPaypal;
        return {
            type,
            reqPromise,
            opType: "next_billing_period",
        };
    }
    if (currUpgrade.value === "immediately") {
        const reqPromise = isStripe ? reqUpgradeSession : reqUpgradeSessionByPaypal;
        return {
            type,
            reqPromise,
            opType: "immediate",
        };
    }
};

const handleBuyNow = () => {
    upgradeLoading.value = true;
    //取 第一次订阅锁定的平台
    const paymentMethod = subscribeStore.paymentPlatform;
    const params = formatSubscribeParams(
        "BUY_PLAN",
        { stripeItems: [{ product: props.currSub.subscribeType, price: props.currSub.billingType }] },
        { ...SUBSCRIBE_CALLBACK_PARAMS.PLAN, paymentMethod }
    );

    const { reqPromise, type, opType } = changePlanFn(paymentMethod === "stripe");

    params.successUrl += "&action=upgradePlan";
    //假如是未来升降级 --- 则修改callback url 不进行轮询
    if (opType === "next_billing_period") {
        params.successUrl = SUBSCRIBE_PARAMS.SUCCESS_CALLBACK();
    }
    //
    reqPromise(params, type, opType)
        .then((res) => {
            if (res.status === 0) {
                //假如是paypal 则跳转第三方支付，否则直接开始轮询
                if (paymentMethod === "paypal") {
                    if (opType === "immediate") {
                        const subscriptionTemp = { plan: vipInfo.value.plan, billing: vipInfo.value.billingPeriod };
                        sessionStorage.setItem("subscriptionTemp", JSON.stringify(subscriptionTemp)); // 缓存当前订阅用于轮询结果比对
                    }
                    location.href = res?.data?.approvalUrl;
                } else {
                    if (currUpgrade.value === "future") {
                        getRenewPlanInfo();
                    } else {
                        emits("close");
                        openRequestLoading({ loopType: "upgradePlan", paymentMethod });
                    }
                }
            } else {
                upgradeLoading.value = false;
                showErrorModal("SUBSCRIBE_EX_TEXT", { code: res.status });
            }
        })
        .catch((err) => {
            upgradeLoading.value = false;
            showErrorModal("SUBSCRIBE_EX_TEXT", { code: 500 });
        });
};

// 获取续订信息
const getRenewPlanInfo = () => {
    Promise.all([subscribeStore.getRefreshOn(), subscribeStore.getSubList()])
        .then((res) => {
            const errorItem = res.find((item) => item.status !== 0);
            if (errorItem) {
                showErrorModal("SUBSCRIBE_EX_TEXT", { code: errorItem.status });
            } else {
                emits("close");
                openPurchaseSuccess();
            }
        })
        .catch(() => {
            showErrorModal("SUBSCRIBE_EX_TEXT", { code: 500 });
        });
};

const handleClose = () => {
    if (upgradeLoading.value) return;
    emits("close");
};

const i18nValue = computed(() => {
    return (key) => {
        if (key) {
            return t(key);
        }
        return "";
    };
});
</script>

<template>
    <div class="p-6 pt-4">
        <div class="flex justify-between items-center gap-2 pb-4">
            <span class="text-base text-text-1 font-semibold ml-2">{{ isUpgrade ? $t("UPGRADE_PLAN_TITLE") : $t("SUBSCRIBE_DOWNGRADE_PLAN") }}</span>
            <div class="w-8 h-8 rounded-full translate-x-2 text-text-4 hover:text-text-2 flex items-center justify-center hover:bg-fill-wd-1 cursor-pointer" @click="handleClose">
                <IconsClose class="w-5 h-5" />
            </div>
        </div>
        <template v-if="isUpgrade">
            <div class="mt-4">{{ $t("UPGRADE_PLAN_CONTENT") }}</div>
            <div class="py-6 space-y-4">
                <div class="bg-fill-wd-1 rounded-xl px-4 h-12 flex items-center gap-1 cursor-pointer transition-colors duration-200 hover:bg-fill-wd-2" @click="handleUpgradeChange('future')">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center border-[6px] border-none border-fill-wd-1 shrink-0 hover:border-solid">
                        <div class="border-2 border-solid border-border-3 rounded-full w-5 h-5 flex items-center justify-center" :class="{ '!border-primary-6': currUpgrade === `future` }">
                            <div class="w-2.5 h-2.5 rounded-full bg-primary-6" :class="{ hidden: currUpgrade !== `future` }"></div>
                        </div>
                    </div>
                    <div>{{ $t("SUBSCRIBE_UPGRADE") }} {{ formatSubExpireDate.shortStr || $t(formatSubExpireDate.shortStrKey) }}</div>
                </div>
                <div
                    v-if="isShowImmediately"
                    class="bg-fill-wd-1 rounded-xl px-4 h-12 flex items-center gap-1 cursor-pointer transition-colors duration-200 hover:bg-fill-wd-2"
                    @click="handleUpgradeChange('immediately')"
                >
                    <div class="w-8 h-8 rounded-full flex items-center justify-center border-[6px] border-none border-fill-wd-1 shrink-0 hover:border-solid">
                        <div class="border-2 border-solid border-border-3 rounded-full w-5 h-5 flex items-center justify-center" :class="{ '!border-primary-6': currUpgrade === `immediately` }">
                            <div class="w-2.5 h-2.5 rounded-full bg-primary-6" :class="{ hidden: currUpgrade !== `immediately` }"></div>
                        </div>
                    </div>
                    <div>{{ $t("SUBSCRIBE_UPGRADE_IMMEDIATELY") }}</div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="py-6">
                <i18n-t keypath="DOWNGRADE_PLAN_CONTENT">
                    <template #time>
                        <span class="text-primary-6">{{ formatSubExpireDate.shortStr || i18nValue(formatSubExpireDate.shortStrKey) }}</span>
                    </template>
                </i18n-t>
            </div>
        </template>
        <div class="flex items-center justify-end mt-6">
            <Button :loading="upgradeLoading" class="min-w-32 max-md:w-full" type="primary" @click="handleBuyNow">{{ $t("SUBSCRIBE_UPGRADE_NEXT") }} </Button>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
