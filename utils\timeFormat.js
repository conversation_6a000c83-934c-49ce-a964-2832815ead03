/**
 * 将东八区服务器时间（无时区标记）转为本地 Date 对象
 * @param serverTimeStr 东八区时间字符串，格式 "YYYY-MM-DDTHH:mm:ss"
 * @returns 本地 Date 对象
 */
export function getLocalDateFromEast8Time(serverTimeStr) {
  if (!serverTimeStr) return null;

  // 如果传入的时间字符串已经包含 'Z'（ISO 格式），我们直接使用它
  if (serverTimeStr.endsWith("Z")) {
    return new Date(serverTimeStr);
  }

  // 否则，我们将时间当作 UTC 时间，并手动加上 +08:00 时区偏移
  const east8TimeStr = serverTimeStr + "+08:00";
  return new Date(east8TimeStr);
}

/**
 * 国际化时间格式输出（如 June 5, 2025）
 * @param date 本地 Date 对象
 * @param locale 语言标识（默认 en-US）
 * @returns 格式化后的字符串
 */
const BCP_47_LOCALES = {
  de: "de", // 德语（German）
  en: "en-US", // 英语（美国，常见默认）
  es: "es-ES", // 西班牙语（西班牙）
  fr: "fr-FR", // 法语（法国）
  it: "it-IT", // 意大利语（意大利）
  ja: "ja-JP", // 日语（日本）
  ko: "ko-KR", // 韩语（韩国）
  pt: "pt-PT", // 葡萄牙语（葡萄牙）
  zhTW: "zh-TW", // 繁体中文（台湾）
};
export function formatDateIntl(date, locale = null) {
  locale = locale || "en";
  if (!date) return "";
  if (typeof locale === "string") {
    locale = locale.replace(/_/g, "-"); // 替换所有的下划线为连字符
  }

  return date.toLocaleDateString(BCP_47_LOCALES[locale], {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

/**
 * 返回相对时间描述（Today / Yesterday / Intl）
 * @param date 本地 Date 对象
 * @param locale 语言标识（默认 en-US）
 * @returns 文本如 "Today"、"Yesterday"、"June 5, 2025"
 */
export function getRelativeDateLabel(dateStr, locale = null) {
  const date = getLocalDateFromEast8Time(dateStr);
  locale = locale || "en";
  if (typeof locale === "string") {
    locale = locale.replace(/_/g, "-"); // 替换所有的下划线为连字符
  }
  if (!date) return "";
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const target = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const diffDays = Math.floor((today - target) / (1000 * 60 * 60 * 24));

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  if (diffDays === 0) {
    return "SHORT_TODAY";
  } else if (diffDays === 1) {
    return "SHORT_YESTERDAY";
  } else {
    return formatDateIntl(date, locale);
  }
}
