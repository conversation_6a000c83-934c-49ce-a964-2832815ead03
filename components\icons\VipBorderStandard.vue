<template>
    <svg v-if="uid"  width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M16.8018 2.59961C18.1714 2.59972 19.4407 3.31873 20.1455 4.49316L22.5371 8.47949C23.4753 10.0431 23.2047 12.0486 21.8857 13.3076L14.6924 20.1738C13.1854 21.612 10.8146 21.612 9.30762 20.1738L2.11426 13.3076C0.795248 12.0486 0.524729 10.0431 1.46289 8.47949L3.85449 4.49316C4.55929 3.31873 5.82855 2.59972 7.19824 2.59961H16.8018Z"
            :fill="`url(#paint0_linear_border_standard_${uid})`"
            stroke="#BAE2ED"
            stroke-width="1.8"
        />
        <path d="M15.5 13L12 16.5L8.5 13" stroke="#BAE2ED" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        <defs>
            <linearGradient :id="`paint0_linear_border_standard_${uid}`" x1="0.999996" y1="4" x2="11.4338" y2="27.2014" gradientUnits="userSpaceOnUse">
                <stop stop-color="#71C6D5" />
                <stop offset="0.4" stop-color="#5CACBA" />
                <stop offset="0.8" stop-color="#2D9AAD" />
            </linearGradient>
        </defs>
    </svg>
</template>
<script setup>
const uid = ref("");
onMounted(() => {
    // 生成短唯一标识（避免id过长）
    uid.value = Math.random().toString(36).slice(2, 10);
});
</script>
<style scoped></style>
