<!--
 * @Author: HuangQS
 * @Description: lumens Records页
 * @Date: 2025-06-06 15:51:51
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-30 13:47:38
-->

<template>
    <div class="flex flex-col w-full items-center">
        <div id="records-header" class="text-center items-center text-2.5xl font-semibold text-text-1 py-[56px]">
            <span>{{ $t("LUMENS_RECORDS.TITLE") }}</span>
        </div>

        <!-- table区域 -->
        <client-only>
            <div class="flex flex-col max-w-[1200px] w-full rounded-2xl bg-bg-2 gap-6 p-6" :style="{ height: `calc(100vh - ${headerViewHeight + bannerAreaHeight}px - 60px)` }">
                <!-- tab选择items -->
                <RecordSwitch :tab-views="tableViews" v-model:selectTabId="selectTabId" />

                <template v-for="(item, index) in tableViews" :key="index">
                    <component :is="item.view" v-if="index === selectTabId" />
                </template>
            </div>
        </client-only>
    </div>
</template>

<script setup>
useSeoMeta({
    title: () => "Free AI Image Generator for AI Art Creation - PicLumen",
    ogTitle: () => "Free AI Image Generator for AI Art Creation - PicLumen",
});
import { storeToRefs } from "pinia";
import RecordSwitch from "@/components/RecordSwitch.vue";
import RecordTableLumens from "@/components/RecordTableLumens.vue";
import RecordTablePurchase from "@/components/RecordTablePurchase.vue";
import { markRaw } from "vue";
import { useThemeStore } from "@/stores/system-config";

const { bannerAreaHeight } = storeToRefs(useThemeStore());

// 表格组件列表
const tableViews = ref([
    { title: "LUMENS_RECORDS.TAB_LUMENS", view: markRaw(RecordTableLumens) },
    { title: "LUMENS_RECORDS.TAB_PURCHASE", view: markRaw(RecordTablePurchase) },
]);

const headerViewHeight = ref(0);

onMounted(() => {
    nextTick(() => {
        // 获取headerView的DOM元素
        const headerElement = document.getElementById("records-header");
        if (headerElement) {
            headerViewHeight.value = headerElement.clientHeight;
        }
    });
});

const selectTabId = ref(0);
</script>
<style lang="scss"></style>
