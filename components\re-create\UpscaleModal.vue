<template>
    <div>
        <h3 class="text-base font-bold">{{ t("TOOLBAR_HD_FIX") }}</h3>
        <div class="text-xs mt-4">{{ t("FEATURE_ENLARGEMENT_TITLE") }}</div>
        <div class="mt-2.5 h-10 p-1 gap-1 rounded-md flex justify-between bg-neutral-100 dark:bg-dark-bg-2 select-group relative" :style="{ '--index': upscaleIndex }">
            <div class="group-item" v-for="item in scaleList" :class="{ 'text-black !opacity-100': item.value === scale }" :key="item.label + '_upscale'" @click="scale = item.value">
                <span class="leading-6">{{ item.label }}</span>
            </div>
        </div>

        <div class="mt-4 flex items-center">
            <div class="text-xs">{{ t("FEATURE_REDRAW_STRENGTH") }}</div>
            <PicPopover v-if="!isMobile" placement="right">
                <template #trigger>
                    <i class="conf-icon-bar ml-1.5">
                        <IconsAlert />
                    </i>
                </template>
                <span class="text-xs">{{ t("TOOLBAR_HD") }}</span>
            </PicPopover>

            <span class="ml-auto mr-2.5 dark:text-dark-text text-neutral-800 text-xs opacity-70 hover:opacity-100 cursor-pointer" @click="denoise = 0.3">{{ t("CONFIG_BASE_RESET_BTN") }}</span>
        </div>
        <div class="mt-1.5 flex items-center gap-3">
            <n-slider class="flex-1" v-model:value="denoise" :step="0.1" :max="0.5" :min="0" />
            <n-input-number
                class="w-[104px] rounded-md shrink-0 text-center dark:text-dark-text bg-black/5 dark:bg-white/5"
                v-model:value="denoise"
                button-placement="both"
                placeholder="0.3"
                :precision="1"
                :max="0.5"
                :min="0"
                :step="0.1"
                :bordered="false"
                @blur="setUpscaleDenoise"
            >
                <template #minus-icon>
                    <div class="act-bar">
                        <IconsMinus />
                    </div>
                </template>
                <template #add-icon>
                    <div class="act-bar">
                        <IconsAdd />
                    </div>
                </template>
            </n-input-number>
        </div>

        <div class="mt-4 flex justify-end gap-3">
            <n-button round :bordered="false" class="h-10 bg-fill-btn-1 hover:!bg-fill-btn-3 focus:!bg-fill-btn-2 text-text-2 min-w-32" @click="handleCancel">
                {{ t("COMMON_BTN_CANCEL") }}
            </n-button>

            <n-tooltip placement="bottom" trigger="hover" :delay="100" :show-arrow="false" raw>
                <template #trigger>
                    <n-button
                        class="h-10 gap-1 text-dark-active-text text-sm min-w-32 bg-primary-6 hover:disabled:!bg-primary-3 hover:!bg-primary-7 focus:!bg-primary-7 disabled:bg-primary-3 disabled:!text-text-t-5 !text-text-white !opacity-100"
                        :bordered="false"
                        round
                        @click="handleConfirm"
                    >
                        {{ t("COMMON_BTN_OK") }}
                        <ExpendConst :feature-type="lumenCostKey" :num="1" :size="size" class="ml-3" />
                    </n-button>
                </template>
                <div v-if="!isMobile" class="bg-white text-dark-bg-2 dark:text-dark-text p-3 rounded dark:bg-dark-bg-2 max-w-96">
                    <span>{{ t("ESTIMATE_COST_LUMENS", lumenCostKey == "FLUX_1_DEV" ? 2 : 1) }}</span>
                </div>
            </n-tooltip>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
import { useThemeStore } from "@/stores/system-config";
import { useCreateStore } from "@/stores/create";
import { getFuncNameByModelId } from "@/utils/tools";
import ExpendConst from "@/components/subscribe/ExpendConst.vue";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());
const createStore = useCreateStore();
const scaleList = [
    { label: "1.25x", value: 1.25 },
    { label: "1.5x", value: 1.5 },
    { label: "1.75x", value: 1.75 },
    { label: "2.0x", value: 2 },
];
const props = defineProps(["size", "model_id"]);

const size = computed(() => {
    const _size = props?.size || "1024 x 1024";
    const _scale = scale.value;
    let [width, height] = _size.split("x");

    width = Number(width);
    height = Number(height);

    return `${width * _scale} x ${height * _scale}`;
});

const lumenCostKey = computed(() => getFuncNameByModelId({ model_id: props.model_id }));

// const emits = defineEmits(["update:base"]);
const emits = defineEmits(["confirm", "cancel"]);

const scale = ref(2);
const denoise = ref(0.3);
const upscaleIndex = computed(() => {
    return scaleList.findIndex((item) => item.value === scale.value);
});

const setUpscaleDenoise = () => {
    try {
        let num = denoise.value;
        if (isNaN(num) || num < 0 || num > 0.5 || num === null) {
            denoise.value = 0.3;
        }
    } catch (error) {
        denoise.value = 0.3;
    }

    console.log(denoise.value);
};
const getParams = () => {
    return {
        scale: scale.value,
        denoise: denoise.value,
    };
};

const handleCancel = () => {
    emits("cancel");
};
const handleConfirm = () => {
    // emits("confirm", getParams());
    emits("confirm", getParams());
};
</script>

<style lang="scss" scoped>
::v-deep(.n-input .n-input__input-el) {
    @apply dark:text-dark-text;
}

.select-group::after {
    @apply content-[''] absolute left-1 top-1 bottom-1  h-8 rounded bg-neutral-200;
    transform: translateX(calc(100% * var(--index) + 2px * var(--index)));
    transition: transform 0.15s cubic-bezier(0.42, 0.05, 0.03, 0.96);
    width: calc(calc(100% - 12px) / 4);
}
.conf-icon-bar {
    @apply opacity-40 cursor-pointer hover:opacity-70;
}
.group-item {
    @apply flex-1 flex items-center justify-center rounded cursor-pointer opacity-70 transition-all duration-300 hover:opacity-100 z-10 h-8;
    &:hover {
        background: rgba(225, 225, 225, 0.7);
    }
}
.dark .group-item {
    &:hover {
        background: rgba(255, 255, 255, 0.1);
    }
}
.act-bar {
    @apply text-sm bg-black/15 dark:bg-white/15 shrink-0 w-6 h-6 rounded-sm flex items-center justify-center dark:text-dark-text opacity-75;
}
</style>
