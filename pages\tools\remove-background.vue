<template>
    <div class="h-screen overflow-hidden">
        <!-- 第一步 图片上传 -->
        <div class="step-one w-full h-full flex flex-col items-center bg-fill-gradient-10 overflow-auto no-scroll py-[3%] gap-[16%] px-6 xl:px-0" v-if="step === 1">
            <!-- title 返回按钮 -->
            <div class="background h-[452px] w-full flex justify-center z-1">
                <GoBackHeader class="!absolute" :border="false" redirect-path="/tools" />
            </div>
            <!-- 图片上传区域 -->
            <div class="w-full text-center font-semibold text-text-1 text-[18px] xs:text-[28px] shrink-0 relative z-2 mt-6">{{ $t("TOOLBAR_REMOVE_BG_FULL") }}</div>
            <div class="flex flex-col h-[66%] gap-[7%] rounded-2xl">
                <div
                    class="w-full flex-1 xl:w-[856px] rounded-2xl backdrop-blur-lg max-h-[456px] min-h-[272px] text-text-2 flex flex-col"
                    :class="{ ' bg-fill-upload-1 border-2 border-border-upload-1 border-dashed': noProcessImages.length }"
                >
                    <div class="remove-bg-upload__header h-12 xl:h-20 p-6 flex justify-between items-center" v-if="noProcessImages.length">
                        <span class="min-w-[76px]"></span>
                        <span>{{ noProcessImages.length }} / {{ limit }} {{ $t("CONFIG_BASE_BATCH_SIZE") }}</span>
                        <span class="text-xs py-2 px-3 rounded-full hover:bg-fill-btn-10 text-danger-6 cursor-pointer" @click="handleClear" v-if="noProcessImages.length">{{
                            $t("BATCH_REMOVE_BG.CLEAR_ALL")
                        }}</span>
                    </div>
                    <ImageList
                        :noProcessImages="noProcessImages"
                        @fileChange="handleFileChange"
                        @fileAdded="handleOpenFilePicker"
                        @delete="handleDelete"
                        :disabled="noProcessImages.length >= limit"
                        class="flex-1 max-h-[456px] px-6 xl:px-0"
                        :class="{ ' mb-6 bg-fill-upload-1 hover:bg-fill-upload-2 border-2 border-border-upload-1 border-dashed hover:border-border-upload-2': !noProcessImages.length }"
                        ref="imageListRef"
                    />
                </div>

                <!-- 生图按钮 -->
                <div class="flex items-center justify-center gap-2 xl:gap-6 shrink-0">
                    <template v-if="noProcessImages.length">
                        <n-popover :show-arrow="false" raw>
                            <template #trigger>
                                <Button
                                    :type="noProcessImages.length > 1 ? 'secondary' : 'primary'"
                                    class="h-11 rounded-lg !w-fit xl:!w-[200px] min-w-[96px]"
                                    :bordered="false"
                                    @click="beforeGenerate('one')"
                                >
                                    <div class="flex items-center">
                                        <span>{{ $t("BATCH_REMOVE_BG.REMOVE_ONE") }}</span>
                                        <IconsLumenFill class="ml-3 mr-0.5 w-2.5 h-4" />
                                        <span>1</span>
                                    </div>
                                </Button>
                            </template>
                            <div class="max-w-[300px] text-sm text-wrap text-text-3 bg-bg-5 rounded-lg py-2 px-3">{{ $t("BATCH_REMOVE_BG.FIRST_IMAGE_PROCESS") }}</div>
                        </n-popover>
                        <Button type="primary" class="h-11 rounded-lg !w-fit xl:!w-[200px] flex items-center" :bordered="false" @click="beforeGenerate('all')" v-if="noProcessImages.length > 1">
                            <vipBtn>
                                <span class="ml-3">{{ $t("BATCH_REMOVE_BG.REMOVE_ALL") }}</span>
                                <IconsLumenFill class="ml-3 mr-0.5 w-2.5 h-4" />
                                <span>{{ noProcessImages.length }}</span>
                            </vipBtn>
                            <FreeTrail class="absolute top-0 right-0 transform translate-y-[-50%]" v-if="showFreeTrail" />
                        </Button>
                    </template>
                    <template v-else>
                        <Button type="primary" disabled class="h-11 rounded-lg !w-fit xl:!w-[200px] min-w-[96px]" :bordered="false" @click="beforeGenerate('one')">
                            <div class="flex items-center gap-3">{{ $t("TOOLBAR_REMOVE") }}</div>
                        </Button>
                    </template>
                </div>
            </div>
            <!-- </div> -->
        </div>
        <!-- 第二步 图片处理+预览 -->
        <div class="w-full h-full result-preview flex flex-col" v-if="step === 2">
            <GoBackHeader class="text-text-2 bg-bg-2" :disabled="!hasDownloaded || isProcessing" @before-leave="shouldReConfirm" ref="goBackRef" redirect-path="/tools">
                <div class="text-text-2 font-medium flex justify-between items-center">
                    <span>{{ $t("TOOLBAR_REMOVE_BG_FULL") }} </span>
                    <!-- 下载 -->
                    <div class="flex items-center gap-3">
                        <div class="flex items-center gap-2" v-if="isProcessing">
                            <n-progress
                                class="ml-auto"
                                :show-indicator="false"
                                style="width: 20px"
                                type="circle"
                                :offset-degree="90"
                                :percentage="(thisTime.current / thisTime.total) * 100"
                                :stroke-width="12"
                                :style="successProgressStyle"
                            />
                            <span class="text-text-4 font-medium hidden xl:inline-block"> {{ thisTime.current }} / {{ thisTime.total }} {{ $t("BATCH_REMOVE_BG.REMOVING") }} . . . </span>
                        </div>
                        <Button
                            type="secondary"
                            class="h-11 rounded-lg !w-fit !px-4"
                            :bordered="false"
                            :disabled="!previewItem.taskStatus || previewItem.taskStatus !== REQ_STATUS.SUCCESS"
                            @click="handleDownload('one')"
                        >
                            <div class="flex items-center gap-2">
                                <IconsDownload />
                                <span class="hidden xl:inline-block">{{ $t("TOOLBAR_DOWNLOAD") }}</span>
                            </div>
                        </Button>
                        <Button type="primary" :disabled="isProcessing || !completedCount" class="h-11 rounded-lg !w-fit !px-4 flex items-center" :bordered="false" @click="handleDownload('all')">
                            <vipBtn class="gap-2">
                                <span class="hidden xl:inline-block">{{ $t("SUBSCRIBE_FEATURES_TABLE_10_1") }}</span>
                            </vipBtn>
                            <FreeTrail class="absolute top-0 right-0 transform translate-y-[-50%]" v-if="showFreeTrail" />
                        </Button>
                    </div>
                </div>
            </GoBackHeader>
            <!-- 生图结果 -->
            <div class="flex-1 w-full overflow-hidden flex">
                <!-- 左侧列表栏 -->
                <ProcessImageList
                    ref="processImageRef"
                    class="w-[128px] bg-bg-2"
                    :list="inProcessImages"
                    :disabled="isProcessing"
                    @fileAdded="shouldOpenUpload"
                    @clear="handleClearProcessedImages"
                    @preview="handleItemPreview"
                    @delete="deleteProcessedImage"
                />
                <!-- 右侧主区域，垂直布局 -->
                <div class="flex-1 bg-bg-1 relative flex flex-col h-[calc(100%-60px)] xl:h-full w-full p-6 pb-0" v-if="inProcessImages.length">
                    <!-- 图片区域：占据剩余空间 -->
                    <div class="preview-wrapper h-fit w-fit max-h-[calc(100%-80px)] max-w-full relative overflow-hidden m-auto">
                        <img :src="contrastImage" alt="" class="object-cover max-h-full" />

                        <!-- 刮屏动画效果区域 -->
                        <div class="absolute top-[-50%] left-0 w-full light-for-loading h-[200%]" v-if="isProcessing">
                            <div class="light h-full w-[200px] rotate-[45deg]" v-show="previewItem.taskStatus < REQ_STATUS.SUCCESS"></div>
                        </div>
                    </div>
                    <!-- 固定高度底栏 -->
                    <div class="contrast flex items-center justify-center shrink-0 h-20">
                        <div
                            v-if="previewItem.taskStatus === REQ_STATUS.SUCCESS"
                            class="bg-bg-2 border border-solid border-border-t-1 rounded-lg text-text-2 p-[3px] cursor-pointer select-none"
                            @mousedown="handleContrast(true)"
                            @touchstart="handleContrast(true)"
                            @mouseup="handleContrast(false)"
                            @touchend="handleContrast(false)"
                            @mouseleave="handleContrast(false)"
                        >
                            <div class="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-fill-wd-1 text-text-3" :class="{ '!text-primary-6': isContrasting }">
                                <IconsContrast />{{ $t("CONTRAST") }}
                            </div>
                        </div>
                    </div>
                    <div class="w-full error-tip absolute top-12 left-0 z-[999]" v-if="previewItem.taskStatus === REQ_STATUS.ERROR">
                        <div class="flex items-center p-3 rounded-lg border-border-t-1 gap-2 bg-bg-6 text-text-3 text-sm w-fit max-w-[56%] mx-auto">
                            <n-icon size="22">
                                <IconsAlertFill />
                            </n-icon>
                            {{ $t(previewItem.errorTip || defaultErrorTip) }}
                        </div>
                    </div>
                </div>
                <Upload
                    ref="uploadRef"
                    eventName="Remove_BG"
                    @before-file-change="shouldOpenModal = true"
                    @change="handleFileChange"
                    @check-failed="handleUploadError"
                    multiple
                    :one-time-max="oneTimeMax"
                    class="w-full h-full flex flex-col items-center justify-center no-data cursor-pointer !bg-bg-1 !rounded-none"
                    v-else
                >
                    <img src="@/assets/images/image_empty.webp" class="w-32 aspect-square hidden dark:block pointer-events-none" />
                    <img src="@/assets/images/image_empty_light.webp" class="w-36 aspect-square dark:hidden pointer-events-none" />
                    <span class="mt-8 font-bold text-center text-text-2">
                        {{ $t("UPLOAD.DRAG_TITLE_BEFORE") }}
                        <span class="font-semibold text-text-1"> {{ oneTimeMax }}</span>
                        {{ $t("UPLOAD.DRAG_TITLE_AFTER") }}
                    </span>
                    <span class="text-sm text-text-4 mt-1 text-center">{{ $t("UPLOAD.FORMAT_DES", { limit: sizeLimit }) }}</span>
                </Upload>
            </div>
        </div>

        <!-- 生图结果页重新上传图片 -->
        <n-modal v-model:show="showNoProcessImages" :z-index="20" class="max-h-[60vh] min-h-[334px] overflow-hidden bg-bg-2">
            <div class="w-full xl:w-[880px] h-full flex flex-col justify-center items-center text-text-2 border border-border-1 border-solid rounded-2xl relative">
                <div class="w-full remove-bg-upload__header p-3 xl:p-6 py-2 xl:py-4 flex justify-between items-center">
                    <span class="font-semibold text-text-1">{{ $t("FEATURE_SELF_UPLOAD_TITLE") }}</span>
                    <span class="cursor-pointer text-text-4 hover:text-text-2 rounded-full hover:bg-fill-wd-1 p-2" @click="showNoProcessImages = false">
                        <IconsClose class="w-5 h-5" />
                    </span>
                </div>
                <ImageList
                    :noProcessImages="noProcessImages"
                    @delete="handleDelete"
                    @fileAdded="handleOpenFilePicker"
                    @fileChange="handleFileChange"
                    :disabled="noProcessImages.length >= limit"
                    class="!bg-transparent flex-1 max-h-[456px] px-6 xl:px-0"
                    ref="imageListRef"
                />
                <div class="flex items-center justify-between w-full h-17 xl:h-22 p-3 xl:p-6">
                    <div class="flex items-center gap-2">
                        <span class="text-nowrap">{{ noProcessImages.length }} / {{ limit }} </span>
                        <span class="hidden xl:inline-block">{{ $t("CONFIG_BASE_BATCH_SIZE") }}</span>
                        <span class="text-xs py-2 px-3 rounded-full hover:bg-fill-btn-10 text-danger-6 cursor-pointer hidden xl:inline-block" @click="handleClear" v-if="noProcessImages.length">{{
                            $t("BATCH_REMOVE_BG.CLEAR_ALL")
                        }}</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <n-popover :show-arrow="false" raw placement="top-end">
                            <template #trigger>
                                <Button
                                    :type="noProcessImages.length > 1 ? 'secondary' : 'primary'"
                                    class="h-10 !w-fit xl:!w-[200px] rounded-full"
                                    :bordered="false"
                                    :disabled="!noProcessImages.length"
                                    @click="beforeGenerate('one')"
                                >
                                    <div class="flex items-center">
                                        <span>{{ $t("BATCH_REMOVE_BG.REMOVE_ONE") }}</span>
                                        <IconsLumenFill class="ml-3 mr-0.5 w-2.5 h-4" />
                                        <span>1</span>
                                    </div>
                                </Button>
                            </template>
                            <div class="max-w-[300px] text-sm text-wrap text-text-3 bg-bg-5 rounded-lg py-2 px-3">{{ $t("BATCH_REMOVE_BG.FIRST_IMAGE_PROCESS") }}</div>
                        </n-popover>
                        <Button
                            type="primary"
                            class="h-10 !w-fit xl:!w-[200px] rounded-full flex items-center"
                            :class="{ ' rounded-tr-lg ': showFreeTrail }"
                            :bordered="false"
                            @click="beforeGenerate('all')"
                            v-if="noProcessImages.length > 1"
                        >
                            <vipBtn>
                                <span>{{ $t("BATCH_REMOVE_BG.REMOVE_ALL") }}</span>
                                <IconsLumenFill class="ml-3 mr-0.5 w-2.5 h-4" />
                                <span>{{ noProcessImages.length }}</span>
                            </vipBtn>
                            <FreeTrail class="absolute top-0 right-0 transform translate-y-[-50%]" v-if="showFreeTrail" />
                        </Button>
                    </div>
                </div>
            </div>
        </n-modal>
        <ClientOnly>
            <BatchDown v-show="false" ref="batchDownLoadRef" customFileType="png" />
        </ClientOnly>
        <Upload ref="uploadRef" eventName="Remove_BG" @change="handleFileChange" @check-failed="handleUploadError" multiple :one-time-max="30" v-show="false" />
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
useSeoMeta({
    title: () => t("SEO_META.SEO_RM_BG_TITLE"),
    ogTitle: () => t("SEO_META.SEO_RM_BG_TITLE"),
    description: () => t("SEO_META.SEO_RM_BG_DESC"),
    ogDescription: () => t("SEO_META.SEO_RM_BG_DESC"),
});
import { uploadToCosNew, downloadImage, loadImage, debounce, getUUID } from "@/utils/tools";
import { successProgressStyle } from "@/utils/constant-style";
import { useSubPermission } from "@/hook/subscribe";
import { REQ_STATUS, ERROR_CODE_ENUM, SUB_EL } from "@/utils/constant.js";
import { compressSingleFileOrUrl } from "@/utils/compress.js";
import { batchRemoveBackground, batchQueryBackground, reqUserCommunityAndLumenInfo } from "@/api/index.js";
import ImageList from "@/components/tools/bg-remover/ImageList.vue";
import ProcessImageList from "@/components/tools/bg-remover/ProcessImageList.vue";
import FreeTrail from "@/components/tools/bg-remover/FreeTrail.vue";
import { useGlobalError } from "@/hook/error";
const { showError } = useGlobalError();
import { NIcon } from "naive-ui";
import { Alert } from "@/icons/index.js";
import { useSyncAction } from "@/stores/syncAction";
const syncAction = useSyncAction();
const { showMessage } = useModal();
const { checkPermission } = useSubPermission();
const step = ref(1);
const showFreeTrail = ref(true); //是否展示限时试用
// 所有未处理的图片
const noProcessImages = ref([]);
const sizeLimit = 20; //文件大小限制
const oneTimeMax = 30; //单次上传最大数限制
const limit = 30; //总数限制
const handleFileChange = async (files) => {
    const processedOriginFile = files.map((file) => {
        const imageUrlLocal = URL.createObjectURL(file);
        return {
            file,
            id: getUUID(),
            imageUrlLocal,
            taskStatus: REQ_STATUS.WAITING,
        };
    });
    let finalFiles = [...processedOriginFile, ...noProcessImages.value];
    //如果长度超过limit 截取到limit
    if (finalFiles.length > limit) {
        finalFiles = finalFiles.slice(-limit);
    }
    noProcessImages.value = finalFiles;
    if (shouldOpenModal.value) {
        showNoProcessImages.value = true;
        shouldOpenModal.value = false;
    }
};
const handleUploadError = (data) => {
    const { type, limit, maxPixels, oneTimeMax } = data;
    switch (type) {
        case "size":
        case "ratio":
            openToast.error(t("UPLOAD_ERROR.SIZE_PIXEL_EXCEED", { limit, maxPixels }));
            break;
        case "count":
            openToast.error(t("UPLOAD_ERROR.COUNT_EXCEED", { oneTimeMax }));
            break;
        default:
            break;
    }
};
const isProcessing = ref(false);
const uploadRef = ref(false);
let shouldOpenModal = ref(false);
const shouldOpenUpload = () => {
    if (isProcessing.value) return;
    returnAllNoProcessImages();
    shouldOpenModal.value = true;
    if (!noProcessImages.value.length) {
        handleOpenFilePicker();
    } else {
        showNoProcessImages.value = true;
        gaTrackEvent({ el: "upload_popup_show" });
    }
};
const handleOpenFilePicker = () => {
    uploadRef.value?.openFilePicker();
};
const handleDelete = (id) => {
    noProcessImages.value = noProcessImages.value.filter((item) => item.id !== id);
};

const imageListRef = ref();
const handleClear = () => {
    noProcessImages.value = [];
};
//生图 单张 多张
const thisTime = ref({
    total: 0,
    current: 0,
    batchId: "",
});
const localePath = useLocalePath();
//生图前lumen校验
const leftTotalLumens = ref(0);
const beforeGenerate = async (type = "one") => {
    // if (type === "all") {
    //     const res = await checkPermission(SUBSCRIBE_PERMISSION.REMOVE_BG);
    //     if (!res) {
    //         return;
    //     }
    // }
    let items = [];
    if (type === "one") {
        items = noProcessImages.value.splice(0, 1);
        handleGenerate(items);
        gaTrackEvent({ el: `remove_one` });
    } else {
        gaTrackEvent({ el: `remove_all` });
        const res = await reqUserCommunityAndLumenInfo();
        const {
            data: { userLumens },
        } = res;
        leftTotalLumens.value = userLumens.leftTotalLumens;
        if (leftTotalLumens.value < noProcessImages.value.length) {
            gaTrackEvent({ el: `insufficient_balance_popup_show` });
            showMessage({
                style: { width: "380px" },
                icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
                title: t("BATCH_REMOVE_BG.NOT_ENOUGH_TITLE"),
                content: h("div", null, t("BATCH_REMOVE_BG.NOT_ENOUGH_CONTENT")),
                cancelBtn: t(leftTotalLumens.value ? "BATCH_REMOVE_BG.NOT_ENOUGH_CANCEL" : "TASKS_CENTER_EARN_MORE"),
                confirmBtn: t("SUBSCRIBE_UPGRADE"),
            })
                .then(async () => {
                    //跳转到订阅页面
                    gaTrackEvent({ el: `insufficient_balance_pop_upgrade` });
                    window.open(`/app${localePath("/user/subscribe")}`, "_blank");
                })
                .catch(async () => {
                    if (leftTotalLumens.value) {
                        // 减少图片数量 继续生图
                        gaTrackEvent({ el: `insufficient_balance_pop_reduce` });
                        items = noProcessImages.value.splice(0, leftTotalLumens.value);
                        handleGenerate(items);
                    } else {
                        // 跳转到任务中心
                        gaTrackEvent({ el: `insufficient_balance_pop_get_more` });
                        window.open(`/app${localePath("/tasks")}`, "_blank");
                    }
                });
        } else {
            items = noProcessImages.value;
            noProcessImages.value = [];
            handleGenerate(items);
        }
    }
};
//开始生图
const handleGenerate = async (items = []) => {
    if (!items.length) return;
    thisTime.value = {
        total: items.length,
        current: 0,
        batchId: getUUID(),
    };
    succeedIds = [];
    inProcessImages.value = [...items, ...inProcessImages.value];
    showNoProcessImages.value = false;
    isProcessing.value = true;
    processNext();
    //查看生图结果
    if (step.value === 1) {
        step.value = 2;
    }
};
let pollTasksId = [];
let isPolling = false;
const defaultErrorTip = "BATCH_REMOVE_BG.REMOVE_BG_ERROR";
const MAX_CONCURRENT = 5; // 最大并发数
let activeCount = 0;
/**并发 */
async function processNext() {
    // 如果已达最大并发数，或者没有待处理的任务，则直接返回
    if (activeCount >= MAX_CONCURRENT || !inProcessImages.value?.some((task) => task.taskStatus === REQ_STATUS.WAITING)) {
        return;
    }

    // 获取待处理的任务（最多MAX_CONCURRENT个）
    const tasksToProcess = inProcessImages.value.filter((task) => task.taskStatus === REQ_STATUS.WAITING).slice(0, MAX_CONCURRENT - activeCount);

    // 如果没有任务了，检查是否需要停止轮询
    if (tasksToProcess.length === 0 && activeCount === 0 && pollTasksId.length === 0) return;

    // 处理这些任务
    tasksToProcess.forEach((task) => {
        activeCount++;
        processTask(task).finally(() => {
            activeCount--; // 一个任务完成后，减少当前活动任务数
            // 确保完成一个任务后，继续处理下一个
            processNext();
        });
    });
}
/**任务状态变更 */
async function processTask(task) {
    try {
        task.taskStatus = REQ_STATUS.PROCESSING;

        // 如果没有图片URL，先进行压缩并上传到OSS
        if (!task.imageUrlOss) {
            const compressedInfo = await compressSingleFileOrUrl(task.file, {
                type: "",
                width: 2500,
                height: 2500,
                quality: 1,
            });
            const { compressedFile } = compressedInfo;
            const { fullPath } = await uploadToCosNew(
                {
                    file: compressedFile,
                    originalFileName: Date.now() + Math.random() + "_.webp",
                    type: "batch_rmbg",
                    batchId: thisTime.value.batchId,
                },
                "batch_rmbg"
            );
            task.imageUrlOss = fullPath;
        }

        // 加载图片，获取宽高
        const img = await loadImage(task.imageUrlLocal);
        const { width, height } = img;

        // 调用背景移除API
        const {
            status,
            data: { id = "" },
        } = await batchRemoveBackground({
            fileUrl: task.imageUrlOss,
            width,
            height,
            batchId: thisTime.value.batchId,
        });

        if (status !== 0 || !id) {
            throw new Error(`任务创建失败，状态码: ${status}`);
        }

        pollTasksId.push(id);
        task.taskId = id;
        task.taskStatus = REQ_STATUS.PROCESSING;

        // 如果没有开始轮询，开始轮询
        if (!isPolling) {
            startPolling();
            isPolling = true;
        }
    } catch (err) {
        const { status, message } = err;
        console.error("任务处理失败:", err);
        task.taskStatus = REQ_STATUS.ERROR;
        task.errorTip = message || defaultErrorTip;
        pollTasksId = pollTasksId.filter((id) => id !== task.taskId);
        if (!pollTasksId.length) {
            isProcessing.value = false;
        }
        if (status === ERROR_CODE_ENUM.LUMENS_LACK_ERROR) {
            showError(status, { triggerEl: SUB_EL.REMOVE_BG });
            return;
        }
    }
}

/**
 * 轮询生图结果（每2秒执行一次）
 */
let succeedIds = [];
let pollingTimeout = null;
let isPollingRequesting = false;
let isPollingStopped = false;

const startPolling = (duration = 2000) => {
    stopPolling(); // 保证不重复开启
    isPollingStopped = false;
    pollNext(duration);
};

const stopPolling = () => {
    if (pollingTimeout) {
        clearTimeout(pollingTimeout);
        pollingTimeout = null;
    }
    isPolling = false;
    isPollingStopped = true;
};

const pollNext = async (duration) => {
    if (isPollingStopped) return;

    await pollAllTaskResults();

    // 成功/失败都在 finally 重置后，再定时发起下一轮
    pollingTimeout = setTimeout(() => {
        pollNext(duration);
    }, duration);
};

const pollAllTaskResults = async () => {
    if (isPollingRequesting) return;
    isPollingRequesting = true;

    try {
        pollTasksId = pollTasksId.filter((item) => !succeedIds.includes(item));
        if (pollTasksId.length === 0) {
            stopPolling();
            return;
        }

        const res = await batchQueryBackground(pollTasksId);
        if (res.status !== 0) {
            return onPollingError(res.message);
        }

        const { data = [] } = res;
        inProcessImages.value = inProcessImages.value.map((item) => {
            const result = data?.find((task) => task.id === item.taskId);
            if (result) {
                item.taskStatus = result.status;
                item.removedUrl = result.fileUrl;
                item.errorTip = result.failureMessage;

                if (result.status >= REQ_STATUS.SUCCESS) {
                    succeedIds.push(item.taskId);
                    result.status >= REQ_STATUS.SUCCESS && thisTime.value.current++;
                    if (succeedIds.length === thisTime.value.total) {
                        setTimeout(() => {
                            isProcessing.value = false;
                        }, 500);
                    }
                }
            }
            return item;
        });
    } catch (err) {
        onPollingError(err.message || "");
    } finally {
        isPollingRequesting = false;
    }
};

/**轮询失败 */
const onPollingError = (msg) => {
    //正在轮询中的pollTaskId 对应inProcessImages中的任务全部失败
    inProcessImages.value?.forEach((item) => {
        if (item.taskStatus !== REQ_STATUS.SUCCESS) {
            item.taskStatus = REQ_STATUS.ERROR;
            item.errorTip = msg;
        }
    });
    pollTasksId = [];
    stopPolling(); // 失败时停止轮询
};

//下载 单张 多张
const batchDownLoadRef = ref();
const handleDownload = debounce(async (type = "one") => {
    gaTrackEvent({ el: `download_${type}` });
    if (type === "all") {
        // const res = await checkPermission(SUBSCRIBE_PERMISSION.BATCH_DOWNLOAD);
        // if (!res) {
        //     return;
        // }
        const rawList = inProcessImages.value
            .map((item) => {
                if (item.taskStatus === REQ_STATUS.SUCCESS && item.removedUrl) {
                    return {
                        fileUrl: item.removedUrl,
                    };
                }
            })
            .filter(Boolean);
        batchDownLoadRef.value?.downloadZip(rawList);
        openToast.success(t("TOAST_DOWNLOAD"), 5e3);
    } else {
        downloadImage(previewItem.value.removedUrl, "png");
    }
    hasDownloaded.value = true;
}, 300);
const showNoProcessImages = ref(false);
const inProcessImages = ref([]); //已经进入处理状态的图片（包含已经处理完成的）

//预览 对比
const previewItem = ref({});
const isContrasting = ref(false);
const contrastImage = computed(() => {
    if (!previewItem.value) {
        return;
    }
    let result = "";
    const { imageUrlLocal = "", removedUrl = "" } = previewItem.value;
    if (removedUrl) {
        result = isContrasting.value ? imageUrlLocal : removedUrl;
    } else {
        result = imageUrlLocal;
    }
    return result;
});
const handleContrast = (value) => {
    if (value) {
        const img = new Image();
        img.src = previewItem.value?.removedUrl;
    }
    isContrasting.value = value;
};

const handleItemPreview = (index) => {
    previewItem.value = inProcessImages.value[index];
};
const clearInProcessState = () => {
    resetPreviewItem();
    inProcessImages.value = [];
};
/**清空已完成的照片 */
const handleClearProcessedImages = debounce(() => {
    if (!hasDownloaded.value && completedCount.value) {
        gaTrackEvent({ el: "clear_confirm_popup_show" });
        showMessage({
            style: { width: "380px" },
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
            title: t("BATCH_REMOVE_BG.CLEAR_TITLE"),
            content: h("div", null, t("BATCH_REMOVE_BG.CLEAR_CONTENT")),
            cancelBtn: t("BATCH_REMOVE_BG.CLEAR_CANCEL"),
            confirmBtn: t("BATCH_REMOVE_BG.CLEAR_CONFIRM"),
        })
            .then(() => {
                //确认清空
                clearInProcessState();
                gaTrackEvent({ el: "clear_confirm_popup_clear" });
            })
            .catch(() => {});
    } else {
        clearInProcessState();
    }
}, 300);
const deleteProcessedImage = (id) => {
    inProcessImages.value = inProcessImages.value.filter((item) => item.id !== id);
    if (!inProcessImages.value.length) {
        resetPreviewItem();
    }
};
const resetPreviewItem = () => {
    previewItem.value = {
        taskStatus: REQ_STATUS.WAITING,
    };
};

const hasDownloaded = ref(false); //是否下载过当前已经完成的图片
const goBackRef = ref();
const completedCount = computed(() => inProcessImages.value.filter((item) => item.taskStatus === REQ_STATUS.SUCCESS).length);
/**退出前确认 */
const shouldReConfirm = debounce(() => {
    gaTrackEvent({ el: "exit_btn" });
    if (!completedCount.value && !isProcessing.value) return goBackRef.value.back();
    if ((completedCount.value && !hasDownloaded.value) || isProcessing.value) {
        gaTrackEvent({ el: "exit_confirm_popup_show" });
        showMessage({
            style: { width: "380px" },
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
            title: t("BATCH_REMOVE_BG.EXIT_TITLE"),
            content: h("div", null, t("BATCH_REMOVE_BG.EXIT_CONTENT")),
            cancelBtn: t("BATCH_REMOVE_BG.EXIT_CANCEL"),
            confirmBtn: t("BATCH_REMOVE_BG.EXIT_CONFIRM"),
        })
            .then(() => {
                //确认退出
                hasDownloaded.value = true;
                goBackRef.value.back();
                gaTrackEvent({ el: "exit_confirm_popup_exit" });
            })
            .catch(() => {});
    } else {
        hasDownloaded.value = true;
    }
}, 300);

const processImageRef = ref();
const returnAllNoProcessImages = () => {
    // 把所有已经失败的任务放noProcessImages中
    const failedTasks = [];
    inProcessImages.value = inProcessImages.value.filter((item) => {
        if (item.taskStatus !== REQ_STATUS.SUCCESS) {
            failedTasks.push({ ...item, taskStatus: REQ_STATUS.WAITING });
            return false; // 过滤掉
        }
        return true;
    });
    if (previewItem.value.taskStatus !== REQ_STATUS.SUCCESS) {
        if (inProcessImages.value?.length) {
            processImageRef.value?.handleItemClick(inProcessImages.value[0], 0);
        } else {
            resetPreviewItem();
        }
    }
    noProcessImages.value = [...failedTasks, ...noProcessImages.value];
};

/**埋点 */
const gaTrackEvent = (options) => {
    console.log("gaTrackEvent", options);
    window.trackEvent("Remove_BG", options);
};
</script>

<style lang="scss" scoped>
.background {
    position: absolute;
    top: 0;
    &::before {
        content: "";
        width: 100%;
        height: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(180deg, var(--p-fill-gradient-8) 0%, var(--p-fill-gradient-9) 39.95%, var(--p-fill-gradient-10) 100%);
    }
    background-image: url(@/assets/images/toolsCover/remove_bg_bg.webp);
    background-size: cover;
    background-position: 50% 0%;
    background-repeat: no-repeat;
}
.light-for-loading {
    animation: lightSweep 1s infinite linear;
    .light {
        background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 49.87%, rgba(255, 255, 255, 0) 100%);
    }
}
@keyframes lightSweep {
    from {
        transform: translateX(-60%) translateY(6%);
    }
    to {
        transform: translateX(120%) translateY(6%);
        // left: %;
    }
}
.preview-wrapper {
    --grid-size: 20px;

    background-image: repeating-conic-gradient(var(--p-fill-dd-2) 0% 25%, var(--p-fill-ww-2) 0% 50%);
    background-size: var(--grid-size) var(--grid-size);
}
</style>
