<template>
    <n-config-provider :theme="isDark ? darkTheme : lightTheme" :theme-overrides="isDark ? naiveUIConfig.dark : naiveUIConfig.light">
        <n-message-provider>
            <NuxtLayout>
                <NuxtPage :keepalive="keepalive" />
            </NuxtLayout>
            <CustomToast />

            <div id="app_content" />
        </n-message-provider>
    </n-config-provider>
</template>
<script setup>
// const route = useRoute();
import { useTheme } from "@/hook/system";
import { useThemeStore } from "@/stores/system-config";
import { naiveUIConfig } from "@/utils/common";
import { KEEPALIVE_PAGES } from "@/utils/constant";
import { darkTheme, lightTheme } from "naive-ui";

const { startMonitorScreen, stopMonitorScreen } = useThemeStore();

const { isDark } = useTheme();
// 只在服务端阶段设置 bodyClass
useHead({ bodyAttrs: { class: () => (isDark.value ? "dark" : "") } });

onMounted(() => {
    startMonitorScreen();
});

onUnmounted(() => {
    stopMonitorScreen();
});

// 设置页面是否需要SSR
// const ssrPagePaths = ref(["/community/explore"]);
// const isNeedSSR = computed(() => {
//     return ssrPagePaths.value.includes(route.path);
// });

//KEEPALIVE_PAGES是一个对象，拿出里面所有的value
const keepalive = {
    include: Object.values(KEEPALIVE_PAGES),
};
</script>
