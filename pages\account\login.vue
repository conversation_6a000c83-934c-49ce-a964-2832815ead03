<template>
    <div class="content overflow-x-hidden dark" id="sign_content">
        <div class="fixed top-0 left-0 right-0 bottom-0 bg-black"></div>
        <!-- <img src="@/assets/images/login_bg.webp" alt="" class="mask" ref="maskRef"> -->

        <div class="absolute pb-16 top-0 left-0 w-screen min-h-full h-max flex justify-center items-center text-dark-active-text">
            <div class="w-full md:w-[488px] relative z-10 bg-black/50 opacity-90 backdrop-blur-3xl mb-24 md:mb-0 md:rounded-3xl overflow-hidden">
                <div class="flex flex-col items-center py-8 text-sm text-dark-active-text">
                    <IconsVectorLogo />
                    <div id="g_id_onload" :data-client_id="clientId" data-auto_prompt="false" data-callback="handleCredentialResponse"></div>
                    <div v-show="componentId !== 'ForgotComp'" class="w-full">
                        <h2 class="text-2xl font-semibold mt-4 text-center">
                            {{ componentId === "SignUpComp" ? t("ACC_SIGN_IN_SIGN_UP") : t("PROFILE_ACCOUNT_WELCOME") }}
                        </h2>
                        <div class="text-xs mt-4 opacity-60 w-80 mx-auto text-center">
                            {{ t("PROFILE_ACCOUNT_CONTINUE_WITH") }}
                        </div>
                        <div class="flex items-center mt-2 w-80 mx-auto gap-2">
                            <div class="gap-2 other-sign-in relative" @click="handleGoogleBtn">
                                <n-icon size="18">
                                    <IconsGoogle />
                                </n-icon>
                                <span>Google</span>
                                <div id="customBtn" class="absolute top-0 left-0 right-0 bottom-0 opacity-[0.01] z-50"></div>
                            </div>

                            <div class="gap-1 other-sign-in" @click="exciteAppleAuth">
                                <n-icon size="24">
                                    <IconsApple />
                                </n-icon>
                                <span>Apple</span>
                            </div>
                        </div>
                        <div class="mt-8 text-xs text-center opacity-60">
                            {{ t("PROFILE_ACCOUNT_CONTINUE_EMAIL") }}
                        </div>
                    </div>
                    <div class="w-80 mx-auto sign-in-account">
                        <component :is="componentMap[componentId]" @changeComponent="changeComponent" v-model:hasRemember="hasRemember"></component>
                    </div>

                    <div class="mt-8 text-xs opacity-60">
                        <Language to="#sign_content" />
                    </div>
                </div>
            </div>
            <div class="text-center text-xs absolute bottom-8 z-20">
                <div>
                    <span class="underline cursor-pointer opacity-50 hover:opacity-80" @click="toWp('/privacy-policy')">
                        {{ t("ACC_SIGN_IN_POLICY") }}
                    </span>
                    <span class="opacity-50"> | </span>
                    <span class="underline cursor-pointer opacity-50 hover:opacity-80" @click="toWp('/terms-of-use')">
                        {{ t("ACC_SIGN_IN_TERMS") }}
                    </span>
                </div>
                <div class="mt-4 opacity-50">© 2024 - {{ currentYear }} Grand Vision Tech Software Limited. All Rights Reserved.</div>
            </div>
        </div>
    </div>
</template>

<script setup>
definePageMeta({
    ssr: false,
    layout: "custom",
});
const { t } = useI18n({ useScope: "global" });
useCookie("authToken", { maxAge: 0 }).value = null;

useSeoMeta({
    title: () => t("SEO_META.SEO_LOGIN_TITLE"),
    ogTitle: () => t("SEO_META.SEO_LOGIN_TITLE"),
    description: () => t("SEO_META.SEO_LOGIN_DESC"),
    ogDescription: () => t("SEO_META.SEO_LOGIN_DESC"),
});
import { useUserProfile } from "@/stores/index";
import { useStorageType } from "@/hook/useStorageType";
import { useAsyncUserCount } from "@/hook/updateAccount";
import { loginByThird, signInByAppleAuth } from "@/api";
import SignInComp from "@/components/users/SignIn.vue";
import SignUpComp from "@/components/users/SignUp.vue";
import ForgotComp from "@/components/users/Forgot.vue";

const userProfile = useUserProfile();
const hasRemember = ref(true);

const currentYear = new Date().getFullYear();
const componentMap = {
    SignInComp,
    SignUpComp,
    ForgotComp,
};

const toWp = (path) => {
    const url = window.location.origin + path;
    window.open(url, "_blank");
};
const componentId = ref("SignInComp");
const changeComponent = (newComponentId) => {
    componentId.value = newComponentId;
};
const clientId = "************-gt8mqu2k3o2r2adm5nj269smgomeg12n.apps.googleusercontent.com";
// const clientId = "************-pbac6mr9vhid3krivt2n0a24v60se967.apps.googleusercontent.com"; //个人 测试用的
//动态加载google sdk
const loadScript = (link) => {
    return new Promise((resolve, reject) => {
        const outScript = document.createElement("script");
        outScript.type = "text/javascript";
        outScript.src = link;
        outScript.onload = resolve;
        outScript.onerror = reject;
        document.body.appendChild(outScript);
    });
};

//初始化google
const initializeGoogleSignIn = async (response) => {
    const access_token = response.credential;
    const platform = "google";
    const { status, data, message } = await loginByThird({
        user: {
            platform,
            access_token,
            code: "",
        },
    });
    if (status !== 0) {
        openToast.error(message);
        return;
    }

    window.trackEvent("Sign", { el: "sign_success_method=google" });

    signInSuccess(data);
};

const registerAppleSDK = async () => {
    if (!window.AppleID) {
        await loadScript("https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js");
    }
    const redirectURI = window.location.origin + "/app/";
    AppleID.auth.init({
        clientId: "com.piclumen.sign", // 服务 ID
        scope: "name email",
        redirectURI, // 回调 URL
        state: "random_string", // 用于防止 CSRF 攻击的状态字符串
        usePopup: true, // 启用弹出窗口登录
    });
};

//唤起苹果 登录
const exciteAppleAuth = async () => {
    window.trackEvent("Sign", { el: "sign_in_by_apple" });

    const response = await AppleID.auth.signIn();
    const identityToken = response.authorization.id_token; // 这是 JWT token
    const appleEmail = response.user?.email;
    let userName = appleEmail;
    if (response.user?.name) {
        const { firstName, lastName } = response.user.name;
        userName = `${firstName}${lastName}`;
    }
    const { status, message, data } = await signInByAppleAuth({
        identityToken,
        userName,
    });
    if (status == 0) {
        signInSuccess(data);

        window.trackEvent("Sign", { el: "sign_success_method=apple" });
        return false;
    }
    openToast.error(message);
    // try {
    // } catch (error) {
    // openToast.error(error)
    // }
};

// 点击Google登录按钮
const handleGoogleBtn = () => {
    window.trackEvent("Sign", { el: "sign_in_by_google" });
};
const localePath = useLocalePath();
const signInSuccess = async (data) => {
    useCookie("authToken", { maxAge: 60 * 60 * 24 * 365 }).value = data?.token;
    const { setStorageType } = useStorageType();
    const storageType = hasRemember.value ? "localStorage" : "sessionStorage";
    localStorage.setItem("storageTypeText", storageType);
    setStorageType(storageType);
    await userProfile.setUser(data);
    useAsyncUserCount();
    await navigateTo({ path: localePath("/community/explore") });
};

onMounted(async () => {
    if (!import.meta.client) return;
    window.handleCredentialResponse = initializeGoogleSignIn;
    registerAppleSDK();
    await loadScript("https://accounts.google.com/gsi/client");
    window.google.accounts.id.initialize({
        client_id: clientId,
        callback: initializeGoogleSignIn,
    });
    const btn = document.getElementById("customBtn");
    // window.google.accounts.id.prompt()
    // 显示 One Tap 登录
    window.google.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            // One Tap UI未显示或被用户跳过
            window.google.accounts.id.renderButton(btn, {
                theme: "outline",
                width: "300px",
                height: "52px",
                language: "en",
            });
        }
        if (notification.isDismissedMoment()) {
        }
    });
});
</script>

<style lang="scss" scoped>
.content {
    @apply bg-black;
    &::after {
        @apply fixed left-0 top-1/2 bottom-0 w-screen;
        content: "";
        display: block;
        background: linear-gradient(180deg, rgba(138, 18, 252, 0) 0%, rgba(138, 18, 252, 0.7) 100%);
    }
    &::before {
        @apply absolute left-0 top-0 bottom-0 right-2/3;
        content: "";
        display: block;
        background: linear-gradient(230.92deg, rgba(138, 18, 252, 0) 41.05%, rgba(138, 18, 252, 0.3) 82.32%);
    }
}
.mask {
    @apply w-screen min-h-screen object-cover opacity-20 fixed;
}
.other-sign-in {
    @apply font-medium w-full overflow-hidden h-10 border-2 rounded-xl border-solid border-[#7B57E5] relative flex items-center justify-center cursor-pointer hover:bg-fill-wd-1;
}

.sign-in-account ::v-deep(.n-input) {
    --n-text-color: #fff !important;
    --n-caret-color: #fff !important;
    --n-text-decoration-color: #fff !important;
}
</style>
