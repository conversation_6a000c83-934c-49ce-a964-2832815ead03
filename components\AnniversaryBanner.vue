<!--
 * @Author: HuangQS
 * @Description: 页面顶部的活动横幅组件 周年庆
 * @Date: 2025-07-05 11:29:03
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-08-13 10:56:58
 周年庆活动banner，数据取自于userPromotionList数据， 当列表中存在【周年庆的key:anniversary】即代表周年庆开启
-->
<template>
    <ClientOnly>
        <div v-if="isShow" ref="activeBannerRef" class="bannerBox fade-in" :style="{ '--view-height': `${viewHeight}px` }">
            <NuxtLinkLocale to="/user/subscribe" class="w-full h-full relative flex items-center justify-between px-5 text-sm font-medium overflow-hidden" @click="handleSubscribeClick">
                <template v-if="isMobile">
                    <div class="flex flex-1 items-center gap-2 text-text-2 cursor-pointer z-10">
                        <div class="h-full flex items-center justify-center" @click.prevent="handleCloseClick">
                            <n-icon :size="20" class="cursor-pointer text-white">
                                <IconsClose />
                            </n-icon>
                        </div>
                        <div class="flex flex-1 items-center justify-center gap-2 overflow-hidden">
                            <img v-if="!isIn48Hour" src="@/assets/images/anniversary/img_anniversary_banner_cake.webp" class="size-[60px] object-cover pointer-events-none block" />
                            <div class="flex-1 text-white font-semibold text-sm" ref="moFontParentRef">
                                <span> {{ $t("ANNIVERSARY_BANNER.TITLE", 0) }} </span>
                            </div>
                        </div>

                        <n-icon v-if="!isIn48Hour" :size="16" class="text-white rotate-180 z-10"> <IconsBack /> </n-icon>
                        <div v-else class="flex shrink-0 items-center gap-1.5 bg-[#FFDE59] rounded-[18px] pl-4 text-[#F54032]">
                            <n-icon :size="16" class="text-[#F54032] rotate-icon"> <IconsAlarmClock /> </n-icon>
                            <div class="flex-1 min-w-[64px] text-sm font-semibold">{{ anniversaryHoursStr }}:{{ anniversaryMinutesStr }}:{{ anniversarySecondsStr }}</div>
                            <div class="size-9 flex items-center justify-center bg-gradient-to-tr from-[rgb(255,205,1)] to-[rgb(255,205,1)] rounded-full">
                                <n-icon :size="20">
                                    <IconsBack class="rotate-180" />
                                </n-icon>
                            </div>
                        </div>
                    </div>
                    <!-- <n-icon :size="16" class="text-white rotate-180 z-10"> <IconsBack /> </n-icon> -->
                    <img
                        src="@/assets/images/anniversary/img_anniversary_banner.webp"
                        class="absolute -z-1 top-0 left-1/2 transform -translate-x-1/2 w-full h-full object-cover object-center pointer-events-none"
                        alt="anniversary banner"
                    />
                </template>
                <template v-else>
                    <img
                        src="@/assets/images/anniversary/img_anniversary_banner.webp"
                        class="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full object-cover object-center pointer-events-none"
                        alt="anniversary banner"
                    />
                    <div class="size-5"></div>

                    <div class="flex flex-1 text-center items-center justify-center text-text-2 text-sm cursor-pointer z-10 gap-4">
                        <img src="@/assets/images/anniversary/img_anniversary_banner_cake.webp" class="hidden md:block size-[60px] object-cover pointer-events-none" />

                        <div class="flex items-center text-white font-semibold gap-4 text-xs md:text-base">
                            <span class="rounded-2xl shrink-0 px-2 py-0.5 md:bg-[#af3ce1]"> {{ $t("ANNIVERSARY_BANNER.TITLE", 1) }} </span>
                            <span> {{ $t("ANNIVERSARY_BANNER.TITLE", 2) }} </span>
                        </div>

                        <!-- 右侧 订阅按钮 | 倒计时按钮 -->
                        <div v-if="!isIn48Hour" class="hidden md:flex shrink-0 items-center gap-4 bg-[#FFDE59] rounded-[18px] px-4 py-2 text-[#F54032]">
                            <span class="text-xs font-medium md:text-sm">{{ $t("ANNIVERSARY_BANNER.SUBSCRIBE") }}</span>
                            <n-icon :size="20" class="rotate-180"> <IconsBack /> </n-icon>
                        </div>
                        <div v-else class="hidden md:flex shrink-0 items-center gap-1.5 bg-[#FFDE59] rounded-[18px] min-w-[128px] pl-4 text-[#F54032]">
                            <n-icon :size="16" class="text-[#F54032] rotate-icon"> <IconsAlarmClock /> </n-icon>
                            <div class="flex-1 min-w-[64px] text-sm font-semibold">{{ anniversaryHoursStr }}:{{ anniversaryMinutesStr }}:{{ anniversarySecondsStr }}</div>
                            <div class="size-9 flex items-center justify-center bg-gradient-to-tr from-[rgb(255,205,1)] to-[rgb(255,205,1)] rounded-full">
                                <n-icon :size="20">
                                    <IconsBack class="rotate-180" />
                                </n-icon>
                            </div>
                        </div>
                    </div>

                    <n-icon :size="20" class="shrink-0 cursor-pointer text-white z-10" @click.prevent="handleCloseClick">
                        <IconsClose />
                    </n-icon>
                </template>
            </NuxtLinkLocale>
        </div>
    </ClientOnly>
</template>
<script setup>
// 设置nuxt组件仅在客户端起效
import { useThemeStore } from "@/stores/system-config";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { storeToRefs } from "pinia";
const { toSubAnniversaryCountDownTimer, toUnSubAnniversaryCountDownTimer } = useSubscribeStore();
const { isIn48Hour, anniversaryHoursStr, anniversaryMinutesStr, anniversarySecondsStr } = storeToRefs(useSubscribeStore());

const activeBannerRef = ref(null);
const $emit = defineEmits(["close"]);
const { isMobile } = storeToRefs(useThemeStore());

const viewHeight = ref(isMobile.value ? 60 : 60);
const subscribeStore = useSubscribeStore();
const { isHasAnniversary, anniversaryEndTime } = storeToRefs(subscribeStore);
const { user } = storeToRefs(useUserProfile());

// const isShow = ref(true); //仅执行一次 仅在组件创建时执行 避免关闭动画不起效

const todayTimeStr = ref(new Date().toISOString().split("T")[0].replace(/-/g, "")); // 今日时间
const userTimeTodayStr = computed(() => {
    return `${todayTimeStr.value}${user.value?.userId || ""}`;
});

const storeKey = "anniversary";

const isShow = computed(() => {
    if (import.meta.server) return false;
    const localTime = localStorage.getItem(storeKey, "");
    if (userTimeTodayStr.value === localTime) {
        return false;
    }

    return isHasAnniversary.value > 0;
});

onMounted(() => {
    toSubAnniversaryCountDownTimer("banner_anniversary");
});

onBeforeUnmount(() => {
    toUnSubAnniversaryCountDownTimer("banner_anniversary");
});

const customTrackEvent = (el) => {
    window.trackEvent("Commercialization", { el });
};

const handleCloseClick = () => {
    if (activeBannerRef.value) {
        activeBannerRef.value.classList.add("fade-out");
        setTimeout(() => {
            localStorage.setItem(storeKey, userTimeTodayStr.value);
            $emit("close");
        }, 300);
        customTrackEvent("top_banner_close");
    }
};

const handleSubscribeClick = async () => {
    customTrackEvent("top_banner");
};
</script>

<style lang="scss" scoped>
.bannerBox {
    --view-height: 60px;
    height: var(--view-height, 60px);
}

.fade-in {
    --view-height: 60px;
    // 组件显示时动画
    animation: animFadeIn 0.3s ease-in-out forwards;
    @keyframes animFadeIn {
        from {
            opacity: 0;
            margin-top: calc(var(--view-height) * -1);
        }
        to {
            opacity: 1;
            margin-top: 0;
        }
    }
}

.fade-out {
    --view-height: 60px;
    // 组件关闭时隐藏动画
    animation: animFadeOut 0.3s ease-in-out forwards;
    @keyframes animFadeOut {
        from {
            opacity: 1;
            margin-top: 0;
        }
        to {
            opacity: 0;
            margin-top: calc(var(--view-height) * -1);
        }
    }
}

// =============================
@keyframes rotateSequence {
    0% {
        transform: rotate(0);
    }
    3.125% {
        transform: rotate(-12deg);
    }
    6.25% {
        transform: rotate(0);
    }

    9.375% {
        transform: rotate(12deg);
    }
    12.5% {
        transform: rotate(0);
    }

    15.625% {
        transform: rotate(-12deg);
    }
    18.75% {
        transform: rotate(0);
    }

    21.875% {
        transform: rotate(12deg);
    }
    25% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(0);
    }
}

.rotate-icon {
    /* 动画总周期4秒（1秒动效 + 3秒间隔），无限循环 */
    animation: rotateSequence 4s infinite ease-in-out;
    transform-origin: center; /* 确保围绕中心旋转 */
}
</style>
