<template>
    <div class="bg-fill-ww-10 rounded-lg p-4 w-full refer-item">
        <!-- imageReference -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex gap-1.5 items-center">
                <span class="text-text-4 text-sm font-medium" v-if="myItem.displayType">{{ $t(myItem.displayType) }}</span>
                <!-- image reference 的说明 -->
                <template v-if="!isMobile">
                    <n-tooltip trigger="click" placement="bottom" :show-arrow="false" raw @click.stop v-if="myItem.displayType === REFER_TYPES.IMAGE_REFER">
                        <template #trigger>
                            <IconsHelp class="size-4 text-text-4 hover:!text-text-2 cursor-pointer" />
                        </template>

                        <div class="tips-box w-[452px] flex flex-col gap-2">
                            <span class="text-text-1 text-sm font-medium my-2">{{ t("IMAGE_GUIDANCE") }}</span>
                            <div class="flex items-center gap-3">
                                <div class="w-[148px] shrink-0 h-[72px] flex items-center">
                                    <img src="@/assets/images/ref_content.webp" alt="" class="object-cover" />
                                </div>
                                <div class="flex flex-col justify-center">
                                    <div class="flex items-center gap-2 text-text-3 text-sm">
                                        <n-icon size="20">
                                            <IconContentRef />
                                        </n-icon>
                                        <span>Content Ref</span>
                                    </div>
                                    <div class="mt-1.5 text-xs text-text-4">{{ t("CONTENT_REFER_TIPS") }}</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-[148px] shrink-0 h-[72px] flex items-center">
                                    <img src="@/assets/images/ref_style.webp" alt="" class="object-cover" />
                                </div>
                                <div class="flex flex-col justify-center">
                                    <div class="flex items-center gap-2 text-text-3 text-sm">
                                        <n-icon size="20">
                                            <IconStyleRef />
                                        </n-icon>
                                        <span>Style Ref</span>
                                    </div>
                                    <div class="mt-1.5 text-xs text-text-4">{{ t("STYLE_REFER_TIPS") }}</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-[148px] shrink-0 h-[72px] flex items-center">
                                    <img src="@/assets/images/ref_char.webp" alt="" class="object-cover" />
                                </div>
                                <div class="flex flex-col justify-center">
                                    <div class="flex items-center gap-2 text-text-3 text-sm">
                                        <n-icon size="20">
                                            <IconCharRef />
                                        </n-icon>
                                        <span>Character Ref</span>
                                    </div>
                                    <div class="mt-1.5 text-xs text-text-4">{{ t("CHARACTER_REFER_TIPS") }}</div>
                                </div>
                            </div>
                        </div>
                    </n-tooltip>
                    <!-- prompt magic的说明 -->
                    <n-tooltip placement="bottom" trigger="click" :show-arrow="false" raw v-if="myItem.displayType === REFER_TYPES.PROMPT_MAGIC">
                        <template #trigger>
                            <IconsHelp class="size-4 text-text-4 hover:!text-text-2 cursor-pointer" />
                        </template>
                        <div class="tips-box w-80 text-sm">
                            <span class="text-text-1">{{ t(REFER_TYPES.PROMPT_MAGIC) }}</span>
                            <div class="mt-2 text-text-4">{{ t("MAGIC_HELPER_DESC") }}</div>
                        </div>
                    </n-tooltip>
                    <!-- pose control 的说明 当前仅判断展示在image control中-->
                    <n-tooltip placement="bottom" trigger="click" :show-arrow="false" raw @click.stop v-if="myItem.displayType === REFER_TYPES.IMAGE_CONTROL">
                        <template #trigger>
                            <IconsHelp class="size-4 text-text-4 hover:!text-text-2 cursor-pointer" />
                        </template>

                        <div class="tips-box w-[400px]">
                            <div class="flex items-center gap-2 text-text-1">
                                <IconsPose class="size-5" />
                                <span>{{ t("POSE_CONTROL_TITLE") }}</span>
                            </div>
                            <div class="mt-2 text-text-4">
                                <span>{{ t("POSE_CONTROL_DESC") }}</span>
                                <a href="/tutorial/pose-control-feature/" target="_blank" class="hover:underline underline-offset-2 text-link-6" @click.stop>
                                    <span class="ml-1">{{ t("SEE_MORE") }}</span>
                                </a>
                            </div>

                            <div class="mt-2 flex items-center gap-2">
                                <div class="w-24 aspect-square rounded-lg bg-bg-2">
                                    <img src="@/assets/images/control/legend_pose_refer.webp" class="w-full h-full object-cover" />
                                </div>
                                <IconsBack class="size-4 rotate-180 text-text-2" />
                                <div class="w-24 aspect-square rounded-lg bg-bg-2">
                                    <img src="@/assets/images/control/legend_pose_result.webp" class="w-full h-full object-cover" />
                                </div>
                            </div>
                        </div>
                    </n-tooltip>
                </template>
            </div>
            <span class="size-[22px] flex items-center justify-center hover:bg-fill-wd-1 rounded-[4px]" @click="handleDelete">
                <IconsDele class="cursor-pointer text-text-4 hover:text-text-1 size-5 lg:size-4" />
            </span>
        </div>
        <div class="flex gap-3">
            <template v-if="myItem.displayType === REFER_TYPES.IMAGE_REFER">
                <img :src="item.imgUrl" alt="" class="!w-12 h-12 rounded-lg aspect-square object-cover shrink-0" />
                <div class="flex flex-col w-full">
                    <n-select
                        class="select-refer mb-3"
                        :value="myItem.style"
                        :options="supportReferList"
                        :render-label="renderReferLabel"
                        :render-tag="renderReferSelectTag"
                        @update:value="handleUpdateMyItem($event, 'style')"
                    >
                        <template #arrow>
                            <IconsArrowRight class="rotate-90" />
                        </template>
                    </n-select>
                    <div class="flex items-center gap-1 lg:mb-2">
                        <span class="text-text-4 text-xs font-medium">{{ t("CONFIG_BASE_STRENGTH") }}</span>
                        <PicPopover v-if="!isMobile" placement="top" class="!rounded-lg overflow-hidden">
                            <template #trigger>
                                <IconsHelp class="size-4 text-text-4 hover:!text-text-2 cursor-pointer" />
                            </template>
                            <span class="text-xs">{{ t("CONFIG_REFER_TIPS") }}</span>
                        </PicPopover>
                    </div>
                    <n-slider class="w-full mt-2 mb-3" :value="myItem.weight" :height="4" :step="0.05" :max="1.0" :min="0.05" @update:value="handleUpdateMyItem($event, 'weight')" />
                </div>
            </template>
            <!-- PromptMagic -->
            <template v-if="myItem.displayType === REFER_TYPES.PROMPT_MAGIC">
                <img :src="renderStaticImage(item.cover)" alt="" class="w-12 h-12 rounded-lg aspect-square object-cover shrink-0" />
                <div class="flex flex-col w-full">
                    <n-select
                        class="select-refer mb-3"
                        :value="myItem.type"
                        value-field="name"
                        label-field="name"
                        :options="magicTypes"
                        :render-label="renderMagicLabel"
                        :render-tag="renderMagicSelectTag"
                        @update:value="handleUpdateSubOptions"
                    >
                        <template #arrow>
                            <IconsArrowRight class="rotate-90" />
                        </template>
                    </n-select>

                    <n-select class="select-refer" label-field="name" value-field="name" v-model:value="myItem.name" :options="promptMagicOption" @update:value="handleUpdateMyItem($event, 'name')">
                        <template #arrow>
                            <IconsArrowRight class="rotate-90" />
                        </template>
                    </n-select>
                </div>
            </template>
            <!-- ImageControl -->
            <template v-if="myItem.displayType === REFER_TYPES.IMAGE_CONTROL">
                <img :src="item.img_url" alt="" class="w-12 h-12 rounded-lg aspect-square object-cover shrink-0" />
                <div class="flex flex-col w-full">
                    <p class="text-text-4 text-xs font-medium mb-2">Strength</p>
                    <n-slider class="w-full mt-2 mb-3" :value="myItem.weight" :height="4" :step="0.05" :max="1.0" :min="0.05" @update:value="handleUpdateMyItem($event, 'weight')" />
                </div>
            </template>
        </div>
    </div>
</template>
<script setup>
import { I18nT, useI18n } from "vue-i18n";
import { throttle } from "@/utils/tools";
import { magicTypes, magicItems } from "@/utils/magic";
import { renderStaticImage } from "@/utils/tools";
import { IconCharRef, IconStyleRef, IconContentRef, IconStyle, IconArt, IconCamera } from "@/icons/index.js";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isMobile } = storeToRefs(useThemeStore());
import { REFER_TYPES } from "@/utils/constant";
import { NIcon } from "naive-ui";
import { templateRef } from "@vueuse/core";
const { t } = useI18n({ useScope: "global" });

const emit = defineEmits(["delete", "update"]);
const props = defineProps({
    item: {
        type: Object,
        default: () => {},
    },
    supportReferList: {
        type: Array,
        default: () => [],
    },
});
const myItem = ref(props.item);

const promptMagicOption = ref([]);
const handleUpdateSubOptions = (type) => {
    promptMagicOption.value = getPromptMagicOptions(type);
    let newItem = {
        ...myItem.value,
        ...promptMagicOption.value[0],
    };
    console.log(newItem, "newItem");
    handleEmitChanges(newItem);
};

const handleDelete = throttle(() => {
    emit("delete", myItem.value?.id);
}, 300);

watch(
    () => props.item,
    (newVal) => {
        myItem.value = newVal;
    }
);

const handleUpdateMyItem = (val, field = "") => {
    if (!field || !val) return;
    myItem.value[field] = val;
    if (field === "name" && props.item.displayType === REFER_TYPES.PROMPT_MAGIC) {
        const finalMagicOption = promptMagicOption.value?.find((item) => item.name === val);
        myItem.value = {
            ...props.item,
            ...finalMagicOption,
        };
    }
    handleEmitChanges();
};

const handleEmitChanges = (val = null) => {
    nextTick(() => {
        emit("update", val || myItem.value);
    });
};

//统一返回icon
const getIcon = (option, type) => {
    let icon = null;
    if (type === "PromptMagic") {
        icon = IconStyle;
        if (option.name == "Painting") {
            icon = IconArt;
        }
        if (option.name == "Photography") {
            icon = IconCamera;
        }
    }
    if (type === "ImageReference") {
        icon = IconStyleRef;
        if (option.value == "contentRefer") {
            icon = IconContentRef;
        }
        if (option.value == "characterRefer") {
            icon = IconCharRef;
        }
    }
    return icon;
}; // magic自定义icon
const renderMagicLabel = (option) => {
    let icon = getIcon(option, "PromptMagic");
    return h(
        "div",
        {
            style: {
                display: "flex",
                alignItems: "center",
            },
        },
        [
            h(
                NIcon,
                {
                    size: "20",
                    class: "w-6 h-6 rounded  flex items-center justify-center",
                },
                {
                    default: () => h(icon),
                }
            ),
            h(
                "div",
                {
                    style: {
                        marginLeft: "12px",
                        padding: "4px 0",
                    },
                },
                [
                    h(
                        "div",
                        {
                            style: {
                                display: "flex",
                                alignItems: "center",
                            },
                        },
                        [h("div", null, t(option.label))]
                    ),
                ]
            ),
        ]
    );
};
const renderMagicSelectTag = ({ option }) => {
    let icon = getIcon(option, "PromptMagic");
    return h(
        "div",
        {
            style: {
                display: "flex",
                alignItems: "center",
                gap: "8px",
            },
        },
        [
            h(
                NIcon,
                {
                    size: "20",
                    class: "w-6 h-6 rounded  flex items-center justify-center",
                },
                {
                    default: () => h(icon),
                }
            ),
            h(
                "div",
                {
                    style: {
                        display: "flex",
                        alignItems: "center",
                    },
                },
                t(option.label)
            ),
        ]
    );
};
// reference自定义icon
const renderReferLabel = (option) => {
    let icon = getIcon(option, "ImageReference");
    return h(
        "div",
        {
            style: {
                display: "flex",
                alignItems: "center",
            },
        },
        [
            h(
                NIcon,
                {
                    size: "20",
                    class: "w-6 h-6 rounded  flex items-center justify-center",
                },
                {
                    default: () => h(icon),
                }
            ),
            h(
                "div",
                {
                    style: {
                        marginLeft: "12px",
                        padding: "4px 0",
                    },
                },
                [
                    h(
                        "div",
                        {
                            style: {
                                display: "flex",
                                alignItems: "center",
                            },
                        },
                        [h("div", null, [option.label])]
                    ),
                ]
            ),
        ]
    );
};

const renderReferSelectTag = ({ option }) => {
    let icon = getIcon(option, "ImageReference");
    return h(
        "div",
        {
            style: {
                display: "flex",
                alignItems: "center",
                gap: "8px",
            },
        },
        [
            h(
                NIcon,
                {
                    size: "20",
                    class: "w-6 h-6 rounded  flex items-center justify-center",
                },
                {
                    default: () => h(icon),
                }
            ),
            h(
                "div",
                {
                    style: {
                        display: "flex",
                        alignItems: "center",
                    },
                },
                option.label
            ),
        ]
    );
    1;
};

onMounted(() => {
    if (props.item?.displayType === REFER_TYPES.PROMPT_MAGIC) {
        promptMagicOption.value = getPromptMagicOptions();
    }
});

const getPromptMagicOptions = (type) => {
    if (!type) type = myItem.value?.type || "";
    if (!type) return [];
    return magicItems.filter((item) => item.type === type);
};
</script>

<style lang="scss" scoped>
.select-refer {
    @apply flex items-center h-8 rounded-lg bg-fill-ipt-1 w-full text-text-2 text-xs;
    ::v-deep(.n-base-selection-input__content) {
        @apply text-xs;
    }
}
::v-deep(.n-base-selection) {
    @apply h-8;
    height: 32px !important;
}
::v-deep(.n-base-selection-label) {
    // @apply h-8;
}
</style>
