<template>
    <!-- 生图结果列表 -->
    <div ref="genResultRef" class="w-full flex-1 h-full overflow-auto pb-[60px] scroll-container pl-4 lg:pl-6 pr-3 lg:pr-4" @scroll="handleScroll">
        <ClientOnly>
            <DynamicScroller ref="virtualRef" :items="dataList" :min-item-size="54" :emit-update="true" keyField="promptId" class="no-scroll min-h-full max-w-[1200px] mx-auto" @scroll="pageScroll">
                <!-- 生图中的占位loading -->
                <template #before>
                    <div v-if="showDate" class="text-sm min-h-10 flex items-center gap-2 text-text-4 font-medium mb-4">{{ t(`SHORT_TODAY`) }}</div>
                    <div class="flex flex-col" v-for="(item, index) in processTaskList" :key="item.markId">
                        <div class="flex items-center justify-between">
                            <div class="flex gap-2 items-center">
                                <img v-if="item?.originCreate !== 'customUpload'" :src="renderModelIcon(item.model_id)" class="size-6 rounded-full" alt="" />
                                <!-- Pc 展示生图比例 -->
                                <CustomTag :label="`${item?.resolution.width} x ${item?.resolution.height} ${getResolutionLabel(item.resolution)}`" type="info" class="hidden lg:flex" />
                                <!-- tags Pc 展示全部tag -->
                                <CreateOriginCreateTag :task="item" type="info" class="hidden lg:flex" />
                                <!-- tags H5 只展示一个tag 和 ...-->
                                <CreateOriginCreateTag :task="item" type="info" class="flex lg:hidden" only-one-tag />
                            </div>
                        </div>
                        <div v-if="item.prompt" class="prompt line-clamp-2 overflow-hidden text-text-2 font-medium text-sm mt-2">{{ promptFilter(item).prompt }}</div>
                        <div class="flex gap-2">
                            <div class="shrink-0" :class="['w-full gap-2 grid  mt-4 mb-6 lg:mb-8', getGridClass(item)]">
                                <div
                                    class="bg-fill-wd-1 p-2 flex flex-col items-center justify-center relative rounded-lg overflow-hidden"
                                    v-for="inner in item.resolution.batch_size"
                                    :key="inner"
                                    :style="{
                                        aspectRatio: `${item.resolution.width / item.resolution.height}`,
                                    }"
                                >
                                    <div class="gen-tag rounded-full">
                                        <n-icon v-if="item.status !== 'running'" size="16" :class="['text-primary']">
                                            <IconsSpinLoading />
                                        </n-icon>
                                        <n-progress
                                            v-else
                                            :style="successProgressStyle"
                                            :show-indicator="false"
                                            style="width: 16px"
                                            type="circle"
                                            :offset-degree="90"
                                            :percentage="item.progress"
                                            :stroke-width="12"
                                        />
                                        <div class="text-xs text-text-3 font-medium">{{ item.fastHour ? t("SHORT_FAST") : t("SHORT_RELAX") }}</div>
                                    </div>
                                    <GlowingText v-if="item.status === 'running'" :text="`${t('MENU_JOB_STATUS_GENERATING')}`" />
                                    <p v-else class="text-text-4">{{ t("MENU_JOB_STATUS_QUEUEING") }}...{{ item.index > 0 ? `(${item.index})` : "" }}</p>
                                    <span v-if="!item.fastHour" class="cursor-pointer text-primary font-medium hidden lg:flex" @click="handleBuyMoreLumen">{{ t("GET_MORE_FAST_TASK") }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- 列表的请求状态 -->
                <template #after>
                    <div class="h-full w-full flex items-center justify-center">
                        <BusinessLoadStatus :status="loadStatus">
                            <template #noData>
                                <img v-if="keyWords" class="dark:block hidden w-[120px]" src="@/assets/images/notice_empty.webp" alt="" />
                                <img v-if="keyWords" class="dark:hidden block w-[120px]" src="@/assets/images/notice_empty_light.webp" alt="" />
                                <img v-if="!keyWords" class="dark:block hidden w-[120px]" src="@/assets/images/no_create_history_dark.webp" alt="" />
                                <img v-if="!keyWords" class="dark:hidden block w-[120px]" src="@/assets/images/no_create_history_light.webp" alt="" />
                                <span class="mt-4 opacity-70 dark:text-dark-text">{{ keyWords ? t("NO_SUCH_RES") : t("GUIDE_CREATE") }}</span>
                            </template>
                        </BusinessLoadStatus>
                    </div>
                </template>
                <template #default="{ item, index, active }">
                    <!-- 任务项  一个任务占据一排 1-4张图-->
                    <DynamicScrollerItem :item="item" :active="active" :size-dependencies="[item.message]" :data-index="index" :data-active="active">
                        <div class="task-item relative w-full group">
                            <div v-if="showTimeGroup(item)" class="text-sm min-h-10 flex items-center gap-2 text-text-4 font-medium mb-4">
                                <span>{{ item.showDay.shortStr || t(item.showDay.shortStrKey) }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex gap-2 items-center">
                                    <img v-if="item.img_urls[0]?.originCreate !== 'customUpload'" :src="renderModelIcon(item.model_id)" class="size-6 rounded-full" alt="" />
                                    <!-- Pc 展示生图比例 -->
                                    <CustomTag :label="`${item.img_urls[0]?.realWidth} x ${item.img_urls[0]?.realHeight} ${getResolutionLabel(item.img_urls[0])}`" type="info" class="hidden lg:flex" />
                                    <!-- tags Pc 展示全部tag -->
                                    <CreateOriginCreateTag :task="item" type="info" class="hidden lg:flex" />
                                    <!-- tags H5 只展示一个tag 和 ...-->
                                    <CreateOriginCreateTag :task="item" type="info" class="flex lg:hidden" only-one-tag />
                                </div>
                                <div class="flex lg:!hidden items-center gap-2 lg:group-hover:!flex">
                                    <!-- 使用提示词 -->
                                    <div class="action-dot !hidden md:!flex" v-if="item.prompt" @click="handleUseText(item)">
                                        <IconsText class="size-5 lg:size-3.5" />
                                        <span class="hidden 2xl:inline-block"> {{ $t("TOOLBAR_USE_TEXT") }} </span>
                                    </div>
                                    <!-- 使用全部参数 -->
                                    <div v-if="item.img_urls[0]?.originCreate !== 'customUpload'" class="action-dot" @click="handleRemix(item)">
                                        <IconsCreate class="size-5 lg:size-3.5" />
                                        <span class="hidden 2xl:inline-block">{{ $t("TOOLBAR_REMIX") }}</span>
                                    </div>
                                    <!-- 重新执行 -->
                                    <div v-if="item.img_urls[0]?.originCreate === 'create'" class="action-dot !hidden md:!flex" @click="handleRerunTask(item)">
                                        <IconsRerun class="size-5 lg:size-3.5" />
                                        <span class="hidden 2xl:inline-block"> {{ $t("TOOLBAR_RERUN") }} </span>
                                    </div>
                                    <!-- 删除整个任务 -->
                                    <div class="action-dot" @click="handleDelTask(item)">
                                        <IconsDele class="size-5 lg:size-3.5" />
                                        <span class="hidden 2xl:inline-block">{{ $t("TOOLBAR_DELETE") }}</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 生图提示词 -->
                            <div v-if="item.prompt" class="prompt line-clamp-2 overflow-hidden text-text-2 font-medium text-sm mt-2">{{ promptFilter(item).prompt }}</div>

                            <div class="flex gap-2">
                                <div class="grid gap-2 shrink-0 flex-1 mt-4 mb-6 lg:mb-8" :class="[getGridClass(item)]">
                                    <!-- 图片项 -->
                                    <div
                                        class="relative virtual-item-img rounded-lg overflow-hidden"
                                        :style="{
                                            aspectRatio: inner.realWidth / inner.realHeight,
                                        }"
                                        v-for="inner in item.img_urls"
                                        :key="inner.imgName"
                                    >
                                        <div
                                            v-if="inner.collectNums && inner.collectNums > 0"
                                            class="w-8 h-8 rounded-full bg-fill-t-1 backdrop-blur-lg flex items-center justify-center text-text-1 absolute top-2 left-2 text-white"
                                        >
                                            <n-icon size="20">
                                                <IconsCollected />
                                            </n-icon>
                                        </div>
                                        <img
                                            :src="inner.highMiniUrl || inner.thumbnailUrl"
                                            loading="lazy"
                                            class="w-full h-full object-cover cursor-pointer transition-all"
                                            :class="{ loading_bg_anime: !inner.loaded }"
                                            :style="{
                                                aspectRatio: `${inner.realWidth / inner.realHeight}`,
                                            }"
                                            @load="inner.loaded = true"
                                            @click="viewDetail(inner)"
                                        />

                                        <div class="action-bar bottom-mask" :class="{ '!block': isMoreOpen === inner.imgName || isIpad }">
                                            <div class="flex items-center justify-between gap-3 text-white">
                                                <!-- 删除单张图片 -->
                                                <div class="action-item hover:!bg-fill-dd-4 cursor-pointer" @click="deleteItem(item.promptId, inner.imgName)">
                                                    <IconsDele class="size-5" />
                                                </div>
                                                <div class="flex gap-3 items-center">
                                                    <!-- 编辑图片 -->
                                                    <div
                                                        class="action-item"
                                                        :class="[inner.sensitive ? 'opacity-40 cursor-not-allowed' : 'hover:!bg-fill-dd-4 cursor-pointer']"
                                                        @click="editItem(item, inner)"
                                                    >
                                                        <IconsChatEdit class="size-5" />
                                                    </div>
                                                    <!-- 下载图片 -->
                                                    <DownLoadPopover
                                                        :link="inner.highThumbnailUrl || inner.imgUrl"
                                                        :thumbnail="inner.thumbnailUrl || inner.highThumbnailUrl || inner.imgUrl"
                                                        :isSensitive="!!inner.sensitive"
                                                        class="!size-fit hidden lg:flex"
                                                        @download-end="trackEvents('create_hover=download')"
                                                    >
                                                        <div class="action-item" :class="[inner.sensitive ? 'opacity-40 cursor-not-allowed' : 'hover:!bg-fill-dd-4 cursor-pointer']">
                                                            <IconsDownload class="size-5" />
                                                        </div>
                                                    </DownLoadPopover>
                                                    <!-- 更多操作 -->
                                                    <PopoverDropdown
                                                        trigger="click"
                                                        placement="bottom-end"
                                                        class="rounded-lg overflow-hidden w-[200px]"
                                                        :options="actionMenuOptions(inner, item)"
                                                        @state-change="handleChangeIsMoreOpen($event, inner.imgName)"
                                                        @select="(command) => handleSelect(command, inner, item)"
                                                    >
                                                        <div class="action-item hover:!bg-fill-dd-4 cursor-pointer" :class="{ '!bg-fill-dd-4': isMoreOpen === inner.imgName }">
                                                            <IconsHorMore class="size-5" />
                                                        </div>
                                                        <template #removeBg="{ item }">
                                                            <n-tooltip placement="right-start" :show-arrow="false" raw>
                                                                <template #trigger>
                                                                    <div class="flex items-center gap-2">
                                                                        <n-icon size="20"> <IconsRemoveBg /> </n-icon>
                                                                        <span>{{ $t(item.label, 2) }}</span>
                                                                    </div>
                                                                </template>
                                                                <div class="text-sm text-nowrap text-text-2 bg-bg-5 rounded-lg py-2 px-3">
                                                                    {{
                                                                        $t(
                                                                            "ESTIMATE_COST_LUMENS",
                                                                            {
                                                                                num: 1,
                                                                            },
                                                                            3
                                                                        )
                                                                    }}
                                                                </div>
                                                            </n-tooltip>
                                                        </template>
                                                    </PopoverDropdown>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 黄图禁止查看遮罩 -->
                                        <div v-show="inner.sensitive" @click.stop="null" class="nsfw-content overflow-hidden">
                                            <n-icon size="24">
                                                <IconsNoEye />
                                            </n-icon>
                                            <span class="mt-1">NSFW Content</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </DynamicScrollerItem>
                </template>
            </DynamicScroller>
            <!-- 详情预览 -->
            <Preview ref="previewRef" :viewHigh="true" v-model:show="showPreview" :total="flatList.length" :list="flatList" @loadMore="loadMore" @delItem="deleteItem" />
        </ClientOnly>
        <!-- 返回顶部 -->
        <BackToTop v-if="genResultRefScrollTop > 200" @click="gotoTop" />
    </div>
</template>
<script setup>
import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import { getHistoryTask, deleteResult, batchDelImg } from "@/api";
import { NIcon } from "naive-ui";
import GlowingText from "@/components/business/glowing-text/index.vue";
import BusinessModal from "@/components/business/modal/index.vue";
import { HD, RemoveBg, DownLoad, Create, CloseRoundFill, IconDescribe } from "@/icons/index";
import { isScrolledToBottom, debounce, handleResetParams, processData, formatPonyV6Prompt, isVipModel, renderModelIcon, downloadImage } from "@/utils/tools";
import { allowUpscale } from "@/hook/allowAction";
import { useUserProfile } from "@/stores";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useSyncAction } from "@/stores/syncAction";
import { useSubPermission, useBuyLumenModal } from "@/hook/subscribe";
import { useDescribe, useUpscale, useImageEdit, useRemix, useRemoveBg, useCheckMaxTask } from "@/hook/create";
import { useDeleteImageConfirm } from "@/hook/updateAccount";
import { SUB_EL, SUBSCRIBE_PERMISSION, KEEPALIVE_PAGES, SHAPE_ALL, ERROR_CODE_ENUM } from "@/utils/constant.js";
import { imageGenerator } from "@/api";
import { successProgressStyle } from "@/utils/constant-style";
import { onActivated } from "vue";
import { useCreateStore, useCurrentTaskQueue } from "@/stores/create";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";
import { useGlobalError } from "@/hook/error.js";

// keep-alive配置
defineOptions({
    name: KEEPALIVE_PAGES.IMAGE_CREATE_WEB,
});
const emit = defineEmits(["rerun", "clearSearch"]);
const props = defineProps({
    keyWords: {
        // 搜索列表的关键字
        type: String,
        default: "",
    },
});

const { t } = useI18n({ useScope: "global" });
const createStore = useCreateStore();
const { showMessage, clearMessageBox } = useModal();
const { isMobile, isIpad } = storeToRefs(useThemeStore());
const userProfile = useUserProfile();
const syncAction = useSyncAction();
const { checkPermission, checkPermissionNotModal } = useSubPermission();
const { openModal: openImageEdit } = useImageEdit();

/**
 * 全局状态变更——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */
/**
 * 返回顶部
 */
const gotoTop = () => {
    genResultRefScrollTop.value = 0;
    restoreScrollTop();
};
//是否展示时间归组
const showTimeGroup = computed(() => {
    return ({ showTime, showDay }) => {
        // 不是组内第一条数据，直接返回false
        if (!showTime) {
            return false;
        }
        //是今天的数据 但是 有任务在生成中 ，返回false
        if (showDay.diff === 0 && showDate.value) {
            return false;
        }
        return true;
    };
});

//是否展示占位图
const showDate = computed(() => {
    return currentTaskQueue.taskQueue.length > 0;
});

// 打开购买Lumen弹窗
const { openModal } = useBuyLumenModal();
const subscribeStore = useSubscribeStore();
const handleBuyMoreLumen = async () => {
    trackEvents("get_more_fast_task");
    if (!subscribeStore.isFreeVip) {
        subscribeStore.setBuyLumenGaEvent(SUB_EL.GET_MORE_FAST);
    }
    const res = await checkPermission(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER, { triggerEl: SUB_EL.FAST_TASK });

    if (!res) return;
    openModal();
};

/**
 * 单张图片状态、处理、操作—————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */
const isMoreOpen = ref("");
const handleChangeIsMoreOpen = (state, name) => {
    isMoreOpen.value = state ? name : "";
};

//提示词过滤处理
const promptFilter = computed(() => {
    return (item) => formatPonyV6Prompt(item, "output");
});

//删除单张图片二次确认
const delLoading = ref(false);
const deleteItem = async (promptId, imgName) => {
    trackEvents("create_hover=delete");
    const delCheck = await useDeleteImageConfirm();
    if (!delCheck) {
        return;
    }
    confirmDel(promptId, imgName);
};

//确认删除
const confirmDel = async (promptId, imgName) => {
    if (delLoading.value) {
        openToast.info(t("DEL_TIPS"));
        return;
    }
    delLoading.value = true;
    let currentImhIndex = flatList.value.findIndex((item) => item.imgName === imgName);
    const taskIndex = dataList.value.findIndex((item) => item.promptId === promptId);
    if (taskIndex < 0) {
        delLoading.value = false;
        return;
    }
    const task = dataList.value[taskIndex];
    const res = await handleDel(promptId, imgName, task.loginName);
    delLoading.value = false;
    if (!res) {
        return;
    }
    syncAction.publish("delImg", {
        promptId,
        imgName,
    });
    const newUrls = task.img_urls.filter((item) => item.imgName !== imgName);
    if (newUrls.length === 0) {
        dataList.value.splice(taskIndex, 1);
        dataList.value = processData([...dataList.value], "createTimestamp");
    } else {
        task.img_urls = newUrls;
        dataList.value.splice(taskIndex, 1, task);
    }
    if (showPreview.value) {
        if (flatList.value.length === 0) {
            closePreview();
            return;
        }
        previewRef.value && previewRef.value.forceUpdateIndex(currentImhIndex);
    }
};
/**
 * 删除单张图片
 * @param promptId
 * @param imgName
 * @param loginName
 */
const handleDel = async (promptId, imgName, loginName) => {
    return new Promise(async (resolve) => {
        const { status, message } = await deleteResult({ promptId, imgName, loginName });
        resolve(status === 0);
        if (status != 0) {
            openToast.error(message);
            return;
        }
        openToast.success(t("ACTION_SUCCESS_TIPS"));
    });
};

//二次编辑图片
const editItem = (outer, inner) => {
    if (inner.sensitive) return;
    const { promptId, model_id } = outer;
    const { imgUrl, img_url = "", realWidth, realHeight, id } = inner;
    const isEditingImg = {
        promptId,
        model_id,
        imgUrl,
        img_url,
        realWidth,
        realHeight,
        id,
    };
    trackEvents("create_hover=edit");
    openImageEdit({ item: isEditingImg });
};

//图片详情预览
const previewRef = ref(null);
const showPreview = ref(false);

const viewDetail = ({ imgName }) => {
    const index = flatList.value.findIndex((item) => item.imgName === imgName);
    showPreview.value = true;
    trackEvents("create_details_show");
    nextTick(() => {
        previewRef.value && previewRef.value.forceUpdateIndex(index);
    });
};

//下拉菜单选择项
const renderIcon = (icon) => {
    return () => {
        return h(
            NIcon,
            { size: 24 },
            {
                default: () => h(icon),
            }
        );
    };
};
const defMenu = [
    {
        label: "TOOLBAR_REMIX",
        key: "remix",
        icon: renderIcon(Create),
        allowCheck: ({ originCreate }) => originCreate === "customUpload" || originCreate === "crop",
    },
    {
        label: "FEATURE_HIR_FIX_TITLE",
        key: "hirfix",
        icon: renderIcon(HD),
        // suffix: renderIcon(IconDiamond),
        allowCheck: (row) => !allowUpscale.value(row),
    },
    {
        label: "FEATURE_DESCRIBE_TITLE",
        key: "describe",
        icon: renderIcon(IconDescribe),
        allowCheck: ({ sensitive }) => !!sensitive,
    },
    {
        label: "TOOLBAR_REMOVE_BG",
        key: "removeBg",
        icon: renderIcon(RemoveBg),
        allowCheck: ({ sensitive }) => !!sensitive,
    },
];
const actionMenuOptions = computed(() => {
    return (inner, task) => {
        let menu = [];
        if (isIpad.value) {
            //移动端 下载按钮放到下拉中
            menu = [
                {
                    label: "TOOLBAR_DOWNLOAD",
                    key: "download",
                    icon: renderIcon(DownLoad),
                    allowCheck: () => {},
                },
            ];
        }
        menu = [...menu, ...defMenu];

        return menu.map((item) => {
            const res = {
                ...item,
                disabled: item.allowCheck({ ...task, ...inner }),
            };
            return res;
        });
    };
});

///去背景
const { removeBg } = useRemoveBg();
const handleClearBackground = (imgUrl, task) => {
    const base = handleResetParams(task);
    trackEvents("create_hover_more=remove_bg");
    removeBg(base, {
        imgUrl,
        promptId: task.promptId,
        modelId: task.model_id,
        loginName: task.loginName,
    });
};
// 超分
const { hiresFixTask } = useUpscale();
const handleHireFix = (imgUrl, task) => {
    const { realWidth, realHeight } = task?.img_urls[0];
    const base = handleResetParams(task);
    const { promptId, model_id, loginName } = task;

    const expandConf = {
        imgUrl,
        promptId,
        model_id,
        loginName,
        realWidth,
        realHeight,
    };
    trackEvents("create_hover_more=upscale");
    hiresFixTask(base, {
        ...expandConf,
    });
};
/**
 * 下拉操作点击后的处理
 * @param e
 * @param item
 * @param task
 */
const handleSelect = async (e, item, task) => {
    if (e === "download") {
        handleDownloadInMobile(item);
        return;
    }
    switch (e) {
        case "removeBg":
            handleClearBackground(item.imgUrl, task);
            break;
        case "remix":
            handleRemix(task);
            break;
        case "hirfix":
            handleHireFix(item.imgUrl, task);
            break;
        case "describe":
            const { highMiniUrl, thumbnailUrl, highThumbnailUrl, imgUrl } = item;
            useDescribe(highMiniUrl || thumbnailUrl || highThumbnailUrl || imgUrl);
            trackEvents("create_hover_more=describe");
            break;
    }
};
// 移动端下载图片
const downloadFileType = computed(() => userProfile.userConfig.downloadFileType); //用户配置的下载格式
const handleDownloadInMobile = (inner) => {
    const key = downloadFileType.value || "png";
    if (!!inner.sensitive) return;
    trackEvents("create_hover=download");
    const link = inner.highThumbnailUrl || inner.imgUrl;
    const thumbnail = inner.thumbnailUrl || inner.highThumbnailUrl || inner.imgUrl;
    if (key === "jpg") {
        downloadImage(thumbnail, key);
        return;
    }
    downloadImage(link, key);
};

//使用当前图片 进行提示词反推

/**
 * 整个任务的状态、处理、操作——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */

// 使用当前任务提示词
const handleUseText = async (row) => {
    createStore.setPrompt(row?.prompt);
};
/**
 * 展示当前图片的比例 如 1:1
 * @param imgInfo
 */
const getResolutionLabel = (imgInfo) => {
    let { realWidth, realHeight, width = 0, height = 0 } = imgInfo || {};
    if (!realWidth || !realHeight) {
        realWidth = width;
        realHeight = height;
    }
    if (!realWidth || !realHeight) return "";
    const label = SHAPE_ALL.find((i) => i.width / i.height === realWidth / realHeight)?.label;
    return label ? `(${label})` : "";
};
// 使用全部参数 remix
const handleRemix = async (row) => {
    let { genInfo } = row;
    // let remixParams = handleResetParams(genInfo || row);
    let remixParams = genInfo || row;
    const { resolution } = remixParams;
    trackEvents("create_hover_more=remix");
    const { multi_img2img_info = {}, img_control_info = {}, model_ability = {} } = genInfo || {};
    useRemix({
        ...remixParams,
        model_ability,
        style_list: multi_img2img_info?.style_list || [],
        image_control_list: img_control_info?.style_list || [],
        batch_size: resolution.batch_size,
    });
};

//重新执行
//开始生图任务
const { showError } = useGlobalError();
const handleSubmitRerunTask = async (conf, longTask = false) => {
    const currentTaskQueue = useCurrentTaskQueue();
    if (currentTaskQueue.isLoading) {
        openToast.error(t("TOAST_TASK_LIMIT"), 5e3);
        return;
    }
    // 检查当前是否允许生图
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus && !checked.preloadStatus) {
        showError(ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR);
        return;
    }
    const isRerun = true;
    conf = await useRemix(conf, isRerun);

    emit("rerun", conf);
};

// 判断当前任务能否rerun
const handleRerunTask = async (task) => {
    const isMember = await checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
    if (!isVipModel({ model_id: task.model_id }) || isMember) {
        handleSubmitRerunTask(task.genInfo || task);
        trackEvents("rerun");
        return;
    }
    trackEvents("member_model_popup_show");
    showMessage({
        style: { width: "500px", padding: 0 },
        showCancel: false,
        showConfirm: false,
        content: h(BusinessModal, {
            base: {
                title: t("CREATE.VIP_ONLY_MODEL.TITLE"),
                description: t("CREATE.VIP_ONLY_MODEL.DESC"),
                cancelText: t("COMMON_BTN_CANCEL"),
                confirmText: t("SUBSCRIBE_UPGRADE"),
            },
        }),
    })
        .then(async () => {
            trackEvents("member_model_popup_upgrade");
            await navigateTo(localePath("/user/subscribe"));
        })
        .catch(() => {
            return;
        });
};

//删除整个任务
const handleDelTask = async (task) => {
    const isDelTask = true;
    const delCheck = await useDeleteImageConfirm(isDelTask);
    if (!delCheck) {
        return;
    }
    trackEvents("delete");
    const imgList = (task.img_urls || []).map((item) => ({ imgName: item.imgName, promptId: task.promptId }));
    const { status, message } = await batchDelImg(imgList);
    if (status !== 0) {
        showMessage({
            style: { width: "420px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, message),
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(CloseRoundFill) }),
            title: t("TOAST_TITLE_WARNING"),
        });
        return;
    }
    const toDelete = new Set(imgList.map((item) => item.imgName));
    syncAction.publish("batchDelImg", toDelete);
};

/**
 * 生图历史数据加载、搜索————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */
const virtualRef = ref(null);
const page = ref({
    pageNum: 0,
    pageSize: 10,
    total: 1,
});

//分页数据
const dataList = ref([]);

const loadStatus = ref("more");
const reH = ref(false);
const getPageList = async () => {
    if (loadStatus.value === "loading") {
        return;
    }
    let { pageNum, pageSize, total } = page.value;

    if (pageNum >= Math.ceil(total / pageSize)) {
        reH.value = true;
        loadStatus.value = "noMore";
        return;
    }
    reH.value = false;

    pageNum += 1;
    loadStatus.value = "loading";

    const { status, message, data } = await getHistoryTask({ pageNum, pageSize, vagueKey: props.keyWords });
    if (status != 0) {
        openToast.error(message);
        return;
    }
    if (!data) {
        return;
    }
    const newArray = data?.resultList || [];
    let list = newArray.map((item) => {
        let { genInfo = {} } = item;
        const { prompt } = formatPonyV6Prompt(genInfo, "output");
        return {
            ...genInfo,
            ...item,
            prompt,
            width: genInfo.resolution?.width,
            height: genInfo.resolution?.height,
        };
    });
    list = list.filter((item) => item.img_urls && item.img_urls.length > 0);
    page.value.pageNum = pageNum;
    page.value.total = data.total;
    loadStatus.value = pageNum * pageSize > data.total ? "noMore" : "more";
    if (pageNum === 1) {
        dataList.value = [];
    }
    const originList = dataList.value.concat(list).reduce(
        (acc, current) => {
            if (!acc.map.has(current.promptId)) {
                acc.map.set(current.promptId, true);
                acc.result.push(current);
            }
            return acc;
        },
        { map: new Map(), result: [] }
    ).result;

    dataList.value = processData(originList, "createTimestamp");
    if (!dataList.value?.length) {
        loadStatus.value = "noData";
    }
    virtualRef.value?.forceUpdate();
    return;
};
getPageList();

//分页数据扁平化
const flatList = computed(() => {
    return dataList.value.reduce((acc, item) => {
        const { img_urls, genInfo, ...rest } = item;
        const flattenedList = (img_urls || []).map((listItem) => ({
            ...rest,
            ...listItem,
        }));
        return acc.concat(flattenedList);
    }, []);
});

const pageScroll = debounce((e) => {
    const isBottom = isScrolledToBottom(e.target);
    if (!isBottom) {
        return;
    }
    getPageList();
}, 60);

//关键词模糊匹配
const handleSearch = debounce(() => {
    trackEvents("search_btn=create", "APP_SEARCH_LIST");
    if (loadStatus.value === "loading") {
        return;
    }
    page.value = {
        pageNum: 0,
        pageSize: 20,
        total: 1,
    };
    getPageList();
    closePreview();
}, 300);

//加载下一页
const loadMore = debounce(() => {
    getPageList();
}, 150);

/**
 * 任务状态、任务队列、任务完成情况——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */
//当前进行中的任务队列
const currentTaskQueue = useCurrentTaskQueue();
const processTaskList = ref(currentTaskQueue.taskQueue);

watch(
    () => currentTaskQueue.taskQueue,
    (tasks) => {
        processTaskList.value = tasks;
        if (tasks.length) {
            loadStatus.value = "more";
        }
        updateTaskProgress(tasks.length === 0);
    }
);
const animationFrameId = ref(null);
// 停止
const stopAnimation = () => {
    if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value);
        animationFrameId.value = null;
        console.log("动画已停止");
    }
};
// 前端维护每个人物耗时 更新生图进度
const animate = () => {
    if (processTaskList.value.length === 0) {
        stopAnimation();
        return;
    }
    processTaskList.value = processTaskList.value.map((task) => {
        if (task.index === 0) {
            //  预估耗时 ms
            // const et = task.resolution.batch_size * 5.5 * 1000;
            //当前耗时 ms
            const diffTime = Date.now() - task.genBeginTime;
            // console.log(`当前任务实际 耗时: ${diffTime / 1000}  s`);
            task.progress = Math.floor(Math.max(0, Math.min((diffTime / task.elapsedTime) * 100, 95)));
        }
        return task;
    });

    animationFrameId.value = requestAnimationFrame(animate);
};
// 启动动画
const startAnimation = () => {
    if (!animationFrameId.value) {
        animate();
    }
};
const updateTaskProgress = (stopAnima = false) => {
    if (stopAnima) {
        stopAnimation();
        return;
    }
    startAnimation();
};

/**
 * 页面布局、窗口监听、滚动、状态保持————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */
const genResultRef = ref();
const genResultRefScrollTop = ref(0);
const handleScroll = (e) => {
    genResultRefScrollTop.value = e.target.scrollTop;
    if (isScrolledToBottom(e.target)) {
        loadMore();
    }
};
const restoreScrollTop = () => {
    genResultRef.value?.scrollTo({ top: genResultRefScrollTop.value });
};

onActivated(() => {
    restoreScrollTop(); //滚动高度持久化
});

let aItemWidth = ref(1424 / 4);
//窗口监听
const handleResize = () => {
    if (genResultRef.value) {
        let finalWidth = Math.min(genResultRef.value.offsetWidth, 1200);
        aItemWidth.value = finalWidth / 4;
    }
};
// 计算每行的图片数量
const getGridClass = (item) => {
    const { width, height } = item?.resolution || {};
    const aspectRatio = width / height;
    const currentItemHeight = aItemWidth.value / aspectRatio;
    return currentItemHeight < 150 ? "grid-cols-2" : "grid-cols-4";
};

/**
 * 多页面状态数据更新事件共享————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
 */
// 更新生图历史数据 （订阅共享事件）
const updateEmit = ({ promptId, updateKey, updateValue, imgName }) => {
    let taskIndex = dataList.value.findIndex((item) => item.promptId === promptId);
    if (taskIndex < 0) {
        return;
    }
    const task = dataList.value[taskIndex];
    const imgIndex = task.img_urls.findIndex((item) => item.imgName === imgName);
    task.img_urls[imgIndex][updateKey] = updateValue;
    dataList.value.splice(taskIndex, 1, task);

    if (showPreview.value) {
        const currentImhIndex = previewRef.value.getCUrrentIndex();
        previewRef.value && previewRef.value.forceUpdateIndex(currentImhIndex);
    }
};
//批量删除更新数据 （订阅共享事件）
const batchDelSync = (ids) => {
    const list = toRaw(dataList.value);
    for (let i = list.length - 1; i >= 0; i--) {
        let subList = list[i].img_urls;
        subList = subList.filter((item) => !ids.has(item.imgName));
        // 如果一维数组的 list 为空，则删除该一维数组
        if (subList.length === 0) {
            list.splice(i, 1);
        } else {
            list[i].img_urls = subList;
        }
    }

    if (list.length === 0) {
        dataList.value.length = 0;
        handleSearch();
        return;
    }
    dataList.value = processData(list, "createTimestamp");
};

//生图任务完成 更新数据 （订阅共享事件）
const latestCompletedMission = (list = []) => {
    /**
     * 过滤 failure  和 关键字匹配
     */
    if (props.keyWords != "") {
        const searchTxt = props.keyWords.toLowerCase();
        list = list.filter((item) => item.prompt.toLowerCase().includes(searchTxt));
    }
    list = list.map((item) => {
        let { width, height } = item.resolution;
        let { originCreate, img_urls = [], ...base } = item;
        img_urls = img_urls.map((inner) => {
            return {
                ...inner,
                originCreate,
                isPublic: 0,
            };
        });
        const { prompt } = formatPonyV6Prompt(base, "output");
        return {
            ...base,
            prompt,
            width,
            height,
            img_urls,
            size: `${width} x ${height}`,
        };
    });

    const oldFlatLen = flatList.value.length;

    dataList.value = processData([...list, ...dataList.value], "createTimestamp");

    page.value.total += list.length;
    if (!showPreview.value || !previewRef.value) {
        return;
    }
    const newFlatLen = flatList.value.length;
    const diff = newFlatLen - oldFlatLen;
    const index = previewRef.value.getCUrrentIndex() + diff;
    previewRef.value.forceUpdateIndex(index);
};

//图片被收藏了 （订阅共享事件）
const collectedFileItem = ({ promptId, imgName }) => {
    const currentTask = dataList.value.find((item) => item.promptId === promptId);
    const items = (currentTask.img_urls || []).find((item) => item.imgName === imgName);
    items.collectNums = 1;
    if (showPreview.value) {
        const currentImhIndex = previewRef.value.getCUrrentIndex();
        previewRef.value && previewRef.value.forceUpdateIndex(currentImhIndex);
    }
};
//关闭预览 （订阅共享事件）
const closePreview = () => {
    showPreview.value = false;
};

let resizeObserver = null;

onMounted(() => {
    resizeObserver = new ResizeObserver(handleResize);
    if (genResultRef.value) {
        resizeObserver.observe(genResultRef.value); // Start observing
    }
    syncAction.subscribe("updateImg", updateEmit);
    syncAction.subscribe("batchDelImg", batchDelSync);
    syncAction.subscribe("latestSuccessfulMission", latestCompletedMission);
    syncAction.subscribe("reloadPage", handleSearch);
    syncAction.subscribe("collectedItem", collectedFileItem);
    syncAction.subscribe("closePreview", closePreview);
});

onBeforeUnmount(() => {
    if (resizeObserver) {
        resizeObserver.disconnect();
    }
    stopAnimation();
    syncAction.unsubscribe("updateImg", updateEmit);
    syncAction.unsubscribe("batchDelImg", batchDelSync);
    syncAction.unsubscribe("latestSuccessfulMission", latestCompletedMission);
    syncAction.unsubscribe("reloadPage", handleSearch);
    syncAction.unsubscribe("collectedItem", collectedFileItem);
    syncAction.unsubscribe("closePreview", closePreview);
});

watch(
    () => props.keyWords,
    () => {
        handleSearch(); //关键字变化 执行搜索操作
    }
);

watch(
    () => dataList.value.length, //站内其他页面引起图片数量发生变化 更新图片列表的状态
    (newLength) => {
        if (!newLength) {
            loadStatus.value = "noData";
        } else if (newLength < page.value?.pageSize) {
            loadStatus.value = "noMore";
        } else {
            loadStatus.value = "more";
        }
    }
);

// 埋点
const trackEvents = (el, event = "") => {
    try {
        window.trackEvent(event || "Create", { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};
</script>

<style lang="scss" scoped>
.virtual-item-img {
    .bottom-mask {
        @apply hidden absolute left-0 right-0 bottom-0 p-3 z-[25];
    }
    .action-item {
        @apply size-8 rounded-full bg-fill-dd-3 backdrop-blur-lg flex items-center justify-center transition-all duration-200;
    }
    &:hover .action-bar {
        display: block;
    }
}

.nsfw-content {
    @apply absolute text-sm font-normal left-0 top-0 right-0 bottom-0 z-20 bg-black/30 flex flex-col justify-center items-center text-dark-active-text/70;
}

.gen-tag {
    @apply absolute top-2 left-2 px-2 py-1 gap-1.5 bg-fill-dw-10 flex items-center justify-center flex-wrap;
}

.action-dot {
    @apply hover:bg-fill-wd-2 rounded-md text-text-3 text-xs font-medium px-1.5 py-1 cursor-pointer flex items-center gap-1.5 transition-all duration-[250];
}
</style>
