<template>
    <div>
        <div class="switch-group !hidden md:!flex" :style="{ '--len': options.length, '--active-index': activeIndex }">
            <div class="switch-item" :class="{ 'active-item': value === item.value }" v-for="item in options" :key="item.value" @click="changeTabItem(item)">
                <span>{{ item.label }}</span>
                <component v-if="item.icon" :is="item.icon" />
            </div>
        </div>
        <n-radio-group class="!block md:!hidden" v-model:value="value" name="radiogroup" :on-update:value="(value) => changeTabItem({ value })">
            <n-space>
                <n-radio :value="item.value" v-for="item in options" :key="item.value">
                    <div class="flex items-center gap-1.5">
                        <span>{{ item.label }}</span> <component v-if="item.icon" :is="item.icon" />
                    </div>
                </n-radio>
            </n-space>
        </n-radio-group>
    </div>
</template>

<script setup>
import { SUB_EL, SUBSCRIBE_PERMISSION } from "@/utils/constant";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
const { checkShowVipNotice } = useVipNotice();

const { checkPermission } = useSubPermission();
const props = defineProps({
    options: {
        type: Array,
        default: () => [],
    },
    value: {
        type: [String, Number, Boolean],
        default: null,
    },
    checkedValues: {
        type: Array,
        default: () => [],
    },
});
const { value } = toRefs(props);
const activeIndex = computed(() => {
    const index = props.options.findIndex((option) => option.value === value.value);
    return Math.max(0, index);
});
const emits = defineEmits(["update:value", "change"]);
const changeTabItem = async ({ value: val }) => {
    const needCheck = props.checkedValues.includes(val);
    if (needCheck) {
        const res = await checkPermission(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER, { triggerEl: SUB_EL.IMAGE_BATCH });
        if (!res) {
            return;
        }
        checkShowVipNotice(`switchtab_${val}`);
    }

    emits("update:value", val);
    emits("change", val);
};
</script>

<style lang="scss" scoped>
.switch-group {
    @apply h-10 rounded-full overflow-hidden flex items-center bg-fill-tab-4 text-text-4 relative;
    &:after {
        @apply z-10 rounded-full bg-fill-tab-3;
        content: "";
        position: absolute;
        top: 2px;
        left: 2px;
        bottom: 2px;
        width: calc((100% - 4px) / var(--len, 2));
        transform: translateX(calc(var(--active-index, 0) * 100%));
        transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    }
    .active-item {
        @apply text-text-1;
    }
}
.switch-item {
    @apply h-full rounded-full cursor-pointer flex items-center justify-center gap-1.5 relative z-20 text-sm  font-medium transition-all;
    width: calc(100% / var(--len, 2));
    &:not(.disabled-item):hover {
        @apply text-text-1;
    }
}

:deep(.n-radio__dot) {
    --radio-color: #7b57e5;
    --n-box-shadow-active: inset 0 0 0 1px var(--radio-color) !important;
    --n-box-shadow-focus: inset 0 0 0 1px var(--radio-color) !important ;
    --n-box-shadow-hover: inset 0 0 0 1px var(--radio-color) !important ;
    --n-dot-color-active: var(--radio-color) !important  ;
}
</style>
