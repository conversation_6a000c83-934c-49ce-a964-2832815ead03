export default defineNuxtPlugin(() => {
    const config = useRuntimeConfig();

    return {
        provide: {
            runConfig: {
                getApiBase() {
                    return config.public.apiBase;
                },
                getEnv() {
                    return config.public.env;
                },
                getVersion() {
                    return config.public.version;
                },
            },
        },
    };
});
