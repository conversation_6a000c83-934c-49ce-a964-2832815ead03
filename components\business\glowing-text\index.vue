<template>
    <div class="glow-text text-text-4" :data-text="text">{{ text }}...</div>
</template>

<script setup>
defineProps({
    text: {
        type: String,
        default: "",
    },
});
</script>

<style scoped>
.glow-text {
    position: relative;
}

.glow-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, var(--p-text-1) 50%, rgba(255, 255, 255, 0) 100%);
    background-size: 24% 100%;
    background-repeat: no-repeat;
    background-position: 0% 0%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: glowSweep 2s infinite linear;
    pointer-events: none;
}

@keyframes glowSweep {
    0% {
        background-position: -40% 0%;
    }
    100% {
        background-position: 100% 0%;
    }
}
</style>
