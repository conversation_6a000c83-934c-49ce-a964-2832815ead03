<template>
    <div>
        <!-- trigger PC -->
        <div
            class="w-full items-center p-2 bg-fill-wd-1 rounded-lg text-text-2 font-medium hidden lg:flex group transition-all duration-[250]"
            :class="[disabled ? 'opacity-40' : 'hover:text-text-1 cursor-pointer hover:!bg-fill-wd-2']"
            @click="handleStateChange(true)"
        >
            <span class="p-1.5">
                <IconsStyleHelper class="size-5" />
            </span>
            <span class="ml-2 mr-1">{{ $t("CREATE.REFERENCE_IMAGE") }}</span>
            <n-tooltip placement="right" :show-arrow="false" raw>
                <template #trigger>
                    <IconsHelp class="size-4 text-text-4 hover:!text-text-2 cursor-pointer hidden group-hover:block" />
                </template>
                <div class="tips-box">
                    <div class="font-medium text-text-1">{{ t("PROMPT_HELPER_TITLE") }}</div>
                    <div class="mt-2.5 text-text-3">{{ t("PROMPT_HELPER_DESC") }}</div>
                </div>
            </n-tooltip>
        </div>
        <!-- trigger h5 -->
        <div
            class="justify-center size-10 items-center gap-2 bg-fill-wd-1 text-text-2 font-medium rounded-full flex lg:hidden"
            :class="[disabled ? 'opacity-40' : 'hover:text-text-1 cursor-pointer hover:!bg-fill-wd-2']"
            @click="handleStateChange(true)"
        >
            <span class="p-2">
                <IconsStyleHelper class="size-5" />
            </span>
        </div>
        <!-- Pc 中间弹窗 -->
        <n-modal v-if="!isIpad" v-model:show="show" :close-on-esc="false" :mask-closable="false" class="!rounded-2xl border border-border-1 border-solid !bg-bg-2 !p-6">
            <div class="w-full tooltip-container max-w-[1200px] h-[80vh] relative mx-6 lg:mx-auto flex flex-col">
                <!-- 图像参考 对应后端模型接口中的  -->
                <div class="w-full shrink-0">
                    <CreateTabs v-model:value="selectedHelper" :options="referTypes" class="!h-10 mx-auto w-fit" @change="handleHelperTypeChange" />
                </div>
                <div class="flex flex-1 mt-6 overflow-hidden">
                    <div class="flex-1 h-full">
                        <div class="max-h-full scroll-container no-scroll !pr-0">
                            <ImageReference :selectedImgRefersUrls="selectedImgRefersUrls" @change="handleAddItem" v-if="selectedHelper === 'ImageReference'" />
                            <PromptMagic :selectedMagic="selectedMagic" @change="handleAddItem" v-if="selectedHelper === 'PromptMagic'" />
                            <ImageControl :selectedControlsId="selectedControlsId" @change="handleAddItem" v-if="selectedHelper === 'ImageControl'" />
                        </div>
                    </div>
                    <!-- 已经选中的参考方式 -->
                    <div class="refer-list h-full p-4 flex flex-col justify-between gap-2 w-[312px] top-[92px] right-6 flex-grow-0">
                        <template v-if="createStore.selectedRefers.length">
                            <div class="scroll-container no-scroll w-full flex-1 flex flex-col gap-3">
                                <div v-for="item in createStore.selectedRefers" :key="item.id" ref="referItemRefs">
                                    <ReferItem :item="item" :supportReferList="createStore.supportReferList" @update="handleUpdateItem" @delete="handleDeleteItem" />
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="w-full h-full flex flex-col items-center justify-center gap-2 font-medium">
                                <p class="text-text-3 text-sm">{{ $t("CREATE.NO_OPTIONS_SELECTED") }}</p>
                                <p class="text-text-4 text-center text-xs">{{ $t("CREATE.NO_SELECT_TIP") }}</p>
                            </div>
                        </template>
                        <Button @click="handleStateChange(false, true)">{{ $t("COMMON_BTN_SAVE") }}</Button>
                    </div>
                </div>
                <IconsClose class="w-5 h-5 absolute top-6 right-6 text-text-4 hover:text-text-1 cursor-pointer" @click="handleClearAll" />
            </div>
        </n-modal>

        <!-- H5 底部弹窗 -->
        <CommonH5Popup v-model:show="show" :mask-closable="false" v-if="isIpad">
            <div class="w-full relative h-[90dvh] flex flex-col pb-4">
                <div class="w-full flex justify-between items-center mt-5 mb-2 shrink-0">
                    <span class="rounded-full size-8 bg-fill-wd-1 hover:bg-fill-wd-2 flex items-center justify-center cursor-pointer" @click.stop="show = false">
                        <IconsClose />
                    </span>
                    <p class="text-text-1 font-medium">{{ $t("CREATE.REFERENCE_IMAGE") }}</p>
                    <span class="text-primary-6 font-medium cursor-pointer text-base" @click.stop="show = false">{{ $t("DONE") }}</span>
                </div>
                <n-tabs type="line" animated :value="selectedHelper" :on-update:value="handleHelperTypeChange" class="h-[calc(100%-250px)] pb-4">
                    <!-- 图像参考 对应后端模型接口中的  -->
                    <n-tab-pane name="ImageReference" :tab="t('REFER_TYPE_TITLE_H5')" class="w-full h-full scroll-container no-scroll">
                        <ImageReference :selectedImgRefersUrls="selectedImgRefersUrls" @change="handleAddItem" />
                    </n-tab-pane>
                    <n-tab-pane name="PromptMagic" :tab="t('MAGIC_TITLE_H5')" class="w-full h-full scroll-container no-scroll">
                        <PromptMagic :selectedMagic="selectedMagic" @change="handleAddItem" />
                    </n-tab-pane>
                    <n-tab-pane name="ImageControl" :tab="t('CONTROL_TITLE_H5')" class="w-full h-full scroll-container no-scroll">
                        <ImageControl :selectedControlsId="selectedControlsId" @change="handleAddItem" />
                    </n-tab-pane>
                </n-tabs>
                <!-- 已经选中的参考方式 -->
                <div class="refer-list py-4 flex flex-col justify-between gap-2 w-full h-[186px] shrink-0 overflow-hidden">
                    <div class="flex w-full items-center gap-3 overflow-x-auto no-scroll no-drag flex-1 overflow-y-hidden px-4">
                        <template v-if="createStore.selectedRefers.length">
                            <div
                                v-for="item in createStore.selectedRefers"
                                :key="item.id"
                                ref="referItemRefs"
                                :class="[createStore.selectedRefers.length > 1 ? '!w-[248px]' : '!w-full']"
                                class="shrink-0"
                            >
                                <ReferItem :item="item" :supportReferList="createStore.supportReferList" @update="handleUpdateItem" @delete="handleDeleteItem" class="h-[152px] no-drag" />
                            </div>
                        </template>
                        <template v-else>
                            <div class="w-full h-full flex flex-col items-center justify-center gap-2 font-medium">
                                <p class="text-text-3 text-sm">{{ $t("CREATE.NO_OPTIONS_SELECTED") }}</p>
                                <p class="text-text-4 text-center text-xs">{{ $t("CREATE.NO_SELECT_TIP") }}</p>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </CommonH5Popup>
    </div>
</template>
<script setup>
import { I18nT, useI18n } from "vue-i18n";
import { picLumenArtV1Id, realisticModelId, animeModelId, lineArtModelId, fluxKontextModelId, MjModelId, ponyV6ModelId, NamiyaModelId, fluxDevModelId, fluxModelId } from "@/utils/constant";
import ImageReference from "./components/ImageReference.vue";
import PromptMagic from "./components/PromptMagic.vue";
import ImageControl from "./components/ImageControl.vue";
import ReferItem from "./components/ReferItem.vue";
import { useSupportModelList, useCreateStore } from "@/stores/create";
import { useGetModelInfo } from "@/hook/create";
import { defaultModel, REFER_TYPES } from "@/utils/constant";
import { Alert } from "@/icons/index.js";
import { NIcon } from "naive-ui";
import { debounce, getUUID, throttle } from "@/utils/tools";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isIpad } = storeToRefs(useThemeStore());

const { t } = useI18n({ useScope: "global" });
const emit = defineEmits(["changeModel"]);
const createStore = useCreateStore();
const { showMessage } = useModal();
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
});

const show = ref(false);
const selectedHelper = ref("PromptMagic");

// 如果当前模型不支持此方式，则推荐切换到支持的模型 否则直接切换
const helperSupportMap = {
    ImageReference: {
        notSupported: [], //不支持image reference的模型id 默认为空 后端接口中的supportStyleList的长度为0 则不可用
        message: "当前模型不支持 ImageReference",
        name: REFER_TYPES.IMAGE_REFER,
    },
    PromptMagic: {
        notSupported: [NamiyaModelId], //不支持prompt magic的模型id
        message: "当前模型不支持 PromptMagic",
        name: REFER_TYPES.PROMPT_MAGIC,
    },
    ImageControl: {
        onlySupported: [realisticModelId, animeModelId, ponyV6ModelId, NamiyaModelId], //支持image control的模型id
        message: "当前模型不支持 ImageControl",
        name: REFER_TYPES.IMAGE_CONTROL,
    },
};
//尝试切换tab的时候 更新 不可使用图像参考的模型id列表
const updateImageReferNotSupportIds = () => {
    const { modelList } = useSupportModelList();
    let list = [];
    modelList.forEach((item) => {
        if (!item.supportStyleList?.length) {
            list.push(item.modelId);
        }
    });
    return list;
};
const referTypes = ref([]);
watchEffect(() => {
    helperSupportMap.ImageReference.notSupported = updateImageReferNotSupportIds();
    const { ImageReference, PromptMagic, ImageControl } = helperSupportMap;
    referTypes.value = [
        {
            label: t("REFER_TYPE_TITLE"),
            value: "ImageReference",
            disabled: ImageReference.notSupported?.includes(createStore.model_id),
        },
        {
            label: t("MAGIC_TITLE"),
            value: "PromptMagic",
            disabled: PromptMagic.notSupported?.includes(createStore.model_id),
        },
        {
            label: t("CONTROL_TITLE"),
            value: "ImageControl",
            disabled: !ImageControl.onlySupported?.includes(createStore.model_id),
        },
    ];
});
const selectedMagic = computed(() => {
    return {
        mainCategory: createStore.mainCategory,
        subCategory: createStore.subCategory,
    };
});
const selectedImgRefersUrls = computed(() => {
    return createStore.style_list?.map((item) => item.img_url);
});

const selectedControlsId = computed(() => {
    return createStore.image_control_list?.map((item) => item.img_url);
});

// 模型可用图生图参考方式校验 和 切换
const handleHelperTypeChange = (val) => {
    const config = helperSupportMap[val];
    helperSupportMap.ImageReference.notSupported = updateImageReferNotSupportIds();
    if (!config) return;
    const model = useGetModelInfo(createStore.model_id); //当前选中的模型
    if (config.notSupported?.includes(createStore.model_id)) {
        // 不支持image reference
        const targetModel = useGetModelInfo(defaultModel.id); // 默认模型
        showMessage({
            style: { width: "400px" },
            confirmBtn: t("MODEL_SWITCH"),
            content: h("div", null, [h("p", { class: "tracking-wider" }, t("MODEL_SWITCH_MSG", { name: model.label, target: targetModel.label, feature: t(config.name) }))]),
            zIndex: 9999,
            icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_NOTICE"),
        })
            .then((_) => {
                emit("changeModel", defaultModel.id);
                selectedHelper.value = val;
            })
            .finally(() => {});
        return;
    } else if (config.onlySupported && !config.onlySupported.includes(createStore.model_id)) {
        // 不支持image control
        const targetModel = useGetModelInfo(realisticModelId); // realistic V2
        showMessage({
            style: { width: "400px" },
            confirmBtn: t("MODEL_SWITCH"),
            content: h("div", null, [h("p", { class: "tracking-wider" }, t("MODEL_SWITCH_MSG", { name: model.label, target: targetModel.label, feature: t(config.name) }))]),
            zIndex: 9999,
            icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_NOTICE"),
        })
            .then((_) => {
                emit("changeModel", realisticModelId);
                // createStore.setGenerateConfig({
                //     model_id: realisticModelId,
                //     style_list: [], //切换模型 清空除了prompt magic以外的图生图参考方式
                //     image_control_list: [],
                // });
                selectedHelper.value = val;
            })
            .finally(() => {});
        return;
    } else {
        selectedHelper.value = val;
    }
    window.trackEvent("Create", { el: `reference_image_popup_tab_click=${val}` });
};
let myOriginalList = []; // 深拷贝初始的内容关闭时恢复原样 保存时应用到store
/**
 * 打开关闭弹窗 开启前判断应该优先选中哪一个tab imageRefer > prompt magic > image control
 * @param val
 * @param track  是否埋点
 */
const handleStateChange = (val, track = false) => {
    if (props.disabled) return;
    console.log(track, "track");
    track && window.trackEvent("Create", { el: `reference_image_popup_save` }); //保存埋点
    if (val) {
        myOriginalList = JSON.parse(JSON.stringify(createStore.selectedRefers));
        const notSupportReferIds = updateImageReferNotSupportIds();
        const notSupportPromptMagicIds = helperSupportMap.PromptMagic.notSupported;
        const supportImageControlIds = helperSupportMap.ImageControl.onlySupported;
        window.trackEvent("Create", { el: "reference_image" });
        let finaleHelperType = "ImageReference";
        //如果当前id不在notSupportReferIds中 就选中ImageReference
        if (!notSupportReferIds.includes(createStore.model_id)) {
            finaleHelperType = "ImageReference";
            //如果当前id不在promptmagicIds中 就选中PromptMagic
        } else if (!notSupportPromptMagicIds.includes(createStore.model_id)) {
            finaleHelperType = "PromptMagic";
            //如果当前id在imageControlIds中 就选中ImageControl
        } else if (supportImageControlIds.includes(createStore.model_id)) {
            finaleHelperType = "ImageControl";
        } else {
            myOriginalList = [];
        }
        selectedHelper.value = finaleHelperType;
    }
    show.value = val;
};

//清空本次选中的全部参考方式
const handleClearAll = () => {
    // selectedHelper.value = "PromptMagic";
    nextTick(() => {
        createStore.updateReferStore(myOriginalList);
        handleStateChange(false);
    });
};
const referItemRefs = ref([]);
//增加参考项
const handleAddItem = throttle((item) => {
    let existReference = createStore.selectedRefers.find((i) => i.displayType === REFER_TYPES.IMAGE_REFER);
    let existControl = createStore.selectedRefers.find((i) => i.displayType === REFER_TYPES.IMAGE_CONTROL);
    switch (item.displayType) {
        case REFER_TYPES.IMAGE_REFER:
            if (existControl) return openToast.error(t("REFER_POSE_ONLY")); // control 和reference不可并存

            if (!item?.style) {
                let targetRefer = createStore.supportReferList?.find((i) => !i.disabled);
                const model = useGetModelInfo(createStore.model_id);
                let supportRefTypes = model.supportStyleList;
                //判断当前模型是否支持多图生图
                let supportMulti = supportRefTypes.find((item) => item.value === "multiRefer");
                if (!targetRefer || (!supportMulti && existReference)) return console.log("没有可选的参考类型");

                item = {
                    ...item,
                    style: targetRefer?.value,
                    weight: targetRefer.weight,
                };
            }
            break;
        case REFER_TYPES.PROMPT_MAGIC:
            let existedMagic = createStore.selectedRefers.find((i) => i.displayType === REFER_TYPES.PROMPT_MAGIC);
            if (existedMagic) {
                let newMagic = {
                    ...existedMagic,
                    ...item,
                };
                handleUpdateItem(newMagic);
                return;
            }
            break;
        case REFER_TYPES.IMAGE_CONTROL:
            if (existReference) return openToast.error(t("REFER_POSE_ONLY")); // control 和reference不可并存

            if (existControl) {
                let newControl = {
                    ...existControl,
                    ...item,
                };
                handleUpdateItem(newControl);
                return;
            }
            break;
    }
    let list = [...createStore.selectedRefers];
    list.push({
        ...item,
        id: getUUID(),
    });
    let allLength = list.length;
    createStore.updateReferStore(list);

    // 代替 setTimeout 使用 nextTick 来确保滚动操作发生在 DOM 更新后
    nextTick(() => {
        const lastItemRef = referItemRefs.value[allLength - 1]; // 获取最后一个 ref
        if (lastItemRef) {
            lastItemRef.scrollIntoView({ behavior: "smooth", inline: "center" });
        }
    });
}, 300);

//更新已有的参考项
const handleUpdateItem = (updatedItem) => {
    // console.log(updatedItem, "updatedItem——————————————————————————————");
    const { id } = updatedItem;
    if (!id) console.error("id is null");
    // 找到 item 的索引
    const index = createStore.selectedRefers.findIndex((item) => item.id === id);
    if (index !== -1) {
        let list = [...createStore.selectedRefers];
        list?.splice(index, 1, updatedItem);
        nextTick(() => {
            createStore.updateReferStore(list); // 确保状态更新后调用
        });
    }
};
//删除已有的参考项
const handleDeleteItem = (id) => {
    if (!id) console.error("id is null");
    let list = [...createStore.selectedRefers];
    let newList = list?.filter((item) => item.id !== id);
    nextTick(() => {
        createStore.updateReferStore(newList);
    });
};
</script>
<style lang="scss" scoped>
::v-deep(.n-tabs-rail) {
    @apply w-full max-w-[600px] mx-auto bg-fill-tab-4 rounded-full py-0.5;
    .n-tabs-tab {
        @apply h-10;
        .n-tabs-tab__label {
            @apply text-text-4;
        }
    }
    .n-tabs-tab--active {
        .n-tabs-tab__label {
            @apply text-text-1;
        }
    }
    .n-tabs-capsule {
        @apply rounded-full;
    }
}

::v-deep(.n-tabs--line-type .n-tabs-wrapper) {
    @apply w-full justify-between;
    .n-tabs-tab-pad {
        width: 0;
    }
}
::v-deep(.n-tabs-bar) {
    @apply bg-primary-6;
}
.refer-list {
    @apply bg-fill-dd-10 rounded-lg border border-dashed border-border-2;
}
</style>
