import { ref } from "vue";
import { decryptResult } from "@/utils/tools";

/**
 * Fetches the user list from the API
 * @param {Object} options
 * @returns {Object} - loading  - 是否正在加载
 * @returns {Object} - list - 用户gallery数据
 */
export default function useListFetch(options) {
    const { queryData, api, decrypt = false, cursorKey, sizeKey = "pageSize", sizeNum = 40, beforeSendHook } = options;

    const list = ref([]);
    const error = ref(null);
    const loading = ref(true);
    const isPageEnd = ref(false);
    const isEmpty = ref(false);

    //获取page数据
    const pages = ref({});
    pages.value[sizeKey] = sizeNum;

    if (cursorKey) {
        pages.value[cursorKey] = "";
    } else {
        pages.value.pageNum = 1;
    }
    const fetch = async () => {
        try {
            loading.value = true;
            let reqQuery = {
                ...pages.value,
                ...queryData.value,
            };
            // ..............
            if (typeof beforeSendHook === "function") {
                reqQuery = await beforeSendHook(reqQuery);
            }
            const res = await api(reqQuery);
            const { data, status, message } = res;
            if (status !== 0) {
                return;
            }
            let resultList = [];
            if (decrypt) {
                resultList = decryptResult(data.encryptResult) || [];
            } else {
                resultList = data?.resultList || [];
            }

            list.value.push(...resultList);
            if (cursorKey) {
                pages.value[cursorKey] = data.lastId || "";
                try {
                    const lastLikeCount = resultList[resultList.length - 1]?.fileLikeNums || 0;
                    pages.value.lastLikeCount = lastLikeCount;
                } catch (error) {
                    console.log("获取lastLikeCount 出错");
                }
            }
            if (resultList.length < pages.value[sizeKey]) {
                isPageEnd.value = true;
            }
            if (list.value.length) {
                isEmpty.value = false;
            } else {
                isEmpty.value = true;
            }
        } catch (err) {
            console.error("err", err);
        } finally {
            loading.value = false;
        }
    };

    const reload = async () => {
        list.value = [];
        isPageEnd.value = false;
        if (cursorKey) {
            pages.value[cursorKey] = "";
        } else {
            pages.value.pageNum = 1;
        }
        await fetch();
    };

    const loadMore = async () => {
        if (isPageEnd.value || loading.value) return;
        if (!cursorKey) {
            pages.value.pageNum++;
        }

        await fetch();
    };

    return {
        list,
        error,
        loading,
        loadMore,
        isEmpty,
        reload,
    };
}
