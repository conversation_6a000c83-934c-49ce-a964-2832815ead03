<template>
    <div class="dark:text-dark-text relative z-[999] h-screen dark:bg-dark-bg-2 overflow-auto">
        <div class="sticky top-0 h-12 flex items-center px-4 dark:bg-black">
            <div class="p-2 text-xl cursor-pointer" @click="$router.back()">
                <IconsBack />
            </div>
        </div>
        <img :src="currentData.thumbnailUrl" class="max-w-full max-h-full" @click="showFeature = !showFeature" />
        <H5FeaturePanel v-if="showFeature" :item="currentData" :fromCollect="true" @removeItem="handleRemoveItem" />
    </div>
</template>

<script setup>
import { reduceCollectionImg } from "@/api";

const { t } = useI18n({ useScope: "global" });

import { useShareCollect, useDefUnCollectionsConfirm } from "@/stores/index";
import { Alert } from "@/icons/index.js";
import { NIcon, NCheckbox } from "naive-ui";
const { showMessage } = useModal();
const shareCollect = useShareCollect();
const defUnCollectionsConfirm = useDefUnCollectionsConfirm();

const router = useRouter();
const currentData = ref({ ...shareCollect.currentCollectItem });
const showFeature = ref(true);
onMounted(() => {
    if (!shareCollect.currentCollectItem.classifyId) {
        router.back();
    }
});
// const getInfo = async () => {
//     const param = { ...route.query };
//     const { status, data, message } = await getTaskDetail(param);
//     if (status !== 0) {
//         openToast.error(message);
//         return;
//     }
//     const { promptId, ...info } = data.genInfo || {};
//     if (info) {
//         currentData.value = Object.assign(data, info);
//     } else {
//         currentData.value = data;
//     }
//     // console.log(data);
// };
// getInfo();

// 取消收藏当前图片
const handleCancelFavorite = async () => {
    const { imgName, promptId, classifyId } = currentData.value;
    const { status, message } = await reduceCollectionImg({ imgName, promptId, classifyId });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    // 更新图片列表
    router.back();
};
const handleRemoveItem = () => {
    if (defUnCollectionsConfirm.isConfirm) {
        handleCancelFavorite();
        return;
    }
    let hasChecked = false;
    showMessage({
        style: { width: "480px" },
        confirmBtn: t("COLLECTION_UN_COLLECTION"),
        content: h("div", null, [
            h("p", null, t("COLLECTION_REMOVE_CONTENT")),
            h(
                NCheckbox,
                {
                    class: "mt-4",
                    style: {
                        "--n-color-checked": "#6904E9", // 修改选中时的颜色
                        "--n-border-checked": "1px solid #6904E9", // 修改选中时的颜色
                        "--n-border-focus": "1px solid #6904E9", // 修改选中时的颜色
                        "--n-box-shadow-focus": "none", // 修改选中时的颜色
                        "--n-border": "1px solid #6904E9", // 修改选中时的颜色
                    },
                    "on-update:checked": (val) => {
                        hasChecked = val;
                    },
                },
                {
                    default: () =>
                        h(
                            "span",
                            {
                                class: " dark:text-dark-text",
                            },
                            t("COLLECTION_REMOVE_NOT_CONFIRM")
                        ),
                }
            ),
        ]),
        icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_ATTEN"),
    }).then(() => {
        defUnCollectionsConfirm.setDefConfirm(hasChecked);
        handleCancelFavorite();
    });
};
</script>

<style lang="scss" scoped></style>
