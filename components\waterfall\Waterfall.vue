<template>
    <div
        ref="content"
        :style="{
            position: 'relative',
            willChange: 'height',
            opacity: showContent ? 1 : 0,
            height: contentHeight,
            padding: isNumber(padding) ? `${padding}px` : padding,
        }"
    >
        <div
            v-for="data in itemRenderList"
            :key="data.item[rowKey] ?? data.index"
            :style="{
                ...props.itemStyle,
                position: 'absolute',
                contentVisibility: 'auto',
                width: `${data.width}px`,
                height: `${data.height}px`,
                transform: `translate(${data.left}px, ${data.top}px)`,
                containIntrinsicSize: `${data.width}px ${data.height}px`,
            }"
            :data-index="data.index"
        >
            <slot :item="data.item" :index="data.index" />
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, ref, nextTick, shallowRef, watchEffect } from "vue";
import { useElementBounding, useElementSize } from "@vueuse/core";

const props = defineProps({
    /**
     * 是否启用虚拟滚动
     * @default true
     */
    virtual: {
        type: Boolean,
        default: true,
    },
    /**
     * 数据项的唯一标识字段
     * @default 'id'
     */
    rowKey: {
        type: String,
        default: "id",
    },
    /**
     * 元素之间的间距（像素）
     * @default 15
     */
    gap: {
        type: Number,
        default: 15,
    },
    /**
     * 容器的内边距，支持数字或CSS padding字符串
     * @default 15
     */
    padding: {
        type: [Number, String],
        default: 15,
    },
    /**
     * 预加载屏幕数量 [上方屏数, 下方屏数]
     * @default [0, 0]
     */
    preloadScreenCount: {
        type: Array,
        default: () => [1, 1],
    },
    /**
     * 单个项目的最小宽度（像素）
     * @default 220
     */
    itemMinWidth: {
        type: Number,
        default: 220,
    },
    /**
     * 最大列数
     * @default 10
     */
    maxColumnCount: {
        type: Number,
        default: 10,
    },
    /**
     * 最小列数
     * @default 2
     */
    minColumnCount: {
        type: Number,
        default: 2,
    },
    /**
     * 瀑布流数据项数组
     * @default []
     */
    items: {
        type: Array,
        default: () => [],
    },
    /**
     * 自定义样式对象，用于设置每个项目的样式
     * @default {}
     */
    itemStyle: {
        type: Object,
        default: () => ({}),
    },
    /**
     * 计算项目高度的函数
     * @param {*} item - 数据项
     * @param {number} itemWidth - 项目宽度
     * @returns {number} 计算得到的高度
     * @default (item, itemWidth) => 250
     */
    calcItemHeight: {
        type: Function,
        default: () => 250,
    },
    /**
     * 计算项目跨列数的函数
     * @param {*} item - 数据项
     * @returns {number} 跨列数
     * @default () => 1
     */
    crossColumns: {
        type: Function,
        default: () => 1,
    },
});

const isSSR = import.meta.env?.SSR;

/** 内容容器引用 */
const content = ref(null);
const showContent = ref(isSSR);
const { width: contentWidth } = useElementSize(content);
const { top: contentTop } = useElementBounding(content);
if (isSSR) {
    contentWidth.value = 1920;
    contentTop.value = 0;
}
onMounted(() => {
    if (contentWidth.value === 0) {
        contentWidth.value = Number.parseInt(window.getComputedStyle(content.value).width);
    }
    nextTick().then(() => {
        showContent.value = true;
    });
});

/** 判断值是否为数字类型
 * @param {*} value - 需要判断的值
 * @returns {boolean} - 是否为数字类型
 */
const isNumber = (value) => {
    return Object.prototype.toString.call(value) === "[object Number]";
};

/** 计算当前应显示的列数
 * @returns {number} 列数
 */
const columnCount = computed(() => {
    if (isSSR) return 5;
    if (!contentWidth.value) return 0;
    const cWidth = contentWidth.value;
    if (cWidth >= props.itemMinWidth * 2) {
        const count = Math.floor(cWidth / props.itemMinWidth);
        return Math.min(count, props.maxColumnCount);
    }
    return props.minColumnCount;
});

const contentHeight = computed(() => {
    // if (isSSR) return 'auto';
    return `${Math.max(...columnsTop.value)}px`;
});
/**
 * 瀑布流布局相关尺寸
 * @type {import('vue').Ref<number[]>}
 */
const columnsTop = ref(new Array(columnCount.value).fill(0));

/** 单列基础宽度
 * @type {import('vue').ComputedRef<number>}
 */
const baseColumnWidth = computed(() => {
    if (!contentWidth.value || columnCount.value <= 0) return 0;
    return (contentWidth.value - (columnCount.value - 1) * props.gap) / columnCount.value;
});

/**
 * 瀑布流每项的位置信息
 * @description 包含每个项目的位置信息，如行键、索引、列索引、位置等
 * @type {import('vue').ShallowRef<Array<{
 *   rowKey: string | number,
 *   index: number,
 *   item: any,
 *   column: number,
 *   top: number,
 *   left: number,
 *   bottom: number,
 *   height: number,
 *   width: number
 * }>>}
 */
const itemSpaces = shallowRef([]);

/** 获取最适合放置项目的列索引
 * @param {*} item - 数据项
 * @returns {number} 目标列索引
 */
const getColumnIndex = (item) => {
    const span = Math.min(props.crossColumns(item), columnCount.value);
    let targetCol = 0;
    let minHeight = Infinity;

    for (let i = 0; i <= columnCount.value - span; i++) {
        const currentMax = Math.max(...columnsTop.value.slice(i, i + span));
        if (currentMax < minHeight) {
            minHeight = currentMax;
            targetCol = i;
        }
    }
    return targetCol;
};

/**
 * 获取是否使用缓存
 * @description 只有当新增元素时，缓存存在且与当前数据一致，则使用缓存
 * @param allItems
 * @param cacheItems
 */
const getUseCache = (allItems, cacheItems) => {
    // 是否启用缓存：只有当新增元素时，需要计算新增元素的信息
    if (itemSpaces.value.length && allItems.length > itemSpaces.value.length) {
        // 判断缓存数据是否与当前数据一致
        const inconsistentData = cacheItems.find((v, index) => v.item[props.rowKey] !== allItems[index][props.rowKey]);
        return !inconsistentData;
    }
    return false;
};

/**
 * 监听并计算每个项目的位置信息
 * @description 当列数或数据项发生变化时重新计算布局
 */
watchEffect(() => {
    if (!columnCount.value) {
        itemSpaces.value = [];
        return;
    }

    const length = props.items.length;
    const spaces = new Array(length);
    let start = 0;
    const cache = getUseCache(props.items, itemSpaces.value);
    if (cache) {
        start = itemSpaces.value.length;
    } else {
        columnsTop.value = new Array(columnCount.value).fill(0);
    }
    for (let i = 0; i < length; i++) {
        if (cache && i < start) {
            spaces[i] = itemSpaces.value[i];
            continue;
        }

        const item = props.items[i];
        const span = Math.min(props.crossColumns(item), columnCount.value);
        const columnIndex = getColumnIndex(item);
        const itemWidth = baseColumnWidth.value * span + (span - 1) * props.gap;
        const height = props.calcItemHeight(item, itemWidth);
        const top = columnsTop.value[columnIndex];
        const left = (baseColumnWidth.value + props.gap) * columnIndex;

        const space = {
            index: i,
            item: item,
            column: columnIndex,
            top: top,
            left: left,
            bottom: top + height,
            height: height,
            width: itemWidth,
        };

        const newHeight = top + height + props.gap;
        for (let j = 0; j < span; j++) {
            columnsTop.value[columnIndex + j] = newHeight;
        }

        spaces[i] = space;
    }
    itemSpaces.value = spaces;
});

/** 需要渲染的项目列表
 * @type {import('vue').ComputedRef<Array>}
 */
const itemRenderList = computed(() => {
    if (isSSR) return itemSpaces.value;
    if (!props.virtual) return itemSpaces.value;
    if (!itemSpaces.value.length) return [];

    const parent = content.value?.parentElement;
    if (!parent) return [];

    const parentTop = parent.offsetTop;
    const tp = -contentTop.value + parentTop;
    const [topPre, bottomPre] = props.preloadScreenCount;
    const innerHeight = parent.clientHeight > window.innerHeight ? window.innerHeight : parent.clientHeight;

    const minLimit = tp - topPre * innerHeight;
    const maxLimit = tp + (bottomPre + 1) * innerHeight;
    // console.log('itemSpaces.value', itemSpaces.value, maxLimit);
    return itemSpaces.value.filter((v) => {
        const t = v.top;
        const b = v.bottom;
        return (t >= minLimit && t <= maxLimit) || (b >= minLimit && b <= maxLimit) || (t < minLimit && b > maxLimit);
    });
});
</script>
