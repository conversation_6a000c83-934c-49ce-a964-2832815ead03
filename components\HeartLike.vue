<template>
    <span class="relative flex justify-center items-center cursor-pointer" :class="{ isLike: checked }" style="--c: red">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path
                class="heartbeat"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17 4c-3.2 0-5 2.667-5 4c0-1.333-1.8-4-5-4S3 6.667 3 8c0 7 9 12 9 12s9-5 9-12c0-1.333-.8-4-4-4"
            />
        </svg>
        <i class="dot"></i>
    </span>
</template>

<script setup>
const props = defineProps({
    checked: false,
});
</script>

<style lang="scss" scoped>
.isLike {
    svg {
        animation: beat 0.5s linear forwards;
    }

    .heartbeat {
        animation: drawHeart 0.5s linear forwards;
    }

    .dot {
        animation: blink 0.35s ease-in-out forwards;
        animation-delay: 0.2s;
    }
}

svg {
    height: 1em;
    width: 1em;
    position: relative;
    z-index: 10;
    .heartbeat {
        fill: transparent;
        stroke: currentColor;
        // stroke-width: 3em;
        stroke-linecap: round;
    }
}

.dot {
    display: block;
    width: 0.15em;
    height: 0.15em;
    border-color: var(--c);
    background: transparent;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    box-shadow: 0 -1em 0 var(--c), 0 1em 0 var(--c), -1em 0 0 var(--c), 1em 0 0 var(--c), -0.7em -0.7em 0 var(--c), 0.7em -0.7em 0 var(--c), 0.7em 0.7em 0 var(--c), -0.7em 0.7em 0 var(--c);
    /* 动画延迟时间 */
}

@keyframes drawHeart {
    0% {
        stroke-dasharray: 2600;
        stroke-dashoffset: 2600;
    }

    80% {
        fill: transparent;
        stroke-dashoffset: 0;
    }

    100% {
        fill: var(--c);
        stroke: var(--c);
        stroke-dashoffset: 0;
    }
}

/* 小圆点闪出的动画 */
@keyframes blink {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.8;
    }

    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }

    100% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0;
    }
}

/* 心跳动的动画 */
@keyframes beat {
    0% {
        transform: scale(1);
    }

    70% {
        transform: scale(1);
    }

    80% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}
</style>
