<svg xmlns="http://www.w3.org/2000/svg" width="140" height="140" fill="none"><defs><linearGradient id="e" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#444"/><stop offset="98.571%" stop-color="#313131"/></linearGradient><linearGradient id="b" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#FFF"/><stop offset="98.571%" stop-color="#FFF"/></linearGradient><linearGradient id="i" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%"/><stop offset="100%" stop-opacity=".48"/></linearGradient><linearGradient id="j" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%"/><stop offset="100%" stop-color="#232323"/></linearGradient><linearGradient id="k" x1=".354" x2=".738" y1=".032" y2=".872"><stop offset="0%" stop-color="#555"/><stop offset="99.286%" stop-color="#444"/></linearGradient><linearGradient id="l" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%"/><stop offset="100%" stop-opacity=".48"/></linearGradient><linearGradient id="m" x1=".354" x2=".738" y1=".032" y2=".872"><stop offset="0%" stop-color="#606060"/><stop offset="99.286%" stop-color="#444"/></linearGradient><linearGradient id="n" x1=".354" x2=".738" y1=".032" y2=".872"><stop offset="0%" stop-color="#646464"/><stop offset="99.286%" stop-color="#555"/></linearGradient><filter id="d" width="121" height="136" x="-20" y="-16" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4"/><feGaussianBlur stdDeviation="5"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.30000001192092896 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter><filter id="a" width="121" height="136" x="-20" y="-16" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4"/><feGaussianBlur stdDeviation="5"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.30000001192092896 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter><filter id="g" width="210.064" height="225.85" x="-60" y="-60" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feGaussianBlur result="effect1_foregroundBlur" stdDeviation="15"/></filter><filter id="h" width="150.064" height="161.631" x="-30" y="-30" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feGaussianBlur result="effect1_foregroundBlur" stdDeviation="7.5"/></filter><clipPath id="c"><rect width="140" height="140" rx="0"/></clipPath><mask id="f" maskUnits="userSpaceOnUse" style="mask-type:alpha"><g filter="url(#a)"><rect width="81" height="96" x="28" y="18" fill="url(#b)" rx="8"/></g></mask></defs><g clip-path="url(#c)"><g filter="url(#d)"><rect width="81" height="96" x="28" y="18" fill="url(#e)" rx="8"/></g><g mask="url(#f)"><g filter="url(#g)" style="opacity:.15000000596046448"><path fill="#D8D8D8" d="m17.513 90.072 19.93 23.778-4.983-86.674L86.882 15.67l18.013-6.136L107.577 8 20.58 9.534l-3.066 80.538Z"/></g><g filter="url(#h)" style="opacity:.10000000149011612"><path fill="#D8D8D8" d="m17.513 90.072 14.947 19.559-2.3-83.606L86.883 15.67l18.013-6.136L107.577 8 20.58 9.534l-3.066 80.538Z"/></g></g><path fill="url(#i)" d="M40.54 67.235c-1.567 2.667.356 6.026 3.448 6.026h50.757c2.643 0 4.56-2.518 3.855-5.065l-5.562-20.13c-.744-2.693-3.949-3.813-6.208-2.17L71.24 57.235a4 4 0 0 1-3.755.51l-14.372-5.378a4 4 0 0 0-4.85 1.72l-7.724 13.148Z"/><ellipse cx="99.5" cy="104" fill="url(#j)" rx="25.5" ry="25"/><ellipse cx="99.5" cy="104" stroke="url(#k)" stroke-width="8" rx="21.5" ry="21"/><ellipse cx="48.545" cy="37.831" fill="url(#l)" rx="6.365" ry="6.368" transform="matrix(.99344 -.11494 .11478 .99332 -3.335 5.059)"/><path fill="url(#m)" fill-rule="evenodd" d="M99.713 96.46h17.782q.196 0 .392.019.195.02.388.057.193.039.38.096.189.057.37.132.182.075.355.168.174.092.337.202.163.109.315.233.152.125.291.264.14.14.264.291.124.152.234.315.109.164.201.337.093.173.168.355.075.181.132.37.057.187.096.38.038.193.057.388.02.196.02.393 0 .196-.02.392-.019.195-.057.388-.039.193-.096.38-.057.189-.132.37-.075.182-.168.355-.092.173-.201.337-.11.163-.234.315-.125.152-.264.291-.139.139-.29.264-.153.124-.316.233-.163.11-.337.202-.173.093-.355.168-.181.075-.37.132-.187.057-.38.096-.193.038-.388.057-.196.02-.392.02H99.713q-.196 0-.392-.02-.195-.019-.388-.057-.193-.039-.38-.096-.189-.057-.37-.132-.182-.075-.355-.168-.174-.092-.337-.202-.163-.109-.315-.233-.152-.125-.291-.264-.139-.139-.264-.29-.124-.153-.234-.316-.109-.164-.201-.337-.093-.173-.168-.355-.075-.181-.132-.37-.057-.187-.096-.38-.038-.193-.057-.388-.02-.196-.02-.392 0-.197.02-.393.019-.195.057-.388.039-.193.096-.38.057-.189.132-.37.075-.182.168-.355.092-.173.201-.337.11-.163.234-.315.125-.152.264-.29.139-.14.29-.265.153-.124.316-.233.163-.11.337-.202.173-.093.355-.168.181-.075.37-.132.187-.057.38-.096.193-.038.388-.057.196-.02.392-.02Z" transform="rotate(45 99.713 91.569)"/><path fill="url(#n)" fill-rule="evenodd" d="M108.604 87.569h17.781q.197 0 .392.02.196.018.389.057.192.038.38.095.189.057.37.132.182.076.355.168.173.093.337.202.163.11.315.234.152.124.29.263.14.14.264.291.125.152.234.316.11.163.202.336.093.174.168.355.075.182.132.37.057.188.095.38.039.193.058.389.02.195.02.392 0 .196-.02.392t-.058.388q-.038.193-.095.381t-.132.37q-.075.181-.168.354-.093.174-.202.337-.109.164-.234.315-.124.152-.263.291-.14.14-.291.264-.152.125-.315.234-.164.109-.337.202-.173.092-.355.167-.181.076-.37.133-.188.057-.38.095-.193.038-.389.058-.195.019-.392.019h-17.781q-.197 0-.392-.02-.196-.019-.388-.057-.193-.038-.381-.095t-.37-.133q-.181-.075-.355-.167-.173-.093-.336-.202-.164-.11-.316-.234-.151-.125-.29-.264-.14-.139-.264-.29-.125-.152-.234-.316-.109-.163-.202-.337-.092-.173-.168-.354-.075-.182-.132-.37-.057-.188-.095-.38-.038-.193-.058-.39-.019-.195-.019-.391 0-.197.02-.392.019-.196.057-.388.038-.193.095-.381t.132-.37q.076-.181.168-.355.093-.173.202-.336.11-.164.234-.316.125-.152.264-.29.139-.14.29-.264.152-.125.316-.234.163-.11.336-.202.174-.092.355-.168.182-.075.37-.132.188-.057.38-.095.193-.039.389-.058.195-.02.392-.02Z" transform="rotate(135 106 94.173)"/></g></svg>