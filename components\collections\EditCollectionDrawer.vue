<template>
    <n-drawer v-model:show="show" height="80vh" class="!rounded-t-2xl dark:!bg-dark-bg dark:text-dark-text" :mask-closable="false" placement="bottom">
        <n-drawer-content>
            <template #header>
                <div class="flex items-center justify-between font-medium text-base">
                    <n-icon size="24" @click="handleClose">
                        <IconsClose />
                    </n-icon>
                    <span v-if="!collection.id">New Collections</span>
                    <span v-else>Edit Collections</span>
                    <span class="text-primary" @click="handleSubmit">Save</span>
                </div>
            </template>

            <div class="rounded-lg mb-20 text-xs">
                <div>{{ t("COLLECTION_NAME") }}</div>
                <div class="mt-2 dark:bg-dark-bg-3 bg-neutral-100 rounded-lg">
                    <n-input type="text" maxlength="30" v-model:value="collection.collectName" placeholder="e.g: Anime Characters" />
                </div>
                <div class="mt-4">{{ t("COLLECTION_DES") }}</div>
                <div class="mt-2 dark:bg-dark-bg-3 bg-neutral-100 rounded-lg">
                    <n-input type="textarea" maxlength="100" v-model:value="collection.description" placeholder="e.g: My favorite anime characters" />
                </div>
            </div>
        </n-drawer-content>
    </n-drawer>
</template>

<script setup>
import { appendCollect, updateCollect } from "@/api";

const { t } = useI18n({ useScope: "global" });
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    currentCollect: {
        type: Object,
        default: () => ({}),
    },
});
const show = ref(props.show);
watch(
    () => props.show,
    (newVal) => {
        show.value = newVal;
        collection.value = { ...props.currentCollect };
    }
);
const collection = ref({});
const emits = defineEmits(["update:show", "change"]);
const handleClose = () => {
    show.value = false;
    emits("update:show", false);
};
const loading = ref(false);
const handleSubmit = async () => {
    if (loading.value) {
        return;
    }
    let { collectName = "", description, id } = collection.value;
    collectName = collectName.trim();
    if (collectName.length < 1) {
        openToast.error(t("COLLECTION_EDIT_TIPS"));
        return;
    }

    let submitRequest = appendCollect;
    const param = { collectName, description };
    if (id) {
        param.id = id;
        submitRequest = updateCollect;
    }
    loading.value = true;
    const { status, data, message } = await submitRequest(param);
    loading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    emits("change", param);
    handleClose();
};
</script>

<style lang="scss" scoped></style>
