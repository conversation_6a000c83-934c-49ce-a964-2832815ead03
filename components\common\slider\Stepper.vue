<template>
    <div
        class="stepper-container min-w-[58px] flex justify-center items-center rounded-lg overflow-hidden bg-fill-ipt-1 text-sm font-medium text-text-2 border border-solid border-border-ipt-1"
        :class="{ '!cursor-not-allowed opacity-40': disabled }"
    >
        <div
            class="minus-btn w-[28px] h-[28px] flex justify-center items-center text-base cursor-pointer select-none"
            :class="{ '!cursor-not-allowed ': modelValue <= min || disabled }"
            v-if="showStepperBtn"
            @click.prevent.stop="handleSetDown"
        >
            <span class="mb-[1px]" :class="{ 'opacity-40': modelValue <= min }">-</span>
        </div>
        <input
            type="number"
            ref="numberInputRef"
            :min="min"
            :max="max"
            :step="step"
            :disabled="disabled"
            :value="handleFormatValue(modelValue)"
            @click.prevent.stop="handleClick"
            @focus="handleFocus"
            @change="handleChange"
            @blur="handleBlur"
            :key="inputKey"
            class="flex-1"
            :class="{ '!cursor-not-allowed': disabled }"
        />
        <div
            class="plus-btn w-[28px] h-[28px] flex justify-center items-center text-base cursor-pointer select-none"
            :class="{ '!cursor-not-allowed ': modelValue >= max || disabled }"
            v-if="showStepperBtn"
            @click="handleSetUp"
        >
            <span :class="{ 'opacity-40': modelValue >= max }"> + </span>
        </div>
    </div>
</template>
<script setup>
const emit = defineEmits(["update:modelValue", "focus", "change", "blur", "clickInput"]);
const props = defineProps({
    modelValue: {
        type: Number,
        default: 1,
    },
    min: {
        type: Number,
        default: 0,
    },
    max: {
        type: Number,
        default: 1,
    },
    step: {
        type: Number,
        default: 0.1,
    },
    showStepperBtn: {
        type: Boolean,
        default: false,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});
const inputKey = ref(0);
const handleBlur = ({ target }) => {
    const formattedValue = handleFormatValue(target.value);
    emit("blur", formattedValue);
    inputKey.value = new Date().getTime();
};
const handleChange = ({ target }) => {
    const formattedValue = handleFormatValue(target.value);
    inputKey.value = new Date().getTime();
    if (formattedValue === props.modelValue) return;
    emit("change", formattedValue);
    emit("update:modelValue", formattedValue);
};
const handleFormatValue = (value) => {
    if (value === "" || value === void 0) return props.modelValue;
    value = String(value);
    const v1 = value.includes("e") || value.includes("E");
    const v2 = value === "";
    if (v1 || v2) {
        value = props.max;
    }
    value = Number(value);
    let floatStep = !Number.isInteger(props.step);
    let intStep = Number.isInteger(props.step) && !Number.isInteger(value);
    if (floatStep || intStep) {
        const decimalPlaces = (props.step.toString().split(".")[1] || "").length;
        const factor = Math.pow(10, decimalPlaces);
        value = Math.round(value * factor) / factor;
    }
    if (value < props.min) {
        value = props.min;
    } else if (value > props.max) {
        value = props.max;
    }
    return value;
};

const numberInputRef = ref(null);
const handleFocus = () => {
    numberInputRef.value && numberInputRef.value.select(); // 确保调用 select
    emit("focus");
};
/**减小 */
const handleSetDown = () => {
    const { modelValue, disabled, step, min } = props;
    if (disabled || modelValue <= min) return;
    let value = Number(modelValue) - step;
    console.log(value, "value");

    handleChange({ target: { value } });
};
/**增加 */
const handleSetUp = () => {
    const { modelValue, disabled, step, max } = props;
    if (disabled || modelValue >= max) return;
    let value = Number(modelValue) + step;
    console.log(value, "value");
    handleChange({ target: { value } });
};

const handleClick = () => {
    emit("clickInput");
};
</script>
<style scoped lang="scss">
input {
    min-width: 48px;
    padding: 4px 8px;
    outline: none;
    border: none;
    text-align: center;
    background-color: transparent;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.stepper-container {
    border: 1px solid rgba(0, 0, 0, 0.05);
    .minus-btn {
        border-right: 1px solid rgba(0, 0, 0, 0.05);
    }
    .plus-btn {
        border-left: 1px solid rgba(0, 0, 0, 0.05);
    }
}
</style>
