<template>
    <div>
        <n-tooltip
            v-if="!isIpad"
            ref="modelTooltipRef"
            :show-arrow="false"
            class="shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)] !rounded-2xl"
            placement="right"
            raw
            trigger="click"
            :disabled="disabled"
            :on-update:show="handleStateChange"
        >
            <template #trigger>
                <slot name="actionTrigger" />
            </template>

            <div class="gap-4 tooltip-container !rounded-2xl">
                <p class="text-text-4 text-sm mb-4">{{ $t("CONFIG_BASE_RESOLUTION") }}</p>
                <div class="flex gap-6">
                    <div class="border-r border-solid border-border-t-2 w-full lg:w-[312px] flex flex-col justify-between pb-4">
                        <div class="w-full h-[328px] flex items-center justify-center relative my-4 mb-6">
                            <div class="size-[280px] flex justify-center items-center absolute top-6">
                                <Aspect class="!size-[204px] shape-creator" :width="1" :height="1" />
                            </div>
                            <div class="size-[280px] flex justify-center items-center absolute top-6 z-[5]">
                                <Aspect
                                    class="shape-creator__dynamic"
                                    :style="[
                                        currentRatio > 1
                                            ? { width: `${currentRatioItem.width / scaleRatio}px`, height: `${currentRatioItem.width / scaleRatio}px` }
                                            : { width: `${currentRatioItem.height / scaleRatio}px`, height: `${currentRatioItem.height / scaleRatio}px` },
                                    ]"
                                    :width="currentRatioItem.width"
                                    :height="currentRatioItem.height"
                                >
                                    <div class="w-full h-full flex items-center justify-center tracking-widest text-text-2">{{ currentRatioItem.label }}</div>
                                </Aspect>
                            </div>
                        </div>
                        <div class="px-4 mb-4">
                            <!-- 滑块控制比例 向左为横图 向右为竖图 中间为 1:1的方图-->
                            <CommonSliderSlide v-model="sliderValue" :min="-shapePairLength" :max="shapePairLength" :step="1" @input="handleSliderChange" />
                            <div class="w-full flex justify-between items-center text-text-drop-2 text-sm font-medium pb-4">
                                <span>{{ $t("CREATE.PORTRAIT") }}</span>
                                <span>{{ $t("CREATE.LANDSCAPE") }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="w-full lg:w-[312px] grid grid-cols-2 gap-x-2 gap-y-1 h-fit">
                        <template v-for="(item, shapeIndex) in shapeList" :key="item.id">
                            <div
                                class="ratio-item flex items-center gap-2 hover:bg-fill-drop-2 rounded-lg p-2"
                                :class="{ 'opacity-0': !item.width || !item.height, 'cursor-pointer': item.width }"
                                @click="handleRatioClick(item, shapeIndex)"
                            >
                                <Aspect :width="item.width" :height="item.height" />
                                <span class="text-text-drop-1">{{ item.label || "" }}</span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </n-tooltip>
        <div class="lg:hidden" @click="handleStateChange(true)">
            <slot name="actionTrigger" />
        </div>
        <CommonH5Popup v-model:show="show">
            <div class="h-[90dvh] overflow-hidden flex flex-col">
                <div class="shrink-0">
                    <div class="w-full flex justify-between items-center mt-5 mb-2">
                        <span class="rounded-full size-8 bg-fill-wd-1 hover:bg-fill-wd-2 flex items-center justify-center cursor-pointer" @click.stop="handleStateChange(false)">
                            <IconsArrowLeft />
                        </span>
                        <p class="text-text-1 font-medium text-base">{{ $t("CONFIG_BASE_RESOLUTION") }}</p>
                        <span class="text-primary-6 font-medium cursor-pointer text-base" @click.stop="handleStateChange(false)">{{ $t("DONE") }}</span>
                    </div>
                    <div class="w-full lg:w-[312px] flex flex-col justify-between pb-4">
                        <div class="w-full h-[120px] flex items-center justify-center relative mb-6">
                            <div class="size-[120px] flex justify-center items-center absolute top-0">
                                <Aspect class="!size-[74px] shape-creator" :width="1" :height="1" />
                            </div>
                            <div class="size-[120px] flex justify-center items-center absolute top-0 z-[5]">
                                <Aspect
                                    class="shape-creator__dynamic"
                                    :style="[
                                        currentRatio > 1
                                            ? { width: `${currentRatioItem.width / h5ScaleRatio}px`, height: `${currentRatioItem.width / h5ScaleRatio}px` }
                                            : { width: `${currentRatioItem.height / h5ScaleRatio}px`, height: `${currentRatioItem.height / h5ScaleRatio}px` },
                                    ]"
                                    :width="currentRatioItem.width"
                                    :height="currentRatioItem.height"
                                >
                                    <div class="w-full h-full flex items-center justify-center tracking-widest">{{ currentRatioItem.label }}</div>
                                </Aspect>
                            </div>
                        </div>
                        <div class="px-4">
                            <CommonSliderSlide v-model="sliderValue" :min="-shapePairLength" :max="shapePairLength" :step="1" @input="handleSliderChange" />
                            <div class="w-full flex justify-between items-center text-text-drop-2 text-sm font-medium mt-6 mb-4">
                                <span>Portrait</span>
                                <span>Landscape</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 flex flex-col items-center scroll-container no-scroll pb-20">
                    <div class="w-[68%] flex-1 grid grid-cols-2 gap-x-2 gap-y-1 mx-auto max-h-full scroll-container no-scroll">
                        <template v-for="(item, shapeIndex) in shapeList" :key="item.id">
                            <div
                                class="ratio-item flex items-center gap-2 hover:bg-fill-drop-2 rounded-lg p-2"
                                :class="{ 'opacity-0': !item.width || !item.height, 'cursor-pointer': item.width }"
                                @click="handleRatioClick(item, shapeIndex)"
                            >
                                <Aspect :width="item.width" :height="item.height" />
                                <span class="tracking-wider">{{ item.label || "" }}</span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </CommonH5Popup>
    </div>
</template>

<script setup>
import { debounce } from "@/utils/tools";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isIpad } = storeToRefs(useThemeStore());

const emit = defineEmits(["state-change", "change"]);

const props = defineProps({
    checkedRatioId: {
        type: String,
        default: "",
    },
    shapeList: {
        //由于slider交互 传入的数组长度必须是偶数
        type: Array,
        default: () => [],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});

const scaleRatio = 5;
const h5ScaleRatio = 14;
const sliderValue = ref(0);
const currentRatioItem = ref({
    width: 1,
    height: 1,
    id: "shape_0",
    label: "1:1",
});

const currentRatio = computed(() => {
    const { width, height } = currentRatioItem.value;
    return width / height;
});
// 选中比例列表中的具体比例
const handleRatioClick = (item, index = void 0, emitChange = true) => {
    console.log(item, "item");
    const { width, height } = item;
    if (!width || !height) return;
    currentRatioItem.value = item;
    emitChange && emit("change", item.id);

    if (index === void 0) return;
    if (width > height) {
        sliderValue.value = Math.floor(index / 2);
    } else {
        sliderValue.value = Math.ceil(index / -2);
    }
};
//滑块控制
const handleSliderChange = (val) => {
    let index = val;
    if (val > 0) {
        index = val * 2 + 1; //横图
    } else {
        index = val * -2; //竖图
    }
    index = Math.min(Math.max(index, 0), props.shapeList.length - 1);
    handleRatioClick(props.shapeList[index]);
};

/** 展开 关闭状态 */
const handleStateChange = (state) => {
    if (props.disabled) return;
    show.value = state;
    emit("state-change", state);
};

//可用比例长度发生变化 需要重新设置slider的边界值和当前值
const shapePairLength = computed(() => {
    const newChooseIndex = props.shapeList.findIndex((item) => item.id === props.checkedRatioId);
    handleRatioClick(props.shapeList[newChooseIndex], newChooseIndex, false);
    return Math.floor(props.shapeList.length / 2) - 1;
});

watch(
    () => props.checkedRatioId,
    (newVal) => {
        const newChooseIndex = props.shapeList.findIndex((item) => item.id === newVal);
        if (newChooseIndex !== -1) {
            handleRatioClick(props.shapeList[newChooseIndex], newChooseIndex, false); //更新选中
        }
    },
    {
        immediate: true,
    }
);

// h5 控制参数
const show = ref(false);
</script>
<style lang="scss" scoped>
.ratio-item {
    @apply h-10;
    ::v-deep(.aspect-item) {
        @apply border-text-drop-1 text-text-drop-1;
    }
}
.shape-creator {
    ::v-deep(.aspect-item) {
        @apply rounded-lg border-dotted border-border-2;
    }
    &__dynamic {
        ::v-deep(.aspect-item) {
            @apply rounded-lg;
        }
    }
}
</style>
