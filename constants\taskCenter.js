import task_daily_login from "@/assets/images/task/task_daily_login.svg";
import task_first_image_post from "@/assets/images/task/task_first_image_post.svg";
import task_first_review from "@/assets/images/task/task_first_review.svg";
import task_follow_users from "@/assets/images/task/task_follow_users.svg";
import task_join_discord from "@/assets/images/task/task_join_discord.svg";
import task_like from "@/assets/images/task/task_like.svg";
import task_follow_ig from "@/assets/images/task/task_follow_ig.svg";
import task_follow_x from "@/assets/images/task/task_follow_x.svg";
import task_vote_us_on from "@/assets/images/task/task_vote_us_on.svg";
import task_share from "@/assets/images/task/task_share.svg";
import task_subscribe_youtube from "@/assets/images/task/task_subscribe_youtube.svg";

import { SOCIAL_PLATFORM } from "@/utils/constant.js";

/** 每日任务奖励 */
export const DAILY_REWARDS_IDS = ["1", "2", "3", "4"];
/** 点喜欢的任务集合 */
export const LIKES_IDS = ["8", "9", "10", "11"];
/** 点关注的任务集合 */
export const FOLLOW_IDS = ["12", "13", "14"];
/** 分享任务的ID */
export const TASK_SHARE_ID = "4";

const communityPath = "community/explore";
const createPath = "image/create";
const galleryPath = "image/gallery";

/** 客户端在用户完成后需要主动调用的 */
export const CLIENT_FINISH_IDS = {
  // 每日登录
  DAILY_LOGIN: "1",
};

/** 任务的状态数据 */
export const TASK_STATE_MAP = {
  1: {
    icon: task_daily_login,
    i18nKey: "TASKS_DAILY_LOGIN",
    // 每日任务
    trackEvent: "daily_reward",
    // 事件task的值
    trackEventTask: "free_lumens",
  },
  2: {
    icon: task_like,
    path: communityPath,
    i18nKey: "TASKS_DAILY_LIKE",
    trackEvent: "daily_reward",
    trackEventTask: "like_20",
  },
  3: {
    icon: task_first_review,
    path: communityPath,
    i18nKey: "TASKS_DAILY_COMMENT",
    trackEvent: "daily_reward",
    trackEventTask: "comment",
  },
  4: {
    icon: task_share,
    path: galleryPath,
    i18nKey: "TASKS_DAILY_SHARE",
    trackEvent: "daily_reward",
    trackEventTask: "share_creation",
  },
  5: {
    icon: task_first_image_post,
    path: createPath,
    i18nKey: "TASKS_ACHIEVEMENT_FIRST_CREATE",
    trackEvent: "achievement",
    trackEventTask: "first_creation",
  },

  6: {
    icon: task_follow_ig,
    externalLink: "https://www.instagram.com/piclumenai/",
    i18nKey: "TASKS_ACHIEVEMENT_FOLLOW_IG",
    // 跳转后 自动完成任务
    jumpedAutoFinish: true,
    trackEvent: "achievement",
    trackEventTask: "follow_ig",
  },
  7: {
    icon: task_follow_x,
    externalLink: "https://x.com/PicLumen",
    i18nKey: "TASKS_ACHIEVEMENT_FOLLOW_X",
    // 跳转后 自动完成任务
    jumpedAutoFinish: true,
    trackEvent: "achievement",
    trackEventTask: "follow_x",
  },
  // 获得点赞
  8: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_LIKE_5",
    trackEvent: "achievement",
    trackEventTask: "get_5_likes",
  },
  9: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_LIKE_20",
    trackEvent: "achievement",
    trackEventTask: "get_20_likes",
  },
  10: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_LIKE_50",
    trackEvent: "achievement",
    trackEventTask: "get_50_likes",
  },
  11: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_LIKE_100",
    trackEvent: "achievement",
    trackEventTask: "get_100_likes",
  },

  // 获得关注
  12: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_FOLLOWER_5",
    trackEvent: "achievement",
    trackEventTask: "get_5_followers",
  },
  13: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_FOLLOWER_30",
    trackEvent: "achievement",
    trackEventTask: "get_30_followers",
  },
  14: {
    path: galleryPath,
    i18nKey: "TASKS_ACHIEVEMENT_GET_FOLLOWER_100",
    trackEvent: "achievement",
    trackEventTask: "get_100_followers",
  },

  15: {
    icon: task_subscribe_youtube,
    i18nKey: "TASKS_ACHIEVEMENT_SUBSCRIBE_YOUTUBE",
    externalLink: "https://www.youtube.com/@PicLumenAI",
    // 跳转后 自动完成任务
    jumpedAutoFinish: true,
    trackEvent: "achievement",
    trackEventTask: "follow_youtube",
  },
  16: {
    icon: task_join_discord,
    i18nKey: "TASKS_ACHIEVEMENT_JOIN_DISCORD",
    externalLink: SOCIAL_PLATFORM.DISCORD.link,
    // 跳转后 自动完成任务
    jumpedAutoFinish: true,
    trackEvent: "achievement",
    trackEventTask: "join_discord",
  },
  17: {
    icon: task_vote_us_on,
    i18nKey: "TASKS_ACHIEVEMENT_VOTE",
    externalLink: "https://www.trustpilot.com/review/piclumen.com",
    tooltip: "TASKS_ACHIEVEMENT_VOTE_HOVER_HINT",
    trackEvent: "achievement",
    trackEventTask: "vote_on_trustpilot",
  },
};
// 不能展示任务弹窗的页面
export const EXCLUDE_PAGES = ["icons", "404", "500", "account"];
