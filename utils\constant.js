import { renderStaticImage, createLocaleUrl } from "@/utils/common";
/**
 * 订阅 -- 账单类型
 */
export const BILLING_TYPE = {
    MONTHLY: "month",
    YEARLY: "year",
};

/**
 * 订阅 -- 订阅类型
 */
export const SUBSCRIBE_TYPE = {
    BASIC: "basic", // basic
    STANDARD: "standard",
    PRO: "pro",
};

//当前系统支持的多语言
export const DEFAULT_LANG_LIST = [
    { label: "English", key: "en" },
    { label: "Deutsch", key: "de" },
    { label: "Français", key: "fr" },
    { label: "Italiano", key: "it" },
    { label: "Português", key: "pt" },
    { label: "Español", key: "es" },
    { label: "繁體中文", key: "zhTW" },
    { label: "日本語", key: "ja" },
    { label: "한국어", key: "ko" },
];

/**
 * 订阅 -- 会员订阅等级权重
 * @enum {string}
 */
export const VIP_WEIGHT = {
    [BILLING_TYPE.MONTHLY]: 1,
    [BILLING_TYPE.YEARLY]: 3,
    [SUBSCRIBE_TYPE.BASIC]: 0,
    [SUBSCRIBE_TYPE.STANDARD]: 1,
    [SUBSCRIBE_TYPE.PRO]: 2,
};

/**
 * 订阅 -- 订阅归属平台类型
 * @type {{WEB: string, IOS: string, ANDROID: string, GIFT: string}}
 */
export const PLATFORM_TYPE = {
    WEB: ["stripe", "paypal"],
    IOS: ["ios"], //无效的
    ANDROID: ["android"], //无效的
    GIFT: ["gift"],
    OTHER: ["ios", "android", "google"], //取代IOS/ANDROID
};

/**
 * 订阅 -- 订阅参数
 * @enum {string}
 */
export const SUBSCRIBE_PARAMS = {
    SUCCESS_CALLBACK: () => createLocaleUrl(location.origin + "/app/user/subscribe", DEFAULT_LANG_LIST),
    FAIL_CALLBACK: () => createLocaleUrl(location.origin + "/app/user/subscribe", DEFAULT_LANG_LIST),
    PLATFORM: "piclumen",
    BUY_PLAN: "PLAN",
    BUY_ONE: "ONE",
};

/**
 * 订阅 -- 支付成功回调参数
 * @enum {string}
 */
export const SUBSCRIBE_CALLBACK_PARAMS = {
    PLAN: { type: "plan" },
    LUMEN: { type: "lumen" },
};

/**
 * 订阅 -- 价格映射
 * @enum {string}
 */
export const PRICE_MAPPING = {
    [BILLING_TYPE.MONTHLY]: {
        [SUBSCRIBE_TYPE.BASIC]: { ORIGINAL: 0, CURRENT: 0, PLAN_KEY: "SUBSCRIBE_PLAN_BASIC", billedDesc: "BILLED_MONTHLY" },
        [SUBSCRIBE_TYPE.STANDARD]: {
            ORIGINAL: 11.99, // 月度标准版原价
            CURRENT: 11.99, // 月度标准版现价
            FIRST_SUB: 8.39, //首购的价格
            SALE: "",
            FIRST_SALE: "30%",
            PLAN_KEY: "SUBSCRIBE_PLAN_STANDARD",
            billedDesc: "BILLED_MONTHLY",
        },
        [SUBSCRIBE_TYPE.PRO]: {
            ORIGINAL: 28.99, // 月度专业版现价
            CURRENT: 28.99, // 月度专业版现价
            FIRST_SUB: 14.5, //首购的价格
            SALE: "",
            FIRST_SALE: "50%",
            PLAN_KEY: "SUBSCRIBE_PLAN_PRO",
            billedDesc: "BILLED_MONTHLY",
        },
    },
    [BILLING_TYPE.YEARLY]: {
        [SUBSCRIBE_TYPE.BASIC]: { ORIGINAL: 0, CURRENT: 0, PLAN_KEY: "SUBSCRIBE_PLAN_BASIC", billedDesc: "BILLED_YEARLY" },
        [SUBSCRIBE_TYPE.STANDARD]: {
            ORIGINAL: 11.99, // 年度标准版原价
            CURRENT: 8.99, // 年度标准版现价
            FIRST_SUB: 6.3, //首购的价格
            SALE: "25%",
            FIRST_SALE: "48%",
            PLAN_KEY: "SUBSCRIBE_PLAN_STANDARD",
            billedDesc: "BILLED_YEARLY",
        },
        [SUBSCRIBE_TYPE.PRO]: {
            ORIGINAL: 28.99, // 年度专业版原价
            CURRENT: 21.99, // 年度专业版现价
            FIRST_SUB: 11.0, //首购的价格
            SALE: "24%",
            FIRST_SALE: "62%",
            PLAN_KEY: "SUBSCRIBE_PLAN_PRO",
            billedDesc: "BILLED_YEARLY",
        },
    },
};

/**
 * 订阅 -- 订阅功能权限枚举
 * @enum {string}
 */
export const SUBSCRIBE_PERMISSION = {
    IMAGE_BATCH_MAX: "imagesPerBatch",
    UPSCALE: "upscale",
    INPAINT: "inpaint",
    EXPAND: "expand",
    COLORIZE: "colorize",
    REMOVE_BG: "removeBg",
    BATCH_DOWNLOAD: "batchDownload",
    TRANSLATION: "promptTranslation",
    ENHANCE: "promptEnhance",
    HISTORY: "historyExplore",
    NOT_BASIC_MEMBER: "notBasicMember",
    CLOUD_STORAGE_COUNT: "cloudStorage",
    OUTPAINT: "outpaint",
};

/**
 * 元素触发订阅弹框枚举
 *
 * 作用在GA事件中，作为事件参数传递
 */
export const SUB_EL = {
    ENHANCE: "enhance",
    TRANSLATION: "auto_translate",
    UPGRADE: "create_upgrade",
    IMAGE_BATCH: "images_per_batch",
    FAST_TASK: "fast_task",
    UPSCALE: "upscale",
    INPAINT: "inpaint",
    COLORIZE: "colorize",
    EXPAND: "expand",
    BATCH_DOWNLOAD: "batch_download",
    DESCRIBE_DOWNLOAD: "description_translate",
    DESCRIBE_TASK: "description",
    PROFILE: "profile",
    OTHER: "other",
    RELAX_LIMIT_BAR: "create_relax_mode",
    RELAX_LIMIT_MODEL: "relax_used_up",
    REMOVE_BG: "remove_bg",
    FLUX_1_DEV: "create_image", //create flux生图 lumen不足时弹出的购买lumen弹窗
    GET_MORE_FAST: "create_get_more_fast",
};

/**
 * 全局错误码
 * @enum {string}
 */
export const SERVICE_ERROR_CODE = {
    UNKNOWN: "TOAST_TITLE_ERROR",
    NETWORK: "",
    700: "TOAST_TITLE_ERROR",
    710: "SUBSCRIBE_EX_GET", // 获取订阅计划异常
    711: "SUBSCRIBE_EX_ACTIVE", // 获取激活订阅异常
    712: "SUBSCRIBE_EX_CR_CHECKOUT", // 创建支付会话异常
    713: "SUBSCRIBE_EX_NOT", // 没有订阅
    714: "SUBSCRIBE_EX_CR_SCHEDULE", // 创建订阅计划异常
    715: "SUBSCRIBE_EX_CA", // 取消订阅异常
    716: "SUBSCRIBE_EX_CA_SCHEDULE", // 取消订阅计划异常
    717: "SUBSCRIBE_EX_CA_CUSTOMER", // 创建 Stripe 支付用户 customer 失败
    719: "SUBSCRIBE_EX_PARAMETERS", // 通过用户在数据库中找不到对应的 customer
    720: "SUBSCRIBE_EX_PARAMETERS", // 没有找到数据库对应的 StripeProduct 数据
    721: "SUBSCRIBE_EX_PARAMETERS", // 不支持的 payment_type，只支持 one 和 plan
    722: "SUBSCRIBE_EX_GET_BILLING", // 获取账单失败
    723: "SUBSCRIBE_EX_UPGRADE", // 升级规则不满足，不能切换指定订阅
    724: "SUBSCRIBE_EX_BAD", // 只能同时有一个订阅生效，不能立即购买其他订阅，接口调用错误
    725: "SUBSCRIBE_EX_NOT_PAYMENT", // 客户没有保存任何支付方式，无法创建订阅
    750: "SUBSCRIBE_EX_NOT_BUY_LUMEN", // 订阅后才能购买
    751: "SUBSCRIBE_EX_BEFORE", // 没有激活的订阅
    752: "SUBSCRIBE_EX_PAYMENT", // 支付处理发生错误
};

/**
 * 全局错误码枚举
 * @enum {string}
 */
export const ERROR_CODE_ENUM = {
    REFER_NO_FACE_ERROR: 3001, // 未检测到人脸
    KONTEXT_NSFW_ERROR: 3008, // Kontext检测、黄图、暴力等
    KONTEXT_COPYRIGHT_ERROR: 3009, // Kontext 检测侵权
    PROMPT_DETECTED_ERROR: 4001, // 提示词检测出错
    EXCEED_TASK_LIMIT_ERROR: 4002, // 超出并发限制
    EXCEED_PRELOAD_LIMIT_ERROR: 4003, // 超出预载限制
    EXCEED_AUTH_FEATURE_ERROR: 4005, // 非VIP用户使用vip功能时
    LUMENS_LACK_ERROR: 4006, // lumen点不足以支持当前功能消耗
    EXCEED_PRELOAD_DEL_ERROR: 4007, // 预载任务已经被删除了
    GEN_NOT_MODEL_ID_ERROR: 4008, // 没有模型ID
    LONG_TASK_ERROR: 4011, // 耗时任务提示
    RELAX_TASK_LIMIT_ERROR: 4012, // Relax 生图达到上限
    MJ_TASK_PROMPT_CHECK_FAILED: 4020, // MJ 提示词检测失败
    MJ_TASK_HIGH_TRAFFIC: 4025, // MJ 队列繁忙
    KONTEXT_TASK_API_FAILED: 4026, // Flux kontext api调用失败
    KONTEXT_TASK_HIGH_TRAFFIC: 4027, // Flux kontext 并发任务超限
    KONTEXT_TASK_PROMPT_EMPTY: 4028, // Flux 提示词不能为空
};
//错误码对应的错误提示枚举
export const ERROR_MESSAGE_ENUM = {
    [ERROR_CODE_ENUM.REFER_NO_FACE_ERROR]: "NO_FACE_ERROR_CONTENT", // 未检测到人脸
    [ERROR_CODE_ENUM.MJ_TASK_PROMPT_CHECK_FAILED]: "TOAST_MJ_PROMPT_CHECK_ERROR", // MJ 提示词检测失败
    [ERROR_CODE_ENUM.MJ_TASK_HIGH_TRAFFIC]: "TOAST_MODEL_HIGH_TRAFFIC_ERROR", // 队列繁忙
    [ERROR_CODE_ENUM.KONTEXT_TASK_API_FAILED]: "TOAST_KONTEXT_TASK_API_FAILED_ERROR", // Flux kontext api调用失败
    [ERROR_CODE_ENUM.KONTEXT_TASK_HIGH_TRAFFIC]: "TOAST_MODEL_HIGH_TRAFFIC_ERROR", // 队列繁忙
    [ERROR_CODE_ENUM.KONTEXT_TASK_PROMPT_EMPTY]: "TOAST_KONTEXT_TASK_PROMPT_EMPTY_ERROR", // Flux 提示词不能为空
    [ERROR_CODE_ENUM.KONTEXT_NSFW_ERROR]: "TOAST_KONTEXT_NSFW_ERROR", // Kontext检测、黄图、暴力等
    [ERROR_CODE_ENUM.KONTEXT_COPYRIGHT_ERROR]: "TOAST_KONTEXT_COPYRIGHT_ERROR", // Kontext 检测侵权
};
// 预载任务 类型
export const PRELOAD_TASK_TYPE = {
    TEXT_TO_PICTURE: "ttp", //文生图
    PICTURE_TO_PICTURE: "ptp", //图生图
    UPSCALE: "upscale", //超分
    INPAINT: "inpaint", //局部重绘
    EXTEND: "expand", //扩图
    REMOVE_BG: "removeBg", //去背景
    LINE_RECOLOR: "lineRecolor", //线稿上色
    EDIT_IMAGE: "edit", //图片编辑
};

/**
 * 社交媒体平台
 */
export const SOCIAL_PLATFORM = {
    FACEBOOK: {
        link: "https://www.facebook.com/profile.php?id=61562588510786",
    },
    GOOGLE: {
        link: "",
    },
    DISCORD: {
        link: "https://discord.gg/bAycHJgbD8",
    },
    TWITTER: {
        link: "https://x.com/PicLumen",
    },
    APPLE: {
        link: "https://apps.apple.com/us/app/piclumen-ai/id6720725066",
    },
    YOUTUBE: {
        link: "https://www.youtube.com/@PicLumenAI",
    },
};

/**
 * 功能消耗Lumen对照表
 * 单位： 1lumen
 */
export const LUMEN_EXPEND_DICT = {
    PONY_DIFFUSION_V6: 1,
    FLUX_1_SCHNELL: 1,
    DESCRIBE_IMAGE: 1,
    FLUX_1_DEV: 3, // <---------- 额外特殊模型 3积分
    // ====
    REMOVE_BG_IMAGE: 1, // 去除背景
    UPSCALE: 1, // 超分
    INPAINT: 1, // 局部重回
    EXPAND: 1, // 扩图
    COLORIZE: 1, // 重新做色
    OTHER: 1, //其他的类型
    MJ_MODEL: 3, //Mid journey的类型
    KREA_MODEL: 3, //flux krea的类型
    FLUX_KONTEXT: 12, //Kontext的类型
};
/**
 * 需要红点标记菜单的新路由
 */
export const NEW_ROUTERS_DOT = ["/tasks", "/activity"];

/**
 * 需要隐藏左侧menu的路由
 */
export const HIDE_MENU_PATH = ["/tools/remove-background"];
export const MENU_STATE = {
    EXPAND: "expand", //展开
    FOLD: "fold", //折叠
    HIDE: "hide", //隐藏
};

export const REQ_STATUS = {
    WAITING: 0,
    PROCESSING: 1,
    SUCCESS: 2,
    ERROR: 3,
};
// web 端支持的支付平台
export const PAYMENT_METHOD = {
    STRIPE: "stripe",
    PAYPAL: "paypal",
};

//生图模型id
export const fluxModelId = "e40d01af-dec2-49dd-8944-f2aae4ba0b05";
export const fluxDevModelId = "08e4f71a-416d-4f1e-a853-6b0178af1f09";
export const picLumenArtV1Id = "23887bba-507e-4249-a0e3-6951e4027f2b";
export const lineArtModelId = "2f0d593a-47db-42e6-b90b-e4534df65a98";
export const realisticModelId = "34ec1b5a-8962-4a93-b047-68cec9691dc2";
export const animeModelId = "cb4af9c7-41b0-47d3-944a-221446c7b8bc";
export const ponyV6ModelId = "14a399de-69d9-4e3b-961d-e95b35853557";
export const MjModelId = "tmj82006-d2a9-4f93-ac2c-74275f80597c";
export const fluxKontextModelId = "flux3dc-cff2-4177-ad3a-28d9b4d3ff48";
export const fluxKreaModelId = "krea87d6-5e4f-3a2b-1c0d-9e8f7g6h5i4j";
export const NamiyaModelId = "f87a123e-4b5c-4d6e-b7a1-2c3d4e5f6a7b";
export const defaultModel = {
    icon: renderStaticImage("modIcon/m-piclumen-art-v1.webp"),
    id: picLumenArtV1Id,
    cfg: 1,
    steps: 6
};
/**
 * 当前支持的生图模型
 * 由于模型上新弹窗 是使用的前端数据进行排序 请确保 最后上新的模型 放在对应组合的最前面
 */
export const modelIconMapping = [
    {
        name: "MODEL_GROUP_PIC_TITLE",
        tooltip: "MODEL_GROUP_PIC_DESC",
        model: {
            [picLumenArtV1Id]: {
                cover: renderStaticImage("modIcon/legend_art_cover.webp"),
                descLangKey: "MODEL_TIPS_ART_V1",
                descIsRich: false,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-piclumen-art-v1.webp"),
                id: picLumenArtV1Id,
                exampleImgs: [
                    renderStaticImage("models-example/img-art-01.webp"),
                    renderStaticImage("models-example/img-art-02.webp"),
                    renderStaticImage("models-example/img-art-03.webp"),
                    renderStaticImage("models-example/img-art-04.webp"),
                    renderStaticImage("models-example/img-art-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Painterly",
                    },
                    {
                        type: "info",
                        text: "Romantic",
                    },
                    {
                        type: "info",
                        text: "Soft",
                    },
                ],
            },
            [realisticModelId]: {
                cover: renderStaticImage("modIcon/legend_realistic_cover.webp"),
                descLangKey: "MODEL_TIPS_REAL",
                descIsRich: false,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-realistic-v2.webp"),
                id: realisticModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-realistic-01.webp"),
                    renderStaticImage("models-example/img-realistic-02.webp"),
                    renderStaticImage("models-example/img-realistic-03.webp"),
                    renderStaticImage("models-example/img-realistic-04.webp"),
                    renderStaticImage("models-example/img-realistic-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Photoreal",
                    },
                    {
                        type: "info",
                        text: "Clean",
                    },
                    {
                        type: "info",
                        text: "Natural",
                    },
                ],
            },
            [animeModelId]: {
                cover: renderStaticImage("modIcon/legend_anime_cover.webp"),
                descLangKey: "MODEL_TIPS_ANIME",
                descIsRich: false,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-anime-v2.webp"),
                id: animeModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-anime-01.webp"),
                    renderStaticImage("models-example/img-anime-02.webp"),
                    renderStaticImage("models-example/img-anime-03.webp"),
                    renderStaticImage("models-example/img-anime-04.webp"),
                    renderStaticImage("models-example/img-anime-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Flat",
                    },
                    {
                        type: "info",
                        text: "Crisp",
                    },
                    {
                        type: "info",
                        text: "Classic",
                    },
                ],
            },
            [lineArtModelId]: {
                cover: renderStaticImage("modIcon/legend_lineart_cover.webp"),
                descLangKey: "MODEL_TIPS_LINEART",
                descIsRich: false,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-lineart-v1.webp"),
                id: lineArtModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-lineart-01.webp"),
                    renderStaticImage("models-example/img-lineart-02.webp"),
                    renderStaticImage("models-example/img-lineart-03.webp"),
                    renderStaticImage("models-example/img-lineart-04.webp"),
                    renderStaticImage("models-example/img-lineart-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Clean",
                    },
                    {
                        type: "info",
                        text: "Sketch",
                    },
                    {
                        type: "info",
                        text: "Manga",
                    },
                ],
            },
        },
    },
    /**
     * 由于模型上新弹窗 是使用的前端数据进行排序 请确保 最后上新的模型 放在对应组合的最前面
     */
    {
        name: "MODEL_GROUP_OTHER_TITLE",
        tooltip: "MODEL_GROUP_OTHER_DESC",
        model_tip: "CREATE.VIP_ONLY_MODEL.MODEL_FOR_PRO_TIP",
        model: {
            [fluxKreaModelId]: {
                // cover: "https://wechatapppro-1252524126.cdn.xiaoeknow.com/appibxs98ig9955/image/b_u_cnqo3vm06saqdkr1tvug/73f6a5maccpsln.png",
                cover: renderStaticImage("modIcon/legend_flux_krea_cover.webp"),
                trackKey: "model_piclumen_kontext",
                descLangKey: "MODEL_TIPS_FLUX_KOREA",
                descIsRich: false,
                isNew: true,
                isVipModel: false,
                // icon: "https://wechatapppro-1252524126.cdn.xiaoeknow.com/appibxs98ig9955/image/b_u_cnqo3vm06saqdkr1tvug/73f6a5maccpsln.png",
                icon: renderStaticImage("modIcon/legend_flux_krea_cover.webp"),
                id: fluxKreaModelId,
                newModelSplashImgs: [
                    renderStaticImage("models-example/img-flux-krea-splash.webp"),
                ],
                exampleImgs: [
                    renderStaticImage("models-example/img-krea-01.webp"),
                    renderStaticImage("models-example/img-krea-02.webp"),
                    renderStaticImage("models-example/img-krea-03.webp"),
                    renderStaticImage("models-example/img-krea-04.webp"),
                    renderStaticImage("models-example/img-krea-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Realism"
                    },
                    {
                        type: "info",
                        text: "Photography"
                    },
                    {
                        type: "info",
                        text: "Aesthetics"
                    },
                ],
                initDefaultConfig: {
                    batch_size: 1,
                },
            },
            // namiya 封面和头像使用同一个
            [NamiyaModelId]: {
                cover: renderStaticImage("modIcon/legend_namiya_cover.webp"),
                trackKey: "model_piclumen_namiya",
                descLangKey: "MODEL_TIPS_NAMIYA",
                descIsRich: false,
                isNew: true,
                isVipModel: false,
                icon: renderStaticImage("modIcon/legend_namiya_cover.webp"),
                id: NamiyaModelId,
                newModelSplashImgs: [
                    renderStaticImage("models-example/img-namiya-splash.webp"),
                ],
                exampleImgs: [
                    renderStaticImage("models-example/img-namiya-01.webp"),
                    renderStaticImage("models-example/img-namiya-02.webp"),
                    renderStaticImage("models-example/img-namiya-03.webp"),
                    renderStaticImage("models-example/img-namiya-04.webp"),
                    renderStaticImage("models-example/img-namiya-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Storytelling",
                    },
                    {
                        type: "info",
                        text: "Emotional",
                    },
                    {
                        type: "info",
                        text: "Elegant",
                    },
                ],
                initDefaultConfig: {
                    resolution: {
                        width: 896,
                        height: 1152,
                        label: "3:4",
                        value: "896 x 1152",
                        baseVersion: ["SDXL", "MJ"],
                        groupKey: "03",
                        id: "shape_3",
                    },
                },
            },
            [fluxKontextModelId]: {
                cover: renderStaticImage("modIcon/legend_flux_kontext_cover.webp"),
                trackKey: "model_piclumen_kontext",
                descLangKey: "MODEL_TIPS_FLUX_KONTEXT",
                descIsRich: false,
                isNew: true,
                isVipModel: true,
                newModelSplashImgs: [
                    renderStaticImage("models-example/img-kontext-splash.webp"),
                ],
                icon: renderStaticImage("modIcon/m-flux_kontext.webp"),
                id: fluxKontextModelId,
                descriptions: [],
                exampleImgs: [
                    renderStaticImage("models-example/img-kontext-01.webp"),
                    renderStaticImage("models-example/img-kontext-02.webp"),
                    renderStaticImage("models-example/img-kontext-03.webp"),
                    renderStaticImage("models-example/img-kontext-04.webp"),
                    renderStaticImage("models-example/img-kontext-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Editable",
                    },
                    {
                        type: "info",
                        text: "Control",
                    },
                    {
                        type: "info",
                        text: "Polished",
                    },
                ],
                initDefaultConfig: {
                    batch_size: 1,
                },
            },
            [MjModelId]: {
                cover: renderStaticImage("modIcon/legend_mj_v1_cover.webp"),
                trackKey: "model_piclumen_primo",
                descLangKey: "MODEL_TIPS_MJ_V1",
                descIsRich: false,
                isNew: false,
                isVipModel: true,
                icon: renderStaticImage("modIcon/m-mj-v1.webp"),
                id: MjModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-primo-01.webp"),
                    renderStaticImage("models-example/img-primo-02.webp"),
                    renderStaticImage("models-example/img-primo-03.webp"),
                    renderStaticImage("models-example/img-primo-04.webp"),
                    renderStaticImage("models-example/img-primo-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Detailed",
                    },
                    {
                        type: "info",
                        text: "Artistic",
                    },
                    {
                        type: "info",
                        text: "Cinematic",
                    },
                ],
                initDefaultConfig: {
                    batch_size: 4,
                },
            },
            [fluxModelId]: {
                cover: renderStaticImage("modIcon/legend_flux_schnell_cover.webp"),
                descLangKey: "MODEL_TIPS_FLUX_SCHNELL",
                descIsRich: false,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-flux.1-schnell.webp"),
                id: fluxModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-schnell-01.webp"),
                    renderStaticImage("models-example/img-schnell-02.webp"),
                    renderStaticImage("models-example/img-schnell-03.webp"),
                    renderStaticImage("models-example/img-schnell-04.webp"),
                    renderStaticImage("models-example/img-schnell-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Fast",
                    },
                    {
                        type: "info",
                        text: "Simple",
                    },
                    {
                        type: "info",
                        text: "Lightweight",
                    },
                ],
            },
            [ponyV6ModelId]: {
                cover: renderStaticImage("modIcon/legend_pony_v6_cover.webp"),
                descLangKey: "MODEL_TIPS_PONY_V6",
                descIsRich: true,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-pony-v6.webp"),
                id: ponyV6ModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-pony-01.webp"),
                    renderStaticImage("models-example/img-pony-02.webp"),
                    renderStaticImage("models-example/img-pony-03.webp"),
                    renderStaticImage("models-example/img-pony-04.webp"),
                    renderStaticImage("models-example/img-pony-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Furry",
                    },
                    {
                        type: "info",
                        text: "Anthro",
                    },
                    {
                        type: "info",
                        text: "Playful",
                    },
                ],
            },
            [fluxDevModelId]: {
                cover: renderStaticImage("modIcon/legend_flux_dev_cover.webp"),
                descLangKey: "MODEL_TIPS_FLUX_DEV",
                descIsRich: false,
                isNew: false,
                isVipModel: false,
                icon: renderStaticImage("modIcon/m-flux.1-dev.webp"),
                id: fluxDevModelId,
                exampleImgs: [
                    renderStaticImage("models-example/img-dev-01.webp"),
                    renderStaticImage("models-example/img-dev-02.webp"),
                    renderStaticImage("models-example/img-dev-03.webp"),
                    renderStaticImage("models-example/img-dev-04.webp"),
                    renderStaticImage("models-example/img-dev-05.webp"),
                ],
                tags: [
                    {
                        type: "primary",
                        text: "Experimental",
                    },
                    {
                        type: "info",
                        text: "Control",
                    },
                    {
                        type: "info",
                        text: "Raw",
                    },
                ],
            },
        },
    },
];

//默认生图比例
export const defaultRatio = {
    width: 1024,
    height: 1024,
    label: "1:1",
    id: "shape_0",
};
//生图比例
export const SHAPE_ALL = [
    {
        width: 1024,
        height: 1024,
        label: "1:1",
        value: "1024 x 1024",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "01", //用于分组的groupKey
        id: "shape_0", //比例的唯一标识
    },
    {
        width: 960,
        height: 1088,
        label: "5:6",
        value: "960 x 1088",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "02",
        id: "shape_1",
    },
    {
        width: 1088,
        height: 960,
        label: "6:5",
        value: "1088 x 960",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "02",
        id: "shape_2",
    },
    {
        width: 896,
        height: 1152,
        label: "4:5",
        value: "896 x 1152",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "03",
        id: "shape_3",
    },
    {
        width: 1152,
        height: 896,
        label: "5:4",
        value: "1152 x 896",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "03",
        id: "shape_4",
    },
    {
        width: 896,
        height: 1088,
        label: "3:4",
        value: "896 x 1088",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "17",
        id: "shape_17",
    },
    {
        width: 1088,
        height: 896,
        label: "4:3",
        value: "1088 x 896",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "17",
        id: "shape_18",
    },
    {
        width: 832,
        height: 1216,
        label: "2:3",
        value: "832 x 1216",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "04",
        id: "shape_5",
    },
    {
        width: 1216,
        height: 832,
        label: "3:2",
        value: "1216 x 832",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "04",
        id: "shape_6",
    },
    {
        width: 768,
        height: 1216,
        label: "5:8",
        value: "768 x 1216",
        baseVersion: ["SDXL"],
        groupKey: "05",
        id: "shape_7",
    },
    {
        width: 1216,
        height: 768,
        label: "8:5",
        value: "1216 x 768",
        baseVersion: ["SDXL"],
        groupKey: "05",
        id: "shape_8",
    },
    {
        width: 768,
        height: 1344,
        label: "9:16",
        value: "768 x 1344",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "06",
        id: "shape_9",
    },
    {
        width: 1344,
        height: 768,
        label: "16:9",
        value: "1344 x 768",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "06",
        id: "shape_10",
    },
    {
        width: 640,
        height: 1280,
        label: "1:2",
        value: "640 x 1280",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "07",
        id: "shape_11",
    },
    {
        width: 1280,
        height: 640,
        label: "2:1",
        value: "1280 x 640",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "07",
        id: "shape_12",
    },
    {
        width: 704,
        height: 1472,
        label: "9:19",
        value: "704 x 1472",
        baseVersion: ["SDXL"],
        groupKey: "08",
        id: "shape_13",
    },
    {
        width: 1472,
        height: 704,
        label: "19:9",
        value: "1472 x 704",
        baseVersion: ["SDXL"],
        groupKey: "08",
        id: "shape_14",
    },
    {
        width: 640,
        height: 1536,
        label: "9:21",
        value: "640 x 1536",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "09",
        id: "shape_15",
    },
    {
        width: 1536,
        height: 640,
        label: "21:9",
        value: "1536 x 640",
        baseVersion: ["SDXL", "MJ"],
        groupKey: "09",
        id: "shape_16",
    },
];

export const SAMPLER_LIST = [
    { label: "Eular", value: "euler" },
    { label: "Eular a", value: "euler_ancestral" },
    { label: "Heun", value: "heun" },
    { label: "Heun++ 2", value: "heunpp2" },
    { label: "DPM2", value: "dpm_2" },
    { label: "DPM2 a", value: "dpm_2_ancestral" },
    { label: "LMS", value: "lms" },
    { label: "DPM Fast", value: "dpm_fast" },
    { label: "DPM Adaptive", value: "dpm_adaptive" },
    { label: "DPM++ 2S a", value: "dpmpp_2s_ancestral" },
    { label: "DPM++ SDE", value: "dpmpp_sde" },
    { label: "DPM++ SDE GPU", value: "dpmpp_sde_gpu" },
    { label: "DPM++ 2M", value: "dpmpp_2m" },
    { label: "DPM++ 2M SDE", value: "dpmpp_2m_sde" },
    { label: "DPM++ 2M SDE GPU", value: "dpmpp_2m_sde_gpu" },
    { label: "DPM++ 3M SDE", value: "dpmpp_3m_sde" },
    { label: "DPM++ 3M SDE GPU", value: "dpmpp_3m_sde_gpu" },
    { label: "DDPM", value: "ddpm" },
    { label: "LCM", value: "lcm" },
];

export const SCHEDULER_LIST = [
    { label: "normal", value: "normal" },
    { label: "karras", value: "karras" },
    { label: "exponential", value: "exponential" },
    { label: "sgm_uniform", value: "sgm_uniform" },
    { label: "simple", value: "simple" },
    { label: "ddim_uniform", value: "ddim_uniform" },
];

export const KEEPALIVE_PAGES = {
    COMMUNITY_EXPLORE: "CommunityExplorePage", //社区列表
    COMMUNITY_PROFILE: "CommunityProfilePage", //别人的社区作品页
    IMAGE_CREATE_WEB: "ImageCreateWebPage", //web 生图
    USER_WORKS: "UserWorksPage", //自己的社区作品页
    ACTIVITY_DETAIL: "ActivityDetailPage", //活动详情
    MESSAGE_CENTER: "MessageCenterPage", //消息中心
    GALLERY_WEB: "GalleryWebPage", //web 画廊
};

export const MAX_SEED = 9999999999;

export const REFER_TYPES = {
    IMAGE_REFER: "REFER_TYPE_TITLE",
    PROMPT_MAGIC: "MAGIC_TITLE",
    IMAGE_CONTROL: "CONTROL_TITLE",
};
