## 参考环境
```bash
node -v     v20.19.0
npm  -v     10.8.2
yarn -v     1.22.22
```

## 


```bash
# npm
npm install
npm run dev
npm run build
npm run preview


# yarn
yarn install
yarn dev
yarn build
yarn preview

```




### 任务活动展示（周年庆、新人折扣、老人召回）、套餐折扣价格
* 组件`userPromotion\index.vue`中的小组件，都受接口 `requestUserPromotionStatus` 影响
* `subscribe.vue`中价格金额 也会受到这个接口返回的金额列表参数`discountedPrices`影响,
* 具体详情在`stores\subscribe.js\initUserPromotionStatus`方法中处理

