<!--
 * @Author: HuangQS
 * @Description: 用户提升相关组件
 * @Date: 2025-07-08 13:04:49
 * @LastEditors: HuangQ<PERSON> <EMAIL>
 * @LastEditTime: 2025-07-28 15:58:51
 * 规则1.[老用户回归]权重大于[周年庆]，老用户回归折扣理论上力度更大。
 * 规则2.每次仅展示1个组件，如果存在多个也仅只展示一个。
 * 规则3.如果屏幕高度低于800px 且是PC端，则不展示侧边栏组件。

-->

<template>
    <!-- 根据服务端的权限，依次获取对应组件 按照服务端的逻辑，现阶段每次有且只有一个会被展示 -->
    <div v-if="isShowView">
        <OldUserReturnBorns v-if="isHaveOldVipBack" />
        <Anniversary v-else-if="isHasAnniversary" />
        <VipTrial v-else-if="isHaveFirstBuySub" />
    </div>
</template>

<script setup>
// 待嵌入的组件列表
import OldUserReturnBorns from "./views/OldUserReturnBorns.vue";
import Anniversary from "./views/Anniversary.vue";
import VipTrial from "./views/VipTrial.vue";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useThemeStore } from "@/stores/system-config";

import { storeToRefs } from "pinia";
const { isMobile } = storeToRefs(useThemeStore());
const subscribeStore = useSubscribeStore();
const { isHasAnniversary, isHaveOldVipBack, isHaveFirstBuySub } = storeToRefs(subscribeStore);

const screenHeight = ref(0);

const isShowView = computed(() => {
    return (screenHeight.value > 800 && !isMobile.value) || isMobile.value;
});

const calcScreenHeight = () => {
    screenHeight.value = document.documentElement.clientHeight;
};

//监听屏幕可视区域高度，然后
onMounted(() => {
    calcScreenHeight();
    window.addEventListener("resize", calcScreenHeight);
});
onUnmounted(() => {
    window.removeEventListener("resize", calcScreenHeight);
});
</script>
<style lang="scss" scoped></style>
