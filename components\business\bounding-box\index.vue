<template>
    <div class="absolute inset-0 z-20 cursor-crosshair" ref="containerRef" :class="{ 'pointer-events-none': !isDrawing }" @mousedown="onMousedown" @mousemove="onMousemove" @mouseup="onMouseup">
        <!-- @mouseleave="onMouseup" -->
        <div v-if="isDrawing" class="absolute border border-solid border-primary-6 pointer-events-none bg-primary/15 z-50" :style="boxStyle" />
    </div>
</template>

<script setup>
const props = defineProps({
    // ✅ 禁用
    disabled: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(["onStart", "onMove", "onEnd", "onBoundaryCrossed", "onUp"]);
const isDrawing = ref(false);
const currentBox = ref({ top: 0, left: 0, width: 0, height: 0 });
const startPoint = ref({ x: 0, y: 0 });
const containerRef = ref(null); // ✅ 新增

let startX = 0;
let startY = 0;
let isMouseDown = false;

// 获取全局鼠标按下
function getStartPos(e) {
    if (e.button !== 0 || props.disabled) return; // 只处理鼠标左键（button === 0）

    const containerRect = containerRef.value.getBoundingClientRect(); // 获取容器的位置信息
    if (e.clientX < containerRect.left || e.clientX > containerRect.right || e.clientY < containerRect.top || e.clientY > containerRect.bottom) {
        return; // 如果点击的区域不在容器内部，就不进行绘制
    }

    startX = e.clientX;
    startY = e.clientY;
    isMouseDown = true;
}

// 获取全局鼠标移动
function shouldOpenDrawing(e) {
    if (!isMouseDown || props.disabled) return (isMouseDown = false);

    const deltaX = Math.abs(e.clientX - startX);
    const deltaY = Math.abs(e.clientY - startY);

    if (deltaX > 5 || deltaY > 5) {
        if (!props.disabled) {
            isDrawing.value = true;
            onMousedown(e);
            isMouseDown = false;
        }
    }
}

/**
 * 获取全局鼠标抬起
 */
const closeMouseDown = (e) => {
    isMouseDown = false;
    onMouseup();
    emit("onUp", e);
};

let listenerAdded = false;
onMounted(() => {
    if (listenerAdded) return;
    listenerAdded = true;
    window.addEventListener("mousedown", getStartPos, { capture: true });
    window.addEventListener("mousemove", shouldOpenDrawing, { capture: true });
    window.addEventListener("mouseup", closeMouseDown, { capture: true });
});

onUnmounted(() => {
    window.removeEventListener("mousedown", getStartPos, { capture: true });
    window.removeEventListener("mousemove", shouldOpenDrawing, { capture: true });
    window.removeEventListener("mouseup", closeMouseDown, { capture: true });
    listenerAdded = false;
});
// 开启框选模式
const onMousedown = (e) => {
    if (!containerRef.value || props.disabled) return;
    const parent = containerRef.value.getBoundingClientRect(); // ✅ 不是 e.currentTarget
    const startXLocal = e.clientX - parent.left;
    const startYLocal = e.clientY - parent.top;

    startPoint.value = { x: startXLocal, y: startYLocal };
    emit("onStart");
};

let lastMouseY = 0; // 记录上一次鼠标的 Y 坐标，用于判断移动方向
const onMousemove = (e) => {
    if (!isDrawing.value || !containerRef.value || props.disabled) return;

    const container = containerRef.value.getBoundingClientRect(); // 获取容器的位置
    const currentX = e.clientX - container.left; // 鼠标相对于容器的X坐标
    const currentY = e.clientY - container.top; // 鼠标相对于容器的Y坐标

    // 判断鼠标的移动方向
    const isMovingUp = currentY < lastMouseY; // 如果当前 Y 小于上次的 Y，表示向上移动
    const isMovingDown = currentY > lastMouseY; // 如果当前 Y 大于上次的 Y，表示向下移动

    const left = Math.min(currentX, startPoint.value.x); // 计算矩形框的左上角位置
    const top = Math.min(currentY, startPoint.value.y); // 计算矩形框的左上角位置
    const width = Math.abs(currentX - startPoint.value.x); // 计算矩形框的宽度
    const height = Math.abs(currentY - startPoint.value.y); // 计算矩形框的高度

    currentBox.value = { left, top, width, height };
    emit("onMove", currentBox.value);
    // 获取鼠标相对于窗口的位置
    const mouseY = e.clientY;

    // 获取窗口的高度
    const windowHeight = window.innerHeight - 200;

    // 判断鼠标距离顶部或底部的距离
    const threshold = 100; // 距离边界小于等于100px才触发

    if (mouseY <= threshold + 100 && isMovingUp) {
        // 鼠标接近上边界
        emit("onBoundaryCrossed", "top");
    } else if (mouseY >= windowHeight - threshold && isMovingDown) {
        // 鼠标接近下边界
        emit("onBoundaryCrossed", "bottom");
    }

    // 更新鼠标 Y 坐标，用于下一次判断
    lastMouseY = currentY;
};

// 结束绘制
const onMouseup = () => {
    isDrawing.value = false;
    currentBox.value = { top: 0, left: 0, width: 0, height: 0 };
    emit("onEnd", currentBox.value);
};

// ✅ 自动计算style
const boxStyle = computed(() => ({
    top: currentBox.value.top + "px",
    left: currentBox.value.left + "px",
    width: currentBox.value.width + "px",
    height: currentBox.value.height + "px",
}));
</script>
