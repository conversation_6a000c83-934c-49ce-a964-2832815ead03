import { watch, h } from "vue";

import {
    getUserByToken,
    userLogoutSys,
    loginSystem,
    getCollect,
    getDailyPostPicLimit,
    getMaintenanceTime,
    collectionImg,
} from "@/api";
import { useUserProfile, useShareCollect, useDailyPostPicLimit } from "@/stores";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useCurrentTaskQueue, useCreateStore } from "@/stores/create";
import ReportModal from "@/components/ReportModal.vue";
import NewFeatModal from "@/components/NewFeatModal.vue";
import FavoriteModal from "@/components/FavoriteModal.vue";

import { Alert } from "@/icons/index.js";
import { NIcon, NCheckbox } from "naive-ui";
import { useStorageType } from "@/hook/useStorageType";

import {
    SUBSCRIBE_TYPE
} from "@/utils/constant.js";
// import { transformLocaleToBCP47 } from "@/utils/tools";
import { t } from "@/utils/i18n-util";

//用户登出
export const useClearUserInfo = () => {
    const authToken = useCookie("authToken");
    authToken.value = null;
    const userProfile = useUserProfile();
    const currentTaskQueue = useCurrentTaskQueue();
    const subscribeStore = useSubscribeStore();
    userProfile.clearUserInfo();
    currentTaskQueue.clearAllTasks();
    subscribeStore.resetVipInfo();
    userProfile.clearUserVipNoticeKeys();
};
export const useLogout = () => {
    window.trackEvent(`Personal_Popup`, { el: "personal_popup_logout" });
    const { showMessage } = useModal();

    showMessage({
        style: { width: "380px" },
        confirmBtn: t("SHORT_CONFIRM_BTN"),
        content: h("div", { class: ["mt-1", "ml-1"] }, t("MENU_LOGOUT")),
        icon: h(Alert, { class: "text-error text-2xl" }),
        title: t("DIALOG_TITLE_CONFIRM"),
    }).then(async () => {
        try {
            await userLogoutSys();
        } catch (error) {
        } finally {
            useClearUserInfo();
            await navigateTo("/account/login", { replace: true });
        }
    });
};

// 邮箱登录
export const useSignInByEmail = () => {
    const localePath = useLocalePath();
    const requestSignIn = (req, storageType) => {
        return new Promise(async (resolve) => {
            const { setStorageType } = useStorageType();
            localStorage.setItem("storageTypeText", storageType);
            setStorageType(storageType);
            const { status, data, message } = await loginSystem(req);
            if (status == 0) {
                const userProfile = useUserProfile();
                await userProfile.setUser(data);
                await useAsyncUserCount();
                resolve({ success: true, err: null });
                await navigateTo({
                    path: localePath("/community/explore"),
                    replace: true,
                });
                window.trackEvent("Sign", { el: "sign_success_method=email" });
            } else {
                resolve({ success: false, err: message });
            }
        });
    };
    return {
        requestSignIn,
    };
};

//获取用户信息
export const useAsyncUserCount = async () => {
    try {
        const token = useCookie("authToken").value;
        if (!token) {
            return Promise.resolve({ status: false });
        }
        const { getVipPermissionList, setVipInfo, initUserPromotionStatus } = useSubscribeStore();
        const { data, status } = await getUserByToken().catch((err) => err);
        if (status !== 0 || !data) {
            return Promise.resolve({ status: false });
        }
        let planList = [];
        if (import.meta.client) {
            planList = (await getVipPermissionList().catch((err) => [])) || [];
            initUserPromotionStatus().catch((_) => { }); //异步获取套餐折扣
        }
        //获取权益列表
        const { updateQueueLimit } = useCurrentTaskQueue();
        const { updateUserLocalConf, updateUser } = useUserProfile();
        const { setGenerateConfig } = useCreateStore();
        let { localLang, totalSize, usedSize, vipType, clearPrompt, userConfig = {}, dailyUpdate, ...conf } = data;

        let plan = vipType || SUBSCRIBE_TYPE.BASIC;

        // 获取并设置当前的并发数/预载数
        if (planList && planList.length > 0) {
            const { concurrentJobs, taskQueue } = planList.find((item) => item.vipType === plan) || {};
            updateQueueLimit({
                concurrency: concurrentJobs,
                queueMax: taskQueue,
            });
        }
        //设置vip信息
        setVipInfo({ plan, getInfoLoading: false });
        totalSize = Number(totalSize);
        usedSize = Number(usedSize);
        //设置用户基础信息
        updateUser({
            totalSize,
            usedSize,
            dailyUpdate,
            ...conf,
            token,
            localLang,
            userId: conf.id,
        });
        //设置用户配置信息
        updateUserLocalConf({ clearPrompt, ...userConfig });
        return Promise.resolve({ status: true, ...userConfig });
    } catch (error) {
        return Promise.resolve({ status: true });
    }
};

//新功能弹窗
export const useNewFeaturePopup = async (id) => {
    if (!id) return;
    if (!import.meta.client || typeof localStorage === "undefined") {
        return;
    }
    const serverNoticeId = Number(id);
    const localeNoticeId = Number(localStorage.getItem("noticeId") || 0);
    if (!serverNoticeId || serverNoticeId <= 0) {
        return;
    }
    if (localeNoticeId === serverNoticeId) {
        return;
    }
    const { data, status } = await getMaintenanceTime();
    if (status !== 0) {
        return;
    }
    localStorage.setItem("noticeId", serverNoticeId);
    const start = data.startTime * 1000;
    const end = data.endTime * 1000;
    const now = Date.now();
    //已经过了维护时间
    if (now >= end) {
        return;
    }

    const { showMessage } = useModal();
    showMessage({
        style: { width: "608px" },
        showCancel: false,
        showConfirm: false,
        content: h(NewFeatModal, { start, end }),
    });
};
//举报图片
export const useReportContent = async (info = {}) => {
    return new Promise((resolve) => {
        const { showMessage } = useModal();
        showMessage({
            style: { width: "500px" },
            showCancel: false,
            showConfirm: false,
            content: h(ReportModal, {
                info,
            }),
        })
            .then(() => resolve("confirm"))
            .catch(() => resolve("cancel"));
    });
};
//强制页面刷新
export const useForceReload = () => {
    const { showMessage, clearMessageBox } = useModal();
    showMessage({
        style: { width: "420px" },
        showCancel: false,
        confirmBtn: t("SHORT_RELOAD_BTN"),
        content: h("div", null, t("SHORT_RELOAD_MESSAGE")),
        icon: h(Alert, { class: "text-[#F2C991] text-2xl" }),
        title: t("DIALOG_TITLE_UPDATE"),
        onPositiveClick: () => {
            //sleepTime 1 ~ 5 int numbers
            //随机延时 1 ~ 5 秒后刷新页面
            return new Promise((resolve) => {
                const sleepTime = Math.floor(Math.random() * 5) + 1;
                setTimeout(resolve, sleepTime * 1000);
            });
        },
    }).then(() => {
        clearMessageBox();
        window.location.reload();
    });
};
//前往社区用户主页
export const useToCommunityHome = async (gid, openNew = true) => {
    const { user } = useUserProfile();
    const localePath = useLocalePath();
    const mineUserId = user.userId || "";
    if (mineUserId === gid) {
        await navigateTo(localePath(`/user/${mineUserId}`));
    } else {
        const targetUrl = localePath(`/community/profile/${gid}`);
        // 跳转到别人的作品中心
        openNew ? window.open("/app" + targetUrl, "_blank") : await navigateTo(targetUrl);
    }
};
//更新云存储用量(收藏/取消收藏/删除文件夹)
export const useUpdateCloudStorage = ({ totalCollectNum = 0, usedCollectNum = 0 }) => {
    totalCollectNum = Number(totalCollectNum);
    usedCollectNum = Number(usedCollectNum);
    const { updateUser } = useUserProfile();
    updateUser({ totalCollectNum, usedCollectNum });
};
//全局初始化收藏夹
export const useInitCollection = () => {
    if (import.meta.client) {
        return new Promise(async (resolve) => {
            const { status, data = [], message } = await getCollect();
            if (status !== 0) {
                openToast.error(message);
                resolve({
                    success: true,
                    data: [],
                });
                return false;
            }
            const shareCollect = useShareCollect();
            shareCollect.setCollectList(data);
            resolve({
                success: true,
                data,
            });
        });
    }
    return {
        success: true,
        data: [],
    };
};
//收藏图片 (加入收藏夹)
export const useAddCollection = async ({ imgName, sensitive, promptId }) => {
    window.trackEvent("APP_COLLECTION_IMG", { el: `collection_img` });
    // if (!!sensitive) {
    //     return;
    // }
    const { showMessage } = useModal();
    const userProfile = useUserProfile();
    const totalSum = userProfile.user.usedCollectNum;
    const subscribeStore = useSubscribeStore();
    const allowCollection = subscribeStore.currVipPermission.collectNum >= totalSum + 1;
    if (!allowCollection) {
        showMessage({
            showCancel: false,
            style: { width: "420px" },
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, [h("p", { class: "tracking-wider" }, t("COLLECTION_STORAGE_LIMIT_MESSAGE"))]),
            icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_NOTICE"),
        });
        return Promise.resolve(false);
    }

    const param = { promptId, imgName };
    const hasCheckCollections = await showMessage({
        style: { width: "420px" },
        confirmBtn: t("ADD_BUTTON"),
        content: h(FavoriteModal, {
            base: null,
            "onUpdate:base": (v) => Object.assign(param, v),
        }),
    })
        .then(() => {
            return Promise.resolve(true);
        })
        .catch(() => {
            return Promise.resolve(false);
        });

    if (!hasCheckCollections) {
        return Promise.resolve(false);
    }
    if (!param.classifyId) {
        return Promise.resolve(false);
    }
    const { status, data = {}, message } = await collectionImg(param);
    if (status !== 0) {
        openToast.error(message);
        return Promise.resolve(false);
    }
    useUpdateCloudStorage(data);
    useInitCollection();
    openToast.success(t("COLLECTION_ADD_SUCCESS"));
    return Promise.resolve(true);
};
// 动态维护 当日可 post 图片数量
export const useMaintainPostCount = () => {
    const dailyPostPicLimit = useDailyPostPicLimit();
    const asyncUpdateLimit = async () => {
        const { status, data = 0 } = await getDailyPostPicLimit();
        if (status === 0) {
            dailyPostPicLimit.setLimit(data);
        }

        console.log("dailyPostPicLimit", dailyPostPicLimit.limit);
    };
    const updateCount = () => {
        dailyPostPicLimit.setLimit(Math.max(dailyPostPicLimit.limit - 1, 0));
    };
    return {
        updateCount,
        asyncUpdateLimit,
    };
};

// 删除图片时二次确认
export const useDeleteImageConfirm = (isDelTask = false) => {
    return new Promise((resolve) => {
        const userProfile = useUserProfile();
        if (!userProfile.userConfig.delConfirm) {
            return resolve(true);
        }
        const { showMessage } = useModal();
        let delConfirm = false;
        showMessage({
            style: { width: "480px" },
            confirmBtn: t("TOOLBAR_DELETE"),
            content: h("div", null, [
                h("p", null, isDelTask ? t("SHORT_DEL_TASK_MESSAGE") : t("SHORT_DEL_MESSAGE")),
                h(
                    NCheckbox,
                    {
                        class: "mt-4",
                        style: {
                            "--n-color-checked": "#6904E9", // 修改选中时的颜色
                            "--n-border-checked": "1px solid #6904E9", // 修改选中时的颜色
                            "--n-border-focus": "1px solid #6904E9", // 修改选中时的颜色
                            "--n-box-shadow-focus": "none", // 修改选中时的颜色
                            "--n-border": "1px solid #6904E9", // 修改选中时的颜色
                        },
                        "on-update:checked": (val) => {
                            delConfirm = val;
                        },
                    },
                    {
                        default: () =>
                            h(
                                "span",
                                {
                                    class: " dark:text-dark-text",
                                },
                                t("SHORT_DEL_DEF_CONFIRM")
                            ),
                    }
                ),
            ]),
            icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_ATTEN"),
        })
            .then(() => {
                if (delConfirm) {
                    userProfile.syncUpdateUserConfig({ delConfirm: false });
                }
                return resolve(true);
            })
            .catch(() => {
                return resolve(false);
            });
    });
};
