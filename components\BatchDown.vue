<template>
    <div class="flex h-10 items-center cursor-pointer overflow-hidden rounded-lg bg-bg-2 hover:text-text-opt-3 select-none">
        <!-- <n-dropdown v-if="downloadOpts === 'options'" trigger="hover" :options="typeList" @select="handleSelect" @click.stop>
                    <div class="px-2.5 h-full flex items-center justify-center gap-1.5 uppercase w-24" @click.stop>
                        <n-icon size="24">
                            <IconsJpg v-if="fileType == 'jpeg'" />
                            <IconsPng v-if="fileType == 'png'" />
                        </n-icon>
                        <span>{{ fileType }}</span>
                    </div>
                </n-dropdown> -->
        <div class="px-3 gap-2 h-full flex items-center justify-center">
            <n-tooltip trigger="hover">
                <template #trigger>
                    <n-icon size="20">
                        <IconsDownload />
                    </n-icon>
                </template>
                <span>{{ t("TOOLBAR_DOWNLOAD") }}</span>
            </n-tooltip>
            <span class="max-2xl:hidden">{{ t("TOOLBAR_DOWNLOAD") }}</span>
            <n-icon size="16" class="text-info-6">
                <IconsDiamond />
            </n-icon>
        </div>
    </div>
</template>

<script setup>
import { useUserProfile } from "@/stores";

import { IconJpg, IconPng } from "@/icons/index.js";
const { t } = useI18n({ useScope: "global" });
import { NIcon } from "naive-ui";
const userProfile = useUserProfile();
const props = defineProps({
    customFileType: {
        type: String,
        default: "",
    },
});
const downloadFileType = computed(() => props.customFileType || userProfile?.userConfig?.downloadFileType || "png");
const emits = defineEmits(["updateIndex", "completed"]);

const typeList = [
    {
        label: "JPEG",
        key: "jpeg",
        icon() {
            return h(
                NIcon,
                { size: 24 },
                {
                    default: () => h(IconJpg),
                }
            );
        },
    },
    {
        label: "PNG",
        key: "png",
        icon() {
            return h(
                NIcon,
                { size: 24 },
                {
                    default: () => h(IconPng),
                }
            );
        },
    },
];
const handleSelect = (val) => {
    console.log(val);
    downloadFileType.value = val;
};
// 响应式状态
// const fileType = ref("png");
const isDownloading = ref(false);

// 并发控制和取消相关
const CONCURRENCY_LIMIT = 5;
let abortControllers = []; // 存储所有的 AbortController
const isCancelled = ref(false);

// 下载 ZIP 文件的函数
const downloadZip = async (rawList = []) => {
    if (isDownloading.value) return;
    isDownloading.value = true;
    isCancelled.value = false;
    abortControllers = [];
    console.log("开始下载图片列表：rawList", rawList);
    let list = rawList.map(({ fileUrl, highThumbnailUrl }) => fileUrl);
    console.log("开始下载图片列表：", list);
    // if (downloadFileType.value === "png") {
    //     list = rawList.map(({ fileUrl }) => fileUrl);
    // }

    try {
        // 动态导入 JSZip 以优化初始加载性能
        const JSZip = (await import("jszip")).default;
        const zip = new JSZip();
        let completed = 0;
        const total = list.length;

        // 更新进度的函数
        const updateProgress = () => {
            emits("updateIndex", completed);
        };

        // 处理单个图片的函数
        const processImage = async (url, index, abortSignal) => {
            if (isCancelled.value) return;

            try {
                const response = await fetch(url, { signal: abortSignal });
                console.log(`开始下载图片 ${index}：${url}`, response);
                if (!response.ok) throw new Error(`无法获取图片: ${url}`);
                const blob = await response.blob();
                const blobType = blob.type;
                const mimeType = `image/${downloadFileType.value}`;
                if (blobType === mimeType) {
                    zip.file(`piclumen-${Date.now()}.${downloadFileType.value}`, blob);
                } else {
                    const imageBitmap = await createImageBitmap(blob);
                    const canvas = document.createElement("canvas");
                    canvas.width = imageBitmap.width;
                    canvas.height = imageBitmap.height;
                    const ctx = canvas.getContext("2d");
                    ctx.drawImage(imageBitmap, 0, 0);
                    const convertedBlob = await new Promise((resolve, reject) => {
                        canvas.toBlob(resolve, mimeType, downloadFileType.value === "png" ? 1 : 0.75);
                    });
                    if (convertedBlob) {
                        zip.file(`piclumen-${Date.now()}.${downloadFileType.value}`, convertedBlob);
                    } else {
                        throw new Error("转换图片失败");
                    }
                }
            } catch (_) {
            } finally {
                completed++;
                updateProgress();
            }
        };

        // 并发控制的函数
        const pool = [];
        for (let i = 0; i < list.length; i++) {
            if (isCancelled.value) break;

            const controller = new AbortController();
            abortControllers.push(controller);
            const promise = processImage(list[i], i + 1, controller.signal);
            pool.push(promise);

            if (pool.length >= CONCURRENCY_LIMIT || i === list.length - 1) {
                // 等待当前批次完成
                await Promise.all(pool);
                pool.length = 0;
            }
        }

        if (isCancelled.value) {
            // 如果下载被取消，退出函数
            return;
        }

        // 生成 ZIP 文件并触发下载
        const content = await zip.generateAsync({ type: "blob" });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(content);
        link.download = `piclumen-${Date.now()}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (err) {
        if (isCancelled.value) {
            console.warn("下载已取消");
        } else {
            console.warn(`下载 ZIP 时出错: ${err.message}`);
        }
    } finally {
        isDownloading.value = false;
        abortControllers = [];
        emits("completed");
    }
};

// 取消下载的函数
const cancelDownload = () => {
    if (!isDownloading.value) return;
    console.log("Canceling download...");
    isCancelled.value = true;
    abortControllers.forEach((controller) => controller.abort());
    abortControllers = [];
    isDownloading.value = false;
};

defineExpose({
    downloadZip,
    cancelDownload,
});

// 清理资源，当组件卸载时取消所有请求
onUnmounted(() => {
    cancelDownload();
});
</script>
