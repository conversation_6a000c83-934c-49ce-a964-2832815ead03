<template>
    <button @click="showIconName = !showIconName" class="bg-red-300 px-4 py-2 mb-2 rounded-5xl">show name</button>
    <div class="flex flex-wrap gap-4">
        <div
            v-for="comp in components"
            :key="comp.name"
            @click="copyCompName(comp.name)"
            class="aspect-square cursor-pointer hover:bg-neutral-200 flex flex-col items-center justify-center shadow-sm bg-neutral-100 px-4"
        >
            <n-icon size="32">
                <component :is="comp.component" />
            </n-icon>

            <span v-if="showIconName" class="text-xs">{{ comp.name }}</span>
        </div>
    </div>
</template>

<script setup>
import { copyToClipboard } from "@/utils/tools";
const components = shallowRef([]);
const showIconName = ref(false);

const loadComponents = async () => {
    // 使用 import.meta.glob 导入指定路径下的所有 .vue 文件
    const modules = import.meta.glob("@/components/icons/*.vue");

    // 遍历所有导入函数，加载组件
    const promises = Object.keys(modules).map(async (path) => {
        const module = await modules[path]();
        return {
            name: path.replace(/.*\/(.*)\.vue/, "$1"),
            component: module.default,
        };
    });

    // 等待所有组件加载完成
    components.value = await Promise.all(promises);

    console.log(components.value);
};

const copyCompName = (comp) => {
    copyToClipboard(`<Icons${comp} />`);
};

onMounted(() => {
    loadComponents();
});
</script>

<style lang="scss" scoped></style>
