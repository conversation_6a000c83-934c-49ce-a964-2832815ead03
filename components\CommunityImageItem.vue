<template>
    <div class="overflow-hidden relative h-full">
        <NuxtLinkLocale v-if="!item.reported" class="w-full h-full backdrop-blur overflow-hidden relative box-border block" :class="!isMobile && 'waterfall-item'" :to="`/community/detail/${item.id}`">
            <img
                loading="lazy"
                :src="item.highMiniUrl || item.thumbnailUrl"
                @error="item.loaded = true"
                @load="item.loaded = true"
                alt=""
                class="w-full h-full object-contain cursor-pointer"
                :class="{ loading_bg_anime: !item.loaded }"
            />
            <div class="mask">
                <div v-if="item.publicType !== 'myself'" class="prompt-text">
                    <span v-if="!isMobile">{{ item.prompt }}</span>
                </div>
                <div v-else class="flex flex-col items-center text-center">
                    <n-icon size="18" class="text-info-6">
                        <IconsLock />
                    </n-icon>
                    <div>{{ t("COMMUNITY_AUTHOR_HIDE_PROMPT") }}</div>
                </div>

                <div v-if="!isMobile" class="flex items-center h-8 gap-1 overflow-hidden shrink-0" @click.stop.prevent>
                    <div class="flex items-center cursor-pointer gap-1 overflow-hidden" @click="useToCommunityHome(item.accountInfo.userId)">
                        <UsersAvatar :src="item?.accountInfo?.userAvatarUrl" :icon-size="32" class="shrink-0" :user-id="item?.accountInfo?.userId" :plan-level="item?.accountInfo?.planLevel" />

                        <div class="flex-1 mr-1 overflow-hidden whitespace-nowrap text-ellipsis text-xs text-dark-active-text">
                            {{ item.accountInfo.userName || "" }}
                        </div>
                    </div>
                    <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-auto" @click="handleLike(item)">
                        <n-icon size="18" class="text-dark-active-text">
                            <HeartLike :checked="isLiked" />
                        </n-icon>
                        <span class="text-dark-active-text text-xs">{{ formatLike(likeNums) }}</span>
                    </div>
                    <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-3" @click="quickComment(item)">
                        <n-icon size="18" class="text-dark-active-text">
                            <IconsNoticeComments />
                        </n-icon>
                        <span class="text-dark-active-text text-xs">{{ formatLike(item.fileCommentNums) }}</span>
                    </div>
                    <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-3">
                        <n-dropdown
                            :options="moreActionList(item)"
                            :render-label="renderDropdownLabel"
                            class="explore-more-dropdown"
                            placement="bottom"
                            trigger="hover"
                            @select="checkMoreAction($event, item)"
                        >
                            <n-icon class="text-dark-active-text rotate-90" size="18">
                                <IconsMore />
                            </n-icon>
                        </n-dropdown>
                    </div>
                </div>
            </div>
        </NuxtLinkLocale>
        <div v-else class="absolute top-0 left-0 right-0 bottom-0 bg-fill-wd-1 flex flex-col items-center justify-center dark:text-dark-text text-black">
            <n-icon size="24">
                <IconsNoEye />
            </n-icon>
            <p class="mt-4">{{ t("COMMUNITY_REPORT_IMG_TXT") }}</p>
        </div>
        <slot></slot>
    </div>
</template>

<script setup>
import { addLikeByFileId, reduceLikeByFileId } from "@/api";
import { formatNumber, copyToClipboard, handleResetParams } from "@/utils/tools";
import { useToCommunityHome, useReportContent } from "@/hook/updateAccount";
import { useUserProfile } from "@/stores";
import { useRecordCommunityScore } from "@/hook/useCommon";
import { useRemix } from "@/hook/create";
import { useTheme } from "@/hook/system.js";
import { storeToRefs } from "pinia";
import { NIcon } from "naive-ui";
import { ShareLink, Alert, IconCreate, CopyText } from "@/icons/index.js";
import { useThemeStore } from "@/stores/system-config";
import UsersAvatar from "@/components/users/Avatar.vue";

const { isMobile } = storeToRefs(useThemeStore());
const { recordCommunityScore } = useRecordCommunityScore();
const props = defineProps({
    item: {
        type: Object,
        default: () => {},
    },
});

const { t } = useI18n({ useScope: "global" });
const { user } = storeToRefs(useUserProfile());

const emits = defineEmits(["updateCommunityItem"]);
const trackEvents = (event, el) => {
    try {
        window.trackEvent(event, { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};
const renderIcon = (icon, props) => {
    return () => h(NIcon, { size: 20, ...props }, { default: () => h(icon) });
};
const showPrompt = computed(() => {
    return user.value?.userId == props.item.accountInfo.userId || props.item.publicType !== "myself";
});
const isOwn = computed(() => {
    return user.value?.userId == props.item.accountInfo.userId;
});
const moreActionList = (item) => {
    if (isOwn.value) {
        return [
            { label: "COPY_PROMPT", key: "COMMON_PROMPT", icon: renderIcon(CopyText), disabled: !showPrompt.value },
            { label: "TOOLBAR_REMIX", key: "TOOLBAR_REMIX", icon: renderIcon(IconCreate), disabled: !showPrompt.value },
            { label: "TOOLBAR_COPY_LINK", key: "TOOLBAR_COPY_LINK", icon: renderIcon(ShareLink) },
        ];
    }
    return [
        { label: "COPY_PROMPT", key: "COMMON_PROMPT", icon: renderIcon(CopyText), disabled: !showPrompt.value },
        { label: "TOOLBAR_REMIX", key: "TOOLBAR_REMIX", icon: renderIcon(IconCreate), disabled: !showPrompt.value },
        { label: "TOOLBAR_COPY_LINK", key: "TOOLBAR_COPY_LINK", icon: renderIcon(ShareLink) },
        { label: "REPORT", key: "REPORT", icon: renderIcon(Alert, { class: "text-text-drop-5" }), props: { class: "danger-bg" } },
    ];
};
const renderDropdownLabel = (option) => {
    if (option.key === "REPORT") {
        return h("span", { class: "text-error" }, { default: () => t(option.label) });
    } else {
        return t(option.label);
    }
};
const localePath = useLocalePath();
const quickComment = async ({ id }) => {
    await navigateTo({ path: localePath(`/community/detail/${id}`), query: { comment: 1 } });
};
const formatLike = computed(() => {
    return (num = 0) => formatNumber(num);
});
const likeNums = computed(() => {
    return props.item.fileLikeNums;
});
const isLiked = computed(() => {
    return props.item.liked;
});

//点赞或取消
const likeLoading = ref(false);
const handleLike = async (row) => {
    const { id } = row;
    let fileLikeNums = likeNums.value;
    let liked = isLiked.value;
    if (likeLoading.value) {
        return;
    }
    likeLoading.value = true;
    let callback = addLikeByFileId;
    let step = 1;
    if (liked) {
        callback = reduceLikeByFileId;
        step = -1;
    }
    fileLikeNums = Math.max(0, (fileLikeNums += step));
    try {
        const { status, message } = await callback({ commFileId: id }).finally(() => {
            likeLoading.value = false;
        });
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        const item = {
            id: id,
            fileLikeNums,
            liked: !liked,
        };
        emits("updateCommunityItem", item);
    } catch (error) {
        console.error("error", error);
    }
};
//更新图片举报状态
const handleUpdateReportState = ({ id }) => {
    const item = {
        id,
        reported: true,
    };
    emits("updateCommunityItem", item);
};

// 选择选项
const checkMoreAction = (key, item) => {
    switch (key) {
        case "COMMON_PROMPT":
            copyToClipboard(item.prompt);
            openToast.success(t("TOAST_COPY_SUCCESS"));
            trackEvents("Community", "thumbnail_more=copy_prompt");
            break;
        case "TOOLBAR_REMIX":
            recordCommunityScore(item.fileId, "remix");
            useRemix(item);
            trackEvents("Community", "thumbnail_more=remix");
            break;
        case "TOOLBAR_COPY_LINK":
            let href = window.location.host + `/app/community/detail/${item.id}`;
            copyToClipboard(href);
            openToast.success(t("TOAST_COPY_SUCCESS"));
            trackEvents("Community", "thumbnail_more=copy_link");
            break;
        case "REPORT":
            useReportContent({ type: "GEN_CONTENT", commFileId: item.id }).then((res) => {
                if (res !== "cancel") {
                    handleUpdateReportState(item);
                }
            });
            trackEvents("Community", "thumbnail_more=report");
            break;
    }
};
</script>

<style lang="scss" scoped>
.waterfall-item {
    cursor: pointer;
    &::after {
        @apply absolute top-0 right-0 bottom-0 left-0 z-0  opacity-0 pointer-events-none bg-gradient-to-b from-black/70 via-white/0 to-black/70;
        content: "";
    }
    &:hover {
        &::after {
            opacity: 1;
        }
        .mask {
            display: flex;
        }
    }
}
.mask {
    @apply absolute top-0 right-0 bottom-0 left-0 p-2 z-10 hidden flex-col justify-between text-dark-active-text;
}
.prompt-text {
    height: 62px;
    transition: all 0.1s ease-in-out;
    pointer-events: none;
    display: -webkit-box; /* 启用弹性盒子布局 */
    -webkit-box-orient: vertical; /* 设置盒子方向为垂直 */
    -webkit-line-clamp: 3;
    overflow: hidden;
}
</style>
