<template>
    <div>
        <h3 class="text-base font-bold">{{ t("FEATURE_VARY_TITLE") }}</h3>
        <div class="text-xs mt-4">{{ t("FEATURE_VARY_SUB_TITLE") }}</div>
        <div class="mt-2.5 h-10 p-1 gap-1 rounded-md flex justify-between bg-neutral-100 dark:bg-dark-bg-2 select-group relative" :style="{ '--index': varyIndex }">
            <div class="group-item" v-for="item in scaleList" :class="{ 'text-black !opacity-100': item.value === vary }" :key="item.label + '_vary'" @click="vary = item.value">
                <span class="leading-6">{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
const scaleList = [
    {
        label: t("FEATURE_VARY_SUBTLE"),
        value: "subtle",
    },
    {
        label: t("FEATURE_VARY_STRONG"),
        value: "strong",
    },
];
// const props = defineProps(['base'])
const emits = defineEmits(["update:base"]);
const vary = ref("subtle");
const varyIndex = computed(() => {
    return scaleList.findIndex((item) => item.value === vary.value);
});

onBeforeUnmount(() => {
    emits("update:base", { strength: vary.value });
});
</script>

<style lang="scss" scoped>
::v-deep(.n-input .n-input__input-el) {
    @apply dark:text-dark-text;
}

.select-group::after {
    @apply content-[''] absolute left-1 top-1 bottom-1  h-8 rounded bg-neutral-200;
    transform: translateX(calc(100% * var(--index) + 2px * var(--index)));
    transition: transform 0.15s cubic-bezier(0.42, 0.05, 0.03, 0.96);
    width: calc(calc(100% - 12px) / 2);
}
.conf-icon-bar {
    @apply opacity-40 cursor-pointer hover:opacity-70;
}
.group-item {
    @apply flex-1 flex items-center justify-center rounded cursor-pointer opacity-70 transition-all duration-300 hover:opacity-100 z-10 h-8;
    &:hover {
        background: rgba(225, 225, 225, 0.7);
    }
}
.dark .group-item {
    &:hover {
        background: rgba(255, 255, 255, 0.1);
    }
}
.act-bar {
    @apply text-sm bg-black/15 dark:bg-white/15 shrink-0 w-6 h-6 rounded-sm flex items-center justify-center dark:text-dark-text opacity-75;
}
</style>
