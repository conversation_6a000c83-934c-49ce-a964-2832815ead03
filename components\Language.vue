<template>
    <PopoverDropdown :options="langList" placement="top" :noTrans="true" trigger="click" @select="(command) => changeLang(command)">
        <div class="px-2 flex items-center gap-3 cursor-pointer">
            <n-icon size="24">
                <IconsGlobal />
            </n-icon>
            <span>{{ localeLangLabel }}</span>
        </div>
    </PopoverDropdown>
</template>

<script setup>
import { useUserProfile } from "@/stores/index";
import { useCurrentLang } from "@/stores/system-config";
import { DEFAULT_LANG_LIST as langList } from "@/utils/constant.js";
const currentLang = useCurrentLang();

const { setLocale } = useI18n({ inheritLocale: true, useScope: "global" });

const changeLang = (v) => {
    window.trackEvent("APP_LANG", { el: `lang=${v}` });
    setLocale(v);
    currentLang.setLang(v);
};
const localeLangLabel = computed(() => {
    return langList.find((item) => item.key === currentLang.lang)?.label;
});
</script>

<style lang="scss" scoped></style>
