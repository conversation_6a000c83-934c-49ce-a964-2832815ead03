// composables/useToast.js

/**
 * 这是一个内部 Composable，主要职责是管理 toasts 状态数组。
 * 它不应被直接在业务代码中调用，而是由插件使用。
 */
export const useToast = () => {
    // 使用 useState 创建全局唯一的响应式状态数组
    const toasts = useState("toasts", () => []);
    /**
     * 内部函数，用于显示一个 toast
     * @param {object} options - Toast 的选项
     * @param {string} options.message - 消息内容
     * @param {'success'|'error'|'info'|'warning'} [options.type='info'] - Toast 类型
     * @param {number} [options.duration=3000] - 显示时长 (ms)
     */
    const show = (options) => {
        const id = Date.now() + Math.random();

        const toastData = {
            id,
            message: options.message,
            type: options.type || "info",
            duration: options.duration || 3000,
        };

        // 新消息添加到数组头部
        toasts.value.push(toastData);
        if (toastData.duration === -1) {
            return;
        }
        // 定时移除
        setTimeout(() => {
            hide(id);
        }, toastData.duration);
    };

    /**
     * 内部函数，用于根据 ID 隐藏一个 toast
     * @param {number} id - Toast 的唯一 ID
     */
    const hide = (id) => {
        const index = toasts.value.findIndex((t) => t.id === id);
        if (index !== -1) {
            toasts.value.splice(index, 1);
        }
    };

    // 返回状态数组和操作方法
    return { toasts, show, hide };
};
