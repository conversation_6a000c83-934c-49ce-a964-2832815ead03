<!--
 * @Author: ?
 * @Description: Lumen计算工具， 计算逻辑为： 功能 * 像素等级 * 生成张数 例如： 超分1 * 200W像素2 * 同时4张图4 = 8lumen
 * @Date: 2025-05-12 14:01:53
 * @LastEditors: DESKTOP-LR99NH8\admin huang<PERSON><EMAIL>
 * @LastEditTime: 2025-05-28 16:56:18
-->
<script setup>
import IconLumenFill from "@/components/icons/LumenFill.vue";
import { LUMEN_EXPEND_DICT } from "@/utils/constant.js";
import { expendCostLumen } from "@/utils/tools.js";
import { useUserProfile } from "@/stores/index";
import { storeToRefs } from "pinia";
import { SHAPE_ALL } from "@/utils/constant";

const userProfileStore = useUserProfile();
const { userConfig } = storeToRefs(userProfileStore);

const props = defineProps({
    featureType: {
        type: String,
        default: "",
    },
    num: {
        type: Number,
        default: 0,
    },

    // 图片像素大小
    size: {
        type: String,
        default: SHAPE_ALL[0].value,
    },
    // lumen的值，针对一些特殊场景的生成，用固定值不参与计算
    lumen: {
        type: Number,
        default: 0,
    },
});

// const size = computed(() => props.size);

// 计算Lumen消耗
const expendCostNum = computed(() => props.lumen || expendCostLumen(props.featureType, props.num, props.size));

const showLumenCost = computed(() => userConfig.value?.showLumenCost);
</script>

<template>
    <span v-show="showLumenCost" class="inline-flex items-center gap-0.5">
        <IconsLumenFill class="text-base text-white" />
        <span class="flex items-center justify-center text-center text-sm text-text-white font-medium">{{ expendCostNum }}</span>
    </span>
</template>

<style lang="scss" scoped></style>
