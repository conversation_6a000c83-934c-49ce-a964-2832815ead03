<template>
    <div class="fixed top-0 left-0 right-0 bottom-0 z-[1000] flex items-center justify-center bg-black/15 backdrop-blur text-sm">
        <div class="rounded-2xl bg-bg-3 p-6 text-text-2 md:w-[600px]">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-semibold text-text-1">{{ t("IMPORT_FILE_TITLE") }}</div>
                <n-icon size="28" class="cursor-pointer" v-if="!submitLoading" @click="handleCancel">
                    <IconsClose />
                </n-icon>
            </div>
            <template v-if="!operationSuccessful">
                <div class="mt-3 font-medium">{{ t("IMPORT_FILE_DESC") }}</div>
                <div class="mt-3 font-medium">{{ t("IMPORT_FILE_LOCAL") }}</div>
                <div
                    class="mt-4 rounded-2xl p-4 relative bg-fill-ipt-1 border-2 border-dashed border-border-upload-1 hover:border-border-3 h-24 flex items-center justify-center gap-2 cursor-pointer"
                    @click="triggerFileInput"
                    @dragover.prevent="handleDragOver"
                    @dragleave.prevent="handleDragLeave"
                    @drop.prevent="handleDrop"
                    :class="{ 'drag-over': isDragOver }"
                >
                    <!-- 隐藏的文件输入 -->
                    <input type="file" ref="fileInput" @change="handleFileChange" accept=".png, .jpg, .jpeg, .jfif, .webp, .bmp" class="hidden" />
                    <n-icon size="24" v-if="!fileName"><IconsUpload /></n-icon>
                    <span v-if="!fileName">{{ t("IMPORT_FILE_LOCAL_DESC") }}</span>

                    <span v-if="fileName" class="w-full overflow-hidden break-words text-center line-clamp-2 mb-2"> {{ fileName }} </span>
                    <div v-if="fileName" class="w-6 h-6 flex items-center justify-center absolute right-2.5 bottom-1.5 text-error hover:text-red-500" @click.stop="resetUploader">
                        <n-icon :size="20"> <IconsDele /></n-icon>
                    </div>
                </div>
                <div class="mt-4 font-medium">{{ t("IMPORT_FILE_ORIGIN") }}</div>
                <div class="mt-3 relative">
                    <input
                        class="px-5 py-0.5 outline-none min-w-0 w-full h-11 bg-fill-ipt-1 rounded-full leading-10 border-2 border-solid border-border-1"
                        :class="{ '!border-error': invalidUri }"
                        :disabled="!!fileName"
                        :placeholder="t('IMPORT_FILE_ORIGIN_DESC')"
                        v-model.trim="originUri"
                        @change="invalidUri = false"
                    />
                    <div v-if="invalidUri" class="text-error text-xs ml-4 mt-0.5">{{ t(invalidUriErrorType || "IMPORT_FILE_INVALID_URL") }}</div>
                </div>
                <div class="font-medium mt-2.5 text-info-6">{{ t("IMPORT_FILE_WARNING") }}</div>
                <div class="flex items-center justify-end gap-3 mt-5">
                    <Button v-if="!submitLoading" size="large" type="secondary" class="min-w-44" @click="handleCancel">{{ t("COMMON_BTN_CANCEL") }}</Button>
                    <Button v-if="!submitLoading" size="large" type="primary" class="min-w-44" @click="handleProcessFile">{{ t("IMPORT_FILE_BTN") }}</Button>
                    <Button v-if="submitLoading" size="large" :loading="true" type="primary" class="min-w-44">{{ t("IMPORT_FILE_LOADING") }}</Button>
                </div>
            </template>
            <template v-else>
                <div class="mt-4 flex items-center gap-2 md:w-[500px]">
                    <n-icon size="24" class="text-success-6">
                        <IconsSuccess />
                    </n-icon>
                    <span class="text-base">{{ t("DIALOG_TITLE_NOTICE") }}</span>
                </div>
                <div class="mt-4">{{ t("IMPORT_FILE_SUCCESSFUL") }}</div>

                <div class="flex items-center justify-end gap-3 mt-5">
                    <Button type="primary" size="large" class="min-w-44" @click="handleGoToCreate">{{ t("IMPORT_FILE_SUCCESSFUL_BTN") }}</Button>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup>
import { uploadToCos } from "@/utils/tools";
import { saveCustomUpload } from "@/api";

import { useSyncAction } from "@/stores/syncAction";

const { t } = useI18n({ useScope: "global" });

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(["update:show"]);
const submitLoading = ref(false);
const handleCancel = () => {
    emits("update:show", false);
};

const fileInput = ref(null);
const isDragOver = ref(false);
// 触发文件选择
const triggerFileInput = () => {
    fileInput.value.click();
};
//清空文件
const resetUploader = () => {
    if (abortController.value) {
        abortController.value.abort();
    }
    if (fileInput.value) {
        fileInput.value.value = ""; // 标准写法
        // 兼容IE写法
        fileInput.value.type = "text";
        fileInput.value.type = "file";
    }
    fileVal = null;
    fileName.value = "";
    originUri.value = "";
    invalidUri.value = false;
    submitLoading.value = false;
    abortController.value = null;
};
const isValidImageFile = (file) => {
    // 允许的 MIME 类型
    const allowedMimeTypes = ["image/png", "image/jpeg", "image/webp", "image/bmp"];
    // 允许的特殊扩展名 (JFIF)
    const allowedExtensions = ["jfif"];

    // 获取文件扩展名
    const extension = file.name.split(".").pop().toLowerCase();

    return allowedMimeTypes.includes(file.type) || allowedExtensions.includes(extension);
};
// 处理文件选择/拖拽
const originUri = ref("");
const invalidUri = ref(false);
const invalidUriErrorType = ref("");
const fileName = ref("");
let fileVal = null;
// 压缩图片主逻辑（二分法优化）
const compressImage = async (canvas, maxSize, signal) => {
    let quality = 0.9;
    let low = 0.1;
    let high = 1;
    let bestBlob = null;
    // 首次快速尝试
    const firstBlob = await canvasToWebP(canvas, quality);
    checkAbort(signal);
    if (firstBlob.size <= maxSize) return firstBlob;
    // 二分法压缩
    for (let i = 0; i < 6; i++) {
        checkAbort(signal);
        const mid = (low + high) / 2;
        const blob = await canvasToWebP(canvas, mid);
        if (blob.size <= maxSize) {
            bestBlob = blob;
            low = mid;
        } else {
            high = mid;
        }
    }

    checkAbort(signal);
    return bestBlob || canvasToWebP(canvas, low);
};
const checkAbort = (signal) => {
    if (signal.aborted) {
        throw new DOMException("Aborted", "AbortError");
    }
};

// 将Canvas转换为WebP Blob
const canvasToWebP = (canvas, quality) => {
    return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => (blob ? resolve(blob) : reject("转换失败")), "image/webp", quality);
    });
};

// 文件选择事件
const handleFileChange = (e) => {
    const file = e.target.files[0];
    // 对拖拽文件进行验证
    if (!isValidImageFile(file)) {
        const limitErr = { type: "PNG, JPG, JPEG, JFIF, WEBP, BMP" };
        openToast.error(t("IMPORT_FILE_TYPE_ERROR", limitErr), 5e3);
        return;
    }
    setLocalFile(file);
};

// 拖拽事件
const handleDrop = (e) => {
    isDragOver.value = false;
    const file = e.dataTransfer.files[0];
    // 对拖拽文件进行验证
    if (!isValidImageFile(file)) {
        const limitErr = { type: "PNG, JPG, JPEG, JFIF, WEBP, BMP" };
        openToast.error(t("IMPORT_FILE_TYPE_ERROR", limitErr), 5e3);
        return;
    }
    setLocalFile(file);
};

const setLocalFile = (file) => {
    fileVal = file;
    fileName.value = file.name;
    //清空URL 输入框，并禁用
    originUri.value = "";
    invalidUri.value = false;
};
// 读取本地图片
const loadImage = async (file, signal) => {
    return new Promise((resolve, reject) => {
        signal.addEventListener("abort", () => reject(new DOMException("Aborted", "AbortError")));
        const reader = new FileReader();
        reader.onload = (e) => {
            if (signal.aborted) return;
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    });
};

const checkUri = (url) => {
    // 校验是不是合法链接 正则表达式模式
    const patterns = {
        http: /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_+.~#?&/=]*)$/,
        dataURI: /^data:image\/(\w+);base64,([^"]*)$/i,
    };
    return patterns.http.test(url) || patterns.dataURI.test(url);
};
// 读取远程图片数据
const loadOriginImage = async (url, signal) => {
    console.log("loadOriginImage", url + "_____");
    if (!checkUri(url)) {
        throw new Error("IMPORT_URL_TYPE_ERROR");
    }
    return new Promise((resolve, reject) => {
        const img = new Image();
        // 处理跨域请求
        img.crossOrigin = "Anonymous";
        img.onload = () => resolve(img);
        img.onerror = (e) => reject(new Error("IMPORT_FILE_INVALID_URL"));

        img.src = url;
        // 修复缓存问题
        if (url.indexOf("?") === -1) {
            img.src = url + "?t=" + Date.now();
        } else {
            img.src = url + "&t=" + Date.now();
        }
    });
};

const MAX_SIZE = 1 * 1024 * 1024; // 1MB
const abortController = ref(null);
const operationSuccessful = ref(false);
const handleProcessFile = async () => {
    operationSuccessful.value = false;
    if (submitLoading.value) {
        return;
    }
    if (!fileVal && !originUri.value) {
        return;
    }
    submitLoading.value = true;
    try {
        abortController.value = new AbortController();
        let image = null;
        if (fileVal) {
            image = await loadImage(fileVal, abortController.value.signal);
        } else {
            image = await loadOriginImage(originUri.value, abortController.value.signal);
        }
        const canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        canvas.getContext("2d").drawImage(image, 0, 0);
        // 压缩图片
        const webpBlob = await compressImage(canvas, MAX_SIZE, abortController.value.signal);
        const res = await uploadToCos({ file: webpBlob, originalFileName: `${fileName.value}.webp` });
        await saveCustomUpload({
            fileUrl: res.fullPath,
            width: image.width,
            height: image.height,
            bePiclumen: false,
        });
        importFileCompleteNext();
    } catch (error) {
        if (originUri.value) {
            invalidUri.value = true;
            invalidUriErrorType.value = error.message;
        }
    } finally {
        submitLoading.value = false;
    }
};
//导入完成 下一步 逻辑
const route = useRoute();
const localePath = useLocalePath();
const importFileCompleteNext = () => {
    const notCreateMenu = route.path !== localePath("/image/create");
    useSyncAction().publish("reloadPage");
    // 发布刷新事件
    if (notCreateMenu) {
        operationSuccessful.value = true;
    } else {
        handleCancel();
    }
};
const handleGoToCreate = async () => {
    await navigateTo(localePath("/image/create"));
    handleCancel();
};
const handleDragOver = () => (isDragOver.value = true);
const handleDragLeave = () => (isDragOver.value = false);
onBeforeUnmount(() => {
    fileVal = null;
    abortController.value = null;
    console.log("ImportModal unmounted");
});
</script>
