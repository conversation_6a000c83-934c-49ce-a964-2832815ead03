<template>
    <div v-if="showQuestion" class="fixed right-8 bottom-24 cursor-pointer flex items-center justify-center flex-col" @click="handleShowQuestionnaire">
        <img src="@/assets/images/IconQnr.webp" class="w-20 h-20 object-contain" />
        <div class="-translate-y-1/2 h-[26px] rounded-full px-3 bg-gradient-to-r from-[#A85AF8] via-[#7457F4] to-[#7457F4] relative shadow-lg shadow-[#7457F4]/70 text-white text-xs flex items-center">
            <div class="bg-gradient-to-r from-[#934CF7] via-[#7457F4] to-[#7457F4] absolute top-[1px] left-[1px] right-0 bottom-[1px] rounded-full -z-10"></div>
            <span>{{ $t("FREE") }} 5</span>
            <n-icon size="14">
                <IconsLumenFill />
            </n-icon>
        </div>
    </div>

    <div v-if="showModal" class="absolute top-0 left-0 right-0 bottom-0 bg-bg-1 z-50 font-medium transform-gpu overflow-hidden">
        <div
            v-if="!reqStatus"
            class="absolute top-6 right-6 w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-1 cursor-pointer hover:bg-fill-wd-2 z-50"
            @click="handleCancel"
        >
            <n-icon size="24">
                <IconsClose />
            </n-icon>
        </div>
        <div v-if="!questionnaire.questionnaireId" class="flex flex-col items-center gap-8 py-[72px] max-w-[900px] mx-auto">
            <n-skeleton height="40px" class="w-2/3 mb-12" />

            <div v-for="i in 8" class="w-full flex flex-col gap-5">
                <n-skeleton text :repeat="1" class="w-full" />
                <n-skeleton text :repeat="4" class="mr-auto ml-4 w-1/3" />
            </div>
        </div>
        <n-scrollbar v-else class="h-screen px-6">
            <div class="max-w-[900px] mx-auto mt-[72px] pb-32">
                <h2 class="text-center text-2xl font-semibold text-text-1">{{ questionnaire.title }}</h2>
                <div class="flex flex-col gap-8 mt-12 md:px-32">
                    <div v-for="(item, index) in questionnaire.questionList" :key="index">
                        <div class="flex items-center text-text-1">
                            <span class="mr-1 shrink-0">{{ index + 1 }}<sup class="mr-0.5 text-danger-6 font-bold" v-show="item.required == 0">*</sup>.</span>
                            <span>{{ item.question }}</span>
                        </div>
                        <div class="mt-3 px-4">
                            <template v-if="item.type === 1">
                                <RadioGroup :options="item.options" v-model:value="item.value" />
                                <div v-if="isShowOther(item)">
                                    <div class="bg-fill-ipt-1 rounded-lg overflow-hidden mt-2 ml-1.5 border border-solid border-fill-ipt-1" :class="{ '!border-danger-6': !item.other }">
                                        <n-input
                                            v-model:value="item.other"
                                            type="textarea"
                                            class="resize-none"
                                            maxlength="100"
                                            :placeholder="$t('QUESTION_PLACEHOLDER')"
                                            :autosize="{ minRows: 3, maxRows: 3 }"
                                        />
                                    </div>
                                    <span v-show="!item.other" class="ml-1.5 mt-0.5 text-xs text-danger-6">{{ $t("QUESTION_EMPTY_ANSWER") }}</span>
                                </div>
                            </template>
                            <template v-else-if="item.type === 2">
                                <CheckboxGroup :options="item.options" v-model:value="item.value" />
                                <div v-if="isShowOther(item)">
                                    <div class="bg-fill-ipt-1 rounded-lg overflow-hidden mt-2 ml-1.5 border border-solid border-fill-ipt-1" :class="{ '!border-danger-6': !item.other }">
                                        <n-input
                                            v-model:value="item.other"
                                            type="textarea"
                                            class="resize-none"
                                            maxlength="100"
                                            :placeholder="$t('QUESTION_PLACEHOLDER')"
                                            :autosize="{ minRows: 3, maxRows: 3 }"
                                        />
                                    </div>
                                    <span v-show="!item.other" class="ml-1.5 mt-0.5 text-xs text-danger-6">{{ $t("QUESTION_EMPTY_ANSWER") }}</span>
                                </div>
                            </template>
                            <template v-else-if="item.type === 3">
                                <div class="bg-fill-ipt-1 rounded-lg overflow-hidden ml-1.5">
                                    <n-input
                                        v-model:value="item.value"
                                        type="textarea"
                                        class="resize-none"
                                        maxlength="1000"
                                        :placeholder="$t('QUESTION_PLACEHOLDER')"
                                        :autosize="{ minRows: 3, maxRows: 3 }"
                                    />
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center items-center gap-4 sticky bottom-0 left-0 right-0 py-8 bg-bg-1">
                <Button type="secondary" :disabled="reqStatus" class="min-w-40" @click="handleCancel">
                    {{ $t("COMMON_BTN_CANCEL") }}
                </Button>
                <Button type="primary" class="min-w-40" :disabled="allowClick" :loading="reqStatus" @click="handleSubmit">
                    {{ $t("SUBMIT_BTN_GET_LUMEN", { count: 5 }) }}
                    <n-icon size="14">
                        <IconsLumenFill />
                    </n-icon>
                </Button>
            </div>
        </n-scrollbar>
    </div>
</template>

<script setup>
import { getQuestionnaire, submitAnswer } from "@/api";
import { useUserProfile } from "@/stores";

const { t } = useI18n({ useScope: "global" });
const userProfile = useUserProfile();

const showModal = ref(false);
//TYPE_ANSWER_TYPE: 1:单选, 2:多选, 3:问答
//required 0 必填  1 选填
const questionnaire = ref({});
const reqStatus = ref(false);

const typeAnswerMpa = {
    1: "scAnswer",
    2: "mcAnswer",
    3: "essayAnswer",
};
//是否展示其他
const isShowOther = computed(() => {
    return (opt) => {
        let { value, type, options } = opt;
        const otherItem = options ? options.find((item) => item.other) : false;
        if (!otherItem) return false;
        if (type === 1) {
            return otherItem.value === value;
        }
        if (type === 2) {
            return Array.isArray(value) && value.includes(otherItem.value);
        }
        return false;
    };
});
const handleCancel = () => {
    reqStatus.value = false;
    showModal.value = false;
    questionnaire.value = {};
};
const handleShowQuestionnaire = () => {
    showModal.value = true;
    queryQuestionnaireContent();
};
// 获取系统问卷
const queryQuestionnaireContent = async () => {
    let { status, data, message } = await getQuestionnaire();
    if (status === 0 && data?.details) {
        questionnaire.value = data.details;
        const questions = JSON.parse(data.details);
        questions.questionList = questions.questionList.filter((item) => item.type !== 4);
        questions.subTitle = data.introduction;
        questionnaire.value = questions;
    } else if (status === 0 && (!data || !data?.details)) {
        // 问卷过期或不存在
        openToast.error(t("QUESTION_EXPIRED_TOAST"));
        userProfile.updateUser({ beAnswer: true });
        handleCancel();
    } else {
        openToast.error(message);
        handleCancel();
    }
};
const showQuestion = computed(() => !userProfile.user.beAnswer && userProfile.user.hasOwnProperty("beAnswer"));
const allowClick = computed(() => {
    const mustAnswerList = questionnaire.value.questionList?.filter((item) => item.required === 0) || [];
    if (!mustAnswerList.length) return false;
    const hasNotAnswer = mustAnswerList.findIndex((item) => {
        const { value, other } = item;
        if (!value || value.length === 0) {
            return true;
        }
        if (isShowOther.value(item) && !other) {
            return true;
        }
        return false;
    });
    return hasNotAnswer > -1;
});
//提交
const handleSubmit = async () => {
    if (reqStatus.value) {
        return;
    }

    const list = questionnaire.value.questionList;
    const categorized = list.reduce((acc, item) => {
        const type = typeAnswerMpa[item.type] || "unknown";
        if (!acc[type]) {
            acc[type] = []; // 初始化该类型的数组
        }
        acc[type].push(item);
        return acc;
    }, {});

    const params = {
        questionnaireId: questionnaire.value.questionnaireId,
        questionnaireTitle: questionnaire.value.title,
    };
    categorized.scAnswer.forEach((item, index) => {
        const answer = isShowOther.value(item) ? `${item.other}` : item.value;
        params[`scAnswer${index + 1}`] = answer;
    });
    categorized.mcAnswer.forEach((item, index) => {
        const showOther = isShowOther.value(item);
        const otherOpt = item.options.find((temp) => temp.other);
        let answers = item.value || [];
        if (showOther && otherOpt) {
            answers = answers.filter((item) => item !== otherOpt.value);
            answers.push(item.other);
        }
        params[`mcAnswer${index + 1}`] = answers.join("#$$#");
    });

    categorized.essayAnswer.forEach((item, index) => {
        params[`essayAnswer${index + 1}`] = item.value;
    });
    reqStatus.value = true;
    const { status, message } = await submitAnswer(params);
    reqStatus.value = false;

    if (status === 400) {
        // 400: 问卷已经填写过
        handleCancel();
        userProfile.updateUser({ beAnswer: true });
        openToast.error(message);
    } else if (status !== 0) {
        // 其他错误
        openToast.error(message);
    } else {
        // 提交成功
        handleCancel();
        userProfile.updateUser({ beAnswer: true });
        openToast.success(t("QUESTION_SUCCESS_TOAST"));
    }
};
</script>
