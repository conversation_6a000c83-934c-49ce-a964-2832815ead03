<template>
    <div ref="scrollView" class="overflow-y-auto scroll-container" @scroll="scrollLoadMore">
        <slot></slot>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { throttle } from "@/utils/tools";

const emits = defineEmits(["scrolltolower", "scroll"]);
const props = defineProps({
    distance: {
        type: Number,
        default: 1000,
    },
});
const scrollView = ref(null);

// let pageRefScrollTop = 0;
const scrollLoadMore = throttle(() => {
    const el = scrollView.value;
    if (el.scrollTop + el.clientHeight >= el.scrollHeight - props.distance) {
        emits("scrolltolower");
    }
    // pageRefScrollTop = e.target.scrollTop;
    //获取元素滚动的高度
    emits("scroll", el);
    // if (isBottom) {
    //     emits("scrolltolower", e);
    // }
}, 100);
const scrollTo = ({ top }) => {
    scrollView.value.scrollTo({ top });
};
defineExpose({
    scrollTo,
});
</script>
