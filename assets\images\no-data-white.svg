<svg xmlns="http://www.w3.org/2000/svg" width="170" height="170" fill="none"><defs><linearGradient id="e" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#FFF"/><stop offset="97.143%" stop-color="#FFF"/></linearGradient><linearGradient id="g" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#FFF"/><stop offset="98.571%" stop-color="#E7E7E7"/></linearGradient><linearGradient id="b" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#FFF"/><stop offset="97.143%" stop-color="#FFF"/></linearGradient><linearGradient id="k" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#BCBCBC"/><stop offset="99.286%" stop-color="#C7C7C7" stop-opacity=".48"/></linearGradient><linearGradient id="l" x1=".354" x2=".738" y1=".032" y2=".872"><stop offset="0%" stop-color="#A1A1A1"/><stop offset="100%" stop-color="#8F8F8F"/></linearGradient><linearGradient id="m" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#FFF"/><stop offset="100%" stop-color="#BBB"/></linearGradient><linearGradient id="n" x1=".354" x2=".738" y1=".032" y2=".872"><stop offset="3.571%" stop-color="#CACACA"/><stop offset="100%" stop-color="#ACACAC"/></linearGradient><linearGradient id="o" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#BCBCBC"/><stop offset="99.286%" stop-color="#C7C7C7" stop-opacity=".48"/></linearGradient><linearGradient id="p" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#BFBFBF"/><stop offset="99.286%" stop-color="#C7C7C7" stop-opacity=".48"/></linearGradient><linearGradient id="q" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#BFBFBF"/><stop offset="99.286%" stop-color="#C7C7C7" stop-opacity=".48"/></linearGradient><linearGradient id="r" x1=".5" x2=".5" y1="0" y2="1"><stop offset="0%" stop-color="#BFBFBF"/><stop offset="99.286%" stop-color="#C7C7C7" stop-opacity=".48"/></linearGradient><filter id="d" width="102.333" height="118.952" x="-20" y="-16" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4"/><feGaussianBlur stdDeviation="5"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.30000001192092896 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter><filter id="f" width="115.118" height="135.878" x="-20" y="-16" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4"/><feGaussianBlur stdDeviation="5"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.20000000298023224 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter><filter id="a" width="115.118" height="135.878" x="-20" y="-16" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4"/><feGaussianBlur stdDeviation="5"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.30000001192092896 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter><filter id="i" width="210.064" height="225.85" x="-60" y="-60" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feGaussianBlur result="effect1_foregroundBlur" stdDeviation="15"/></filter><filter id="j" width="150.064" height="161.631" x="-30" y="-30" color-interpolation-filters="sRGB" filterUnits="objectBoundingBox"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feGaussianBlur result="effect1_foregroundBlur" stdDeviation="7.5"/></filter><clipPath id="c"><rect width="170" height="170" rx="0"/></clipPath><mask id="h" maskUnits="userSpaceOnUse" style="mask-type:alpha"><g filter="url(#a)" transform="matrix(.99344 -.11494 .11478 .99332 -4.208 2.205)"><rect width="75.118" height="95.878" x="17" y="37.634" fill="url(#b)" rx="8"/></g></mask></defs><g clip-path="url(#c)"><g filter="url(#d)" transform="rotate(10.117 92.64 35.815) skewX(.014)"><rect width="62.333" height="78.952" x="92.636" y="35.863" fill="url(#e)" rx="8"/></g><g filter="url(#f)" transform="matrix(.99344 -.11494 .11478 .99332 -4.208 2.205)"><rect width="75.118" height="95.878" x="17" y="37.634" fill="url(#g)" rx="8"/></g><g mask="url(#h)"><g filter="url(#i)" style="opacity:.15000000596046448"><path fill="#D8D8D8" d="m8.84 105.1 19.93 23.778-4.983-86.674L78.21 30.699l18.012-6.137 2.683-1.534-86.998 1.534L8.84 105.1Z"/></g><g filter="url(#j)" style="opacity:.10000000149011612"><path fill="#D8D8D8" d="m8.84 105.1 14.947 19.56-2.3-83.606L78.21 30.699l18.012-6.137 2.683-1.534-86.998 1.534L8.84 105.1Z"/></g></g><path fill="url(#k)" d="M33.387 86.37c-1.558 2.667.365 6.017 3.454 6.017h44.27c2.71 0 4.637-2.64 3.81-5.22l-5.728-17.869c-.886-2.763-4.326-3.693-6.483-1.754l-11.026 9.91a4 4 0 0 1-4.037.785l-12.967-4.7a4 4 0 0 0-4.817 1.742L33.387 86.37Z" transform="matrix(.99344 -.11494 .11478 .99332 -7.083 3.857)"/><path fill="url(#l)" fill-rule="evenodd" d="m106.396 122.83 14.423 14.332q.281.28.502.608.22.328.372.693.152.366.23.754.077.387.077.783 0 .196-.02.392-.018.196-.057.388-.038.193-.095.381t-.132.37q-.076.181-.168.355-.093.173-.202.336-.11.164-.234.316-.125.151-.264.29-.138.14-.29.264-.152.125-.316.234-.163.11-.336.202-.174.092-.355.167-.182.076-.37.133-.188.057-.38.095-.193.038-.389.058-.195.019-.392.019-.392 0-.777-.076-.385-.077-.748-.226-.363-.15-.69-.367-.326-.217-.604-.494h-.001l-14.423-14.333q-.28-.278-.501-.607-.22-.328-.373-.693-.152-.366-.23-.754-.077-.387-.077-.783 0-.197.02-.392.019-.196.057-.388.039-.193.096-.381t.132-.37q.075-.181.168-.355.092-.173.201-.336.11-.164.234-.316.125-.151.264-.29.139-.14.29-.264.153-.125.316-.234.163-.11.337-.202.173-.092.355-.168.181-.075.37-.132.187-.057.38-.095t.388-.058q.196-.019.392-.019.393 0 .778.076.384.076.747.226.363.15.69.367.326.217.605.494Z"/><ellipse cx="91.312" cy="110.326" fill="url(#m)" rx="20.312" ry="20.326"/><ellipse cx="91.312" cy="110.326" stroke="url(#n)" stroke-width="8" rx="16.312" ry="16.326"/><ellipse cx="38.734" cy="56.913" fill="url(#o)" rx="5.749" ry="5.753" transform="matrix(.99344 -.11494 .11478 .99332 -5.656 4.133)"/><rect width="31.684" height="6.135" x="108.587" y="66.234" fill="url(#p)" rx="3.068" transform="rotate(10.007 108.594 66.145) skewX(.013)"/><rect width="37.974" height="6.134" x="105.064" y="52" fill="url(#q)" rx="3.067" transform="rotate(10.007 105.07 51.93) skewX(.013)"/><rect width="23.598" height="6.137" x="113.794" y="80.276" fill="url(#r)" rx="3.068" transform="rotate(10.007 113.804 80.168) skewX(.013)"/></g></svg>