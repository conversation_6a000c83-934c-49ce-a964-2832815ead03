<!--
 * @Author: HuangQS
 * @Description: 支付记录Table Purchase-table
 * @Date: 2025-06-10 15:39:51
 * @LastEditors: <PERSON><PERSON><PERSON>uang<PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-08-08 15:29:22
-->
<template>
    <Table :columns="columns" :data="tableDataList" :loading="loading"  @on-scroll-to-bottom="handleScrollToBottom" />
</template>
<script setup>
import { h } from "vue";
import Table from "@/components/Table.vue";
import { requestUserPayRecord } from "@/api";
import { converTimeByTimestamp } from "@/utils/tools.js";

const tableLastId = ref("");
const tableDataList = ref([
    //格式：{userId ,userLoginName ,changeType}
    // { sourceName: "test", detail: "test", amount: 100, time: "2025-06-10 16:38:02" },
]);
const loading = ref(true);

const columns = ref([
    { title: t("LUMENS_RECORDS.SOURCE_NAME"), key: "sourceName" },
    { title: t("LUMENS_RECORDS.DETAILS"), key: "detail" },
    {
        title: t("LUMENS_RECORDS.COST"),
        key: "amount",
        render(row) {
            return h("div", { class: "text-danger-6" }, [h("p", { class: "" }, `${(parseFloat(row.amount ?? 0) / 1000).toFixed(2)} ${row.currency}`)]);
        },
    },
    {
        title: t("LUMENS_RECORDS.TIME"),
        key: "time",
        render(row) {
            return converTimeByTimestamp(Number(row.time));
        },
    },
]);

const refreshData = async () => {
    let params = { pageSize: tableDataList.value?.length ? 10 : 50 };
    if (tableLastId.value) {
        params.lastId = tableLastId.value;
    }

    let { data, status, message } = await requestUserPayRecord(params);
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    data = data ?? {
        lastId: "", // 最后一条数据id
        pageNum: null,
        total: null, // 总数
        resultList: [], // 分页数据集合
    };

    loading.value = false;
    const { lastId, pageNum, total, resultList } = data;
    tableDataList.value = [...new Set([...tableDataList.value, ...resultList].map((item) => JSON.stringify(item)))].map((item) => JSON.parse(item));
};

onMounted(() => {
    refreshData();
});

const handleScrollToBottom = () => {
    refreshData();
};
</script>

<style scoped></style>
