<!--
 * @Author: byc
 * @Description: 打字机效果组件
 * @Date: 2025-06-30 13:43:01
 * @LastEditors: HuangQ<PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-08-06 10:04:22
-->
<template>
    <div class="overflow-hidden">
        <span>{{ typedText }}</span>
        <span v-show="!complete" class="ml-0.5 cursor">|</span>
    </div>
</template>

<script setup>
const props = defineProps({
    isWordOut: { type: Boolean, default: true }, // 是否是以【单词】形式输出，以空格作为分割
    fullText: { type: String, required: true }, // 打字机输出的全文
    complete: { type: Boolean, default: false },
    speed: { type: Number, default: 100 }, // 输入速度
});
const emits = defineEmits(["update:complete"]);
const { isWordOut, fullText, speed } = toRefs(props);

const typedText = ref("");
let words = [];
let timeout = null;
const typeText = () => {
    if(isWordOut.value) {
        typedText.value += ` ${words.shift()} `;
    } else {
        typedText.value += `${words.shift()}`;
    }

    if (words.length != 0) {
        timeout = setTimeout(typeText, speed.value);
    } else {
        emits("update:complete", true);
    }
};

onMounted(() => {
    words = fullText?.value.split(isWordOut.value ? " " : "") ?? [];

    typeText();
});
onBeforeMount(() => {
    timeout && clearTimeout(timeout);
    timeout = null;
});
</script>

<style scoped>
.cursor {
    animation: blink 0.7s infinite;
}

@keyframes blink {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}
</style>
