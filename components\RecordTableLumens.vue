<!--
 * @Author: HuangQS
 * @Description: <PERSON><PERSON>记录 Lumens-table
 * @Date: 2025-06-10 15:39:51
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON>ush<PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-07-31 14:15:52
-->
<template>
    <Table :columns="columns" :data="tableDataList" :loading="loading" @on-scroll-to-bottom="handleScrollToBottom" />
</template>
<script setup>
import { h } from "vue";
import Table from "@/components/Table.vue";
import { IconLumenFill } from "@/icons/index.js";
import { requestLumenChangeRecord } from "@/api";
import { converTimeByTimestamp } from "@/utils/tools.js";
const { t } = useI18n({ useScope: "global" });

const loading = ref(true);

const tableLastId = ref("");
const tableDataList = ref([
    //格式：{userId ,userLoginName ,changeType}
    // { source: "test", detail: "test", changeLumen: 100, changeType: "add", happenTime: "2025-06-10 16:38:02" },
]);

const columns = ref([
    { title: t("LUMENS_RECORDS.SOURCE_NAME"), key: "source" },
    { title: t("LUMENS_RECORDS.DETAILS"), key: "detail" },
    {
        title: t("LUMENS_RECORDS.COST_OR_GET_LUMENS"),
        key: "changeLumen",
        render(row) {
            const isAdd = row.changeType === "add";

            return h("div", { class: `flex items-center cursor-default gap-0.5 ${isAdd ? "text-success-6" : "text-danger-6"}` }, [
                h("p", { class: `` }, isAdd ? "+" : "-"),
                h(IconLumenFill, { class: "size-4 text-base" }),
                h("span", { class: "text-sm " }, row.changeLumen),
            ]);
        },
    },
    {
        title: t("LUMENS_RECORDS.TIME"),
        key: "happenTime",
        render(row) {
            return converTimeByTimestamp(Number(row.happenTimeSec));
        },
    },
]);

const refreshData = async () => {
    let params = { pageSize: tableDataList.value?.length ? 10 : 50 };
    if (tableLastId.value) {
        params.lastId = tableLastId.value;
    }

    let { data, status, message } = await requestLumenChangeRecord(params);
    if (status !== 0) {
        openToast.error(message);
        return;
    }

    data = data ?? {
        lastId: "", // 最后一条数据id
        pageNum: null,
        total: null, // 总数
        resultList: [], // 分页数据集合
    };
    loading.value = false;
    const { lastId, pageNum, total, resultList } = data;
    tableLastId.value = lastId;
    tableDataList.value = [...new Set([...tableDataList.value, ...resultList].map((item) => JSON.stringify(item)))].map((item) => JSON.parse(item));
};
onMounted(() => {
    refreshData();
});

const handleScrollToBottom = () => {
    console.log("bottom!!");
    refreshData();
};
</script>

<style scoped></style>
