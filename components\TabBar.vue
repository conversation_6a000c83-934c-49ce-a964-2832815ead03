<template>
    <div id="tabbar" class="tabbar-wrapper fixed left-0 right-0 bottom-0 z-[40]">
        <div class="relative translate-y-0">
            <div class="absolute bottom-0 right-0 z-10 p-4">
                <ClientOnly> <UserPromotion /> </ClientOnly>
            </div>
        </div>
        <div class="bg-bg-2 backdrop-blur-sm rounded-t-2xl">
            <div class="h-16 px-10 flex items-center justify-between text-xs text-neutral-300 pt-2 pb-4">
                <NuxtLinkLocale v-for="(item, index) in list" :key="index" class="flex flex-col items-center text-text-4" :class="{ '!text-text-2': isActiveLink(item.path) }" :to="item.path">
                    <n-icon size="22">
                        <component :is="item.icon" />
                    </n-icon>
                    <span class="mt-1">{{ t(item.text) }}</span>
                </NuxtLinkLocale>
            </div>
        </div>
    </div>
    <!-- tabbar占位空间 -->
    <template v-if="tabbarHeight > 0">
        <div class="flex items-center justify-between" :style="{ height: `${tabbarHeight}px` }" />
    </template>
</template>

<script setup>
const localePath = useLocalePath();
const { t } = useI18n({ useScope: "global" });
const userProfile = useUserProfile();
const route = useRoute();
const isActiveLink = computed(() => {
    return (link) => route.path === localePath(link);
});
import { IconH5Explore, IconCreate, IconGallery, IconPersonal } from "@/icons/index.js";
import { useCurrentTheme } from "@/stores/system-config";
const { getTabBarHeight } = useCurrentTheme();

const showPopover = ref(false);

const list = ref([
    { path: "/community/explore", icon: shallowRef(IconH5Explore), text: "MENU_EXPLORE" },
    { path: "/image/create", icon: shallowRef(IconCreate), text: "MENU_CREATE" },
    { path: "/m/collection", icon: shallowRef(IconGallery), text: "MENU_GALLERY" }, //临时路径
    { path: `/user/${userProfile.user.userId}`, icon: shallowRef(IconPersonal), text: "MENU_PERSONAL" },
]);

const tabbarHeight = computed(() => {
    return getTabBarHeight() || 0;
});
</script>

<style lang="scss" scoped>
.user-link {
    @apply px-6 py-3 bg-[#2F3035] text-dark-active-text/70 rounded-lg font-medium leading-6 border border-solid border-white/20 shadow-2xl text-sm;
}
.user-link-item {
    @apply h-9  flex items-center rounded-md cursor-pointer hover:bg-[#3A3B3F];
}

.tabbar-wrapper {
    animation: when-show-animation 0.3s ease-in-out;
}

@keyframes when-show-animation {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}
</style>

<style lang="scss"></style>
