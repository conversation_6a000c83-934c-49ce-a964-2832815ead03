<!--
 * @Author: HuangQS
 * @Description: 移动端切换tab 临时方案 对应原组件:SwitchTab.vue
 * @Date: 2025-05-23 17:13:33
 * @LastEditors: DESKTOP-LR99NH8\admin huang<PERSON><EMAIL>
 * @LastEditTime: 2025-05-23 18:39:45
-->
<template>
    <div class="rounded-lg p-1 bg-fill-wd-1">
        <div class="flex w-full col-span-2 pd-1 rounded-lg">
            <div
                v-for="(item, index) in options"
                class="flex p-4 gap-1 w-1/4 justify-center items-center rounded-lg"
                :class="index == activeIndex ? 'bg-bg-5' : ''"
                @click="changeTabItem(item)"
            >
                {{ item.label }}
                <component v-if="item.icon" :is="item.icon" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { SUB_EL, SUBSCRIBE_PERMISSION } from "@/utils/constant";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
const { checkShowVipNotice } = useVipNotice();

const { checkPermission } = useSubPermission();
const props = defineProps({
    options: {
        type: Array,
        default: () => [],
    },
    value: {
        type: [String, Number, Boolean],
        default: null,
    },
    checkedValues: {
        type: Array,
        default: () => [],
    },
});
const currentVal = ref(props.value);
const activeIndex = computed(() => {
    const index = props.options.findIndex((option) => option.value === props.value);
    return Math.max(0, index);
});
const emits = defineEmits(["update:value", "change"]);

const changeTabItem = async ({ value }) => {
    const needCheck = props.checkedValues.includes(value);
    if (needCheck) {
        const res = await checkPermission(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER, { triggerEl: SUB_EL.IMAGE_BATCH });
        if (!res) {
            return;
        }
        checkShowVipNotice(`switchtab_${value}`);
    }

    currentVal.value = value;
    emits("update:value", value);
    emits("change", value);
};
</script>

<style lang="scss" scoped></style>
