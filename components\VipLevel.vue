<template>
    <div v-if="plan" :style="{ width: `${currPlan.width}px`, height: `${currPlan.height}px` }">
        <img :src="currPlan.icon" />
    </div>
    <!-- <div :class="[planStyle]" class="text-xs font-semibold pl-7 pr-2 py-0.5 border-2 border-solid border-transparent rounded-full relative flex items-center">
        <component :is="planIcon" class="absolute -left-3 text-4xl" />
        <span class="text-white">{{ $t(planName) }}</span>
    </div> -->
</template>

<script setup>
import { SUBSCRIBE_TYPE } from "@/utils/constant";

import IconBasic from "@/assets/images/vip/icon_vip_level_basic.webp";
import IconStandard from "@/assets/images/vip/icon_vip_level_standard.webp";
import IconPro from "@/assets/images/vip/icon_vip_level_pro.webp";

const props = defineProps({
    plan: { type: String, required: true, default: "" },
});

const levelDict = {
    [SUBSCRIBE_TYPE.BASIC]: { icon: IconBasic, name: "basic", width: 60, height: 24 },
    [SUBSCRIBE_TYPE.STANDARD]: { icon: IconStandard, name: "standard", width: 88, height: 24 },
    [SUBSCRIBE_TYPE.PRO]: { icon: IconPro, name: "pro", width: 50, height: 24 },
};

const currPlan = computed(() => {
    return levelDict[props.plan] || levelDict[SUBSCRIBE_TYPE.BASIC];
});
</script>

<style lang="scss" scoped></style>
