<template>
    <n-tooltip ref="modelTooltipRef" :show-arrow="false" class="rounded-lg shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)]" placement="top-start" raw trigger="click">
        <template #trigger>
            <div class="w-11 h-11 rounded-full p-1.5 bg-fill-wd-1 overflow-hidden cursor-pointer">
                <img :src="currentModelCover" class="w-8 h-8 rounded-full object-cover" />
            </div>
        </template>
        <div class="bg-bg-6 border border-solid border-border-t-1 rounded-lg space-y-2 p-2 min-w-60">
            <div
                v-for="item in modelList"
                :key="item.id"
                class="px-3 py-2 flex items-center gap-2 text-text-drop-2 hover:bg-fill-drop-2 rounded cursor-pointer transition-all duration-200"
                @click="checkModel(item.id)"
            >
                <img :src="item.cover" class="w-8 h-8 rounded-full object-cover" />
                <span>{{ item.label }}</span>
            </div>
        </div>
    </n-tooltip>
</template>

<script setup>
import { renderStaticImage } from "@/utils/tools";
import { fluxModelId, fluxDevModelId, lineArtModelId, realisticModelId, animeModelId, ponyV6ModelId, MjModelId,NamiyaModelId, picLumenArtV1Id } from "@/utils/constant";
import { useSupportModelList } from "@/stores/create";
const supportModelList = useSupportModelList();
const emits = defineEmits("chooseModel");
const modelTooltipRef = ref(null);
const modelIconMapping = {
    [realisticModelId]: {
        cover: renderStaticImage("modIcon/m-realistic-v2.webp"),
        descLangKey: "MODEL_TIPS_REAL",
    },
    [NamiyaModelId]: {
        cover: renderStaticImage("modIcon/legend_namiya_cover.webp"),
        descLangKey: "MODEL_TIPS_ANIME",
    },
    [animeModelId]: {
        cover: renderStaticImage("modIcon/m-anime-v2.webp"),
        descLangKey: "MODEL_TIPS_ANIME",
    },
    [lineArtModelId]: {
        cover: renderStaticImage("modIcon/m-lineart-v1.webp"),
        descLangKey: "MODEL_TIPS_LINEART",
    },
    [picLumenArtV1Id]: {
        cover: renderStaticImage("modIcon/m-piclumen-art-v1.webp"),
        descLangKey: "MODEL_TIPS_ART_V1",
    },
    [fluxModelId]: {
        cover: renderStaticImage("modIcon/m-flux.1-schnell.webp"),
        descLangKey: "MODEL_TIPS_FLUX",
    },
    [ponyV6ModelId]: {
        cover: renderStaticImage("modIcon/m-pony-v6.webp"),
        descLangKey: "MODEL_TIPS_PONY_V6",
    },
    [fluxDevModelId]: {
        cover: renderStaticImage("modIcon/m-flux.1-dev.webp"),
        descLangKey: "MODEL_TIPS_DEV_FLUX",
    },
};
const props = defineProps({
    modelId: {
        type: String,
        default: "",
    },
    models: {
        type: Array,
        default: () => [],
    },
});
const modelList = computed(() => {
    const list = supportModelList.modelList
        .map((item) => {
            return {
                label: item.label,
                id: item.value,
                cover: modelIconMapping[item.value].cover,
            };
        })
        .filter((item) => props.models.includes(item.id));

    return list;
});
const checkModel = (id) => {
    modelTooltipRef.value?.setShow(false);
    emits("chooseModel", id);
};
const currentModelCover = computed(() => modelIconMapping[props.modelId]?.cover);
</script>
