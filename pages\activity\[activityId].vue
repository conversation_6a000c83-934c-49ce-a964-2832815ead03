<template>
    <MobileHeader :title="t('ACTIVITY.TITLE')" />

    <ScrollView class="overflow-hidden h-screen relative overflow-y-scroll w-full" @scroll="handleScroll" @scrolltolower="scrollLoadMore" ref="virtualRef">
        <div v-if="!isMobile" class="h-[72px] bg-bg-2 px-6 text-text-2 sticky top-0 flex items-center gap-6 z-50 border-border-1 border-b">
            <NuxtLinkLocale to="/activity" replace @click="handleBack">
                <n-icon size="24" class="cursor-pointer">
                    <IconsBack />
                </n-icon>
            </NuxtLinkLocale>

            <div class="">{{ $t("ACTIVITY.TITLE") }}</div>
        </div>
        <div class="p-6">
            <div class="bg-bg-2 p-6 mb-6 rounded-lg">
                <div class="flex justify-between items-center gap-x-2 mb-6 overflow-hidden w-full">
                    <div class="min-w-0">
                        <h1 class="text-2xl font-semibold mb-3 text-text-2">{{ activityData.title }}</h1>
                        <div class="flex gap-x-2 items-center">
                            <ActivityReward :tags="tags" />
                            <ActivityLabel :icon="IconFire" :text="numberFormat(activityData.imageNum)" />
                            <ActivityLabel v-if="!isPostEnd" :icon="IconTime" :text="submissionTime" />
                        </div>
                    </div>
                    <div v-if="isStarted && !isPostEnd" class="flex gap-x-4 shrink-0">
                        <NuxtLinkLocale to="/image/create">
                            <Button type="secondary" size="medium">{{ $t("ACTIVITY.CREATE") }}</Button>
                        </NuxtLinkLocale>
                        <Button @click="handleSubmissionModalVisible(true)" size="medium">{{ $t("ACTIVITY.SUBMISSION") }}</Button>
                    </div>
                </div>
                <div class="border-t border-border-1 pt-6">
                    <p class="text-text-2 mb-2 text-base font-semibold">{{ $t("ACTIVITY.ACTIVITY_INTRO") }}</p>
                    <div>
                        <OverflowFolding v-if="activityData.detail">
                            <pre class="w-full whitespace-pre-wrap text-text-3 text-sm flex flex-col gap-y-2 break-words">{{ `${activityData.detail}` }}</pre>
                        </OverflowFolding>
                        <div class="mt-2 flex">
                            <div class="flex py-2 items-center text-sm text-primary-6 gap-x-[2px] cursor-pointer" @click="ruleDetailVisible = true">
                                <span>{{ $t("ACTIVITY.RULE_DETAILS") }}</span>
                                <n-icon size="12">
                                    <IconsArrowRight />
                                </n-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-bg-2 p-6 pb-4 rounded-lg">
                <p class="text-2xl font-semibold mb-4 text-text-2">{{ $t("ACTIVITY.CREATORS_SHOWCASE") }}</p>
                <div class="flex justify-between items-center">
                    <div class="flex flex-1 min-w-0 overflow-hidden gap-x-2">
                        <div class="flex overflow-x-auto gap-x-2">
                            <div
                                @click="handleType(TYPE_LIST['all'])"
                                class="flex items-center relative justify-center rounded-full text-sm h-9 px-3.5 bg-fill-btn-1 text-text-4 cursor-pointer"
                                :class="{ 'text-text-opt-3': currentType.value === TYPE_LIST['all'].value }"
                            >
                                <span>{{ $t("ACTIVITY.ALL_WORKS") }}</span>
                            </div>
                            <div
                                v-if="activityData.reward"
                                @click="handleType(TYPE_LIST['winning'])"
                                class="flex items-center relative justify-center rounded-full text-sm h-9 px-3.5 bg-fill-btn-1 text-text-4 cursor-pointer"
                                :class="{ 'text-text-opt-3': currentType.value === TYPE_LIST['winning'].value }"
                            >
                                <span>{{ $t("ACTIVITY.WINNING_WORK") }}</span>
                            </div>
                        </div>
                    </div>
                    <div v-show="currentType.showSort" class="sort-filter flex shrink-0 gap-x-5">
                        <div v-for="item in SORT_LIST" :key="item.value" class="sort-item" :class="{ active: imagesQueryData.collationName === item.value }" @click="handleSort(item.value)">
                            {{ $t(item.label) }}
                        </div>
                    </div>
                </div>
                <div class="pt-6">
                    <VirtualWaterfall
                        v-show="showImagesList.length > 0"
                        :key="currentType.value"
                        style="box-sizing: content-box; min-height: 30vh"
                        padding="0 0 0 0"
                        rowKey="id"
                        :gap="8"
                        :virtual="true"
                        :items="showImagesList"
                        :calcItemHeight="calcItemHeight"
                        :itemMinWidth="128"
                        :preload-screen-count="[1, 2]"
                        :columnCount="waterfallColCount"
                        :maxColumnCount="waterfallColCount"
                    >
                        <template #default="{ item, index }">
                            <CommunityImageItem :item="item" class="activity-image-item rounded-2xl" @click="setShareList" @updateCommunityItem="updateCommunityItem">
                                <ActivityRank v-if="currentType.showRank && !item.reported" class="activity-image-rank" :activity="item" :prizeList="prizeList" />
                            </CommunityImageItem>
                        </template>
                    </VirtualWaterfall>
                    <Empty v-if="!showImagesList.length && !currentType.fetch.loading && !pageLoading" text="ACTIVITY.IMAGE_EMPTY"></Empty>
                </div>
                <div v-if="currentType.fetch.loading && !pageLoading" class="sticky left-0 bottom-0 right-0 py-10 flex items-center justify-center">
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </div>
            </div>
        </div>
    </ScrollView>
    <SubmissionModal v-model:visible="submissionModalVisible" :activity="activityData" @success="handleSubmissionSuccess" />
    <RuleDetailModal v-model:visible="ruleDetailVisible" :ruleDetail="activityData.ruleDetail" />
    <BackToTop v-if="virtualRefScrollTop > 200" @click="gotoTop" />
</template>

<script setup>
import { useThemeStore } from "@/stores/system-config";

const { t } = useI18n({ useScope: "global" });
const updateSeo = () => {
    useSeoMeta({
        title: () => t("SEO_META.SEO_ACTIVITY_DETAIL_TITLE"),
        ogTitle: () => t("SEO_META.SEO_ACTIVITY_DETAIL_TITLE"),
        description: () => t("SEO_META.SEO_ACTIVITY_DETAIL_DESC"),
        ogDescription: () => t("SEO_META.SEO_ACTIVITY_DETAIL_DESC"),
    });
};
import { convertBeijingToLocal, numberFormat, timeDifference, dateIsAfter, formatPonyV6Prompt, debounce } from "@/utils/tools";
import { KEEPALIVE_PAGES } from "@/utils/constant";
import useWindowResize from "@/hook/windowResize";
import useListFetch from "@/hook/useListFetch";
import { useUserProfile, useShareDataStore } from "@/stores";

import { getActivityDetailApi, getActivityImagesApi, getActivityWinImagesApi, getActivityPrizeListApi } from "@/api/activity";
import { storeToRefs } from "pinia";
import { VirtualWaterfall } from "@lhlyu/vue-virtual-waterfall";
import { useSyncAction } from "@/stores/syncAction";

import ScrollView from "@/components/ScrollView.vue";
import SubmissionModal from "@/components/activity/SubmissionModal.vue";
import RuleDetailModal from "@/components/activity/RuleDetailModal.vue";
import ActivityReward from "@/components/activity/ActivityReward.vue";
import OverflowFolding from "@/components/OverflowFolding.vue";
import ActivityLabel from "@/components/activity/ActivityLabel.vue";
import ActivityRank from "@/components/activity/ActivityRank.vue";
import IconFire from "@/components/icons/Fire.vue";
import IconTime from "@/components/icons/Time.vue";
const activityData = ref({});
const { isMobile } = storeToRefs(useThemeStore());

defineOptions({
    name: KEEPALIVE_PAGES.ACTIVITY_DETAIL,
});

const syncAction = useSyncAction();
const route = useRoute();
const { user } = useUserProfile();
let currentId = ref("");

/**
 * 排序集合
 */
const SORT_LIST = [
    { label: "ACTIVITY.SORT_BY_POPULARITY", value: "Hot" },
    { label: "ACTIVITY.SORT_BY_TIME", value: "Time" },
];

const imagesQueryData = ref({
    activityId: route.params.activityId,
    collationName: SORT_LIST[0].value,
});
const winningQueryData = ref({
    activityId: route.params.activityId,
    // collationName: SORT_LIST[0].value
});

const fetch = useListFetch({
    api: getActivityImagesApi,
    queryData: imagesQueryData,
    cursorKey: "lastFileId",
    beforeSendHook: (reqQuery) => {
        console.log(reqQuery, "<<<<<<<<<<<<<<<<<");
        // const query = {...reqQuery}
        // 接口要求
        if (reqQuery.collationName === "Hot" && reqQuery.lastFileId) {
            reqQuery.lastFileId = `${reqQuery.lastLikeCount}#${reqQuery.lastFileId}`;
        }
        return reqQuery;
    },
    decrypt: true,
});
const winFetch = useListFetch({
    api: getActivityWinImagesApi,
    queryData: winningQueryData,
    decrypt: true,
});

const TYPE_LIST = {
    all: { label: "All Works", value: "all", showRank: true, showSort: true, fetch },
    winning: { label: "Winning Work", value: "winning", showRank: true, showSort: false, fetch: winFetch },
};
const pageLoading = ref(false);
const submissionModalVisible = ref(false);
const ruleDetailVisible = ref(false);
const currentType = ref(TYPE_LIST["all"]);
const pageInit = debounce(() => {
    currentType.value = TYPE_LIST["all"];
    getActivityDetail();
    getActivityPrizeList();
}, 300);
const handleType = (item) => {
    if (currentType.value.value === item.value) {
        return;
    }
    currentType.value = item;
    item.fetch.reload();
};
const virtualRef = ref();
const virtualRefScrollTop = ref(0);
const gotoTop = () => {
    virtualRefScrollTop.value = 0;
    restoreScrollTop();
};
const restoreScrollTop = () => {
    virtualRef.value?.scrollTo({ top: virtualRefScrollTop.value });
};
const handleScroll = (e) => {
    virtualRefScrollTop.value = e.scrollTop;
};
const scrollLoadMore = () => {
    currentType.value.fetch.loadMore();
};
const handleSort = (sort) => {
    if (currentType.value.fetch.loading || imagesQueryData.value.collationName === sort) {
        return;
    }

    imagesQueryData.value.collationName = sort;
    currentType.value.fetch.reload();
};

/**
 * 获取活动详情
 */
async function getActivityDetail() {
    pageLoading.value = true;
    const res = await getActivityDetailApi({ activityId: currentId.value });
    if (res.status === 0) {
        activityData.value = {
            ...res.data,
            // maxSubmissions:
        };

        currentType.value.fetch.reload();
    }
    pageLoading.value = false;
}
const prizeList = ref([]);
async function getActivityPrizeList() {
    const res = await getActivityPrizeListApi();
    console.log(res, "<<<<<<<<<<");
    if (res?.status === 0) {
        prizeList.value = res.data;
    }
}
const tags = computed(() => {
    try {
        return JSON.parse(activityData.value.tags) || [];
    } catch {
        return [];
    }
    // JSON.parse(props.activity.tags)
});
/**
 * 获取当前时区的实际开始时间
 */
const beginTime = computed(() => {
    if (activityData.value?.beginTime) {
        return convertBeijingToLocal(activityData.value?.beginTime);
    }
    return "";
});
/**
 * 活动是否已开始
 */
const isStarted = computed(() => {
    if (beginTime.value) {
        return dateIsAfter(new Date(), beginTime.value);
    }
    return false;
});
/**
 * 获取当前时区的实际投稿截止日期
 */
const postEndTime = computed(() => {
    if (activityData.value?.postEndTime) {
        return convertBeijingToLocal(activityData.value?.postEndTime);
    }
    return "";
});
/**
 * 是否截止投稿
 */
const isPostEnd = computed(() => {
    if (postEndTime.value) {
        return dateIsAfter(new Date(), postEndTime.value);
    }
    return true;
});
/**
 * 投稿倒计时
 */
const submissionTime = computed(() => {
    if (isPostEnd.value) {
        return "";
    }
    return `${t("ACTIVITY.EXPIRES_IN")}: ${timeDifference(new Date(), postEndTime.value)}`;
});

/**
 * 瀑布流展示的图片
 */
const showImagesList = computed(() => {
    return currentType.value.fetch.list.map((item) => {
        const genInfo = JSON.parse(item.genInfo) || {};
        const { resolution = {} } = genInfo;
        const { prompt } = formatPonyV6Prompt(genInfo, "output");
        return {
            ...genInfo,
            ...item,
            prompt,
            width: resolution.width,
            height: resolution.height,
            dataUserId: item.accountInfo.userId,
            showPrompt: user.userId == item.accountInfo.userId || item.publicType !== "myself",
        };
    });
});
const shareData = useShareDataStore();
const setShareList = () => {
    shareData.setList(showImagesList.value);
    console.log("-------------------");
};
//高度计算
const calcItemHeight = (item, w) => {
    let { width, realWidth, height, realHeight } = item;
    realWidth = realWidth || width;
    realHeight = realHeight || height;
    return w / (realWidth / realHeight);
};
const windowResize = useWindowResize();
const waterfallColCount = computed(() => {
    let base = 5;
    if (windowResize.width.value <= 1660) {
        base = 4;
    }
    if (windowResize.width.value <= 1440) {
        base = 3;
    }
    if (windowResize.width.value <= 768) {
        base = 2;
    }
    return base;
});

const handleSubmissionModalVisible = (val = false) => {
    submissionModalVisible.value = val;
};
const handleSubmissionSuccess = () => {
    handleSubmissionModalVisible();
    currentType.value.fetch.reload();
};
const handleBack = () => {
    currentType.value = TYPE_LIST["all"];
};
/** 社区图片属性值发生变更 */
const updateCommunityItem = (communityItem) => {
    // console.log('communityItem', communityItem)
    const findIndex = showImagesList.value.findIndex((item) => item.id === communityItem.id);
    if (findIndex > -1) {
        const current = showImagesList.value[findIndex];
        currentType.value.fetch.list[findIndex] = { ...current, ...communityItem };
        // showImagesList.value[findIndex] = { ...current, ...communityItem }
        syncAction.publish("updateCommunityItem", communityItem);
    }
};

const unWatch = ref();
const localePath = useLocalePath();
onActivated(() => {
    nextTick(updateSeo);
    // 当页面激活时，监听 URL 参数变化
    unWatch.value = watch(
        () => route.params.activityId,
        (newId, oldId) => {
            if (route.path === localePath(`/activity/${newId}`)) {
                if (newId !== currentId.value) {
                    currentId.value = newId;
                    imagesQueryData.value = {
                        activityId: newId,
                        collationName: SORT_LIST[0].value,
                    };
                    pageInit(); // 页面重新加载数据
                } else {
                    restoreScrollTop?.(); // 可选：恢复滚动位置
                }
            }
        },
        { immediate: true }
    );
});

onDeactivated(() => {
    unWatch.value?.(); // 取消监听，避免重复绑定
});
</script>

<style lang="scss" scoped>
.sort-filter {
    .sort-item {
        @apply relative text-text-4 text-sm cursor-pointer;
        &.active {
            @apply text-text-2;
        }
        &:before {
            content: "";
            @apply block absolute top-[50%] -left-2.5 w-[2px] h-[70%] bg-text-4 translate-y-[-50%];
        }
        &:first-child {
            &:before {
                @apply hidden;
            }
        }
    }
}
.activity-image-item {
    &:hover {
        .activity-image-rank {
            display: none;
        }
    }
}
</style>
