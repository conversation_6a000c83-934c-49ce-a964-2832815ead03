import { defineStore } from "pinia";
import { MENU_STATE, DEFAULT_LANG_LIST as langList } from "@/utils/constant";
import { debounce } from "@/utils/tools.js";
//菜单折叠/显示隐藏状态
export const useSystemConfig = defineStore("systemConfig", () => {
    const systemConfig = ref({
        menuState: MENU_STATE.EXPAND,
    });
    const setSystemConfig = (value) => {
        systemConfig.value = { ...systemConfig.value, ...value };
    };

    return { systemConfig, setSystemConfig };
});

//主题
export const useCurrentTheme = defineStore(
    "currentTheme",
    () => {
        const isShowMoDrawer = ref(false); //是否展示移动端侧边栏
        const setShowMoDrawer = (isShow) => {
            isShowMoDrawer.value = isShow;
        };
        const getShowMoDrawer = () => {
            return isShowMoDrawer.value;
        };

        // 主体切换
        const theme = ref("system");

        const setTheme = (themeColor) => {
            console.log("setTheme", themeColor);
            theme.value = themeColor;
        };
        // 底部Tabbar组件高度
        const tabbarHeight = ref(0);

        /**
         * 获取tabbar组件高度
         * @returns
         */
        const getTabBarHeight = () => {
            try {
                if (!import.meta.client) {
                    return 0;
                }
                if (tabbarHeight.value > 0) return tabbarHeight.value;
                const tabbarView = document.getElementById("tabbar");
                if (!tabbarView) return 0;
                return (tabbarHeight.value = tabbarView.clientHeight);
            } catch (error) {
                console.error("getTabBarHeight error", error);
                return 0;
            }
        };

        return { theme, setTheme, getTabBarHeight, isShowMoDrawer, setShowMoDrawer, getShowMoDrawer };
    },
    {
        persist: import.meta.client,
    }
);

export const useThemeStore = defineStore("themeStore", () => {
    //====================== 响应式状态：是否为移动设备 =====================
    const isMobile = ref(false);
    const isIpad = ref(false);
    const isPc = ref(true);

    const calcMobile = debounce(() => {
        isMobile.value = window.innerWidth < 768;
        isIpad.value = window.innerWidth <= 1024;
        isPc.value = window.innerWidth > 1024;
        // console.log("checkMobile   ", isMobile.value);
    });
    const checkMobile = () => {
        calcMobile();
    };

    const startMonitorScreen = () => {
        checkMobile();
        window.addEventListener("resize", checkMobile);
    };

    const stopMonitorScreen = () => {
        window.removeEventListener("resize", checkMobile);
    };

    //======================= 各个页面的组件高度，避免重复计算，在此处统一处理和监听

    //====== banner区域
    const bannerAreaHeight = ref(0);

    const setBannerAreaHeight = (height) => {
        bannerAreaHeight.value = height;
    };

    return {
        // 移动端识别
        isMobile,
        isIpad,
        isPc,
        startMonitorScreen,
        stopMonitorScreen,
        // banner区域
        bannerAreaHeight,
        setBannerAreaHeight,
    };
});

// 多语言 lang
export const useCurrentLang = defineStore("currentLang", () => {
    const lang = ref(null);
    onMounted(() => {
        const { locale } = useI18n({
            inheritLocale: true,
            useScope: "global",
        });
        if (locale.value) {
            setLang(locale.value);
        }
    });

    const localeLangLabel = computed(() => {
        const temp = langList.find((item) => item.key === lang.value);
        return temp.label || "English";
    });

    const setLang = (val) => {
        lang.value = val;
    };
    return { lang, setLang, localeLangLabel };
});
