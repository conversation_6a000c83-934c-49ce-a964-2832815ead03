<template>
    <div class="h-screen relative dark:bg-black bg-neutral-200 dark:text-dark-text font-medium overflow-y-auto">
        <div class="h-[72px] bg-bg-2 p-6 sticky top-0 flex items-center gap-6 z-50 border-b border-solid border-border-1">
            <n-icon size="24" class="cursor-pointer" @click="handleBack">
                <IconsBack />
            </n-icon>
            <div>
                <div>{{ t("FEATURE_DESCRIBE_FEATURE_TITLE") }}</div>
                <div class="mt-0.5 text-xs dark:text-dark-tag-bg">{{ t("DESCRIBE_DES") }}</div>
            </div>
        </div>

        <div class="p-6 flex gap-6">
            <div class="p-4 rounded-2xl overflow-hidden sticky top-24 h-max shrink-0 bg-bg-3">
                <n-upload
                    class="rounded-lg w-[368px] aspect-square border-none"
                    style="--n-border-radius: 8px"
                    trigger-class="h-full flex flex-col items-center justify-center border-none "
                    accept=".jpeg,.jpg,.png,.webp"
                    :show-file-list="false"
                    :disabled="uploading"
                    :on-before-upload="chooseFile"
                >
                    <n-upload-dragger style="--n-dragger-color: transparent" class="h-full flex flex-col items-center justify-center bg-neutral-100 dark:bg-transparent">
                        <div v-if="!describeConf.tempLink">
                            <n-icon size="24"><IconsUpload /></n-icon>
                            <div class="text-center mt-3">
                                <div>{{ t("FEATURE_UPLOAD_TITLE") }}</div>
                                <div class="mt-1 dark:text-dark-tag-bg text-xs">PNG, JPG, WEBP up to 20MB</div>
                            </div>
                        </div>
                        <div v-else class="w-full h-full rounded-lg overflow-hidden">
                            <img :src="describeConf.tempLink" class="w-full h-full object-contain" />
                            <div
                                class="w-8 h-8 rounded-md bg-dark-bg-2/70 hover:bg-dark-bg-2 absolute top-6 right-6 cursor-pointer flex items-center justify-center text-dark-text"
                                @click.stop="handleRemoveTempLink"
                            >
                                <n-icon size="24">
                                    <IconsClose />
                                </n-icon>
                            </div>
                        </div>
                    </n-upload-dragger>
                </n-upload>

                <div class="mt-6">
                    <n-select class="dark:bg-dark-bg-2 bg-white rounded-lg describe-select" v-model:value="describeConf.type" :options="describeType" />
                </div>
                <div class="mt-3">
                    <n-button
                        type="primary"
                        block
                        :bordered="false"
                        :disabled="!describeConf.tempLink"
                        :loading="uploading"
                        class="!bg-primary h-11 rounded-lg !text-dark-active-text"
                        @click="handleUpload"
                    >
                        <template v-if="!uploading">
                            <span class="!text-dark-active-text">{{ t("FEATURE_DESCRIBE_TITLE") }}</span>
                            <ExpendConst :lumen="1" class="ml-3" />
                        </template>
                    </n-button>
                </div>
            </div>

            <div class="flex-1">
                <div>{{ t("COLLECTION_DES") }}</div>
                <div class="mt-2 bg-bg-3 rounded-2xl px-4 py-3 leading-6 min-h-[150px]">
                    <Typewriter :fullText="describeConf.describe" v-model:complete="describeConf.complete" v-if="describeConf.describe" />
                </div>

                <div class="flex gap-4 mt-4">
                    <n-button
                        type="primary"
                        :bordered="false"
                        :disabled="!describeConf.complete"
                        class="w-60 dark:!bg-dark-bg-2 !bg-white h-11 rounded-lg dark:!text-dark-text !text-dark-bg"
                        @click="handleCopy"
                    >
                        {{ t("DESCRIBE_BTN_COPY") }}
                    </n-button>
                    <n-button
                        type="primary"
                        :bordered="false"
                        :disabled="!describeConf.complete"
                        class="w-60 dark:!bg-dark-bg-2 !bg-white h-11 rounded-lg dark:!text-dark-text !text-dark-bg"
                        @click="handleSendPromptToCreate"
                    >
                        {{ t("DESCRIBE_BTN_SEND") }}
                    </n-button>
                </div>
                <div class="mt-8 gap-2 flex items-center">
                    <n-switch style="--n-rail-color-active: #7b63fe" size="large" v-model:value="showTranslate" :on-update:value="changeShowTranslate" />
                    <div class="flex items-center gap-2">
                        <span>{{ t("DESCRIBE_TRANSLATE") }}</span>
                    </div>
                </div>
                <div v-if="showTranslate">
                    <div class="flex gap-4 mt-4">
                        <n-button
                            type="primary"
                            :bordered="false"
                            :disabled="!describeConf.complete"
                            :loading="translating"
                            class="w-60 dark:!bg-dark-bg-2 !bg-white h-11 rounded-lg dark:!text-dark-text !text-dark-bg"
                            @click="handleTranslate"
                        >
                            <span v-if="!translating" class="dark:!text-dark-text !text-dark-bg">{{ t("DESCRIBE_TRANSLATE_TO") }}</span>
                        </n-button>
                        <n-select class="w-60 dark:bg-dark-bg-2 bg-white rounded-lg describe-select" v-model:value="describeConf.targetLang" :options="targetLanguages" />
                    </div>
                    <div class="mt-4 bg-bg-3 rounded-2xl px-4 py-3 leading-6 min-h-[150px]">
                        <span v-if="!describeConf.translateRes" class="text-dark-tag-bg">{{ t("DESCRIBE_TRANSLATE_PLACEHOLDER") }}</span>
                        <span v-else>{{ describeConf.translateRes }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
useSeoMeta({
    title: () => t("SEO_META.SEO_DESCRIBE_TITLE"),
    ogTitle: () => t("SEO_META.SEO_DESCRIBE_TITLE"),
    description: () => t("SEO_META.SEO_DESCRIBE_DESC"),
    ogDescription: () => t("SEO_META.SEO_DESCRIBE_DESC"),
});
import { image2TxtDescribe, translateText } from "@/api";
import { useGlobalError } from "@/hook/error";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
import { SUB_EL, SUBSCRIBE_PERMISSION } from "@/utils/constant.js";
import { uploadToCos, copyToClipboard } from "@/utils/tools";
import ExpendConst from "@/components/subscribe/ExpendConst.vue";
const { showError } = useGlobalError();
import { useShareDescribe, useCreateStore } from "@/stores/create";
import { useGoToCreate } from "@/hook/create";
import { ERROR_CODE_ENUM } from "@/utils/constant";
const { checkPermission } = useSubPermission();
const createStore = useCreateStore();

const router = useRouter();
const describeType = computed(() => {
    return [
        { label: t("DESCRIBE_OPTS_DESC"), value: "des" },
        { label: t("DESCRIBE_OPTS_TAG"), value: "tag" },
    ];
});
const localePath = useLocalePath();

const { checkShowVipNotice } = useVipNotice();

const targetLanguages = [
    { alias: "德语", label: `Deutsch`, value: "de" }, // 德国是欧洲最大经济体，德语在中欧广泛使用
    { alias: "日语", label: `日本語`, value: "ja" }, // 日本是全球第三大经济体，科技和文化影响力强
    { alias: "简体中文", label: `简体中文`, value: "zh-CN" }, // 中国是全球第二大经济体，使用人数最多
    { alias: "法语", label: `Français`, value: "fr" }, // 法语在非洲、欧洲和加拿大广泛使用，经济和文化影响力强
    { alias: "韩语", label: `한국어`, value: "ko" }, // 韩国是亚洲主要经济体，科技和文化影响力强
    { alias: "西班牙语", label: `Español`, value: "es" }, // 西班牙语在拉丁美洲和西班牙广泛使用，使用基数大
    { alias: "繁体中文", label: `繁體中文`, value: "zh-TW" }, // 台湾地区经济发达，但使用基数相对较小
    { alias: "意大利语", label: `Italiano`, value: "it" }, // 意大利是欧洲主要经济体，文化影响力强
    { alias: "荷兰语", label: `Nederlands`, value: "nl" }, // 荷兰是欧洲发达经济体，荷兰语在荷兰和比利时使用
    { alias: "俄语", label: `Русский`, value: "ru" }, // 俄语在俄罗斯及独联体国家广泛使用，俄罗斯是资源大国
    { alias: "葡萄牙语", label: `Português`, value: "pt" }, // 葡萄牙语在巴西和葡萄牙广泛使用，巴西是南美最大经济体
    { alias: "阿拉伯语", label: `العربية`, value: "ar" }, // 阿拉伯语在中东和北非广泛使用，石油经济影响力大
    { alias: "丹麦语", label: `Dansk`, value: "da" }, // 丹麦是北欧发达经济体，生活质量高
    { alias: "瑞典语", label: `Svenska`, value: "sv" }, // 瑞典是北欧发达经济体，科技和文化影响力强
    { alias: "挪威语", label: `Norsk`, value: "no" }, // 挪威是北欧发达经济体，人均GDP高
    { alias: "印度尼西亚语", label: `Bahasa Indonesia`, value: "id" }, // 印度尼西亚是东南亚最大经济体，人口基数大
    { alias: "泰语", label: `ไทย`, value: "th" }, // 泰国是东南亚主要经济体，旅游业发达
    { alias: "越南语", label: `Tiếng Việt`, value: "vi" }, // 越南是东南亚新兴经济体，经济增长快
];
const showTranslate = ref(false);
const uploading = ref(false);
const describeConf = ref({
    type: "des",
    describe: "",
    targetLang: "de",
});
//选择文件
const chooseFile = async ({ file }) => {
    return new Promise((resolve, reject) => {
        const maxSizeStr = "20MB";
        const maxShapeStr = "5000 X 5000";
        const maxPix = Math.pow(5000, 2);
        const maxSize = 20 * 1024 * 1024;
        //安全值
        const safePix = 1000;
        const limitErr = { size: maxSizeStr, resolution: maxShapeStr, type: "PNG, JPG, JPEG, WEBP" };
        const res = file.file;
        if (res.size > maxSize) {
            openToast.error(t("FEATURE_UPLOAD_SIZE_ERROR", limitErr), 5e3);
            return reject(t("FEATURE_UPLOAD_SIZE_ERROR", limitErr));
        }
        const img = new Image();
        img.onload = async () => {
            let width = img.width;
            let height = img.height;
            if (width * height >= maxPix) {
                openToast.error(t("FEATURE_UPLOAD_SIZE_ERROR", limitErr), 5e3);
                return reject(t("FEATURE_UPLOAD_SIZE_ERROR", limitErr));
            }
            // const offscreenCanvas = new OffscreenCanvas(width, height);
            const canvas = document.createElement("canvas");
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext("2d");
            // const ctx = offscreenCanvas.getContext("2d");
            if (width > height) {
                if (width > safePix) {
                    height *= safePix / width;
                    width = safePix;
                }
            } else {
                if (height > safePix) {
                    width *= safePix / height;
                    height = safePix;
                }
            }
            handleRemoveTempLink();

            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);
            canvas.toBlob(
                (blob) => {
                    describeConf.value.tempLink = URL.createObjectURL(blob);
                    describeConf.value.file = blob;
                    resolve(file);
                },
                "image/webp",
                0.5
            );
        };
        img.onerror = function () {
            openToast.error(t("FEATURE_UPLOAD_TYPE_ERROR", limitErr), 5e3);
            return reject(t("FEATURE_UPLOAD_TYPE_ERROR", limitErr));
        };
        img.src = URL.createObjectURL(res);
    });
};
//删除选中的文件
const handleRemoveTempLink = () => {
    URL.revokeObjectURL(describeConf.value.tempLink);
    describeConf.value.tempLink = "";
    describeConf.value.file = null;
    describeConf.value.originLink = null;
};
const changeShowTranslate = async (v) => {
    const res = await checkPermission(SUBSCRIBE_PERMISSION.TRANSLATION, { triggerEl: SUB_EL.DESCRIBE_DOWNLOAD });
    if (!res) return;

    // checkShowVipNotice(`describe_translate`);
    showTranslate.value = v;
};
// 清空 进行中的
const handleClearUnderway = () => {
    describeConf.value.describe = null;
    describeConf.value.translateRes = null;
    describeConf.value.complete = false;
    if (controller.value) {
        controller.value.abort();
    }
};
//上传
const handleUpload = async () => {
    let { file, originLink, type } = describeConf.value;
    gaTrackEvent("description_btn=description");
    if (type === "des") {
        gaTrackEvent("description_style=natural_language");
    } else {
        gaTrackEvent("description_style=tag");
    }
    uploading.value = true;
    if (!originLink && file) {
        const { fullPath } = await uploadToCos({ file: describeConf.value.file, originalFileName: Date.now() + "_.webp", type: "temp" });
        describeConf.value.originLink = fullPath;
        originLink = fullPath;
    }
    if (!originLink) {
        return;
    }
    handleClearUnderway();
    const { status, data, message } = await image2TxtDescribe({ imageUrl: originLink, type });
    uploading.value = false;
    if (status === 0) {
        describeConf.value.describe = data;
        return;
    }
    if (status === ERROR_CODE_ENUM.LUMENS_LACK_ERROR) {
        showError(status, { triggerEl: SUB_EL.DESCRIBE_TASK });
        return;
    }
    openToast.error(message, 5e3);
};
// 复制提示词
const handleCopy = () => {
    gaTrackEvent("description_btn=copy");
    copyToClipboard(describeConf.value.describe);
    openToast.success(t("TOAST_COPY_SUCCESS"));
};

const getFirstShortPhrase = (text, maxLength = 2500) => {
    // 处理连续空格并过滤空单词
    const words = text.trim().split(/\s+/).filter(Boolean);
    if (words.length === 0) return "";
    let currentLine = "";
    for (const word of words) {
        // 计算添加后的潜在长度
        const space = currentLine ? 1 : 0; // 空格占位
        const potentialLength = currentLine.length + space + word.length;
        // 处理超长单词（首个单词就超长的情况）
        if (currentLine === "" && word.length > maxLength) {
            return word.slice(0, maxLength);
        }
        // 长度超过限制时立即停止
        if (potentialLength > maxLength) {
            return currentLine || word.slice(0, maxLength);
        }
        // 拼接单词
        currentLine = currentLine ? `${currentLine} ${word}` : word;
    }
    return currentLine;
};
// 发送提示词到create 页面
const { toCreate } = useGoToCreate();
const handleSendPromptToCreate = async () => {
    gaTrackEvent("description_btn=send_to_create");
    const str = describeConf.value.describe;
    if (str.length <= 2500) {
        createStore.setPrompt(str);
    } else {
        createStore.setPrompt(getFirstShortPhrase(str));
    }
    await toCreate();
};
const translating = ref(false);
const controller = ref(null);
const requestTranslate = (signal) => {
    return new Promise(async (resolve, reject) => {
        const params = {
            content: describeConf.value.describe,
            languageType: describeConf.value.targetLang,
        };
        translateText(params)
            .then((res) => {
                const { status, data, message } = res;
                if (status === ERROR_CODE_ENUM.EXCEED_AUTH_FEATURE_ERROR) {
                    showError(status);
                    return resolve("");
                }
                if (status !== 0) {
                    openToast.error(message, 5e3);
                    return resolve("");
                }
                resolve(data);
            })
            .catch((_) => {
                resolve("");
            })
            .finally((_) => {
                translating.value = false;
            });

        // // 监听 signal 的 abort 事件
        signal.addEventListener("abort", () => {
            translating.value = false;
            resolve("");
        });
    });
};
const handleTranslate = async () => {
    gaTrackEvent(`description_translate=${describeConf.value.targetLang}`);
    if (translating.value) return;
    translating.value = true;
    controller.value = new AbortController();
    const res = await requestTranslate(controller.value.signal);
    describeConf.value.translateRes = res;
    translating.value = false;
};
const handleBack = async () => {
    if (!history.state.back) {
        await navigateTo({ path: localePath("/tools") });
    } else {
        router.back();
    }
};
const gaTrackEvent = (el) => {
    window.trackEvent("Tools", { el });
};

const shareDescribe = useShareDescribe();
//取用store中的图片链接 使用后置空
onMounted(() => {
    const { fileLink } = shareDescribe;
    if (fileLink) {
        describeConf.value.tempLink = fileLink;
        describeConf.value.originLink = fileLink;
        shareDescribe.setLink("");
    }
});
</script>

<style lang="scss" scoped>
::v-deep(.describe-select .n-base-selection) {
    --n-height: 44px !important;
}
::v-deep(.n-button.n-button--disabled) {
    cursor: default;
}
</style>
