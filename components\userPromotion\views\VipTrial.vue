<!--
 * @Author: HuangQS
 * @Description: VIP试用组件，在网页端左侧导航栏底部展示，非会员且非试用时展示
 * @Date: 2025-06-04 10:47:06
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-17 17:02:46
-->

<template>
    <template v-if="isMobile">
        <div v-if="isVisibleView">
            <n-icon size="20" class="absolute right-6 top-5" @click.stop="isVisibleView = false">
                <IconsCloseVipTrial />
            </n-icon>
            <div @click="onViewClick">
                <img class="size-[98px] aspect-square" src="@/assets/images/viptrial/image_vip_trial_mobile.webp" />
            </div>
        </div>
    </template>
    <template v-else>
        <div class="w-[204px] h-[170px] relative cursor-pointer mx-auto" @click="onViewClick">
            <!-- 背景 -->
            <img class="absolute bottom-0 size-full aspect-square dark:hidden" src="@/assets/images/viptrial/image_vip_trial_light.webp" />
            <img class="absolute bottom-0 size-full aspect-square hidden dark:block" src="@/assets/images/viptrial/image_vip_trial.webp" />

            <!-- 标题 -->
            <div class="absolute bottom-[96px] w-full h-[42px] px-3 py-2 text-xs font-semibold">
                <div class="text-white text-sm">{{ $t("VIP_TRIAL.VIEW.TITLE") }}</div>
            </div>

            <!-- 底部最外层 -->
            <div class="absolute bottom-0 w-full h-[96px] px-3 py-2 text-xs font-medium">
                <div class="flex h-full flex-col gap-2 justify-center">
                    <div v-for="(item, index) in fonts" :key="index" class="flex items-center text-white">
                        <div class="flex items-center text-white gap-2">
                            <div class="size-1.5 rounded-full bg-white shrink-0" />
                            <div>{{ item.content }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>
    <n-modal v-model:show="showModal" :z-index="100" :maskClosable="false" transform-origin="center" :style="isMobile && { margin: '0 27.5px' }">
        <VipTrialModal @cancel="handleCancel" />
    </n-modal>
</template>
<script setup>
import { computed } from "vue";
import { useThemeStore } from "@/stores/system-config";
import VipTrialModal from "@/components/VipTrialModal.vue";
import { storeToRefs } from "pinia";

import IconCloseVipTrial from "@/components/icons/CloseVipTrial.vue";

const { t } = useI18n({ useScope: "global" });

const customTrackEvent = (el) => {
    window.trackEvent("Commercialization", { el });
};

const { isMobile } = storeToRefs(useThemeStore());
const showModal = ref(false);
const isVisibleView = ref(true);

const fonts = computed(() => {
    return [
        { id: 0, content: t("VIP_TRIAL.VIEW.NOTICE_1") },
        { id: 1, content: t("VIP_TRIAL.VIEW.NOTICE_2") },
    ];
});

const onViewClick = () => {
    showModal.value = true;
    customTrackEvent("new_free_trial_bottom_left_corner");
};

const handleCancel = () => {
    showModal.value = false;
};
</script>

<style lang="less"></style>
