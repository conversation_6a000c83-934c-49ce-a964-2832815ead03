<template>
    <div v-if="data.length" class="p-6 bg-bg-2 flex flex-col justify-between rounded-2xl">
        <div class="relative">
            <div class="relative flex gap-x-4 z-10">
                <div v-for="(item, index) in data" :key="item.id" class="flex-1 flex flex-col items-center justify-between gap-y-3">
                    <p class="text-sm font-medium text-text-2 text-center">{{ $t(TASK_STATE_MAP[item.taskId]?.i18nKey) }}</p>
                    <div class="relative w-full flex items-center justify-center">
                        <div
                            ref="stepItems"
                            class="h-7 relative z-10 min-w-[42px] px-[7px] flex items-center justify-center border border-solid border-border-t-1 rounded-full bg-bg-3 text-primary-6 gap-x-[2px]"
                            :class="{ 'bg-primary-6 text-text-white': item.beFinish }"
                        >
                            <template v-if="!item.beReward">
                                <span class="text-base font-medium">{{ item.reward }}</span>
                                <n-icon size="14">
                                    <IconsLumenFill />
                                </n-icon>
                            </template>
                            <template v-else>
                                <n-icon size="18">
                                    <IconsSuccess />
                                </n-icon>
                            </template>
                        </div>

                        <div
                            class="absolute top-1/2 mt-[-4px] h-[8px] overflow-hidden z-0 bg-gray-200 dark:bg-fill-wd-1"
                            :class="{
                                'rounded-l-full': index === 0,
                                'rounded-r-full': index === data.length - 1,
                            }"
                            :style="getProgressStyle(index)"
                        >
                            <div class="progress-bar absolute left-0 h-full" :style="getStepBarStyle(item, index)"></div>
                        </div>
                        <div v-if="index === data.length - 1" class="absolute right-0 w-1/2 top-1/2 rounded-full mt-[-4px] h-[8px] overflow-hidden z-0 bg-gray-200 dark:bg-fill-wd-1">
                            <div class="progress-bar absolute left-0 h-full" :class="{ 'w-full': item.beFinish }"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <RewardButtons :tasks="data" class="mt-8 flex justify-end gap-x-4" />
    </div>
</template>
<script setup>
import { TASK_STATE_MAP } from "@/constants/taskCenter";
import RewardButtons from "@/components/task/RewardButton.vue";

const emits = defineEmits(["claim"]);
const props = defineProps({
    data: {
        type: Array,
        default: () => [],
    },
});

const stepItems = ref(null);
const getProgressStyle = (index) => {
    if (stepItems.value?.[index]) {
        const curRect = stepItems.value[index].getBoundingClientRect();
        let width = `calc(100% )`;
        let left = `calc(-1rem)`;
        if (!index) {
            left = `0`;
            width = `calc(50% - ${curRect.width / 3}px)`;
        } else {
            const curWidth2 = curRect.width / 2;
            width = `calc(100% - ${curWidth2}px)`;
            left = `calc(-50% + 2px)`;
        }
        return {
            width: width,
            left: left,
        };
    }
};
const getStepBarStyle = (item, index) => {
    if (item.beFinish) {
        return `width: 100%`;
    }
    const { finishCount, targetCount } = item;
    const pre = props.data[index - 1];
    let preTargetCount = pre?.targetCount || 0;
    if (!preTargetCount) {
        return `width: ${(finishCount / targetCount) * 100}%`;
    }
    // 当前任务，用户需要完成的数量
    const curTotal = targetCount - preTargetCount;
    const curCount = finishCount - preTargetCount;
    return `width: ${(curCount / curTotal) * 100}%`;
};
</script>

<style lang="scss" scoped>
.step-groups {
    @apply relative flex justify-between z-10;
}
.progress-bar {
    background: repeating-linear-gradient(-45deg, var(--p-primary-7), var(--p-primary-7) 2px, var(--p-primary-6) 3px, var(--p-primary-6) 5px);
}
.step-line {
    @apply absolute w-full h-[8px] left-0 bottom-[10px] right-0 bg-gray-200 dark:bg-fill-t-1  rounded-full overflow-hidden z-0;
    .step-line-inner {
        @apply h-full absolute top-0 left-0 transition-all duration-300 ease-in-out rounded-full;
        background: repeating-linear-gradient(-45deg, var(--p-primary-7), var(--p-primary-7) 2px, var(--p-primary-6) 3px, var(--p-primary-6) 5px);
    }
}
</style>
