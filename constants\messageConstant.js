export const noticeMap = {
  //会员升级
  3: {
    title: "",//取后端返回的
    detailTitle: "Subscription Updated Successfully!",//取后端返回的
    introduction: "",
  },
  //活动获奖 图片待返回 如果lumen records不上，要取消跳转
  4: {
    title: "",//取后端返回的
    detailTitle: "You’ve Won!",//取后端返回的
    introduction: "",
  },
  5: {
    //会员即将到期 
    title: "Your Membership is Expiring in 3 Days",
    introduction: "Your current plan ends in 3 days—renew now to keep all your benefits!",
  },
  6: {
    //会员已经到期 
    title: "Your Membership Has Expired",
    introduction: "Your PicLumen membership has ended - click and renew now.",
  },
  7: {
    //试订阅结束 
    title: "Your PicLumen Free Trial Ends in 24 Hours",
    introduction: "You'll be charged in 24 hours. ",
  },
  8: {
    //支付失败
    title: "Payment Failed – Membership Paused",
    detailTitle: "Payment Failed",
    introduction: "We couldn’t process your payment. Your membership has been paused. Update payment method to restore access...",
  },
};