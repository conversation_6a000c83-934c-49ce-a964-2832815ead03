<template>
    <div class="flex items-center" @click="handleVipBtnClick">
        <img
            src="@/assets/images/subscribe/icon_fo_all_member.webp"
            :class="{ 'opacity-40': disabled }"
            :style="{
                width: iconSize + 'px',
                height: iconSize + 'px',
            }"
            alt=""
            v-if="display === 'before'"
        />
        <slot></slot>
        <img
            v-if="display === 'after'"
            src="@/assets/images/subscribe/icon_fo_all_member.webp"
            :class="{ 'opacity-40': disabled }"
            :style="{
                width: iconSize + 'px',
                height: iconSize + 'px',
            }"
            alt=""
        />
    </div>
</template>
<script setup>
import { SUBSCRIBE_PERMISSION } from "@/utils/constant";
import { useSubPermission } from "@/hook/subscribe";
const { checkPermission } = useSubPermission();
const emit = defineEmits(["click"]);
const props = defineProps({
    display: {
        type: String,
        default: "before",
    },
    iconSize: {
        type: Number,
        default: 16,
    },
    iconClass: {
        type: String,
        default: "text-info-6",
    },
    iconName: {
        type: String,
        default: "IconDiamond",
    },
    auth: {
        //是否开启鉴权拦截
        type: Boolean,
        default: false,
    },
    disabled: {
        //当前仅用于展示禁用状态UI
        type: Boolean,
        default: false,
    },
});

const handleVipBtnClick = async () => {
    if (!props.auth) return emit("click");
    const isMember = await checkPermission(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
    if (isMember) {
        emit("click");
    }
};
</script>
