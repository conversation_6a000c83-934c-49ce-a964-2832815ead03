<template>
    <div class="w-full">
        <!-- <h2>{{ t('PROFILE_ACCOUNT_RESET_PASSWORD') }}</h2> -->
        <h2 class="text-2xl font-semibold mt-4 text-center">
            {{ t("PROFILE_ACCOUNT_RESET_PASSWORD") }}
        </h2>

        <div class="form-item pb-7 relative w-full mt-8" v-if="!hasDisplayCode" :style="{ '--err': `'${t(errorTips.email)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center pl-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsEmail />
                </n-icon>
                <n-input type="email" maxlength="100" v-model:value="config.email" :placeholder="t('ACC_SIGN_IN_EMAIL')" @change="checkEmail" />
            </div>
        </div>
        <div class="form-item pb-7 relative w-full" v-if="!hasDisplayCode" :style="{ '--err': `'${t(errorTips.password)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center px-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsLock />
                </n-icon>
                <n-input :type="inputType" maxlength="32" v-model:value="config.password" :placeholder="t('ACC_SIGN_IN_PWD')" @change="checkPassword" />
                <n-icon size="24" @click="displayPwd = !displayPwd" class="cursor-pointer hover:text-dark-active-text">
                    <IconsNoEye v-if="displayPwd" />
                    <IconsEye v-else />
                </n-icon>
            </div>
        </div>
        <div class="form-item pb-7 relative w-full" v-if="!hasDisplayCode" :style="{ '--err': `'${t(errorTips.aginPassword)}'` }">
            <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center px-4 text-dark-active-text/70">
                <n-icon size="24">
                    <IconsLock />
                </n-icon>
                <n-input
                    :type="aginPwdInputType"
                    maxlength="32"
                    v-model:value="config.aginPassword"
                    :placeholder="t('PROFILE_ACCOUNT_CONFIRM_PASSWORD')"
                    @change="checkConfirmPassword"
                    @keydown.enter="getValidateCode"
                />
                <n-icon size="24" @click="displayAginPwd = !displayAginPwd" class="cursor-pointer hover:text-dark-active-text">
                    <IconsNoEye v-if="displayAginPwd" />
                    <IconsEye v-else />
                </n-icon>
            </div>
        </div>
        <div v-else class="mt-8">
            <div class="text-xs mb-2 opacity-60">
                {{ $t("ACCOUNT_CURRENT", { email: desensitization }) }}
            </div>
            <div class="form-item pb-7 relative w-full flex gap-2" :style="{ '--err': `'${t(errorTips.validateCode)}'` }">
                <div class="h-10 rounded-lg bg-[#2B2B2B] flex items-center pl-4 text-dark-active-text/70">
                    <n-icon size="24">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 -960 960 960" width="1em" fill="currentColor">
                            <path
                                d="M280-240q-100 0-170-70T40-480q0-100 70-170t170-70q66 0 121 33t87 87h432v240h-80v120H600v-120H488q-32 54-87 87t-121 33Zm0-80q66 0 106-40.5t48-79.5h246v120h80v-120h80v-80H434q-8-39-48-79.5T280-640q-66 0-113 47t-47 113q0 66 47 113t113 47Zm0-80q33 0 56.5-23.5T360-480q0-33-23.5-56.5T280-560q-33 0-56.5 23.5T200-480q0 33 23.5 56.5T280-400Zm0-80Z"
                            />
                        </svg>
                    </n-icon>
                    <n-input type="text" :placeholder="t('PROFILE_ACCOUNT_VERIFY_CODE')" maxlength="4" v-model:value="config.validateCode" @change="checkValidateCode" @keydown.enter="handleSubmit" />
                </div>
                <Button class="flex-1" rounded="xl" :disabled="isDisabled || isLoading" :bordered="false" :loading="isLoading" @click="getValidateCode">
                    <n-countdown v-if="isDisabled" ref="countdown" :duration="60000" :active="true" :render="renderCountdown" :on-finish="resetCountdown" />
                    <span v-else>{{ t("PROFILE_ACCOUNT_GET_CODE") }}</span>
                </Button>
            </div>
        </div>

        <div class="mt-8 w-full">
            <Button v-if="!hasDisplayCode" :disabled="isLoading" type="primary" :loading="isLoading" class="h-10 w-full" block rounded="lg" @click="getValidateCode">
                <span class="text-dark-active-text" v-if="!isLoading">{{ t("CONFIG_BASE_RESET_BTN") }}</span>
            </Button>
            <Button v-else type="primary" :disabled="subLoading" :loading="subLoading" class="h-10 w-full" block rounded="lg" @click="handleSubmit">
                <span class="text-dark-active-text" v-if="!subLoading">{{ t("PROFILE_ACCOUNT_CONFIRM_EMAIL") }}</span>
            </Button>
        </div>
        <div class="mt-4">
            <Button type="textSecondary" size="small" @click="toFeature('SignInComp')">
                <template #icon>
                    <n-icon>
                        <IconsBack />
                    </n-icon>
                </template>
                <span class="ml-1.5 !text-dark-active-text text-xs">{{ t("ACC_SIGN_IN_BACK") }}</span>
            </Button>
        </div>
    </div>
</template>

<script setup>
import { resetPassword, sendCodeByReset } from "@/api";

const { t } = useI18n({ useScope: "global" });
import { strToMd5, validateEmail } from "@/utils/tools.js";
const emit = defineEmits(["changeComponent"]);
const toFeature = (key) => {
    emit("changeComponent", key);
};
//  同意协议
const isAgree = ref(false);
//  loading
const isLoading = ref(false);
// 提交 loading
const subLoading = ref(false);
//禁用 验证码 button
const isDisabled = ref(false);
const countdown = ref(null);
const renderCountdown = ({ seconds }) => {
    if (seconds == 0) {
        return "60 s";
    }
    return `${String(seconds).padStart(2, "0")} s`;
};

//重置倒计时
const resetCountdown = () => {
    countdown.value.reset();
    isDisabled.value = false;
    isLoading.value = false;
};
const desensitization = computed(() => {
    try {
        let email = config.value.email;
        let lastIndex = email.lastIndexOf("@");
        return email.substring(0, 1) + "***@" + email.substring(lastIndex + 1, lastIndex + 2) + "***";
    } catch (error) {
        return email.substring(0, 1) + "***@***";
    }
});
const hasDisplayCode = ref(false);
const displayPwd = ref(false);
const displayAginPwd = ref(false);
const config = ref({
    email: "",
    password: "",
    aginPassword: "",
    validateCode: "",
});
const errorTips = ref({
    email: "NULL_CONTENT",
    password: "NULL_CONTENT",
    aginPassword: "NULL_CONTENT",
    validateCode: "NULL_CONTENT",
});

const inputType = computed(() => {
    return displayPwd.value ? "text" : "password";
});
const aginPwdInputType = computed(() => {
    return displayAginPwd.value ? "text" : "password";
});

// 邮箱校验
const checkEmail = () => {
    const email = config.value.email;
    if (validateEmail(email)) {
        errorTips.value.email = "NULL_CONTENT";
        return true;
    }
    errorTips.value.email = "PROFILE_ACCOUNT_INVALID_EMAIL";
    return false;
};
//密碼校验
const checkPassword = () => {
    const val = config.value.password;
    const aginVal = config.value.aginPassword;
    aginVal && checkConfirmPassword();
    if (val.length >= 6) {
        errorTips.value.password = "NULL_CONTENT";
        return true;
    }
    errorTips.value.password = "PROFILE_ACCOUNT_INVALID_PASSWORD";
    return false;
};
const checkConfirmPassword = () => {
    const val = config.value.password;
    const aginVal = config.value.aginPassword;
    if (val === aginVal && val.length >= 6) {
        errorTips.value.aginPassword = "NULL_CONTENT";
        errorTips.value.password = "NULL_CONTENT";
        return true;
    }
    errorTips.value.aginPassword = "ACC_SIGN_IN_PWD_NOT_MATCH";
    return false;
};

// 验证码校验正则
const checkValidateCode = () => {
    const code = config.value.validateCode;
    const codeReg = /^[0-9]{4}$/;
    if (codeReg.test(code)) {
        errorTips.value.validateCode = "NULL_CONTENT";
        return true;
    }
    errorTips.value.validateCode = "PROFILE_ACCOUNT_INVALID_VERIFY_CODE";
    return false;
};

//获取验证码
const getValidateCode = async () => {
    const t1 = checkEmail();
    const t2 = checkPassword();
    const t3 = checkConfirmPassword();
    if (isLoading.value || isDisabled.value || !t1 || !t2 || !t3) {
        return false;
    }

    try {
        isLoading.value = true;
        let account = config.value.email;
        const { status, message } = await sendCodeByReset({ account });
        isLoading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        isDisabled.value = true;
        hasDisplayCode.value = true;
    } catch (error) {
        isLoading.value = false;
    }
};

// 检测是否允许提交
const checkAllowSubmit = () => {
    return new Promise((resolve) => {
        const t1 = checkEmail();
        const t2 = checkPassword();
        const t3 = checkValidateCode();
        resolve(t1 && t2 && t3);
    });
};

//提交
const handleSubmit = async () => {
    window.trackEvent("Sign", { el: "reset" });

    const hasAllow = await checkAllowSubmit();
    if (!hasAllow || subLoading.value) {
        return;
    }
    try {
        const reqBody = {
            account: config.value.email,
            password: strToMd5(config.value.password),
            validateCode: config.value.validateCode,
        };
        subLoading.value = true;
        let { status, message } = await resetPassword(reqBody);
        subLoading.value = false;
        if (status != 0) {
            openToast.error(message);

            window.trackEvent("Sign", { el: "reset_error=" + message });
            return;
        }
        openToast.success(t("TOAST_RESET_PASSWORD"));
        emit("changeComponent", "SignInComp");
    } catch (error) {
        subLoading.value = false;

        window.trackEvent("Sign", { el: "reset_error=" + error.message });
    }
};
</script>

<style lang="scss" scoped>
.form-item::after {
    @apply absolute text-xs text-error top-10 mt-0.5 left-0 block scale-90 origin-top-left;
    content: var(--err, "");
}
.custom-checkbox {
    @apply border border-solid cursor-pointer border-white/40 w-4 h-4 rounded-sm flex items-center justify-center;
}
</style>
