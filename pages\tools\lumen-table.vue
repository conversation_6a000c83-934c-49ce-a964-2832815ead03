<template>
    <div class="h-screen bg-bg-1 relative text-text-2 font-medium scroll-container">
        <div class="h-[72px] bg-bg-2 p-6 sticky top-0 flex items-center gap-6 z-50">
            <n-icon size="24" class="cursor-pointer" @click="handleBack">
                <IconsBack />
            </n-icon>
            <div>{{ t("LUMEN_COST_TABLE") }}</div>
        </div>

        <div class="p-6 max-w-5xl">
            <div class="text-text-1">{{ t("LUMEN_COST_TABLE_DESC") }}</div>
            <div class="mt-6 text-2xl">{{ t("LUMEN_COST_TABLE") }}</div>
            <div class="mt-6 text-base">{{ t("LUMEN_COST_TABLE_TYPE_GEN_TITLE") }}</div>
            <div class="mt-4 border border-solid border-dark-bg-2 rounded-lg grid grid-cols-2 table-box text-base text-text-1">
                <div
                    v-for="(item, index) in tableList1"
                    :key="item.label"
                    class="h-14 border-b border-solid border-dark-bg-2 even:border-l px-4 flex items-center"
                    :class="{ 'dark:text-dark-active-text text-black': index < 2 }"
                >
                    <span v-if="item.hasI18n">{{ t(item.label) }}</span>
                    <span v-else>{{ item.label }}</span>
                </div>
            </div>
            <div class="mt-4 text-text-1">{{ t("LUMEN_COST_TABLE_FEATURE") }}</div>
            <div class="mt-6 text-base">{{ t("MENU_TOOLS") }}</div>
            <div class="mt-4 border border-solid border-dark-bg-2 rounded-lg grid grid-cols-2 table-box text-base text-text-1">
                <div
                    v-for="(item, index) in tableList2"
                    :key="item.label"
                    class="h-14 border-b border-solid border-dark-bg-2 even:border-l px-4 flex items-center"
                    :class="{ 'dark:text-dark-active-text text-black': index < 2 }"
                >
                    <span v-if="item.hasI18n">{{ t(item.label) }}</span>
                    <span v-else>{{ item.label }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
useSeoMeta({
    title: () => "Free AI Image Generator for AI Art Creation - PicLumen",
    ogTitle: () => "Free AI Image Generator for AI Art Creation - PicLumen",
});
const router = useRouter();
const tableList1 = [
    { label: "CONFIG_BASE_MODEL", hasI18n: true },
    { label: "LUMEN_COST_TABLE_TYPE_GEN", hasI18n: true },
    { label: "PicLumen Series" },
    { label: "1 Lumen / image" },
    { label: "Pony Diffusion V6" },
    { label: "1 Lumen / image" },
    { label: "FLUX.1-Schnell" },
    { label: "1 Lumen / image" },
    { label: "FLUX.1-dev" },
    { label: "3 Lumen / image" },
    { label: "Primo" },
    { label: "3 Lumen / image" },
    { label: "FLUX.1 Kontext" },
    { label: "12 Lumen / image" },
    { label: "Namiya" },
    { label: "1 Lumen / image" },
    { label: "Namiya" },
    { label: "1 Lumen / image" },
    { label: "FLUX.1 Krea" },
    { label: "3 Lumen / image" },
];
const tableList2 = [
    { label: "MENU_TOOLS", hasI18n: true },
    { label: "LUMEN_COST_TABLE_TYPE_COST", hasI18n: true },
    { label: "Describe Image" },
    { label: "1 Lumen / image" },
    // { label: "4x Upscale" },
    // { label: "10 Lumen / image" },
    { label: "Colorize" },
    { label: "1 Lumen / image" },
    { label: "Remove Background" },
    { label: "1 Lumen / image" },
    { label: "Edit" },
    { label: "12 Lumen / image" },
];
const handleBack = () => {
    router.back();
};
</script>

<style lang="scss" scoped>
.table-box > div {
    &:nth-last-child(1),
    &:nth-last-child(2) {
        border-bottom: none;
    }
}
</style>
