<template>
    <div class="min-h-full flex gap-3 flex-col p-4 bg-bg-2">
        <div
            class="bg-fill-wd-1 rounded-lg w-[98px] h-[98px] border border-dashed border-border-2 flex flex-col items-center justify-center"
            :class="{ 'cursor-pointer': !disabled }"
            @click="openUpload"
        >
            <n-icon size="20"><IconsUpload class="text-text-2" :class="{ 'text-text-6': disabled }" /></n-icon>
            <div class="mt-3 text-text-3" :class="{ 'text-text-6': disabled }">
                {{ $t("FEATURE_SELF_UPLOAD_TITLE") }}
            </div>
        </div>
        <div class="w-full flex flex-col gap-3 overflow-auto no-scroll flex-1">
            <div
                class="rounded-lg w-[96px] h-[96px] relative group cursor-pointer shrink-0"
                :class="{ 'border-2 border-solid border-primary-6 p-0.5': activeId === i.id }"
                v-for="(i, idx) in list"
                :key="i.id"
                @click="handleItemClick(i, idx)"
            >
                <img :src="i.imageUrlLocal" alt="" class="block w-full h-full object-cover pointer-events-none rounded-[4px]" />
                <div
                    class="delete items-center justify-center p-1 rounded-[4px] bg-fill-t-2 hidden group-hover:flex absolute top-2 right-2 text-white cursor-pointer z-[15]"
                    v-if="i.taskStatus === REQ_STATUS.SUCCESS || i.taskStatus === REQ_STATUS.ERROR"
                    @click.stop.prevent="handleDelete(i.id, idx)"
                >
                    <IconsDele />
                </div>
                <div
                    class="mask absolute top-0 left-0 right-0 bottom-0 w-full h-full rounded-[4px] z-10 flex items-center justify-center"
                    :class="[activeId === i.id ? 'bg-fill-t-4' : i.taskStatus !== REQ_STATUS.SUCCESS ? 'bg-fill-t-2' : 'bg-fill-ww-1']"
                >
                    <n-spin :size="20" stroke="var(--p-success-6)" v-if="i.taskStatus < REQ_STATUS.SUCCESS" />
                    <n-icon size="20" class="" v-if="i.taskStatus === REQ_STATUS.ERROR">
                        <IconsAlertFill />
                    </n-icon>
                </div>
            </div>
        </div>
        <div class="flex flex-col text-xs gap-1 items-center">
            <!-- <span class="text-text-4">1 / 30 IMAGES</span> -->
            <span class="text-xs py-2 px-3 rounded-full" :class="[disabled ? 'text-text-6' : 'cursor-pointer  hover:bg-fill-btn-10 text-danger-6']" @click="handleClear" v-if="list.length">{{
                $t("BATCH_REMOVE_BG.CLEAR_ALL")
            }}</span>
        </div>
    </div>
</template>
<script setup>
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { REQ_STATUS } from "@/utils/constant.js";
const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});
const myList = computed(() => {
    return Array.isArray(props.list) ? [...props.list].reverse() : [];
});

const emit = defineEmits(["fileAdded", "clear", "preview", "delete"]);
const openUpload = () => {
    if (props.disabled) return;
    emit("fileAdded");
};
const handleClear = () => {
    if (props.disabled) return;
    gaTrackEvent({ el: "detail_clear_all" });
    emit("clear");
};
const previewIndex = ref(0);
const handleItemClick = (item, index) => {
    if (!item) return;
    const { id } = item;
    activeId.value = id;
    emit("preview", index);
    previewIndex.value = index;
};
const handleDelete = (id, oldIndex) => {
    emit("delete", id);
    gaTrackEvent({ el: "detail_delete_image" });
    nextTick(() => {
        const { list } = props;
        if (!list.length) return;
        if (activeId.value === id) {
            // 删除的是激活项，找新激活项
            const newIndex = Math.min(oldIndex, list.length - 1); // fallback 到前/后一个
            const fallbackItem = list[newIndex];
            if (fallbackItem) {
                handleItemClick(fallbackItem, newIndex);
            }
        }
    });
};

const activeId = ref(null);

watch(
    () => props.list,
    (newList, oldList) => {
        if (!newList?.length) {
            // 列表为空时，清空 activeId
            activeId.value = null;
        } else if (!oldList?.length && newList?.length > 0) {
            // 从空变为非空，重新赋值 activeId
            handleItemClick(newList[0], 0);
        }
    },
    { deep: true, immediate: true } // 如果 list 是响应式 prop，需要监听内容变化
);

const handleKeyDown = (e) => {
    let oldIndex = previewIndex.value;
    if (e.key === "ArrowUp") {
        oldIndex--;
    } else if (e.key === "ArrowDown") {
        oldIndex++;
    }
    let newIndex = Math.max(0, Math.min(oldIndex, props.list.length - 1));
    handleItemClick(props.list[newIndex], newIndex);
};

onMounted(() => {
    //开启上下左右按钮的监听 如果是上和左 就执行previewIndex++ 否则--注意判断边界
    window.addEventListener("keydown", handleKeyDown);
});
onUnmounted(() => {
    window.removeEventListener("keydown", handleKeyDown);
});

defineExpose({
    handleItemClick,
});

/**埋点 */
const gaTrackEvent = (options) => {
    console.log("gaTrackEvent", options);
    window.trackEvent("Remove_BG", options);
};
</script>
