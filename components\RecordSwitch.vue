<!--
 * @Author: HuangQS
 * @Description: 消息记录页 顶部选项
 * @Date: 2025-06-09 20:01:41
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-08-01 10:53:11
-->
<template>
    <div class="flex">
        <div class="flex bg-fill-tab-4 h-11 rounded-full p-0.5">
            <div class="h-full flex justify-start relative gap-2 text-text-4">
                <div
                    ref="tabSlider"
                    class="rounded-full transition-all duration-300 bg-fill-tab-3 absolute top-0 bottom-0"
                    :style="{
                        width: sliderWidth,
                        transform: `translateX(${sliderOffset}px)`,
                    }"
                />

                <div
                    v-for="(tabItem, tabIndex) in tabs"
                    :key="tabIndex"
                    :class="{ 'text-text-1': selectTabId === tabIndex }"
                    class="h-full flex justify-center items-center rounded-full text-sm font-medium cursor-pointer select-none transition-colors duration-300 px-4 relative"
                    @click="handleTabClick(tabIndex)"
                    :ref="(el) => (tabRefs[tabIndex] = el)"
                >
                    <span>{{ $t(tabItem) }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";

const props = defineProps({
    selectTabId: { type: Number, default: 0 },
    tabViews: { type: Array, default: () => [] },
});

const emits = defineEmits(["update:selectTabId", "onClick"]);

const tabRefs = ref([]);
const tabSlider = ref(null);
const sliderWidth = ref("0px");
const sliderOffset = ref(0);

const { selectTabId, tabViews } = toRefs(props);

const tabs = computed(() => {
    return tabViews.value.map((item) => item.title);
});

// 初始化/更新slider的宽度和位置
const updateSliderStyle = (tabIndex) => {
    const currentTab = tabRefs.value[tabIndex];
    if (!currentTab) return;

    const tabWidth = currentTab.offsetWidth;
    const tabOffset = currentTab.offsetLeft;

    sliderWidth.value = `${tabWidth}px`;
    sliderOffset.value = tabOffset;
};

// 点击tab时更新
const handleTabClick = (index) => {
    updateSliderStyle(index);
    emits("update:selectTabId", index);
    emits("onClick", index);
};

onMounted(() => {
    updateSliderStyle(selectTabId.value);

    watch(selectTabId, (newIndex) => {
        updateSliderStyle(newIndex);
    });
});
</script>

<style lang="scss" scoped>
.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
</style>
