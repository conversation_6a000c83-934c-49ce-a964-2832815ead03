<script setup>
import { reqSubscribeSession, reqSubscribeSessionByPaypal, firstGift, reqBiSavePayLog } from "@/api";
import { formatSubscribeParams, numberFormat } from "@/utils/tools.js";
import { round56 } from "@/utils/subscribeTools.js";
import { useSubscribeErrorModal } from "@/hook/subscribe.js";
import { SUBSCRIBE_TYPE, SUBSCRIBE_CALLBACK_PARAMS, PAYMENT_METHOD } from "@/utils/constant.js";
import { storeToRefs } from "pinia";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { I18nT, useI18n } from "vue-i18n";
const emits = defineEmits(["close"]);
const firstPurchase = ref(false); //是否享受首充
const subscribeStore = useSubscribeStore();
const { vipInfo, buyLumenGaEvent } = storeToRefs(subscribeStore);

const props = defineProps({
    triggerEl: {
        type: String,
        default: "other",
    },
});

//根据当前会员等级 计算折扣
const off = computed(() => {
    switch (vipInfo.value.plan) {
        case SUBSCRIBE_TYPE.STANDARD:
            return 10; // 标准会员享受10%折扣
        case SUBSCRIBE_TYPE.PRO:
            return 20; // 专业会员享受20%折扣
        default:
            return 0;
    }
});

const lumenList = ref([
    { count: 0, giftUnit: 50, lumen: 100, price: 0.99, discount: off.value, promotionType: "" },
    { count: 0, giftUnit: 500, lumen: 1000, price: 8.99, discount: off.value, promotionType: "" },
    { count: 0, giftUnit: 5000, lumen: 10000, price: 79.99, discount: off.value, promotionType: "" },
]);

watchEffect(() => {
    //   假如存在网络的lumen数据,则采用来自于网络数据
    if (import.meta.client) {
        const { isFirst, lumenList: reslist } = subscribeStore.lumenPromotionBox;
        firstPurchase.value = isFirst;
        if (reslist.length) {
            lumenList.value = reslist.map((item) => {
                return {
                    ...item,
                    count: 0,
                };
            });
        }
    }
    //兜底数据
    else {
        queryFirstGift();
    }
});

onMounted(() => {
    customTrackEvent("purchase_popup_show");
    customTrackEvent(`purchase_popup_source=${buyLumenGaEvent.value || props.triggerEl}`);
});

const queryFirstGift = async () => {
    const { data, status } = await firstGift();
    firstPurchase.value = data;
};

const { showErrorModal } = useSubscribeErrorModal();
const buyBtnLoading = ref(false);
const handleBuy = (paymentMethod) => {
    customTrackEvent("purchase_popup_buy");
    if (buyBtnLoading.value) return;
    buyBtnLoading.value = true;
    const source = `purchase_popup_source=${buyLumenGaEvent.value || props.triggerEl}`;
    reqBiSavePayLog({ source, priceType: "lumen", paymentChannel: "stripe" }).catch((_) => {});
    const buyItem = lumenList.value.filter((item) => item.count > 0).map((item) => ({ amount: item.count, lumen: item.lumen }));
    const params = formatSubscribeParams("BUY_ONE", { stripeItems: buyItem }, { ...SUBSCRIBE_CALLBACK_PARAMS.LUMEN, paymentMethod });

    customTrackEvent("purchase_popup_package");

    let gaBuySource = "purchase_popup_package";
    (params.stripeItems || params.items).forEach((item) => {
        gaBuySource += `#package=${item.lumen}#count=${item.amount}`;
    });

    customTrackEvent(gaBuySource);

    let requestPromise = reqSubscribeSession;
    if (paymentMethod === PAYMENT_METHOD.PAYPAL) {
        requestPromise = reqSubscribeSessionByPaypal;
    }
    requestPromise(params)
        .then((res) => {
            if (res.status === 0) {
                let redirectUrl;
                if (paymentMethod === PAYMENT_METHOD.PAYPAL) {
                    redirectUrl = res?.data?.approvalUrl;
                } else {
                    redirectUrl = res?.data;
                }
                emits("close");
                sessionStorage.setItem("lumenTemp", subscribeStore.vipInfo.awardedLumenLeft); // 记录点击前的lumen点，当支付完成后作为轮询依据
                location.href = redirectUrl;
            } else {
                buyBtnLoading.value = false;
                showErrorModal("SUBSCRIBE_EX_TEXT", { code: res.status });
            }
        })
        .catch((err) => {
            buyBtnLoading.value = false;
            showErrorModal("SUBSCRIBE_EX_TEXT", { code: 500 });
        });
};

const handleClose = () => {
    if (buyBtnLoading.value) return;
    emits("close");
};

import IconVipBasic from "@/components/icons/VipBasic.vue";
import IconVipStandard from "@/components/icons/VipStandard.vue";
import IconVipPro from "@/components/icons/VipPro.vue";

// 会员权益
const benefitsList = {
    [SUBSCRIBE_TYPE.BASIC]: {
        name: "SUBSCRIBE_PLAN_BASIC",
        style: "basic-plan",
        icon: "IconVipBasic",
        list: ["SUBSCRIBE_BENEFITS_1", "SUBSCRIBE_BENEFITS_2", "SUBSCRIBE_BENEFITS_3"],
    },
    [SUBSCRIBE_TYPE.STANDARD]: {
        name: "SUBSCRIBE_PLAN_STANDARD",
        style: "standard-plan",
        icon: "IconVipStandard",
        list: [
            "SUBSCRIBE_BENEFITS_1",
            "SUBSCRIBE_BENEFITS_19",
            "SUBSCRIBE_BENEFITS_3",
            "SUBSCRIBE_BENEFITS_5",
            "SUBSCRIBE_BENEFITS_6",
            "SUBSCRIBE_BENEFITS_7",
            "SUBSCRIBE_BENEFITS_8",
            "SUBSCRIBE_BENEFITS_20",
        ],
    },
    [SUBSCRIBE_TYPE.PRO]: {
        name: "SUBSCRIBE_PLAN_PRO",
        style: "pro-plan",
        icon: "IconVipPro",
        list: [
            "SUBSCRIBE_BENEFITS_1",
            "SUBSCRIBE_BENEFITS_21",
            "SUBSCRIBE_BENEFITS_3",
            "SUBSCRIBE_BENEFITS_5",
            "SUBSCRIBE_BENEFITS_10",
            "SUBSCRIBE_BENEFITS_11",
            "SUBSCRIBE_BENEFITS_12",
            "SUBSCRIBE_BENEFITS_8",
            "SUBSCRIBE_BENEFITS_20",
        ],
    },
};
const vipIconComp = { IconVipBasic, IconVipStandard, IconVipPro };
const currPlanData = computed(() => benefitsList[vipInfo.value.plan]);
const currPlanName = computed(() => currPlanData.value.name);
const currPlanStyle = computed(() => currPlanData.value.style);
const currPlanIcon = computed(() => vipIconComp[currPlanData.value.icon]);
const isBasic = computed(() => vipInfo.value.plan === SUBSCRIBE_TYPE.BASIC);
const isStandardPlan = computed(() => vipInfo.value.plan === SUBSCRIBE_TYPE.STANDARD);
const isProPlan = computed(() => vipInfo.value.plan === SUBSCRIBE_TYPE.PRO);

//产品金额小计
const subtotalPrice = computed(() => {
    let total = 0;
    lumenList.value.forEach((item) => {
        total += item.price * item.count * (1 - item.discount / 100);
    });
    return numberFormat(round56(total));
});

//! todo 判断金额是否小于临界值，避免转换为港元后，因价格小于期望值而无法购买
const isPaymentAmountIsSmall = computed(() => {
    return (subtotalPrice.value ?? 0) < 0.6;
});

//购买lumen小计
const subtotalCount = computed(() => {
    let total = 0;
    lumenList.value.forEach((item) => {
        total += item.lumen * item.count; // 如果是首充，赠送的lumen也计算在内
    });
    return total;
});
//赠送lumen小计
const giftCount = computed(() => {
    let total = 0;
    lumenList.value.forEach((item) => {
        total += item.giftUnit * (item.count ? 1 : 0) * (firstPurchase.value ? 1 : 0); // 如果是首充，赠送的lumen也计算在内
    });
    return total;
});
//Lumen统计
const subtotalLumen = computed(() => {
    return giftCount.value + subtotalCount.value;
});
//计算折扣
const salePrice = computed(() => (item) => {
    // return (item.price * (1 - item.discount / 100)).toFixed(2);

    // 转回原数值并保留2位小数格式
    return numberFormat(round56(item.price * (1 - item.discount / 100)));
});

/**
 * 修改指定商品数量
 * @param index 当前第x个商品
 * @param item  商品对象
 * @param count 操作数量
 */
const handleChangeCount = (index, item, count) => {
    // 周年庆期间 操作第一个商品 第一次添加会连加2个，存在2个商品时删除商品，会清空次数
    if (index == 0 && item.promotionType === "anniversary") {
        if (item.count === 0 && count > 0) {
            item.count = 2;
            return;
        }
        if (item.count === 2 && count < 0) {
            item.count = 0;
            return;
        }
    }

    item.count = Math.max(0, item.count + count);
};

const { t } = useI18n({ useScope: "global" });
const i18nUpgrade = computed(() => {
    return t("SUBSCRIBE_UPGRADE");
});

const route = useRoute();
const localePath = useLocalePath();

//前往订阅
const handleUpgrade = async () => {
    customTrackEvent("purchase_popup_upgrade");
    handleClose();
    const subscribePage = localePath("/user/subscribe");
    if (route.path !== subscribePage) {
        await navigateTo({ path: subscribePage });
    }
};
const channels = ref([]); // 支付方式列表
//获取本地支付方式
const getSupportPayChannels = async () => {
    channels.value = await subscribeStore.getPaymentChannels();
    if (channels.value.length === 0) {
        showErrorModal("SUBSCRIBE_NO_PAY_CHANNELS");
    }
};

const showPaymentChannels = computed(() => {
    return (payment) => channels.value.includes(payment);
});

getSupportPayChannels();

const customTrackEvent = (el) => {
    window.trackEvent("Subscribe", { el });
};
</script>

<template>
    <div class="max-h-[80vh] overflow-y-auto p-6">
        <div class="pb-4 flex items-center gap-2">
            <span class="text-base font-semibold text-text-1">{{ $t("SUBSCRIBE_CURRENT_PLAN") }}</span>
            <div class="text-xs font-medium gap-1 py-0.5 border-2 border-solid border-transparent rounded-full relative flex items-center">
                <VipLevel :plan="vipInfo?.plan" />
            </div>
            <div
                class="ml-auto w-8 h-8 translate-x-2 rounded-full flex items-center justify-center hover:bg-fill-wd-1 text-text-4 hover:text-text-2 cursor-pointer transition-colors"
                @click="handleClose"
            >
                <IconsClose class="text-xl" />
            </div>
        </div>
        <div class="pt-4 pb-6">
            <div class="flex items-center text-text-3">
                <span v-if="isBasic">
                    <i18n-t keypath="SUBSCRIBE_DISCOUNT_BASIC">
                        <template v-slot:count>
                            <span class="text-primary-6 mx-0.5 cursor-pointer hover:underline underline-offset-4" @click="handleUpgrade">{{ i18nUpgrade }}</span>
                        </template>
                    </i18n-t>
                </span>
                <!-- <span v-if="isStandardPlan">
                    <i18n-t keypath="SUBSCRIBE_DISCOUNT_STANDARD">
                        <template v-slot:count>
                            <span class="text-primary-6 mx-0.5 cursor-pointer hover:underline underline-offset-4" @click="handleUpgrade">{{ i18nUpgrade }}</span>
                        </template>
                    </i18n-t>
                </span>
                <span v-if="isProPlan">
                    {{ $t("SUBSCRIBE_DISCOUNT_PRO") }}
                </span> -->
            </div>

            <div class="mt-8 grid md:grid-cols-3 gap-4">
                <div v-for="(item, index) in lumenList" :key="item.lumen" class="bg-bg-2 rounded-2xl border border-solid border-border-1 p-4 relative">
                    <div class="mask-box absolute top-0 left-0 right-0 bottom-0 pointer-events-none rounded-2xl overflow-hidden">
                        <img src="@/assets/images/subscribe/grid.svg" class="absolute top-0 left-0 right-0 bottom-0 object-cover pointer-events-none" />
                    </div>
                    <div
                        v-if="item.discount !== 0"
                        class="rounded-[2px_16px_0px_16px] text-white text-xs px-3 h-6 flex items-center justify-center tag-icon absolute top-0 right-0 -translate-y-1/2 uppercase"
                    >
                        {{ $t("VIP_BENEFITS_DISCOUNT_X", item.discount) }}
                    </div>
                    <div class="flex items-center gap-1.5">
                        <n-icon size="28">
                            <IconsLumenFill class="text-primary-6" />
                        </n-icon>
                        <span class="text-text-1 text-xl font-semibold">{{ numberFormat(item.lumen) }}</span>
                    </div>
                    <div v-if="firstPurchase" class="mt-3 w-max px-2 py-1 rounded-lg backdrop-blur text-primary-6 bg-fill-wd-1 text-xs font-medium flex items-center gap-0.5">
                        <i18n-t keypath="SUBSCRIBE_FRIST_GIFT">
                            <template v-slot:count>
                                <n-icon size="14">
                                    <IconsLumenFill />
                                </n-icon>
                                <span>{{ item.giftUnit }}</span>
                            </template>
                        </i18n-t>
                    </div>

                    <div class="mt-6 flex items-center gap-1 text-text-2">
                        <span class="text-base">$ {{ salePrice(item) }}</span>
                        <span v-show="item.discount > 0" class="text-xs line-through text-text-4 mt-1">$ {{ item.price }}</span>

                        <div class="ml-auto flex gap-1 items-center">
                            <div
                                class="hover:bg-fill-btn-2 transition-colors w-6 h-6 bg-fill-wd-1 rounded backdrop-blur flex items-center justify-center cursor-pointer"
                                :class="{ 'cursor-no-drop !bg-fill-btn-8 !text-text-6': item.count === 0 }"
                                @click="handleChangeCount(index, item, -1)"
                            >
                                <IconsMinus class="text-base" />
                            </div>
                            <div class="flex items-center justify-center min-w-8 select-none">{{ item.count }}</div>
                            <div
                                class="hover:bg-fill-btn-2 transition-colors w-6 h-6 bg-fill-wd-1 rounded backdrop-blur flex items-center justify-center cursor-pointer"
                                @click="handleChangeCount(index, item, 1)"
                            >
                                <IconsAdd class="text-base" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-text-4 pt-6 flex items-center max-md:flex-wrap max-md:justify-center max-md:gap-4">
            <span class="text-base">{{ $t("SUBSCRIBE_LUMEN_TOTAL") }}</span>
            <span class="mx-2.5 text-text-2 text-xl">$ {{ subtotalPrice }}</span>
            <div v-show="subtotalLumen > 0" class="text-text-2 w-max font-medium flex items-center gap-1.5">
                <span class="w-0.5 h-[18px] bg-text-5"></span>
                <IconsLumenFill class="text-xl" />
                <span class="text-xl">{{ numberFormat(subtotalLumen) }}</span>
                <span v-if="giftCount > 0" class="text-text-4 text-sm">({{ $t("SUBSCRIBE_LUMEN_INCLIDES") }}) </span>
            </div>
            <div class="ml-auto flex gap-3 flex-wrap">
                <Button
                    v-if="showPaymentChannels(PAYMENT_METHOD.STRIPE)"
                    :disabled="subtotalLumen === 0 || buyBtnLoading || isPaymentAmountIsSmall"
                    :loading="buyBtnLoading"
                    class="min-w-36 max-md:w-full"
                    type="primary"
                    @click="handleBuy(PAYMENT_METHOD.STRIPE)"
                >
                    <IconsStripe class="w-6 h-6 mr-1" />
                    <span>Stripe</span>
                </Button>
                <Button
                    v-if="showPaymentChannels(PAYMENT_METHOD.PAYPAL)"
                    :disabled="subtotalLumen === 0 || buyBtnLoading"
                    :loading="buyBtnLoading"
                    class="min-w-36 max-md:w-full"
                    type="secondary"
                    @click="handleBuy(PAYMENT_METHOD.PAYPAL)"
                >
                    <IconsPaypal class="w-6 h-6 mr-1" />
                    <span>Paypal</span>
                </Button>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.lumen-icon-box {
    position: absolute;
    @for $i from 2 through 10 {
        @if $i == 2 {
            z-index: 50;
        }
        &:nth-child(#{$i}) {
            left: (($i - 1) * 16px);
            z-index: 50 + $i;
        }
    }
}
.mask-box {
    background: linear-gradient(180deg, rgba(123, 87, 229, 0.12) 0%, rgba(123, 87, 229, 0) 100%);
}
.tag-icon {
    background: linear-gradient(270deg, #ff3700 0%, #ff9417 99.83%);
}

.basic-plan {
    @apply border-[#BCBCBC];
    background: linear-gradient(90deg, #7c7c7c 0%, #bcbcbc 100%);
}

.standard-plan {
    @apply border-[#3CEA85];
    background: linear-gradient(90deg, #2c9e5d 0%, #3cd97e 100%);
}

.pro-plan {
    @apply border-[#FFDD34];
    background: linear-gradient(90deg, #fb6842 0%, #ffd500 100%);
}
</style>
