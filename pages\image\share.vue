<template>
    <main class="min-h-screen relative text-dark-active-text bg-black font-medium text-base dark">
        <div class="px-10 h-[60px] flex items-center">
            <a href="/" target="_blank" class="cursor-pointer">
                <IconsLogo class="h-8" />
            </a>
        </div>
        <section class="relative page-content flex" @click="toggleConf">
            <div class="flex-1 h-full flex justify-center items-center">
                <img v-if="!!task.promptId" :src="task.thumbnailUrl" class="w-full h-full object-contain" alt="" />
                <div v-else-if="!loading && !task.promptId" class="flex flex-col items-center justify-center">
                    <img src="@/assets/images/no-detail.svg" />
                    <span class="mt-4 text-dark-active-text">{{ $t("AUTHOR_DEL_RES") }}</span>
                </div>
                <n-icon size="20" v-if="loading" class="text-primary">
                    <IconsSpinLoading />
                </n-icon>
            </div>

            <div class="w-[364px] h-full shrink-0 bg-black/20 rounded-xl relative p-6 text-sm leading-6 overflow-hidden max-md:!hidden" v-if="!isMobile">
                <div v-if="loading || !task.promptId" class="h-full overflow-y-auto no-scroll pb-32 opacity-70 dark:opacity-100">
                    <div class="flex items-center gap-2">
                        <n-skeleton height="32px" circle />
                        <n-skeleton text class="w-36" />
                    </div>

                    <div class="mt-6 flex items-center gap-2.5">
                        <n-skeleton text class="w-20" />
                    </div>
                    <div class="mt-3 rounded-lg bg-white/5">
                        <n-skeleton text :repeat="4" />
                    </div>
                    <div class="mt-6 flex items-center gap-2.5">
                        <n-skeleton text class="w-20" />
                    </div>
                    <div class="mt-3 rounded-lg bg-white/5">
                        <n-skeleton text :repeat="4" />
                    </div>
                    <div class="mt-6 p-1.5 bg-white/5 flex items-center gap-2.5 rounded-lg text-dark-active-text">
                        <n-skeleton height="32px" circle />
                        <n-skeleton text class="w-36" />
                    </div>
                    <div class="mt-4 flex gap-3">
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                    </div>
                    <div class="mt-4 flex gap-3">
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                    </div>
                    <div class="mt-4 flex gap-3">
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                    </div>
                    <div class="mt-4 flex gap-3">
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                        <div class="w-1/2">
                            <n-skeleton text class="w-16" />
                            <n-skeleton text />
                        </div>
                    </div>
                </div>

                <div v-else class="h-full overflow-y-auto no-scroll pb-32">
                    <div class="flex items-center gap-2">
                        <n-avatar v-if="!!task.avatar" round :size="32" :src="task.avatar"></n-avatar>
                        <n-avatar v-else round :size="32">
                            <n-icon> <IconsPerson /> </n-icon
                        ></n-avatar>
                        <span>{{ task.userName }}</span>
                    </div>

                    <div class="mt-6 flex items-center gap-2.5">
                        <span class="font-medium">{{ $t("COMMON_PROMPT") }}</span>
                        <PicPopover placement="right" trigger="click" :duration="2" v-if="!!task.prompt">
                            <template #trigger>
                                <n-icon size="16" class="opacity-40 cursor-pointer hover:opacity-70" @click="copyToClipboard(task.prompt)">
                                    <IconsCopyText />
                                </n-icon>
                            </template>
                            <div class="gap-1 text-base flex items-center">
                                <n-icon size="20">
                                    <IconsSuccess />
                                </n-icon>
                                <span>{{ $t("TOAST_COPY_SUCCESS") }}</span>
                            </div>
                        </PicPopover>
                    </div>
                    <div class="mt-3 rounded-lg bg-white/5">
                        <n-input
                            type="textarea"
                            :bordered="false"
                            style="--n-placeholder-color: #c2c6cf"
                            class="!text-dark-active-text"
                            placeholder="prompt"
                            @wheel.stop="null"
                            v-model:value="task.prompt"
                            readonly
                            round
                            :autosize="{
                                minRows: 3,
                                maxRows: 6,
                            }"
                        />
                    </div>
                    <div class="mt-6 flex items-center gap-2.5">
                        <span class="font-medium">{{ $t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>

                        <PicPopover placement="right" trigger="click" :duration="2" v-if="!!task.negative_prompt">
                            <template #trigger>
                                <n-icon size="16" class="opacity-40 cursor-pointer hover:opacity-70" @click="copyToClipboard(task.negative_prompt)">
                                    <IconsCopyText />
                                </n-icon>
                            </template>
                            <div class="gap-1 text-base flex items-center">
                                <n-icon size="20">
                                    <IconsSuccess />
                                </n-icon>
                                <span>{{ $t("TOAST_COPY_SUCCESS") }}</span>
                            </div>
                        </PicPopover>
                    </div>
                    <div class="my-3 rounded-lg bg-white/5">
                        <n-input
                            type="textarea"
                            :bordered="false"
                            class="!text-dark-active-text"
                            placeholder="negative prompt"
                            @wheel.stop="null"
                            v-model:value="task.negative_prompt"
                            readonly
                            round
                            :autosize="{
                                minRows: 2,
                                maxRows: 4,
                            }"
                        />
                    </div>
                    <PictureInfo :info="task" />
                    <div v-if="task.tagList?.length > 0">
                        <div class="act-tag" v-for="item in task.tagList" :key="item">
                            <span>{{ item }}</span>
                        </div>
                    </div>
                </div>
                <NuxtLinkLocale to="/community/explore" class="block sticky bottom-3">
                    <n-button class="w-[316px] remix-btn rounded-full" :bordered="false">
                        <span class="font-bold text-xl text-dark-active-text">{{ $t("MENU_CREATE") }}</span>
                    </n-button>
                </NuxtLinkLocale>
            </div>
        </section>
        <section
            v-if="isMobile"
            class="absolute top-1/3 left-0 bottom-0 right-0 bg-dark-bg/90 rounded-t-xl p-4 text-sm overflow-y-auto opacity-0 -z-10"
            :class="{ 'z-10 opacity-100': showConf }"
            @click="showConf = !showConf"
        >
            <div class="bg-dark-bg rounded-lg p-3">
                <div>{{ $t("COMMON_PROMPT") }}</div>
                <div class="mt-2">{{ task.prompt }}</div>
            </div>
            <div class="bg-dark-bg rounded-lg p-3 mt-3">
                <div>{{ $t("CONFIG_BASE_NEGATIVE_PROMPT") }}</div>
                <div class="mt-2">{{ task.negative_prompt }}</div>
            </div>
            <div class="grid grid-cols-2 mt-3">
                <div class="col-span-2 act-tag">
                    <span>Date created：</span>
                    <span>{{ task.time }}</span>
                </div>
                <div class="mt-1 act-tag">
                    <span>Resolution：</span>
                    <span>{{ task.realWidth }} x {{ task.realHeight }}</span>
                </div>
                <div class="mt-1 act-tag">
                    <span>Guidance Scale：</span>
                    <span>{{ task.cfg }}</span>
                </div>
                <div class="mt-1 act-tag">
                    <span>Seed：</span>
                    <span>{{ task.seed }}</span>
                </div>
            </div>
            <div class="mt-3" v-if="task.tagList?.length > 0">
                <div class="act-tag" v-for="item in task.tagList" :key="item">
                    <span>{{ item }}</span>
                </div>
            </div>
        </section>

        <NuxtLinkLocale to="/community/explore" class="sticky bottom-3 mx-8 block" v-if="isMobile">
            <n-button class="w-full remix-btn rounded-full !h-12" :bordered="false">
                <span class="font-bold text-xl text-dark-active-text">{{ $t("MENU_CREATE") }}</span>
            </n-button>
        </NuxtLinkLocale>
    </main>
</template>

<script setup>
definePageMeta({
    layout: "custom",
});
const { t } = useI18n({ useScope: "global" });
import useWindowResize from "@/hook/windowResize";
const windowResize = useWindowResize();
const isMobile = computed(() => windowResize.width.value <= 768);

import { getTaskDetail } from "@/api";
import { copyToClipboard, formatPonyV6Prompt, formatDate } from "@/utils/tools";
import { renderModelIcon } from "@/utils/tools";
const task = ref({});

const route = useRoute();
const loading = ref(true);

const showConf = ref(false);

const atobUniversal = (str) => {
    if (typeof atob === "function") {
        return atob(str);
    } else {
        return Buffer.from(str, "base64").toString("binary");
    }
};
//model
//查询详情接口对接
const getImageTaskById = async (promptId, imgName, gId) => {
    if (!promptId || !imgName || !gId) return;
    try {
        const loginName = atobUniversal(gId);
        loading.value = true;
        const { status, data } = await getTaskDetail({ promptId, imgName, loginName });
        loading.value = false;
        const { genInfo, ...info } = data;
        info.time = formatDate(info.createTime);
        Object.assign(genInfo, info);
        const model_id = genInfo.model_id;

        let icon = renderModelIcon(model_id);
        genInfo.icon = icon;
        task.value = formatPonyV6Prompt(genInfo, "output");
        const UserName = task.value?.userName || "-";
        useSeoMeta({
            title: () => t("SEO_META.SEO_COMMUNITY_SHARE_TITLE", { UserName }),
            ogTitle: () => t("SEO_META.SEO_COMMUNITY_SHARE_TITLE", { UserName }),
            description: () => t("SEO_META.SEO_COMMUNITY_SHARE_DESC", { UserName }),
            ogDescription: () => t("SEO_META.SEO_COMMUNITY_SHARE_DESC", { UserName }),
        });
        getHDImg();
    } catch (error) {
        loading.value = false;
    }
};
const promptId = route.query.promptId;
const imageName = route.query.imageName;
const gId = route.query.gId;
getImageTaskById(promptId, imageName, gId);

const getHDImg = () => {
    if (import.meta.client) {
        const img = new Image();
        const preUrl = task.value.thumbnailUrl || task.value.highThumbnailUrl || task.value.imgUrl;
        img.src = preUrl;
        img.onload = () => {
            task.value.highMiniUrl = preUrl;
        };
    }
};

const toggleConf = () => {
    if (!isMobile.value) {
        return false;
    }
    showConf.value = !showConf.value;
};
</script>

<style lang="scss" scoped>
.page-content {
    height: calc(100vh - 60px);
    background: linear-gradient(180deg, #0b0b0b 0%, #291c18 100%);
}
.remix-btn {
    height: 72px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0.3) 99%), linear-gradient(78deg, #5438ff 2%, #d658ff 29%, #ff5645 68%, #ffcc87 95%);
}
.login-btn {
    width: 106px;
    height: 36px;
    border: 1px solid transparent;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    background-image: linear-gradient(180deg, #000 0%, #000 99%), linear-gradient(78deg, #5438ff 2%, #d658ff 29%, #ff5645 68%, #ffcc87 95%);
    span {
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0.3) 99%), linear-gradient(70deg, #5438ff 3%, #d658ff 30%, #ff5645 68%, #ffcc87 95%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }
}
::v-deep(.n-input .n-input__textarea-el) {
    color: inherit;
}
.def-tag {
    @apply gap-2 px-2.5 py-2 bg-black/5 dark:bg-white/5 flex text-xs items-center rounded dark:text-neutral-200;
}
.act-tag {
    @apply relative overflow-hidden mt-2.5 px-3 py-2 flex text-xs w-max items-center bg-[#6904E9]/30 border border-solid border-[#6904E9] rounded;
    & span {
        @apply dark:text-[#977EEE] text-dark-active-text;
    }
}
</style>
