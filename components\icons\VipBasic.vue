<template>
    <!-- <svg fill="none" height="1em" version="1.1" viewBox="0 0 48 48" width="1em" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <mask id="master_svg0_281_4518" maskUnits="objectBoundingBox" style="mask-type: alpha">
                <g>
                    <rect fill="#FFFFFF" fill-opacity="1" height="48" rx="0" width="48" x="0" y="0" />
                </g>
            </mask>
        </defs>
        <g mask="url(#master_svg0_281_4518)">
            <g>
                <path
                    d="M23.999999961853028,43.58999984741211C22.52399996185303,43.58999984741211,21.13399996185303,43.01399984741211,20.089999961853028,41.96799984741211C18.479999961853025,40.35799984741211,16.335999961853027,39.46999984741211,14.057999961853028,39.46999984741211C11.009999961853026,39.46999984741211,8.529999961853028,36.98999984741211,8.529999961853028,33.941999847412106C8.529999961853028,31.66199984741211,7.641999961853028,29.521999847412108,6.031999961853027,27.90999984741211C3.875999961853027,25.75399984741211,3.875999961853027,22.245999847412108,6.031999961853027,20.08999984741211C7.641999961853028,18.477999847412107,8.529999961853028,16.33799984741211,8.529999961853028,14.057999847412109C8.529999961853028,11.009999847412109,11.009999961853026,8.52999984741211,14.057999961853028,8.52999984741211C16.335999961853027,8.52999984741211,18.479999961853025,7.64199984741211,20.089999961853028,6.031999847412109C21.13399996185303,4.985999847412109,22.52399996185303,4.409999847412109,23.999999961853028,4.409999847412109C25.475999961853027,4.409999847412109,26.865999961853028,4.985999847412109,27.90999996185303,6.031999847412109C29.519999961853028,7.64199984741211,31.663999961853026,8.52999984741211,33.94199996185303,8.52999984741211C36.98999996185303,8.52999984741211,39.46999996185303,11.009999847412109,39.46999996185303,14.057999847412109C39.46999996185303,16.33799984741211,40.357999961853025,18.477999847412107,41.967999961853025,20.08999984741211C44.12399996185303,22.245999847412108,44.12399996185303,25.75399984741211,41.967999961853025,27.90999984741211C40.357999961853025,29.521999847412108,39.46999996185303,31.66199984741211,39.46999996185303,33.941999847412106C39.46999996185303,36.98999984741211,36.98999996185303,39.46999984741211,33.94199996185303,39.46999984741211C31.663999961853026,39.46999984741211,29.519999961853028,40.35799984741211,27.90999996185303,41.96799984741211C26.865999961853028,43.01399984741211,25.475999961853027,43.58999984741211,23.999999961853028,43.58999984741211C23.999999961853028,43.58999984741211,23.999999961853028,43.58999984741211,23.999999961853028,43.58999984741211Z"
                    fill="#EBEBEB"
                    fill-opacity="1"
                />
            </g>
            <g>
                <path
                    d="M23.999999940872193,5.911999940872192C25.075999940872194,5.911999940872192,26.08799994087219,6.3299999408721925,26.84999994087219,7.091999940872192C28.743999940872193,8.985999940872192,31.261999940872194,10.029999940872193,33.94199994087219,10.029999940872193C36.16199994087219,10.029999940872193,37.96999994087219,11.837999940872193,37.96999994087219,14.057999940872193C37.96999994087219,16.737999940872193,39.01399994087219,19.255999940872194,40.907999940872195,21.149999940872192C42.47999994087219,22.72199994087219,42.47999994087219,25.277999940872192,40.907999940872195,26.84999994087219C39.01399994087219,28.743999940872193,37.96999994087219,31.261999940872194,37.96999994087219,33.94199994087219C37.96999994087219,36.16199994087219,36.16199994087219,37.96999994087219,33.94199994087219,37.96999994087219C31.261999940872194,37.96999994087219,28.743999940872193,39.01399994087219,26.84999994087219,40.907999940872195C26.08799994087219,41.669999940872195,25.075999940872194,42.087999940872194,23.999999940872193,42.087999940872194C22.923999940872193,42.087999940872194,21.911999940872192,41.669999940872195,21.149999940872192,40.907999940872195C19.255999940872194,39.01399994087219,16.737999940872193,37.96999994087219,14.057999940872193,37.96999994087219C11.837999940872193,37.96999994087219,10.029999940872193,36.16199994087219,10.029999940872193,33.94199994087219C10.029999940872193,31.261999940872194,8.985999940872192,28.743999940872193,7.091999940872192,26.84999994087219C5.519999940872193,25.277999940872192,5.519999940872193,22.72199994087219,7.091999940872192,21.149999940872192C8.985999940872192,19.255999940872194,10.029999940872193,16.737999940872193,10.029999940872193,14.057999940872193C10.029999940872193,11.837999940872193,11.837999940872193,10.029999940872193,14.057999940872193,10.029999940872193C16.737999940872193,10.029999940872193,19.255999940872194,8.985999940872192,21.149999940872192,7.091999940872192C21.911999940872192,6.3299999408721925,22.923999940872193,5.911999940872192,23.999999940872193,5.911999940872192ZM23.999999940872193,2.9119999408721924C22.20199994087219,2.9119999408721924,20.40199994087219,3.5979999408721923,19.02999994087219,4.969999940872192C19.02999994087219,4.969999940872192,19.02999994087219,4.969999940872192,19.02999994087219,4.969999940872192C17.711999940872193,6.287999940872192,15.923999940872193,7.029999940872193,14.057999940872193,7.029999940872193C10.175999940872192,7.029999940872193,7.029999940872193,10.175999940872192,7.029999940872193,14.057999940872193C7.029999940872193,14.057999940872193,7.029999940872193,14.057999940872193,7.029999940872193,14.057999940872193C7.029999940872193,15.923999940872193,6.287999940872192,17.711999940872193,4.969999940872192,19.02999994087219C4.969999940872192,19.02999994087219,4.969999940872192,19.02999994087219,4.969999940872192,19.02999994087219C2.2259999408721924,21.77399994087219,2.2259999408721924,26.225999940872192,4.969999940872192,28.969999940872192C4.969999940872192,28.969999940872192,4.969999940872192,28.969999940872192,4.969999940872192,28.969999940872192C6.287999940872192,30.287999940872194,7.029999940872193,32.075999940872194,7.029999940872193,33.94199994087219C7.029999940872193,33.94199994087219,7.029999940872193,33.94199994087219,7.029999940872193,33.94199994087219C7.029999940872193,37.82399994087219,10.175999940872192,40.96999994087219,14.057999940872193,40.96999994087219C15.923999940872193,40.96999994087219,17.711999940872193,41.71199994087219,19.02999994087219,43.029999940872194C20.40199994087219,44.401999940872194,22.20199994087219,45.087999940872194,23.999999940872193,45.087999940872194C25.79799994087219,45.087999940872194,27.597999940872192,44.401999940872194,28.969999940872192,43.029999940872194C30.287999940872194,41.71199994087219,32.075999940872194,40.96999994087219,33.94199994087219,40.96999994087219C37.82399994087219,40.96999994087219,40.96999994087219,37.82399994087219,40.96999994087219,33.94199994087219C40.96999994087219,33.94199994087219,40.96999994087219,33.94199994087219,40.96999994087219,33.94199994087219C40.96999994087219,32.075999940872194,41.71199994087219,30.287999940872194,43.029999940872194,28.969999940872192C43.029999940872194,28.969999940872192,43.029999940872194,28.969999940872192,43.029999940872194,28.969999940872192C45.773999940872194,26.225999940872192,45.773999940872194,21.77399994087219,43.029999940872194,19.02999994087219C43.029999940872194,19.02999994087219,43.029999940872194,19.02999994087219,43.029999940872194,19.02999994087219C41.71199994087219,17.711999940872193,40.96999994087219,15.923999940872193,40.96999994087219,14.057999940872193C40.96999994087219,14.057999940872193,40.96999994087219,14.057999940872193,40.96999994087219,14.057999940872193C40.96999994087219,10.175999940872192,37.82399994087219,7.029999940872193,33.94199994087219,7.029999940872193C32.075999940872194,7.029999940872193,30.287999940872194,6.287999940872192,28.969999940872192,4.969999940872192C28.969999940872192,4.969999940872192,28.969999940872192,4.969999940872192,28.969999940872192,4.969999940872192C27.597999940872192,3.5979999408721923,25.79799994087219,2.9119999408721924,23.999999940872193,2.9119999408721924C23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924C23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924,23.999999940872193,2.9119999408721924Z"
                    fill="#B8B8B8"
                    fill-opacity="1"
                />
            </g>
            <g>
                <g>
                    <path
                        d="M23.99713896484375,35.9993467300415C23.99713896484375,35.9993467300415,11.99993896484375,22.000046730041504,11.99993896484375,22.000046730041504C11.99993896484375,22.000046730041504,35.99983896484375,22.000046730041504,35.99983896484375,22.000046730041504C35.99983896484375,22.000046730041504,23.99713896484375,35.9993467300415,23.99713896484375,35.9993467300415C23.99713896484375,35.9993467300415,23.99713896484375,35.9993467300415,23.99713896484375,35.9993467300415Z"
                        fill="#ADADAD"
                        fill-opacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M31.99993896484375,16.000046730041504C31.99993896484375,16.000046730041504,15.99993896484375,16.000046730041504,15.99993896484375,16.000046730041504C15.99993896484375,16.000046730041504,11.99993896484375,22.000516730041504,11.99993896484375,22.000516730041504C11.99993896484375,22.000516730041504,35.99983896484375,22.000516730041504,35.99983896484375,22.000516730041504C35.99983896484375,22.000516730041504,31.99993896484375,16.000046730041504,31.99993896484375,16.000046730041504C31.99993896484375,16.000046730041504,31.99993896484375,16.000046730041504,31.99993896484375,16.000046730041504Z"
                        fill="#C2C2C2"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <g>
                        <path
                            d="M17.9993896484375,22.00012125582993C17.9993896484375,22.00012125582993,23.9993896484375,15.99987125582993,23.9993896484375,15.99987125582993C23.9993896484375,15.99987125582993,29.9993896484375,22.00012125582993,29.9993896484375,22.00012125582993C29.9993896484375,22.00012125582993,17.9993896484375,22.00012125582993,17.9993896484375,22.00012125582993C17.9993896484375,22.00012125582993,17.9993896484375,22.00012125582993,17.9993896484375,22.00012125582993Z"
                            fill="#FFFFFF"
                            fill-opacity="1"
                        />
                    </g>
                    <g>
                        <path
                            d="M17.99945068359375,22.00001621246338C17.99945068359375,22.00001621246338,23.99676068359375,35.99931621246338,23.99676068359375,35.99931621246338C23.99676068359375,35.99931621246338,29.99945068359375,22.00001621246338,29.99945068359375,22.00001621246338C29.99945068359375,22.00001621246338,17.99945068359375,22.00001621246338,17.99945068359375,22.00001621246338C17.99945068359375,22.00001621246338,17.99945068359375,22.00001621246338,17.99945068359375,22.00001621246338Z"
                            fill="#FFFFFF"
                            fill-opacity="1"
                        />
                    </g>
                    <g>
                        <path
                            d="M15.99945068359375,15.999886512756348C15.99945068359375,15.999886512756348,17.99945068359375,22.00013651275635,17.99945068359375,22.00013651275635C17.99945068359375,22.00013651275635,11.99945068359375,22.00013651275635,11.99945068359375,22.00013651275635C11.99945068359375,22.00013651275635,15.99945068359375,15.999886512756348,15.99945068359375,15.999886512756348C15.99945068359375,15.999886512756348,15.99945068359375,15.999886512756348,15.99945068359375,15.999886512756348Z"
                            fill="#FFFFFF"
                            fill-opacity="1"
                        />
                    </g>
                    <g>
                        <path
                            d="M31.99945068359375,15.968796730041504C31.99945068359375,15.968796730041504,29.99945068359375,22.000046730041504,29.99945068359375,22.000046730041504C29.99945068359375,22.000046730041504,35.99945068359375,22.000046730041504,35.99945068359375,22.000046730041504C35.99945068359375,22.000046730041504,31.99945068359375,15.968796730041504,31.99945068359375,15.968796730041504C31.99945068359375,15.968796730041504,31.99945068359375,15.968796730041504,31.99945068359375,15.968796730041504Z"
                            fill="#FFFFFF"
                            fill-opacity="1"
                        />
                    </g>
                </g>
            </g>
        </g>
    </svg> -->

    <svg v-if="uid" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path
            d="M29.0207 11.9237C29.9829 13.5274 29.7054 15.5838 28.3526 16.8751L18.7616 26.0301C17.2159 27.5056 14.7835 27.5056 13.2378 26.0301L3.64677 16.8751C2.29394 15.5838 2.01648 13.5274 2.9787 11.9237L6.1678 6.60852C6.89069 5.4037 8.19272 4.6665 9.59777 4.6665H22.4016C23.8066 4.6665 25.1087 5.4037 25.8316 6.60852L29.0207 11.9237Z"
            :fill="`url(#paint0_linear_basic_${uid})`"
        />
        <path d="M20.6663 17.3335L15.9997 22.0002L11.333 17.3335" stroke="#D8DAE8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <defs>
            <linearGradient :id="`paint0_linear_basic_${uid}`" x1="1.33301" y1="4.6665" x2="30.6663" y2="28.6665" gradientUnits="userSpaceOnUse">
                <stop stop-color="#C2C7E0" />
                <stop offset="0.4" stop-color="#A3A8C4" />
                <stop offset="0.8" stop-color="#8186A6" />
            </linearGradient>
        </defs>
    </svg>
</template>
<script setup>
const uid = ref("");

onMounted(() => {
    uid.value = Math.random().toString(36).slice(2, 10);
});
</script>
<style scoped></style>
