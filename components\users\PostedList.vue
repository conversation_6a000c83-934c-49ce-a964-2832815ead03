<template>
    <VirtualWaterfall
        v-bind="$attrs"
        v-show="showImagesList.length > 0"
        style="box-sizing: content-box; min-height: 30vh"
        class="h-full"
        padding="0 0 0 0"
        rowKey="id"
        :gap="8"
        :virtual="true"
        :items="showImagesList"
        :calcItemHeight="calcItemHeight"
        :itemMinWidth="128"
        :preload-screen-count="[1, 2]"
        :columnCount="waterfallColCount"
        :maxColumnCount="waterfallColCount"
    >
        <template #default="{ item, index }">
            <div :key="item.id" class="overflow-hidden relative h-full rounded-2xl">
                <NuxtLinkLocale :to="`/community/detail/${item.id}`" @click.stop="setShareList(item)" class="w-full h-full backdrop-blur overflow-hidden relative waterfall-item box-border block">
                    <img
                        loading="lazy"
                        :src="item.highMiniUrl || item.thumbnailUrl"
                        @error="item.loaded = true"
                        @load="item.loaded = true"
                        alt=""
                        class="w-full h-full object-cover cursor-pointer"
                        :class="{ loading_bg_anime: !item.loaded }"
                    />
                    <FeaturedTips v-if="item.featured" />
                    <div class="mask cursor-pointer">
                        <div class="flex items-center justify-end gap-4 text-text-white" @click.stop.prevent>
                            <div class="flex items-center gap-1 text-xs mt-auto">
                                <n-icon size="18">
                                    <HeartLike class="!cursor-default" />
                                </n-icon>
                                <span>{{ formatLike(item.fileLikeNums) }}</span>
                            </div>

                            <div class="flex items-center gap-1 text-xs mt-auto" @click="quickComment(item)">
                                <n-icon size="18">
                                    <IconsNoticeComments />
                                </n-icon>
                                <span>{{ formatLike(item.fileCommentNums) }}</span>
                            </div>

                            <n-dropdown :options="actionMenuOptions(item)" class="explore-more-dropdown" placement="bottom-start" trigger="click" @select="(command) => handleSelect(command, item)">
                                <n-icon size="18" class="cursor-pointer">
                                    <IconsHorMore />
                                </n-icon>
                            </n-dropdown>
                        </div>
                    </div>
                </NuxtLinkLocale>
            </div>
        </template>
    </VirtualWaterfall>
    <div class="py-20 flex justify-center" v-if="pageLoading">
        <n-icon size="48" class="text-primary-6">
            <IconsSpinLoading />
        </n-icon>
    </div>
    <Empty class="py-20" v-if="!showImagesList.length && !pageLoading" text="EMPTY_DATA"></Empty>
</template>

<script setup>
import { getCommunityPersonalImgsById, delCommunityImgById, updateCommunityPromptDisplay } from "@/api";
import { decryptResult, formatNumber } from "@/utils/tools";
import { Dele, Alert, Eye, NoEye, IconVisb, Success } from "@/icons/index.js";
import { NIcon } from "naive-ui";
import LangText from "@/components/LangText.vue";
import VirtualWaterfall from "@/components/waterfall/Waterfall.vue";
import useWindowResize from "@/hook/windowResize";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";
const localePath = useLocalePath();
import { useUserCommunity, useShareDataStore } from "@/stores";
const { isMobile } = storeToRefs(useThemeStore());

const userCommunity = useUserCommunity();
const { t } = useI18n({ useScope: "global" });

const showImagesList = ref([]);
//高度计算
const calcItemHeight = (item, w) => {
    let { width, realWidth, height, realHeight } = item;
    realWidth = realWidth || width;
    realHeight = realHeight || height;
    return w / (realWidth / realHeight);
};
const windowResize = useWindowResize();
const waterfallColCount = computed(() => {
    if (isMobile.value) return 3;

    let base = 5;
    if (windowResize.width.value <= 1660) {
        base = 4;
    }
    if (windowResize.width.value <= 1440) {
        base = 3;
    }
    if (windowResize.width.value <= 768) {
        base = 2;
    }
    return base;
});
const shareData = useShareDataStore();
const setShareList = async () => {
    shareData.setList(showImagesList.value);
};
const formatLike = computed(() => {
    return (num = 0) => formatNumber(num);
});
//快速评论
const quickComment = async (item) => {
    setShareList();
    await navigateTo({ path: localePath(`/community/detail/${item.id}`), query: { comment: 1 } });
};
//更多操作
const actionMenuOptions = computed(() => {
    return ({ publicType }) => {
        return [
            {
                label: () => h(LangText, { labelKey: "COMMUNITY_SHOW_PROMPT_TITLE" }),
                icon() {
                    return h(NIcon, null, {
                        default: () => h(IconVisb),
                    });
                },
                key: "visibility",
                children: [
                    {
                        label: () =>
                            h("div", { class: "flex items-center justify-between gap-2 min-w-32" }, [
                                h(LangText, { labelKey: "SHORT_VISIBILITY_PUBLIC" }),
                                publicType == "everyone"
                                    ? h(
                                          NIcon,
                                          { size: 24 },
                                          {
                                              default: () => h(Success),
                                          }
                                      )
                                    : null,
                            ]),
                        key: "everyone",
                        icon() {
                            return h(NIcon, null, {
                                default: () => h(Eye),
                            });
                        },
                    },
                    {
                        label: () =>
                            h("div", { class: "flex items-center justify-between gap-2 min-w-32" }, [
                                h(LangText, { labelKey: "SHORT_VISIBILITY_HIDE" }),
                                publicType == "myself"
                                    ? h(
                                          NIcon,
                                          { size: 24 },
                                          {
                                              default: () => h(Success),
                                          }
                                      )
                                    : null,
                            ]),
                        key: "myself",
                        icon() {
                            return h(NIcon, null, {
                                default: () => h(NoEye),
                            });
                        },
                    },
                ],
            },
            {
                label: () => h(LangText, { labelKey: "TOOLBAR_DELETE", class: "text-text-drop-5" }),
                icon() {
                    return h(
                        NIcon,
                        { class: "text-text-drop-5" },
                        {
                            default: () => h(Dele),
                        }
                    );
                },
                key: "delete",
                props: { class: "danger-bg" },
            },
        ];
    };
});

const handleSelect = async (key, row) => {
    if (key === "delete") {
        handleDel(row);
        return;
    }
    let id = row.id;
    const { status, message } = await updateCommunityPromptDisplay({ commFileId: id, publicType: key });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    showImagesList.value = showImagesList.value.map((item) => {
        if (item.id === id) {
            item.publicType = key;
        }
        return item;
    });
};

//删除当前图片
const handleDel = (item) => {
    const { showMessage } = useModal();
    showMessage({
        style: { width: "480px" },
        confirmBtn: t("COMMON_BTN_CONTINUE"),
        content: h("div", null, [h("p", null, t("SHORT_DEL_MESSAGE"))]),
        icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_ATTEN"),
    }).then(async () => {
        const commFileId = item.id;
        const { status, message } = await delCommunityImgById({ commFileId });
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        showImagesList.value = showImagesList.value.filter((item) => item.id !== commFileId);
        userCommunity.updateCommunityUser({ posted: Math.max(0, userCommunity.communityUser.posted - 1) });
    });
};

const pageLoading = ref(false);
const pages = ref({
    pageNum: 0,
    pageSize: 50,
    isDone: false,
});
const loadMoreData = async () => {
    console.log("loadMoreData");
    if (pageLoading.value) {
        return;
    }
    let { pageNum, pageSize, isDone, lastFileId = "" } = pages.value;
    if (isDone) {
        return;
    }
    pageNum += 1;
    pageLoading.value = true;
    //游标分页  当前数据最后一条数据ID
    if (pageNum === 1) {
        lastFileId = "";
    }
    const { status, data, message } = await getCommunityPersonalImgsById({ pageSize, lastFileId });
    pageLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    pages.value.lastFileId = data.lastId;
    const list = (decryptResult(data.encryptResult) || []).map((item) => {
        const genInfo = JSON.parse(item.genInfo) || {};
        const { resolution = {} } = genInfo;
        return {
            ...item,
            width: resolution.width,
            height: resolution.height,
        };
    });
    console.log(list);
    if (pageNum === 1) {
        showImagesList.value = list;
    } else {
        showImagesList.value = [...showImagesList.value, ...list];
    }
    pages.value.isDone = list.length < pageSize;
    pages.value.pageNum = pageNum;
};
loadMoreData();

defineExpose({ loadMoreData });
</script>

<style lang="scss" scoped>
.waterfall-item {
    &::after {
        @apply absolute top-0 right-0 bottom-0 left-0 z-0  opacity-0 pointer-events-none;
        content: "";
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.7) 100%);
    }
    &:hover {
        &::after {
            opacity: 1;
        }
        .mask {
            display: flex;
        }
    }
}

.mask {
    @apply absolute top-0 right-0 bottom-0 left-0 p-2 z-10 hidden flex-col justify-end text-dark-active-text;
}

.hover-action-btn {
    @apply bg-white/30 w-9 h-9 rounded-full backdrop-blur flex items-center justify-center cursor-pointer hover:text-dark-active-text;
}
:global(.explore-more-dropdown),
:global(.explore-more-dropdown .n-dropdown-menu) {
    @apply min-w-[200px];
    padding: 8px !important;
}

:global(.explore-more-dropdown .n-dropdown-option-body) {
    @apply px-0;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before) {
    left: 0 !important;
    right: 0 !important;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending.danger-bg::before) {
    background-color: var(--p-fill-drop-5);
}
</style>
