<!--
 * @Author: HuangQS
 * @Description: 自适应弹出组件，期望在移动端、网页端中实现相同逻辑，完成
 * @Date: 2025-05-16 13:51:44
 * @LastEditors: <PERSON><PERSON><PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-07-16 10:44:16
-->
<template>
    <template v-if="isMobile">
        <!-- -------------------------------- 移动端  -------------------------------- -->
        <n-drawer
            style="--n-header-padding: 8px 16px; --n-body-padding: 0 16px; --n-border-radius: 16px; --n-header-border-bottom: 0px; --n-color: var(--p-bg-2)"
            :z-index="mobilePage ? 21 : 2000"
            :style="mobilePage ? '--n-border-radius: 0px' : ''"
            v-model:show="active"
            width="100vw"
            :height="`calc(100vh - ${mobilePage ? 0 : 112}px)`"
            :placement="mobilePage ? 'right' : `bottom`"
            :mask-closable="false"
            :trap-focus="false"
            :block-scroll="true"
        >
            <n-drawer-content>
                <template #header>
                    <div class="title-bar"></div>
                    <div class="grid grid-cols-[1fr_2fr_1fr] justify-between items-center mb-4">
                        <div class="size-8 flex justify-center items-center" :class="mobilePage ? '' : 'rounded-full bg-fill-ww-1'">
                            <n-icon size="24" @click="handleTitleLLClick">
                                <IconsArrowLeft v-if="mobilePage" />
                                <IconsClose v-else />
                            </n-icon>
                        </div>
                        <div class="text-text-2 text-center text-base font-medium">{{ title }}</div>
                        <div class="text-primary-6 text-base flex justify-end items-center" @click="handleTitleRRClick">
                            <slot v-if="$slots.titlerr" name="titlerr" />
                            <span v-else>{{ rrTitle ?? "" }}</span>
                        </div>
                    </div>
                </template>
                <!-- 内容 -->
                <slot name="content" />
            </n-drawer-content>
        </n-drawer>
    </template>
    <template v-else-if="!isMobile">
        <!-- -------------------------------- 网页端  -------------------------------- -->
        <slot name="content" />
    </template>
</template>

<script setup>
import { computed, onMounted, ref, useTemplateRef, watchEffect } from "vue";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());
const emits = defineEmits(["update:active", "titleLLClick", "titleRRClick"]);

const props = defineProps({
    title: {
        type: String,
        default: "",
    },
    rrTitle: {
        type: String,
        default: "",
    },
    active: {
        type: Boolean,
        default: false,
    },
    // 是否为页面组件,当为true时,组件风格将发生改变
    mobilePage: {
        type: Boolean,
        default: false,
    },
});

const { title, active, rrTitle } = toRefs(props);

// 标题左侧按钮被点击
const handleTitleLLClick = () => {
    emits("titleLLClick");
};

// 标题右侧按钮被点击
const handleTitleRRClick = () => {
    if (rrTitle.value) {
        emits("titleRRClick");
    }
};

onDeactivated(() => {
    // active.value =false
    emits("update:active", false);
});

// watchEffect(() => {
//     console.log("refresh --->", isMobile.value);
//     // if (!isMobile.value) {
//     //     emits("update:active", false);
//     // }
// });
</script>

<style lang="scss" scoped></style>
