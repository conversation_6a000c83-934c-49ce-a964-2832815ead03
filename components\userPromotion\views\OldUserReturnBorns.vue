<!--
 * @Author: HuangQS
 * @Description: 老用户回归奖励活动
 * @Date: 2025-07-08 10:40:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-17 17:02:27
-->

<template>
    <!-- 网页端 -->
    <template v-if="!isMobile">
        <div class="flex flex-col w-full items-center cursor-pointer">
            <div class="flex flex-col justify-center relative w-[204px]" @click="handleViewClick">
                <img src="@/assets/images/userPromotion/old_user_return_borns_bg.webp" class="object-cover pointer-events-none" />

                <div class="absolute left-0 right-0 bottom-0 text-white text-xs font-medium flex flex-col w-full h-[96px] py-[13px] px-2 gap-2">
                    <div class="text-sm w-full text-center">{{ t("USER_RETURN_BORNS.OLD_USER_RETURN_BORNS_TITLE") }}</div>
                    <ul class="list-disc pl-5 gap-1">
                        <li class="w-full text-start">{{ t("USER_RETURN_BORNS.OLD_USER_RETURN_BORNS_DEST_1") }}</li>
                        <li class="w-full text-start">{{ t("USER_RETURN_BORNS.OLD_USER_RETURN_BORNS_DEST_2") }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </template>
    <!-- 移动端 -->
    <template v-else>
        <div v-if="isVisibleView" class="relative">
            <n-icon :size="20" class="absolute right-1 -top-3" @click.stop="handleCloseMobileWindow">
                <IconsCloseVipTrial />
            </n-icon>
            <div @click="handleViewClick">
                <img class="w-[97px] h-[74px] aspect-square" src="@/assets/images/userPromotion/old_user_return_borns_bg_mo.webp" />
            </div>
        </div>
    </template>
</template>

<script setup>
import { useThemeStore } from "@/stores/system-config";
import { useSubscribeModal } from "@/hook/subscribe";
import { storeToRefs } from "pinia";

const { t } = useI18n({ useScope: "global" });
const { isMobile } = storeToRefs(useThemeStore());
const { openModal } = useSubscribeModal(); // 引入订阅弹窗
const isVisibleView = ref(true);

const handleViewClick = () => {
    // 唤起支付弹窗
    openModal({ triggerEl: "old_vip_back" });
    customTrackEvent("expired_member_left_corner");
    window.trackEvent("Subscribe", { el: "subscribe_from=expired_member_popup" });
};

const handleCloseMobileWindow = () => {
    // 关闭移动端窗口
    isVisibleView.value = false;
};

const customTrackEvent = (el) => {
    window.trackEvent("Commercialization", { el });
};
</script>
<style lang="scss" scoped></style>
