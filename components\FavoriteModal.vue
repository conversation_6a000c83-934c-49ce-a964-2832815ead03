<template>
    <div>
        <div class="text-xl font-semibold px-4 pt-2 pb-6 flex items-center justify-between">
            <span>{{ title || t("MENU_COLLECTION_TIPS") }}</span>

            <n-icon size="24" class="cursor-pointer" @click="handleCancel">
                <IconsClose />
            </n-icon>
        </div>
        <n-virtual-list ref="virtualListInst" class="h-72 pr-4" :item-size="48" :items="options">
            <template #default="{ item, index }">
                <div
                    class="h-11 flex items-center gap-2 px-3 py-2 mt-1 rounded-md hover:bg-fill-drop-2 cursor-pointer"
                    :class="{ 'text-text-drop-3 hover:bg-transparent': disabledItem(item.key) }"
                    :key="item.key"
                    @click="chooseCollect(item.key)"
                >
                    <n-icon size="20"><IconsBookMark /> </n-icon>
                    <span>{{ item.collectName }}</span>
                    <n-icon v-if="classifyId === item.key" size="20" class="ml-auto"><IconsSuccess /></n-icon>
                </div>
            </template>
        </n-virtual-list>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
import { useShareCollect } from "@/stores";
import { useInitCollection } from "@/hook/updateAccount";
const shareCollect = useShareCollect();
const classifyId = ref("");
const props = defineProps({
    title: {
        type: String,
        default: "",
    },
    subTitle: {
        type: String,
        default: "",
    },
    filterId: {
        type: [String, Number, undefined, null],
        default: "",
    },
});
const virtualListInst = ref(null);
const options = computed(() => {
    return shareCollect.collectList.map((item) => ({ key: item.id, collectName: item.collectName }));
});
const disabledItem = computed(() => {
    return (id) => id === props.filterId;
});
onMounted(() => {
    if (options.value.length === 0) {
        useInitCollection();
    }
    classifyId.value = props.filterId || options.value[0]?.key;
    nextTick(() => {
        virtualListInst.value?.scrollTo({
            key: classifyId.value,
            behavior: "smooth",
        });
    });
});
const emits = defineEmits(["update:base", "cancel"]);
const handleCancel = () => {
    emits("cancel", false);
};
const chooseCollect = (id) => {
    if (!disabledItem.value(id)) {
        classifyId.value = id;
    }
};
onBeforeUnmount(() => {
    emits("update:base", { classifyId: classifyId.value });
});
</script>
