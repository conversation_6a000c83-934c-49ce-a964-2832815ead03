<template>
    <MobileHeader :title="t('ACCOUNT_SETTINGS')">
        <template #right>
            <div class="flex items-center justify-end gap-2">
                <n-icon class="text-text-2" size="20" @click="handleUserExit">
                    <IconsMenuLogout />
                </n-icon>
            </div>
        </template>
        <template #extra>
            <div class="h-8 px-6 pt-3 bg-bg-1">
                <div class="h-full flex gap-8 relative">
                    <div
                        v-for="(item, index) in tabList"
                        :key="item.key"
                        ref="tabRef"
                        :class="{ '!text-text-2': tabIndex === index }"
                        class="h-full flex items-center cursor-pointer select-none text-sm text-text-4 font-medium"
                        @click="handleTabChange((tabIndex = index))"
                    >
                        {{ $t(item.label) }}
                    </div>
                    <div ref="tabSlideRef" class="absolute bottom-0 h-0.5 bg-primary-6 transition-all rounded"></div>
                </div>
            </div>
        </template>
    </MobileHeader>

    <div class="w-full h-full max-md:pb-36 text-xs dark:text-dark-text" :class="isMobile && 'overflow-y-auto'">
        <div v-if="!isMobile" class="sticky top-0 z-50 border-b border-solid dark:border-dark-bg-2 border-black/10 bg-bg-2">
            <div class="flex items-center gap-4 h-[60px] text-sm">
                <n-icon class="ml-4" size="24">
                    <IconsSetting />
                </n-icon>
                <span>{{ $t("ACCOUNT_SETTINGS") }}</span>
            </div>

            <div class="h-8 px-6 mt-3 md:mt-0">
                <div class="h-full flex gap-8 relative">
                    <div
                        v-for="(item, index) in tabList"
                        :key="item.key"
                        ref="tabRef"
                        :class="{ '!text-text-2': tabIndex === index }"
                        class="h-full flex items-center cursor-pointer select-none text-sm text-text-4 font-medium"
                        @click="handleTabChange((tabIndex = index))"
                    >
                        {{ $t(item.label) }}
                    </div>
                    <div ref="tabSlideRef" class="absolute bottom-0 h-0.5 bg-primary-6 transition-all rounded"></div>
                </div>
            </div>
        </div>

        <!-- 用户信息 -->
        <BaseInfoPanel v-show="tabIndex === 0" class="px-4 pt-6 md:m-6 md:bg-bg-2 md:rounded-lg" />

        <!-- 系统设置 -->
        <SettingPanel v-show="tabIndex === 1" class="px-4 py-6 md:m-6 md:bg-bg-2 md:rounded-lg" />
    </div>
</template>

<script setup>
definePageMeta({
    ssr: false,
});
const { t } = useI18n({ useScope: "global" });
useSeoMeta({
    title: () => t("SEO_META.SEO_ACCOUNT_TITLE"),
    ogTitle: () => t("SEO_META.SEO_ACCOUNT_TITLE"),
    description: () => t("SEO_META.SEO_ACCOUNT_DESC"),
    ogDescription: () => t("SEO_META.SEO_ACCOUNT_DESC"),
});

import { NIcon } from "naive-ui";
import { useThemeStore } from "@/stores/system-config";
import BaseInfoPanel from "@/components/users/BaseInfoPanel.vue";
import SettingPanel from "@/components/users/SettingPanel.vue";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";
import { storeToRefs } from "pinia";
import { useLogout } from "@/hook/updateAccount";

const { isMobile } = storeToRefs(useThemeStore());

const tabIndex = ref(0);
const tabList = ref([
    { label: "BASE_INFO", key: "basic" },
    { label: "SHORT_SETTING", key: "setting" },
]);
const handleTabChange = (index) => {
    tabIndex.value = index;
    updateSlide(index, true);
};

const tabRef = ref();
const tabSlideRef = ref();
const updateSlide = (index, transition = false) => {
    tabSlideRef.value.style.transition = transition ? "all 0.3s" : "none";
    tabSlideRef.value.style.width = tabRef.value[index].offsetWidth + "px";
    tabSlideRef.value.style.transform = "translateX(" + tabRef.value[index].offsetLeft + "px)";
};

const route = useRoute();
onMounted(() => {
    // tabIndex.value = route.params.name === "setting" ? 1 : 0;
    updateSlide(tabIndex.value, false);
});

const handleUserExit = () => {
    useLogout();
};
</script>

<style lang="scss" scoped>
//文本换行
.switch-slot {
    @apply mt-2 relative cursor-pointer h-10 dark:bg-dark-bg-2 bg-neutral-100 rounded-lg p-1 flex items-center gap-1.5 shadow-sm w-full;
}
.switch-slot::after {
    content: "";
    position: absolute;
    left: var(--left);
    right: var(--right);
    top: 4px;
    z-index: 0;
    bottom: 4px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
}
.dark .switch-slot::after {
    background: #808080;
}
.switch-item {
    @apply relative z-10 w-1/3 flex items-center justify-center rounded h-full;
}
.form-item::after {
    @apply absolute left-1 bottom-3 text-xs text-error;
    content: var(--error-msg);
}
.shad {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
}
.enter-anime {
    animation: enter 0.15s ease-in;
}
@keyframes enter {
    0% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}
.custom-input-border {
    @apply dark:bg-dark-bg-2 bg-neutral-100 h-10 rounded-lg border border-solid flex-1 max-w-80 border-neutral-100 dark:border-dark-bg-2  dark:focus-within:border-primary focus-within:border-primary px-3 dark:focus-within:bg-dark-bg-2 dark:hover:bg-dark-bg-2 flex items-center gap-2;
}
</style>
