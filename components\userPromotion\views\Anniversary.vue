<template>
    <!-- 网页端 -->
    <template v-if="!isMobile">
        <NuxtLinkLocale to="/user/subscribe?from=anniversary_card" class="flex flex-col w-full items-center cursor-pointer" @click="handleViewClick">
            <div class="flex flex-col justify-center relative w-[204px]">
                <img v-if="!isIn48Hour" src="@/assets/images/anniversary/img_anniversary_banner_bg.webp" class="object-cover pointer-events-none" />
                <img v-else src="@/assets/images/anniversary/img_anniversary_banner_bg_48.webp" class="object-cover pointer-events-none" />

                <div v-if="isIn48Hour" class="absolute text-[#F54032] text-sm font-semibold flex w-full justify-center gap-[3px]">
                    <div class="w-[26px] h-[22px] p-1 flex items-center justify-center bg-[#FFCD01] rounded-[4px]">{{ anniversaryHoursStr }}</div>
                    <div>:</div>
                    <div class="w-[26px] h-[22px] p-1 flex items-center justify-center bg-[#FFCD01] rounded-[4px]">{{ anniversaryMinutesStr }}</div>
                    <div>:</div>
                    <div class="w-[26px] h-[22px] p-1 flex items-center justify-center bg-[#FFCD01] rounded-[4px]">{{ anniversarySecondsStr }}</div>
                </div>

                <div class="absolute left-0 right-0 bottom-0 text-white text-xs font-medium flex flex-col w-full h-[96px] py-[13px] px-2 gap-2">
                    <div class="text-sm w-full text-center">{{ t("ANNIVERSARY_BANNER.CARD_TITLE") }}</div>
                    <ul class="gap-1">
                        <li class="w-full text-xs text-center">{{ t("ANNIVERSARY_BANNER.CARD_DESC") }}</li>
                    </ul>
                </div>
            </div>
        </NuxtLinkLocale>
    </template>
    <!-- 移动端 -->
    <template v-else>
        <NuxtLinkLocale v-if="isVisibleView" to="/user/subscribe" class="relative flex flex-col items-center" @click="handleViewClick">
            <n-icon :size="20" class="absolute right-3 -top-2" @click.stop="handleCloseMobileWindow">
                <IconsCloseVipTrial />
            </n-icon>
            <div>
                <img class="ml-1 w-[58px] h-[67px] aspect-square" src="@/assets/images/anniversary/img_anniversary_banner_bg_mo.webp" />
            </div>
            <div class="flex items-center justify-center text-white bg-gradient-to-r from-[#9457F5] to-[#FF5781] py-1 px-2 rounded-2xl -mt-2.5" :class="isIn48Hour && 'w-[96px]'">
                <span v-if="!isIn48Hour">{{ t("ANNIVERSARY_BANNER.CARD_TITLE") }}</span>
                <span v-else>{{ anniversaryHoursStr }} : {{ anniversaryMinutesStr }} : {{ anniversarySecondsStr }}</span>
            </div>
        </NuxtLinkLocale>
    </template>
</template>

<script setup>
import { useThemeStore } from "@/stores/system-config";
import { useSubscribeModal } from "@/hook/subscribe";
import { storeToRefs } from "pinia";
import { useSubscribeStore } from "@/stores/subscribe";
const { toSubAnniversaryCountDownTimer, toUnSubAnniversaryCountDownTimer } = useSubscribeStore();
const { isIn48Hour, anniversaryHoursStr, anniversaryMinutesStr, anniversarySecondsStr } = storeToRefs(useSubscribeStore());

// 倒计时
const hoursStr = ref("00");
const minutesStr = ref("00");
const secondsStr = ref("00");

const countdownTimer = ref(null);
const refreshCountdown = () => {
    const { days, hours, minutes, seconds } = getCountDownTime(anniversaryEndTime.value);
    hoursStr.value = countDonwTimeFormat(days * 24 + hours);
    minutesStr.value = countDonwTimeFormat(minutes);
    secondsStr.value = countDonwTimeFormat(seconds);
};
onMounted(() => {
    toSubAnniversaryCountDownTimer("left_card_anniversary");
});
onBeforeUnmount(() => {
    toUnSubAnniversaryCountDownTimer("left_card_anniversary");
});

const { t } = useI18n({ useScope: "global" });
const { isMobile } = storeToRefs(useThemeStore());

const isVisibleView = ref(true);
const handleViewClick = async () => {
    // openModal({ triggerEl: "anniversary_left" });

    openBirthdayModal();
};

const openBirthdayModal = () => {
    customTrackEvent("anniversary_left_corner");
};

const handleCloseMobileWindow = () => {
    // 关闭移动端窗口
    isVisibleView.value = false;
};

const customTrackEvent = (el) => {
    window.trackEvent("Commercialization", { el });
};
</script>
<style lang="scss" scoped>
// border-radius: 16px;
// background: linear-gradient(98deg, #9457F5 0%, #FF5781 100.9%);
</style>
