<template>
    <template v-if="isMobile">
        <MobileHeader
            :disableBack="true"
            @onCalcFixedHeight="
                (height) => {
                    headerHeight = height;
                }
            "
        >
            <template #left>
                <div class="flex row-1 items-center text-text-1">
                    <n-icon size="42"> <IconsPiclumen /> </n-icon>
                    <span>PicLumen</span>
                </div>
            </template>

            <template #extra>
                <HeaderSearch
                    v-bind:search-param="searchParam"
                    v-bind:is-loading="isLoading"
                    :absTopHeihgt="headerHeight"
                    @whenChangeTag="
                        () => {
                            dataList = [];
                        }
                    "
                    @whenSearch="
                        () => {
                            page = {
                                pageNum: 0,
                                pageSize: 50,
                                lastId: '',
                                isDone: false,
                            };
                            showPreview = false;
                            loadMore();
                        }
                    "
                />
            </template>
        </MobileHeader>
    </template>

    <div class="relative w-full h-full bg-bg-1">
        <div class="h-full" :style="scrollAreaStyle" v-show="!showPreview">
            <div ref="virtualRef" class="scroll-box overflow-hidden h-full relative overflow-y-scroll" tabindex="0" @scroll="scrollLoadMore">
                <div v-if="!isMobile" class="top-bar font-medium">
                    <div class="p-4">
                        <div class="w-full text-sm text-text-2 mb-5 ml-0.5">{{ t("COMMON_SORT_EXPLORE_TITLE") }}</div>
                        <HeaderSearch
                            v-bind:search-param="searchParam"
                            v-bind:is-loading="isLoading"
                            @whenChangeTag="
                                () => {
                                    dataList = [];
                                }
                            "
                            @whenSearch="
                                () => {
                                    page = {
                                        pageNum: 0,
                                        pageSize: 50,
                                        lastId: '',
                                        isDone: false,
                                    };
                                    showPreview = false;
                                    loadMore();
                                }
                            "
                        >
                        </HeaderSearch>
                    </div>
                </div>

                <VirtualWaterfall
                    class="bg-bg-1"
                    v-show="dataList.length > 0"
                    :key="page.searchId"
                    padding="16px 16px 60px 16px"
                    style="box-sizing: content-box; min-height: 30vh"
                    rowKey="id"
                    :gap="8"
                    :virtual="true"
                    :items="dataList"
                    :calcItemHeight="calcItemHeight"
                    :itemMinWidth="128"
                    :preload-screen-count="[1, 2]"
                    :columnCount="waterfallColCount"
                    :maxColumnCount="waterfallColCount"
                    :cross-columns="(item) => item.columnSpan || 1"
                >
                    <template #default="{ item, index }">
                        <div :key="item.id" class="overflow-hidden relative h-full rounded-lg">
                            <CommunityAd v-if="item.isAd" :adList="item.list" />
                            <div v-else class="h-full">
                                <NuxtLinkLocale
                                    :to="`/community/detail/${item.id}`"
                                    v-if="!item.reported"
                                    class="w-full h-full backdrop-blur overflow-hidden relative waterfall-item box-border block"
                                    @click="setShareList()"
                                >
                                    <img
                                        loading="lazy"
                                        :src="item.highMiniUrl || item.thumbnailUrl"
                                        @error="item.loaded = true"
                                        @load="item.loaded = true"
                                        class="w-full h-full object-contain cursor-pointer"
                                        :class="{ loading_bg_anime: !item.loaded }"
                                    />
                                    <div class="mask">
                                        <div v-if="item.publicType !== 'myself'" class="prompt-text">{{ item.prompt }}</div>
                                        <div v-else class="flex flex-col items-center text-center">
                                            <n-icon size="18" class="text-info-6">
                                                <IconsLock />
                                            </n-icon>
                                            <div>{{ t("COMMUNITY_AUTHOR_HIDE_PROMPT") }}</div>
                                        </div>

                                        <div class="flex items-center h-6 gap-1 overflow-hidden shrink-0" @click.stop.prevent>
                                            <div class="flex items-center cursor-pointer gap-1 overflow-hidden" @click="useToCommunityHome(item.accountInfo.userId)">
                                                <n-avatar v-if="item.accountInfo.userAvatarUrl" round :size="24" class="shrink-0" :src="item.accountInfo.userAvatarUrl"></n-avatar>
                                                <n-icon v-else :size="24" class="shrink-0">
                                                    <IconsPerson />
                                                </n-icon>
                                                <div class="flex-1 mr-1 overflow-hidden whitespace-nowrap text-ellipsis text-xs text-dark-active-text">
                                                    {{ item.accountInfo.userName || "" }}
                                                </div>
                                            </div>
                                            <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-auto" @click="handleLike(item)">
                                                <n-icon size="18" class="text-dark-active-text">
                                                    <HeartLike :checked="item.liked" />
                                                </n-icon>
                                                <span class="text-dark-active-text text-xs">{{ formatLike(item.fileLikeNums) }}</span>
                                            </div>
                                            <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-3" @click="quickComment(item)">
                                                <n-icon size="18" class="text-dark-active-text">
                                                    <IconsNoticeComments />
                                                </n-icon>
                                                <span class="text-dark-active-text text-xs">{{ formatLike(item.fileCommentNums) }}</span>
                                            </div>
                                            <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-3">
                                                <n-dropdown
                                                    :options="moreActionList(item)"
                                                    :render-label="renderDropdownLabel"
                                                    class="explore-more-dropdown"
                                                    placement="bottom"
                                                    trigger="hover"
                                                    @select="checkMoreAction($event, item)"
                                                >
                                                    <n-icon class="text-dark-active-text rotate-90" size="18">
                                                        <IconsMore />
                                                    </n-icon>
                                                </n-dropdown>
                                            </div>
                                        </div>
                                    </div>
                                </NuxtLinkLocale>
                                <div
                                    v-else
                                    class="absolute top-0 left-0 right-0 bottom-0 bg-white dark:bg-dark-bg-2 flex flex-col items-center justify-center dark:text-dark-text text-black opacity-70"
                                >
                                    <n-icon size="24">
                                        <IconsNoEye />
                                    </n-icon>
                                    <p class="mt-4">{{ t("COMMUNITY_REPORT_IMG_TXT") }}</p>
                                </div>
                            </div>
                        </div>
                    </template>
                </VirtualWaterfall>
                <div v-if="isLast" class="text-center mt-auto">
                    <span class="text-black/40 dark:text-dark-text/40 end-msg">{{ t("SHORT_NO_MORE") }}</span>
                </div>
                <div v-if="isLoading" class="sticky left-0 bottom-0 right-0 py-10 flex items-center justify-center">
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </div>
                <div v-if="dataList.length == 0 && !isLoading" class="flex items-center justify-center flex-col h-3/5 text-black/40 dark:text-dark-text/40">
                    <img v-show="noData('search')" class="dark:block hidden" src="@/assets/images/search_empty_dark.svg" alt="" />
                    <img v-show="noData('search')" class="dark:hidden block" src="@/assets/images/search_empty_light.svg" alt="" />
                    <img v-show="noData('pull')" class="dark:block hidden" src="@/assets/images/data_empty_dark.svg" alt="" />
                    <img v-show="noData('pull')" class="dark:hidden block" src="@/assets/images/data_empty_light.svg" alt="" />
                    <span class="mt-4 opacity-70">{{ noData("search") ? t("NO_SUCH_RES") : t("EMPTY_DATA") }}</span>
                </div>
            </div>
        </div>
        <Questionnaire />

        <div
            v-if="!isMobile && virtualRefScrollTop > 200"
            @click="gotoTop"
            class="scroll-to-top w-12 h-12 rounded-full fixed right-9 bottom-6 bg-bg-5 shadow-[0px 4px 8px -2px rgba(0, 0, 0, 0.12), 0px 8px 24px 0px rgba(0, 0, 0, 0.16)] flex items-center justify-center text-text-1 text-2xl cursor-pointer"
        >
            <IconsArrowTop />
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
const updateSeo = () => {
    useSeoMeta({
        title: () => t("SEO_META.SEO_EXPLORE_TITLE"),
        ogTitle: () => t("SEO_META.SEO_EXPLORE_TITLE"),
        description: () => t("SEO_META.SEO_EXPLORE_DESC"),
        ogDescription: () => t("SEO_META.SEO_EXPLORE_DESC"),
    });
};

import { addLikeByFileId, reduceLikeByFileId, getCommunityPage, getCommunityBanner } from "@/api";
import useWindowResize from "@/hook/windowResize";
import { useToCommunityHome, useReportContent } from "@/hook/updateAccount";
import { useRecordCommunityScore } from "@/hook/useCommon";
import { debounce, isScrolledToBottom, formatPonyV6Prompt, formatNumber, decryptResult, copyToClipboard, createLocaleUrl } from "@/utils/tools";
import { KEEPALIVE_PAGES, DEFAULT_LANG_LIST } from "@/utils/constant";
import { NIcon } from "naive-ui";
import { useUserProfile, useForceUpdatePageState, useShareDataStore } from "@/stores";
import { useThemeStore } from "@/stores/system-config";
import { useRemix } from "@/hook/create";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";
import HeaderSearch from "@/components/mobile/explore/HeaderSearch.vue";
import { useCurrentTheme } from "@/stores/system-config";
import { storeToRefs } from "pinia";
import { useSyncAction } from "@/stores/syncAction";
const syncAction = useSyncAction();
const { recordCommunityScore } = useRecordCommunityScore();
const { setShowMoDrawer } = useCurrentTheme();

import VirtualWaterfall from "@/components/waterfall/Waterfall.vue";

import { ShareLink, Alert, IconCreate, CopyText } from "@/icons/index.js";
import { onMounted } from "vue";
defineOptions({
    name: KEEPALIVE_PAGES.COMMUNITY_EXPLORE,
});
const windowResize = useWindowResize();
const { user } = useUserProfile();
const { isMobile, bannerAreaHeight } = storeToRefs(useThemeStore());

const scrollAreaStyle = computed(() => {
    if (isMobile.value) {
        return `height:calc(100dvh - ${headerHeight.value}px - ${bannerAreaHeight.value}px)`;
    }

    return "";
});

const forceUpdatePageState = useForceUpdatePageState();
const headerHeight = ref(0);

const route = useRoute();

const initPageState = () => {
    showPreview.value = false;
    dataList.value = [];
    searchParam.value.vagueKey = "";
    searchParam.value.tags = "";
    searchParam.value.collationName = route.query?.tab || "Featured";
    viewItem.value = {};
    isLoading.value = false;
    handleSearch();
};

const dataList = ref([]);
const virtualRef = ref(null);
const searchParam = ref({
    collationName: route.query?.tab || "Featured",
    tags: "",
    vagueKey: "",
});

//高度计算
const calcItemHeight = (item, w) => {
    let { width, realWidth, height, realHeight } = item;
    realWidth = realWidth || width;
    realHeight = realHeight || height;
    return w / (realWidth / realHeight);
};
const waterfallColCount = computed(() => {
    let base = 5;
    if (windowResize.width.value <= 1660) {
        base = 4;
    }
    if (windowResize.width.value <= 1440) {
        base = 3;
    }
    if (windowResize.width.value <= 768) {
        base = 2;
    }
    return base;
});
const formatLike = computed(() => {
    return (num = 0) => formatNumber(num);
});
//关键词模糊匹配
const handleSearch = () => {
    if (isLoading.value) {
        return;
    }
    page.value = {
        pageNum: 0,
        pageSize: 50,
        lastId: "",
        isDone: false,
    };
    showPreview.value = false;
    loadMore();
};

//已经到底了
const isLast = computed(() => {
    const { isDone } = page.value;
    return isDone && dataList.value.length > 0;
});
//查看详情
const showPreview = ref(false);
//当前预览数据
const viewItem = ref(null);
const shareData = useShareDataStore();
const setShareList = () => {
    shareData.setList(dataList.value.filter((item) => !item.isAd));
};
const localePath = useLocalePath();
//快速评论
const quickComment = async (item) => {
    setShareList();
    await navigateTo({ path: localePath(`/community/detail/${item.id}`), query: { comment: 1 } });
};

// 触发Google事件
const trackEvents = (event, el) => {
    try {
        window.trackEvent(event, { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};
const renderIcon = (icon, props) => {
    return () => h(NIcon, { size: 20, ...props }, { default: () => h(icon) });
};
const moreActionList = (item) => {
    if (!item.isOwn) {
        return [
            { label: "COPY_PROMPT", key: "COMMON_PROMPT", icon: renderIcon(CopyText), disabled: !item.showPrompt },
            { label: "TOOLBAR_REMIX", key: "TOOLBAR_REMIX", icon: renderIcon(IconCreate), disabled: !item.showPrompt },
            { label: "TOOLBAR_COPY_LINK", key: "TOOLBAR_COPY_LINK", icon: renderIcon(ShareLink) },
            { label: "REPORT", key: "REPORT", icon: renderIcon(Alert, { class: "text-text-drop-5" }), props: { class: "danger-bg" } },
        ];
    }
    return [
        { label: "COPY_PROMPT", key: "COMMON_PROMPT", icon: renderIcon(CopyText), disabled: !item.showPrompt },
        { label: "TOOLBAR_REMIX", key: "TOOLBAR_REMIX", icon: renderIcon(IconCreate), disabled: !item.showPrompt },
        { label: "TOOLBAR_COPY_LINK", key: "TOOLBAR_COPY_LINK", icon: renderIcon(ShareLink) },
    ];
};
const renderDropdownLabel = (option) => {
    if (option.key === "REPORT") {
        return h("span", { class: "text-text-drop-5" }, { default: () => t(option.label) });
    } else {
        return t(option.label);
    }
};
// 选择选项
const checkMoreAction = (key, item) => {
    switch (key) {
        case "COMMON_PROMPT":
            copyToClipboard(item.prompt);
            openToast.success(t("TOAST_COPY_SUCCESS"));
            trackEvents("Community", "thumbnail_more=copy_prompt");
            break;
        case "TOOLBAR_REMIX":
            recordCommunityScore(item.fileId, "remix");
            useRemix(item);
            trackEvents("Community", "thumbnail_more=remix");
            break;
        case "TOOLBAR_COPY_LINK":
            let href = window.location.host + `/app/community/detail/${item.id}`;
            copyToClipboard(href);
            openToast.success(t("TOAST_COPY_SUCCESS"));
            trackEvents("Community", "thumbnail_more=copy_link");
            break;
        case "REPORT":
            useReportContent({ type: "GEN_CONTENT", commFileId: item.id }).then((res) => {
                if (res !== "cancel") {
                    viewItem.value = item;
                    handleUpdateReportState();
                }
            });
            trackEvents("Community", "thumbnail_more=report");
            break;
    }
};

//分页信息
const page = ref({
    pageNum: 0,
    pageSize: 50,
    isDone: false,
    lastId: "",
});

const noData = computed(() => {
    return (key) => {
        let { pageNum = 1 } = page.value;
        if (searchParam.value.vagueKey.trim() && pageNum === 1 && !isLoading.value) {
            return "search" === key;
        }
        return key === "pull";
    };
});

const isLoading = ref(false);
//加载下一页
const loadMore = async () => {
    if (isLoading.value) {
        return;
    }

    let { pageNum = 0, pageSize = 50, isDone = false, lastId = "", lastScoreIndex = "" } = page.value;
    if (isDone) {
        return;
    }
    isLoading.value = true;
    pageNum += 1;
    if (pageNum === 1) {
        dataList.value = [];
    }
    let idParam = {};
    if (searchParam.value.collationName === "Likes") {
        idParam = { lastLikeId: lastId };
    } else {
        idParam = { lastFileId: lastId };
    }
    const { status, data, message } = await getCommunityPage({
        // pageNum,
        lastScoreIndex,
        pageSize,
        ...idParam,
        ...searchParam.value,
    });
    isLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    if (!data) {
        return;
    }
    page.value.lastId = data.lastId;
    /***
     * 注意： 勿动！！！！！！！
     * 修正重复数据导致的虚拟滚动列表切割异常问题
     */

    page.value.searchId = searchParam.value;
    const arr = decryptResult(data.encryptResult) || [];
    const list = arr.map((item) => {
        const genInfo = JSON.parse(item.genInfo) || {};
        const { resolution = {} } = genInfo;
        const { prompt } = formatPonyV6Prompt(genInfo, "output");
        return {
            ...genInfo,
            ...item,
            prompt,
            width: resolution.width,
            height: resolution.height,
            dataUserId: item.accountInfo.userId,
            showPrompt: user.userId == item.accountInfo.userId || item.publicType !== "myself",
            isOwn: user.userId == item.accountInfo.userId,
        };
    });
    const lastItem = list.slice(-1).pop();
    lastItem && (page.value.lastScoreIndex = lastItem.lastScoreIndex);
    dataList.value.push(...list);
    page.value.pageNum = pageNum;
    page.value.isDone = list.length === 0;
    insertBanner();
};

const banners = {};
const loadBanners = ref(false);
const pullCommunityBanner = async () => {
    if (banners.id) {
        insertBanner();
        return;
    }
    if (loadBanners.value) {
        return;
    }
    loadBanners.value = true;
    const { status, data } = await getCommunityBanner();
    if (status !== 0 || !data) {
        return;
    }
    const list = data
        .sort((a, b) => a.sort - b.sort)
        .map(({ imgUrl, id, jumpUrl }) => {
            jumpUrl = createLocaleUrl(jumpUrl, DEFAULT_LANG_LIST);
            return { src: imgUrl, id, jumpUrl };
        });
    Object.assign(banners, {
        id: "waterfall-ad",
        isAd: true,
        columnSpan: 2,
        width: 598,
        height: 247,
        list,
    });

    insertBanner();
};
loadMore();
pullCommunityBanner();

// 是否允许获取banner
const allowInsertBanners = () => {
    const { vagueKey, collationName, tags } = searchParam.value;
    const isDefQuery = !vagueKey && !tags && collationName === "Featured"; //是默认查询
    const nonEmptyData = dataList.value.length > 0; //非空数据
    const notInserted = dataList.value[0]?.id !== "waterfall-ad"; // 未被插入广告
    const bannerNotEmpty = banners.list && banners.list.length > 0; //广告列表非空
    return isDefQuery && nonEmptyData && notInserted && bannerNotEmpty;
};
const insertBanner = () => {
    if (allowInsertBanners()) {
        dataList.value.splice(0, 0, banners);
    }
};
//滚动加载更多
const virtualRefScrollTop = ref(0);
const scrollLoadMore = debounce(async (e) => {
    const isBottom = isScrolledToBottom(e.target);
    virtualRefScrollTop.value = e.target.scrollTop;
    //获取元素滚动的高度
    if (isBottom && !isLoading.value) {
        loadMore();
    }
}, 100);
const previewRef = ref(null);

//更新图片举报状态
const handleUpdateReportState = () => {
    const current = dataList.value.find((item) => viewItem.value.id === item.id);
    current.reported = true;
    if (showPreview.value) {
        previewRef.value?.forceUpdatePreview(current);
    }
};

//点赞或取消
const likeLoading = ref(false);
const handleLike = async (row) => {
    let { id, fileLikeNums, liked } = row;
    if (likeLoading.value) {
        return;
    }
    likeLoading.value = true;
    let callback = addLikeByFileId;
    let step = 1;
    if (liked) {
        callback = reduceLikeByFileId;
        step = -1;
    }
    fileLikeNums = Math.max(0, (fileLikeNums += step));
    try {
        const { status, message } = await callback({ commFileId: id });
        likeLoading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        const index = dataList.value.findIndex((item) => item.id === id);
        if (index > -1) {
            dataList.value[index].fileLikeNums = fileLikeNums;
            dataList.value[index].liked = !liked;
        }
        if (showPreview.value) {
            previewRef.value?.forceUpdatePreview(dataList.value[index]);
        }
    } catch (error) {
        likeLoading.value = false;
    }
};

const onDrawerClick = () => {
    setShowMoDrawer(true);
};

const closePreview = () => {
    showPreview.value = false;
    viewItem.value = {};
};

const gotoTop = () => {
    virtualRefScrollTop.value = 0;
    restoreScrollTop();
};
const restoreScrollTop = () => {
    virtualRef.value?.scrollTo({ top: virtualRefScrollTop.value });
};

// 同步更新社区数据
const updateCommunity = (item) => {
    const index = dataList.value.findIndex((current) => current.id === item.id);
    if (index < 0) {
        return;
    }
    if (item.isDel) {
        dataList.value.splice(index, 1);
        return;
    }
    const current = dataList.value[index];
    dataList.value.splice(index, 1, { ...current, ...item });
};

//修复切换路由时，偏移量不正确
onActivated(() => {
    restoreScrollTop();
    nextTick(updateSeo);
});
//初次进入页面，获取URL参数
onMounted(() => {
    syncAction.subscribe("updateCommunityItem", updateCommunity);
    syncAction.subscribe("closePreview", closePreview);
});
onBeforeUnmount(() => {
    syncAction.unsubscribe("updateCommunityItem", updateCommunity);
    syncAction.unsubscribe("closePreview", closePreview);
});
//修复预览模式，切换路由后  浏览状态不正常
watch(
    () => showPreview.value,
    (v) => {
        if (!v) {
            nextTick(() => {
                restoreScrollTop();
            });
        }
    }
);
watch(
    () => forceUpdatePageState.key["/community/explore"],
    () => {
        initPageState();
    }
);
</script>

<style lang="scss" scoped>
.top-bar {
    @apply z-10 bg-bg-2 border-b border-solid border-black/10 dark:border-dark-bg-2 sticky -top-12;
}

.search-box {
    @apply h-11 backdrop-blur-sm bg-fill-ipt-1 relative pr-6  rounded-full p-2.5 text-sm flex items-center gap-1.5  border border-solid border-white/0 dark:text-dark-text/70;
}

.waterfall-item {
    cursor: pointer;
    &::after {
        @apply absolute top-0 right-0 bottom-0 left-0 z-0  opacity-0 pointer-events-none bg-gradient-to-b from-black/70 via-white/0 to-black/70;
        content: "";
    }
    &:hover {
        &::after {
            opacity: 1;
        }
        .mask {
            display: flex;
        }
    }
}
.mask {
    @apply absolute top-0 right-0 bottom-0 left-0 p-2 z-10 hidden flex-col justify-between text-dark-active-text;
}
.end-msg {
    border-top: 1px solid;
    @apply pt-6 px-3 my-7 inline-block dark:border-white/20 border-black/20;
}
.prompt-text {
    height: 62px;
    transition: all 0.1s ease-in-out;
    pointer-events: none;
    display: -webkit-box; /* 启用弹性盒子布局 */
    -webkit-box-orient: vertical; /* 设置盒子方向为垂直 */
    -webkit-line-clamp: 3;
    overflow: hidden;
}

:global(.explore-more-dropdown) {
    @apply min-w-[200px];
    padding: 8px !important;
}

:global(.explore-more-dropdown .n-dropdown-option-body) {
    @apply px-0;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before) {
    left: 0 !important;
    right: 0 !important;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending.danger-bg::before) {
    background-color: var(--p-fill-drop-5);
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
    cursor: pointer;
}

::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 6px;
    border: 3px solid transparent;
    cursor: pointer;
}

.scroll-box:hover {
    ::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.4);
    }
}
</style>
