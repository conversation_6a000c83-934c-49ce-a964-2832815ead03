<template>
    <div class="w-full h-17 xl:h-[72px] p-4 sticky top-0 left-0 flex items-center z-10 text-text-2 hover:text-text-1" :class="{ 'border border-solid border-border-1': border }">
        <div class="cursor-pointer mr-2" @click="handleBack">
            <slot name="icon">
                <div class="flex items-center justify-center rounded-full hover:bg-fill-wd-1 p-2">
                    <IconsArrowLeft class="w-6 h-6" />
                </div>
            </slot>
        </div>

        <div class="flex-1">
            <slot> </slot>
        </div>
    </div>
</template>
<script setup>
const emit = defineEmits(["before-leave"]);
const router = useRouter();
const props = defineProps({
    title: {
        type: String,
        default: "FEATURE_DESCRIBE_FEATURE_TITLE",
    },
    describe: {
        type: String,
        default: "DESCRIBE_DES",
    },
    redirectPath: {
        type: String,
        default: "",
    },
    border: {
        type: Boolean,
        default: true,
    },
    disabled: {
        //如果需要在离开前二次确认 请设置此值 为 true 可以正常离开时再关闭禁用
        type: Boolean,
        default: false,
    },
});

const handleBack = () => {
    emit("before-leave");
    if (props.disabled) return;
    back();
};
const localePath = useLocalePath();
const back = async (step = -1) => {
    if (!history.state.back) {
        await navigateTo(localePath(props.redirectPath || "/tools"));
    } else {
        router.go(step);
    }
};

defineExpose({ back });
</script>
