<!--
 * @Author: HuangQS
 * @Description: 基于MobileHeader组件再次封装的组件，主要页面的顶部 PicLumen Logo + 抽屉按钮
 * @Date: 2025-06-20 10:08:23
 * @LastEditors: <PERSON><PERSON><PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-06-20 13:10:10
-->
<template>
    <MobileHeader :disableBack="true">
        <template #left>
            <div class="flex row-1 items-center text-text-1">
                <n-icon size="42"> <IconsPiclumen /> </n-icon>
                <span>PicLumen</span>
            </div>
        </template>
        <template #right>
            <div class="flex rounded-full size-11 bg-fill-btn-1 text-text-1 justify-center items-center" @click="onDrawerClick">
                <n-icon size="24">
                    <IconsBread />
                </n-icon>
            </div>
        </template>
    </MobileHeader>
</template>

<script setup>
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";

const { t } = useI18n({ useScope: "global" });
import { useCurrentTheme } from "@/stores/system-config";
const { setShowMoDrawer, getShowMoDrawer } = useCurrentTheme();

const onDrawerClick = () => {
    setShowMoDrawer(true);
};
</script>

<style lang="sass"></style>
