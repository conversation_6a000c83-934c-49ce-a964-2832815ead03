<template>
    <div>
        <input type="file" accept=".png,.jpeg,.jpg,.webp" class="absolute -z-50 opacity-0 w-1" ref="chooseFileRef" @change="chooseFileDone" />
        <n-modal v-model:show="showModal" :mask-closable="false">
            <div class="w-11/12 md:w-[480px] aspect-square dark:bg-dark-bg-2 bg-neutral-100 p-4 rounded-lg border border-solid border-white/20 shadow-md relative">
                <div class="dark:text-dark-active-text text-dark-bg text-center font-bold text-base mb-4">{{ t("PROFILE_ACCOUNT_ADJUSTING_AVATAR") }}</div>
                <div class="w-full h-full">
                    <CropperImage ref="cropperRef" :imageSrc="previewUrl" :aspectRatio="1" />
                </div>
                <div class="gap-4 flex mt-5 justify-around overflow-hidden">
                    <n-button
                        :bordered="false"
                        class="flex-1 h-10 rounded-lg dark:!text-dark-active-text !text-dark-bg-2 dark:!bg-white/10 !bg-neutral-200 hover:!bg-primary/70"
                        @click="showModal = false"
                    >
                        {{ t("COMMON_BTN_CANCEL") }}</n-button
                    >
                    <n-button :bordered="false" class="flex-1 h-10 rounded-lg !text-dark-active-text !bg-primary hover:!bg-primary/70" :loading="updateLoading" @click="handleSubmit">
                        {{ t("COMMON_BTN_SAVE") }}</n-button
                    >
                </div>
            </div>
        </n-modal>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { uploadAvatar } from "@/api";
import { useUserProfile, useUserCommunity } from "@/stores/index";
const userInfo = useUserProfile();
const userCommunity = useUserCommunity();
const chooseFileRef = ref(null);
const showModal = ref(false);
const cropperRef = ref(null);
const previewUrl = ref("");

//选择图片
const chooseFileDone = (e) => {
    const file = e.target.files[0];
    e.target.value = "";
    const imgUrl = URL.createObjectURL(file);
    previewUrl.value = imgUrl;
    const img = new Image();
    img.crossOrigin = "Anonymous";
    img.onload = function () {
        // 获取原宽高
        showModal.value = true;
    };
    img.src = imgUrl;
};

//获取裁剪内容
const getCropRawBlob = () => {
    return new Promise((resolve) => {
        const canvas = cropperRef.value.getCropper().getCroppedCanvas({ width: 240, height: 240, imageSmoothingQuality: "high" });
        canvas.toBlob(
            (blob) => {
                resolve(blob);
            },
            "image/webp",
            0.92
        );
    });
};
//提交loading
const updateLoading = ref(false);
const chooseLocalPic = () => {
    updateLoading.value = false;
    showModal.value = false;
    nextTick(() => {
        chooseFileRef.value.click();
    });
};
const handleSubmit = async () => {
    if (updateLoading.value) {
        return;
    }
    updateLoading.value = true;
    const blobData = await getCropRawBlob();
    const formData = new FormData();
    formData.append("avatarImg", blobData, "avatarImg.webp");
    const { status, message } = await uploadAvatar(formData);
    updateLoading.value = false;
    showModal.value = false;
    const tempAvatar = URL.createObjectURL(blobData);
    userInfo.updateUser({ avatarUrl: tempAvatar });
    userCommunity.updateCommunityUser({ userAvatarUrl: tempAvatar });
    if (status !== 0) {
        openToast.error(message);
    }
};

defineExpose({
    chooseLocalPic,
});
</script>

<style lang="scss" scoped></style>
