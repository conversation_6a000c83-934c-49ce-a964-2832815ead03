{"i18n-ally.localesPaths": ["i18n/locales"], "i18n-ally.translate.engines": ["deepl"], "i18n-ally.translate.deepl.enableLog": true, "i18n-ally.translate.deepl.apiKey": "e3006588-b02e-490a-b127-83130fa71fe3", "i18n-ally.translate.parallels": 50, "i18n-ally.translate.deepl.useFreeApiEntry": false, "vue.codeActions.enabled": false, "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "en", "i18n-ally.pathMatcher": "{locale}.yaml", "i18n-ally.enabledParsers": ["yaml"], "i18n-ally.sortKeys": false, "i18n-ally.namespace": true, "i18n-ally.keystyle": "nested"}