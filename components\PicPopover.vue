<template>
    <n-tooltip ref="picPopRef" :placement="placement" :trigger="trigger" :delay="delay" :show-arrow="false" raw :on-update:show="statusUpdate">
        <template #trigger>
            <slot name="trigger"></slot>
        </template>
        <div class="py-2.5 px-3 bg-white text-dark-bg-2 dark:text-dark-text p-3 rounded dark:bg-dark-bg-2 font-medium" :style="{ maxWidth: `${maxWidth}px` }">
            <slot></slot>
        </div>
    </n-tooltip>
</template>
<script setup>
import { ref, onBeforeUnmount } from "vue";
const picPopRef = ref(null);
const props = defineProps({
    placement: {
        type: String,
        default: "top",
    },
    trigger: {
        type: String,
        default: "hover",
    },
    //自动消失时间（s）
    duration: {
        type: Number,
        default: -1,
    },
    maxWidth: {
        type: Number,
        default: 320,
    },
    delay: {
        type: Number,
        default: 500,
    },
});
let timer = null;
const statusUpdate = (status) => {
    if (!status) {
        clearTimer();
        return;
    }
    if (status && props.duration > -1) {
        timer = setTimeout(() => {
            picPopRef.value.setShow(false);
        }, props.duration * 1000);
    }
};

const clearTimer = () => {
    timer && clearTimeout(timer);
};
onBeforeUnmount(() => {
    clearTimer();
});
</script>
