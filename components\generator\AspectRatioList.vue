<script setup>
import Aspect from "@/components/Aspect.vue";
import AutoSize from "@/components/icons/AutoSize.vue";

const props = defineProps({
    size: {
        type: String,
        default: "",
    },
    shapeList: {
        type: Array,
        default: () => [],
    },
    includeContentRefer: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(["change"]);

// 方向
const squareList = computed(() => {
    return props.shapeList.filter((item) => item.width === item.height);
});
// 垂直方向
const verticalList = computed(() => {
    return props.shapeList.filter((item) => item.width < item.height);
});
// 水平方向
const horizontalList = computed(() => {
    return props.shapeList.filter((item) => item.width > item.height);
});

const chooseAspect = (item) => {
    emits("change", item);
};
</script>

<template>
    <div class="min-w-60 grid grid-cols-2 gap-2 p-2 rounded-lg border border-solid border-border-t-1 bg-bg-6 z-[30]">
        <div class="col-span-2 grid grid-cols-2 gap-2">
            <div
                v-for="item in squareList"
                :key="item.label"
                :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                class="aspect-item"
                @click="chooseAspect(item)"
            >
                <Aspect :height="item.height" :width="item.width" />
                <span class="tracking-widest">{{ item.label }}</span>
            </div>
            <div v-if="includeContentRefer" :class="{ 'active-aspect-item': includeContentRefer }" class="aspect-item">
                <IconsAutoSize class="text-2xl" />
                <span class="tracking-widest">Auto</span>
            </div>
        </div>
        <div class="flex flex-col gap-1">
            <div
                v-for="item in verticalList"
                :key="item.label"
                :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                class="aspect-item"
                @click="chooseAspect(item)"
            >
                <Aspect :height="item.height" :width="item.width" />
                <span class="tracking-widest">{{ item.label }}</span>
            </div>
        </div>
        <div class="flex flex-col gap-1">
            <div
                v-for="item in horizontalList"
                :key="item.label"
                :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                class="aspect-item"
                @click="chooseAspect(item)"
            >
                <Aspect :height="item.height" :width="item.width" />
                <span class="tracking-widest">{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.aspect-item {
    @apply flex items-center gap-2 py-2 px-3 cursor-pointer  rounded-lg;
    &:not(.disabled-item) {
        @apply hover:dark:bg-dark-tag-bg hover:bg-neutral-200;
    }
}

.active-aspect-item:not(.disabled-item) {
    @apply dark:bg-dark-tag-bg  bg-neutral-200;
}

.disabled-item {
    @apply opacity-30 cursor-not-allowed;
}
</style>
