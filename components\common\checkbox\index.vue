<template>
    <div class="p-1 cursor-pointer rounded-full backdrop-blur-md" :class="[{ 'opacity-70': disable }, modelValue ? 'bg-primary-6' : ' bg-fill-t-2']" @click.stop.prevent="toggle">
        <div class="flex items-center justify-center" :class="{ 'border border-transparent': outline }">
            <n-icon :size="size" class="text-text-white">
                <IconsSuccess />
            </n-icon>
        </div>
    </div>
</template>

<script setup>
const emit = defineEmits(["update:modelValue", "change"]);

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    outline: {
        type: Boolean,
        default: false,
    },
    disable: {
        type: Boolean,
        default: false,
    },
    size: {
        type: String,
        default: "16",
    },
});

const toggle = () => {
    if (props.disable) return;
    emit("update:modelValue", !props.modelValue);
    emit("change", !props.modelValue);
};
</script>
