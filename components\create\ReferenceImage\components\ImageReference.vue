<template>
    <div class="w-full h-full pb-4 lg:pb-0">
        <div class="w-full flex gap-2 flex-wrap">
            <Upload
                class="!w-fit !h-fit"
                accept=".png,.jpg,.jpeg,.jfif,.webp,.ico,.gif,.svg,.bmp"
                :maxPixels="maxPixels"
                :limit="sizeLimit"
                :disabled="disableUpload"
                @disabled-click="handleUploadDisableClick"
                @change="customRequest"
                @check-failed="handleUploadError"
            >
                <div
                    class="flex flex-col gap-2 px-4 size-[calc((100vw-48px)/3)] lg:w-[200px] lg:h-[96px] min-w-[96px] items-center justify-center rounded-lg border border-dashed border-border-2 bg-fill-wd-1 text-xs text-text-3"
                    :class="[disableUpload ? 'cursor-not-allowed' : 'hover:bg-fill-wd-2  hover:text-text-1']"
                >
                    <template v-if="!uploading">
                        <!-- PC icon 和 文案 -->
                        <IconsUpload class="text-xl hidden lg:inline-block" />
                        <span class="hidden lg:inline-block">{{ $t("FEATURE_UPLOAD_TITLE") }}</span>
                        <!-- H5 icon 和 文案 -->
                        <IconsAdd class="text-xl inline-block lg:hidden" />
                        <span class="inline-block lg:hidden">{{ $t("ADD_BUTTON") }}</span>
                    </template>
                    <template v-else> <IconsSpinLoading class="size-5" /> </template>
                </div>
            </Upload>
            <!-- 已上传图片列表 -->
            <div
                v-for="item in albumList"
                :key="item.albumImgId"
                @click="handleChoose(item)"
                draggable="true"
                class="album-item size-[calc((100vw-48px)/3)] lg:size-[96px] rounded-[6px] group relative shrink-0 cursor-pointer"
                :class="{ 'selected-border': cantDelete(item.imgUrl) }"
            >
                <img :src="item.thumbImgUrl" class="w-full h-full object-cover rounded-lg" />
                <span
                    class="size-6 items-center justify-center rounded-sm bg-fill-t-2 absolute top-1 right-1 z-10 flex lg:hidden backdrop-blur-[8px]"
                    :class="{ 'lg:group-hover:!flex': !cantDelete(item.imgUrl), '!hidden': cantDelete(item.imgUrl) }"
                    @click.stop="handleDeleteImage(item.albumImgId)"
                >
                    <IconsDele class="text-base text-white" />
                </span>
                <div class="selected-mask rounded-[6px]" :class="[cantDelete(item.imgUrl) ? 'opacity-100' : 'opacity-0']"></div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { delAlbumItemById, queryAlbumByPage, uploadAlbum } from "@/api";
import { getImageInfo } from "@/utils/tools";
import { REFER_TYPES } from "@/utils/constant";
const emits = defineEmits(["change"]);
const props = defineProps({
    selectedImgRefersUrls: {
        type: Array,
        default: () => [],
    },
});
const albumList = ref([]);
const disableUpload = computed(() => uploading.value || albumList.value.length >= 30);
//获取相册分页列表
const getAlbumList = async () => {
    const { status, data, message } = await queryAlbumByPage({ pageNum: 1, pageSize: 30 });
    if (status != 0) {
        openToast.error(message);
        return;
    }
    albumList.value = data.resultList;
    window.trackEvent("Create", { el: `reference_image_popup_image_ref_save_count=${data.resultList?.length}` });
};
getAlbumList();

//设置选中状态
const handleChoose = (item) => {
    // if (cantDelete(item.imgUrl)) return;
    emits("change", {
        ...item,
        style: "",
        displayType: REFER_TYPES.IMAGE_REFER,
    });
    window.trackEvent("Create", { el: "image-reference" });
};

//上传相册相关
const uploading = ref(false);
//上传前校验文件 400w px  5MB
const maxPixels = 4000000;
const sizeLimit = 5;
//校验失败
const handleUploadError = (data) => {
    const { type, limit, maxPixels, format } = data;
    switch (type) {
        case "size":
        case "ratio":
            openToast.error(t("UPLOAD_ERROR.SIZE_PIXEL_EXCEED", { limit, maxPixels }));
            break;
        case "format":
            openToast.error(t("FEATURE_UPLOAD_TYPE_ERROR", { type: format })); //TODO 多语言
            break;
    }
};

//上传文件
const customRequest = async (files) => {
    if (uploading.value) {
        return;
    }
    try {
        let file = files[0];
        uploading.value = true;
        let name = "pic_" + Date.now() + file.name.substr(-20);
        const formData = new FormData();

        const { width: realWidth, height: realHeight, size } = await getImageInfo(file); // 获取真实宽高
        const { width, height } = autoMatchResolution({ width: realWidth, height: realHeight }); // 获取近似宽高
        const albumParams = {
            width,
            height,
            realWidth,
            realHeight,
            size,
        };
        formData.append("albumImg", file, name);
        formData.append("albumParams", JSON.stringify(albumParams));
        window.trackEvent("Create", { el: `reference_image_popup_image_ref_upload` });
        const { status, message } = await uploadAlbum(formData);
        if (status !== 0) {
            return handleToastError(message);
        }
        getAlbumList();
    } catch (error) {
        handleToastError(error?.message);
    } finally {
        uploading.value = false;
    }
};

//如果当前图片已经被选中作为参考 不可删除
const cantDelete = (url) => {
    return props.selectedImgRefersUrls?.includes(url);
};

const handleDeleteImage = throttle(async (id) => {
    if (cantDelete(id)) return;
    window.trackEvent("Create", { el: "reference_image_popup_image_ref_delete" });
    try {
        const { status, message } = await delAlbumItemById({ id });
        if (status === 0) {
            getAlbumList();
        }
    } catch (error) {
        console.error(error, "图片删除失败");
    }
}, 300);

const handleUploadDisableClick = () => {
    if (uploading.value) return;
    handleToastError(t("UPLOAD_ERROR.REACHED_THE_MAXIMUM"));
};
const handleToastError = (message = t("UPLOAD_ERROR.UNKNOWN_ERROR")) => {
    openToast.error(message);
};
</script>
