import ImageEdit from '@/components/re-create/ImageEdit.vue'
import Inpaint from '@/components/re-create/Inpaint.vue'
import Outpaint from '@/components/re-create/Outpaint.vue'
import PhotoCrop from '@/components/re-create/PhotoCrop.vue'
import Colorize from '@/components/re-create/Colorize.vue'
import UpscaleModal from '@/components/re-create/UpscaleModal.vue'
import BusinessModal from "@/components/business/modal/index.vue";
import { createModalHook, useSubPermission } from "@/hook/subscribe";

import {
  getTaskQueueByUser,
  pullModelList,
  batchCheckExecuteResult,
  delTask,
  addPreloadTask,
  submitPreloadTaskToExecute,
  translateText,
} from "@/api";
import { useUserProfile, } from "@/stores";
import { useSupportModelList, useCurrentTaskQueue, useCreateStore, useShareDescribe } from "@/stores/create";
import { useSubscribeStore } from "@/stores/subscribe";
import { magicItems } from "@/utils/magic";

import { Alert } from "@/icons/index.js";
import { removeBackground, hiresFix } from "@/api/index.js";
import { NIcon } from "naive-ui";
import { useSyncAction } from "@/stores/syncAction";
import { useGlobalError } from "@/hook/error";
import { getStaticModelById, handleResetParams, isVipModel, getUUID } from "@/utils/tools";

import { ERROR_CODE_ENUM, PRELOAD_TASK_TYPE, ERROR_MESSAGE_ENUM, defaultModel, SUBSCRIBE_PERMISSION, SHAPE_ALL, REFER_TYPES, NamiyaModelId } from "@/utils/constant.js";
import { t } from "@/utils/i18n-util";

//初始化未完成的任务状态
export const useAsyncTaskQueue = async () => {
  const { updateTaskQueue, updateLocalPreloadTasks } = useCurrentTaskQueue();
  const { data, status } = await getTaskQueueByUser().catch((err) => err);
  if (status != 0) {
    return Promise.resolve({ status: false, taskQueue: [] });
  }
  updateTaskQueue(data.concurrentJobList || []);
  updateLocalPreloadTasks(data.preloadTaskQueue || []);
  return Promise.resolve({ status: true });
};

//检查使用超出最大任务数量限制
export const useCheckMaxTask = () => {
  const { taskQueue, preloadQueue, queueLimit } = useCurrentTaskQueue();
  const { concurrency = 1, queueMax = 1 } = queueLimit;
  const preloadLen = preloadQueue.length;
  const taskLen = taskQueue.length;
  const queueLen = preloadLen + taskLen;
  //预载任务 和并发任务超过限制，不允许 添加任务

  if (queueLen >= queueMax) {
    return Promise.resolve({ concurrencyStatus: false, preloadStatus: false });
  }
  // 并发任务超过限制，预载任务未上限，允许添加 预载任务
  if (taskLen >= concurrency && preloadLen < queueMax - taskLen) {
    return Promise.resolve({ concurrencyStatus: false, preloadStatus: true });
  }
  // 并发任务未上限，允许添加 并发任务
  return Promise.resolve({ concurrencyStatus: true, preloadStatus: false });
};

// 添加预载任务
export const useAppendPreloadTask = async (params, type) => {
  const { syncUpdatePreloadQueue, updateReqStatus } = useCurrentTaskQueue();
  updateReqStatus(true);
  const { genParameters, ...conf } = params;
  if (type === PRELOAD_TASK_TYPE.UPSCALE || type === PRELOAD_TASK_TYPE.REMOVE_BG) {
    conf.dealImgParams = JSON.stringify(genParameters);
  } else {
    conf.genParameters = JSON.stringify(genParameters);
  }
  const ps = { genInfo: conf, featuresTypeValue: type };
  const { status } = await addPreloadTask(ps);
  if (status === 0) {
    await syncUpdatePreloadQueue();
  }
  updateReqStatus(false);
  return Promise.resolve(status === 0);
};


//更新剩余lumen
export const useUpdateLumen = async () => {
  const subscribeStore = useSubscribeStore()
  subscribeStore.updateRemainLumens();
};

/* *
 * 自动查询任务结果----请勿重复调用此函数，可能会引起并发任务重复轮询，造成资源消耗和提示异常
 * */

export const autoLoopQueryTaskResult = () => {
  const currentTaskQueue = useCurrentTaskQueue();
  const userProfile = useUserProfile();
  const { showError } = useGlobalError();
  let timer = null;
  // 将预载任务提交到并发任务并检查
  const pushTaskToQueue = async () => {
    // 允许并发的数量
    const maxConcurrency = currentTaskQueue.queueLimit.concurrency;
    //当前并发数量
    const concurrencyLen = currentTaskQueue.taskQueue.length;
    if (concurrencyLen > 0) {
      timer && clearTimeout(timer);
      timer = setTimeout(loopQuery, 3 * 1000);
    }
    if (concurrencyLen >= maxConcurrency) {
      return Promise.resolve();
    }
    const preloadLen = currentTaskQueue.preloadQueue.length;
    if (preloadLen === 0) {
      return Promise.resolve();
    }
    // hotFix---越权---会员状态已成为非会员 ，清空预载任务
    if (maxConcurrency === 1 && preloadLen > 0) {
      await currentTaskQueue.syncDelPreloadTasks(currentTaskQueue.preloadQueue.map(({ id }) => id));
      return Promise.resolve();
    }
    if (currentTaskQueue.isLoading) {
      return Promise.resolve();
    }
    const firstPreload = currentTaskQueue.preloadQueue[0];
    timer && clearTimeout(timer);
    timer = null;
    currentTaskQueue.updateReqStatus(true);
    console.log("提交并发任务", new Date().toLocaleString());
    const res = await submitPreloadTaskToExecute({
      preloadId: firstPreload.id,
    });
    currentTaskQueue.updateReqStatus(false);
    if (res.status !== 0 && res.status !== ERROR_CODE_ENUM.EXCEED_PRELOAD_DEL_ERROR && res.status !== ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR) {
      showError(res.status);
    }
    await useAsyncTaskQueue();
    timer = setTimeout(loopQuery, 3 * 1000);
  };

  // 存在未完成的任务
  const unfinishedTaskQueue = computed(() => {
    // *10 是为了让两个队列长度发生改变(预载 补位到 并发任务队列) 时 这个值一定会发生变化
    return currentTaskQueue.taskQueue.length + currentTaskQueue.preloadQueue.length * 10;
  });

  watch(
    () => currentTaskQueue.queueLimit.concurrency,
    async (v) => {
      if (v > 0) {
        await useAsyncTaskQueue();
        await pushTaskToQueue();
      }
    },
    { immediate: true }
  );

  // 轮询 500 重试3次 后终止轮询
  let reTry = 0;
  const loopQuery = async () => {
    timer && clearTimeout(timer);
    timer = null;
    if (reTry >= 3) {
      return;
    }
    console.log(new Date().toLocaleString(), "查询任务结果...");
    try {
      const ids = currentTaskQueue.taskQueue.map((item) => item.markId);
      if (ids.length === 0) {
        timer && clearTimeout(timer);
        timer = null;
        return;
      }
      const { status, data } = await batchCheckExecuteResult(ids);
      if (status === 0 && Array.isArray(data)) {
        handleWebLoopResMessage(data);
        reTry = 0;
      } else {
        reTry++;
      }
      timer && clearTimeout(timer);
      timer = setTimeout(loopQuery, 3 * 1000);
    } catch (error) {
      reTry++;
    }
  };
  //根据任务队列情况，启动或关闭轮询
  watch(
    () => unfinishedTaskQueue.value,
    async (val) => {
      if (val === 0) {
        return;
      }
      await pushTaskToQueue();
    },
    { immediate: true }
  );

  //数据格式化
  const formatterTaskList = (newList) => {
    const taskList = currentTaskQueue.taskQueue;
    const successTask = [];
    const failTask = [];
    const pendingTask = [];
    let combinedMap = new Map();
    // 将第一个数组的元素添加到Map中
    taskList.forEach((item) => {
      combinedMap.set(item.markId, { ...item });
    });
    // 将第二个数组的元素合并到Map中的元素，如果id已存在则合并属性，否则添加新元素
    newList.forEach((item) => {
      if (combinedMap.has(item.markId)) {
        const { index, status, promptId, img_urls, info, genBeginTime = null, featureName = null } = item;
        combinedMap.set(item.markId, {
          genBeginTime,
          featureName,
          ...combinedMap.get(item.markId),
          index,
          status,
          promptId,
          img_urls,
          info,
        });
      } else {
        combinedMap.set(item.markId, { ...item });
      }
    });
    const arr = Array.from(combinedMap.values());
    const def_user = {
      userName: userProfile.user.userName,
      loginName: userProfile.user.loginName,
      avatar: userProfile.user.avatarUrl,
    };
    arr.forEach((item) => {
      if (item.status === "success") {
        successTask.push({ ...item, ...def_user });
      } else if (item.status === "failure") {
        failTask.push({ ...item, ...def_user });
      } else if (item.status !== "cancel") {
        pendingTask.push(item);
      }
    });
    return {
      failTask,
      successTask,
      pendingTask,
    };
  };

  //消息处理
  const handleWebLoopResMessage = (list = []) => {
    //更新 Store
    try {
      let { failTask, successTask, pendingTask } = formatterTaskList(list);
      const syncAction = useSyncAction();
      if (successTask.length > 0) {
        syncAction.publish("latestSuccessfulMission", successTask);
        syncAction.publish("latest-completed-mission", {
          status: "success",
          message: "",
          code: 200,
        });
      }
      if (failTask.length > 0) {
        const infoErrorInPoll = failTask.find(({ info }) => ERROR_MESSAGE_ENUM[info]);
        let code = 500;
        let message = t("TOAST_GEN_ERROR_CONTENT");
        // 2025-07-11 把3001等 info中返回的错误状态 统一维护到ERROR_MESSAGE_ENUM中
        if (infoErrorInPoll) {
          code = infoErrorInPoll.info
        }
        console.log(failTask, "failTask.length");
        showError(code, { message });
      }
      currentTaskQueue.updateTaskQueue(pendingTask);
    } catch (error) {
      showError();
      currentTaskQueue.updateTaskQueue([]);
      return;
    }
  };
};

//手动取消未完成的任务
export const useCancelUnfinishedTask = async (markId) => {
  const { status, message } = await delTask({ markId });
  if (status !== 0) {
    return Promise.resolve({ status: false, message });
  }
  const currentTaskQueue = useCurrentTaskQueue();
  const list = currentTaskQueue.taskQueue.filter((item) => item.markId !== markId);
  currentTaskQueue.updateTaskQueue(list);
  return Promise.resolve({ status: true });
};

//获取 远程模型列表
export const useGetRemoteModelList = async () => {
  const { setModelList, modelList } = useSupportModelList();
  if (modelList.length > 0) {
    return Promise.resolve(modelList);
  }

  const { status, data, message } = await pullModelList();
  console.log(data, 'data')
  if (status !== 0) {
    return Promise.reject(message);
  }
  let list = (data || []).map((item) => {
    let {
      positivePrompt: prompt,
      negativePrompt: negative_prompt,
      samplerName: sampler_name,
      modelAbility: model_ability,
    } = item.defaultConfig;
    const currentModel = getStaticModelById(item.modelId) // 获取前端存储的模型信息 和后端数据融合
    return {
      ...item,
      label: item.modelDisplay,
      value: item.modelId,
      des: item.modelDesc,
      modelType: item.modelType,
      defaultParams: {
        ...item.defaultConfig,
        prompt,
        negative_prompt,
        sampler_name,
        model_ability,
      },
      model_ability,
      icon: item.modelAvatar,
      modelOrder: item.modelOrder,
      supportStyleList: item.supportStyleList,
      ...currentModel
    };
  });
  list.sort((a, b) => a.modelOrder - b.modelOrder);
  setModelList(list);
  return Promise.resolve(list);
};


// 设置用户存储的默认生图配置
export const useSetDefaultCreateConfig = () => {
  const createStore = useCreateStore();
  const setCreateConfig = async (userConfig) => {
    //提前校验比例可用性
    const resolution = SHAPE_ALL.find((i) => i.value === userConfig.size) || defaultRatio;
    //提前校验模型可用性 不可用修改为默认模型
    const batch_size = isBatchSizeValid(userConfig.batch_size) ? userConfig.batch_size : 2
    // console.log('userConfig', userConfig)
    createStore.setGenerateConfig({
      model_id: defaultModel.id, //由于用户可能不存在设置为默认设置 此处对默认模型的相关参数进行初始化
      cfg: defaultModel.cfg,
      steps: defaultModel.steps,
      ...userConfig,
      batch_size,
      resolution
    });
  }

  return {
    setCreateConfig
  }
};

//根据模型ID获取模型对应的详细信息
export const useGetModelInfo = (modelId) => {
  const { modelList } = useSupportModelList();
  return modelList.find((item) => item.value === modelId) || {};
};


//提示词被拒绝的弹窗
export const usePromptRefusedDialog = () => {
  const { showMessage } = useModal();
  showMessage({
    style: { width: "520px" },
    showCancel: false,
    confirmBtn: t("COMMON_BTN_OK"),
    content: h("div", null, t("PROMPT_DETECTED_NOT_PASS_CONTENT")),
    icon: h(Alert, { class: "text-error text-2xl" }),
    title: t("DIALOG_TITLE_NOTICE"),
  }).then(() => { });
};

//长任务 --二次确认弹窗
export const longTaskDialog = () => {
  return new Promise((resolve) => {
    const { showMessage } = useModal();
    showMessage({
      style: { width: "500px" },
      cancelBtn: t("RE_SUBMIT_TASK"),
      confirmBtn: t("COMMON_BTN_CANCEL"),
      content: h("div", null, t("LONG_TASK_TIPS")),
      icon: h(Alert, { class: "text-error text-3xl" }),
      title: t("DIALOG_TITLE_NOTICE"),
    })
      .then((_) => resolve(false))
      .catch((_) => resolve(true));
  });
};

export const translateOrEnhanceText = async (prompt, mode) => {
  try {
    const { showMessage } = useModal();

    const { status, data = prompt } = await translateText({
      content: prompt,
      mode,
    });
    if (status !== 0) {
      let errorMessage = t("SHORT_ENHANCE_ERR");
      if (mode === "translation") {
        errorMessage = t("SHORT_TRANS_ERR");
      }
      showMessage({
        style: { width: "420px" },
        showCancel: false,
        confirmBtn: t("COMMON_BTN_OK"),
        content: h("div", null, [h("p", { class: "tracking-wider" }, errorMessage)]),
        icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_NOTICE"),
      });

      return Promise.resolve({
        success: false,
        data: prompt,
        message: errorMessage,
      });
    }

    return Promise.resolve({
      success: true,
      data: data,
    });
  } catch (error) {
    return Promise.resolve({
      success: false,
      data: prompt,
    });
  }
};

//前往生图页面
export const useGoToCreate = () => {
  const route = useRoute();
  const localePath = useLocalePath();
  const isCreatePage = () => route.path === localePath("/image/create");
  const toCreate = async () => {
    if (!isCreatePage()) {
      await navigateTo(localePath("/image/create"));
    }
    const syncAction = useSyncAction();
    syncAction.publish("closePreview");
  };

  return {
    toCreate,
    isCreatePage,
  };
};

/**
 * 通用校验函数 判断当前是否可以继续创建任务
 */
export const useCommonLimitValidate = () => {
  const currentTaskQueue = useCurrentTaskQueue();
  const { showError } = useGlobalError();
  const checkLimit = async () => {
    if (currentTaskQueue.isLoading) {
      openToast.error(t("TOAST_TASK_LIMIT"), 5000);
      return false;
    }

    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus && !checked.preloadStatus) {
      showError(ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR);
      return false;
    }
    return true;
  };
  return { checkLimit }
}

/**
 * 随机提示词控制器 
 * @returns 提示词控制器
 */
export const usePromptController = () => {
  const modelPromptMap = {
    [NamiyaModelId]: [
      `slit eyes, valbun, r17329_illu, 1girl, masterpiece, ultra detailed, looking at viewer, white hair, short hair, animal ears, cat ears, school uniform, pleated skirt, indoors`,
      `cute, (lovely), illustration, dynamic angle, upper body, rose, a cute girl, 1girl, solo, maid, beautiful pink hair, beautiful purple eyes, ((beautiful eyes)), long hair, cat ears, earrings, tattoo, cool, flat color, frill, Overly detailed, decorated, lots of accessories, piercings, bandages, bandaids, under-eye bags, dark circles under the eyes, Jirai kei, gothic lolita`,
      `1girl, smirk, zako (meme), (multicolored hair, pink hair, black hair), twintails, hair ornament, ribbon, x hair ornament, black dragon girl, jirai kei, pink shirt, too many frills, frilled shirt, black miniskirt, holding stuffed toy, mini bag, chain, jewelry, frilled dress, layerd miniskirt, checkered thighhighs, ribbon-trimmed thighhighs, ((dragon horns, dragon wings, dragon tail)), cute background`,
      `sharp focus, female focus, jewelry, masterpiece, best quality, very aesthetic, absurdres, detailed background, perfect anatomy, closeup, breast, open mouth, off shoulder, purple theme, ultra-detailed, anime style, 4K, ethereal beautiful female character, large expressive eyes, long flowing hair, highlights, dreamy patterns, surreal fantasy landscape, floating blossoms, glowing butterflies, color palette, depth of field, red eyes, white hair, pointy ears`,
      `sumi-e background, SOLO, 1girl, usada pekora (1st costume), AddXL, 748cmstyle, usada pekora (1st costume), CCpkmnStyleMix, watercolor texture, white background, gradient colors, gradient watercolor, abstract background`,
      `1girl, solo, smile, Qiqiu style, karasuchan, greyscale with colored background, hatching (texture), monochrome, Sameko Saba, blonde hair, long hair, animal ears, fluffy ears, blue eyes, lock of blue hair, lifesaving float pigtails, blue fish tail`,
    ]
  };
  //随机提示词 基于模型id进行控制
  const getRandomPrompt = (model_id) => {
    const promptsList = modelPromptMap[model_id]
    if (!promptsList?.length) return ""
    console.log(promptsList, 'promptsList')
    return promptsList[Math.floor(Math.random() * promptsList.length)];
  };

  return {
    getRandomPrompt,
  };
};




const getBaseConf = (conf) => {
  let { anime, batch_size, cfg, height, width, model_id, negative_prompt, prompt, sampler_name, scheduler, seed, size, steps, resolution } = conf;
  if (resolution) {
    height = resolution.height;
    width = resolution.width;
  }
  return {
    model_id,
    prompt,
    negative_prompt,
    resolution: {
      width,
      height,
      batch_size,
    },
    model_ability: {
      anime_style_control: anime,
    },
    size,
    seed,
    steps,
    cfg,
    sampler_name,
    scheduler,
  };
};

// 判断模型是否可用
export const isModelValidForMe = (id) => {
  console.log(id, "这个用户能用这个模型吗？")
  if (!id) return false;
  const { checkPermissionNotModal } = useSubPermission();
  const model = useGetModelInfo(id);
  const { isVipModel } = model;
  if (isVipModel && !checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER)) {
    console.log("不能")
    return false
  } else {
    console.log("能")
    return true
  }
};
// 判断生图张数是否可用
export const isBatchSizeValid = (size) => {
  console.log(size, "这个用户能用这个张数吗？")
  const { checkPermissionNotModal } = useSubPermission();
  if (size < 3) return true
  if (!checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER)) {
    return false
  } else {
    return true
  }
};

//二次编辑——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————

// remove bg 点击 直接创建任务
export const useRemoveBg = () => {
  const currentTaskQueue = useCurrentTaskQueue();
  const { clearPromptInput } = useCreateStore();
  const { checkLimit } = useCommonLimitValidate();
  const { toCreate } = useGoToCreate();
  const { showError } = useGlobalError();

  const isLoading = ref(false);
  const error = ref(null);
  const removeBg = async (base, removeBgParams, longTask = false) => {
    toCreate();
    isLoading.value = true;
    error.value = null;

    const isAllowedToCreate = await checkLimit();
    if (!isAllowedToCreate) {
      isLoading.value = false;
      return;
    }

    const task = getBaseConf(base);
    task.resolution.batch_size = 1;

    const originCreate = "removeBackground";
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus) {
      useAppendPreloadTask({ genParameters: removeBgParams, ...task }, PRELOAD_TASK_TYPE.REMOVE_BG);
      clearPromptInput();
      isLoading.value = false;
      return;
    }

    currentTaskQueue.updateReqStatus(true);

    try {
      removeBgParams.continueCreate = longTask;
      const { status, data } = await removeBackground(removeBgParams);
      currentTaskQueue.updateReqStatus(false);
      if (status !== 0) {
        showError(status);
        return;
      }
      clearPromptInput();

      if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
        const hasReSubmit = await longTaskDialog();
        hasReSubmit && (await removeBg(base, removeBgParams, true));
        return;
      }

      const list = mergeArraysById(currentTaskQueue.taskQueue, [
        {
          ...task,
          originCreate,
          markId: data.markId,
          status: "pending",
          featureName: data.featureName,
          fastHour: data.fastHour,
          index: data.index,
          createTimestamp: getCurrentISO8601Time(),
        },
      ]);
      currentTaskQueue.updateTaskQueue(list);
      //  
      isLoading.value = false;
    } catch (errorMsg) {
      console.error(errorMsg, 'errorMsg')
      error.value = errorMsg;
      openToast.error(errorMsg.message);
      currentTaskQueue.updateReqStatus(false);
      isLoading.value = false;
    }
  };

  return {
    isLoading,
    error,
    removeBg,
  };
};


//超分
export const useUpscale = () => {
  const { checkLimit } = useCommonLimitValidate();
  const { showError } = useGlobalError();
  const { clearPromptInput } = useCreateStore();
  const { toCreate } = useGoToCreate();
  // 处理并执行超分任务
  const hiresFixTask = async (base, upscaleConf) => {
    const isAllowedToCreate = await checkLimit();
    if (!isAllowedToCreate) return;


    let { model_id } = upscaleConf;
    const task = getBaseConf(base);
    task.resolution.batch_size = 1;

    const originCreate = "hiresFix";
    const modelInfo = useGetModelInfo(model_id);
    if (!modelInfo.value) {
      openToast.error(t("TOAST_MODEL_DEL"), 5000);
      return;
    }
    const { realWidth, realHeight } = upscaleConf
    // 如果参数不完整，展示modal让用户设置
    if (!upscaleConf.scale || !upscaleConf.denoise) {
      const size = `${realWidth} x ${realHeight}`;
      try {
        const { showMessage } = useModal();
        await showMessage({
          showCancel: false,
          showConfirm: false,
          content: h(UpscaleModal, { size, model_id }),
          zIndex: 2000
        }).then((v) => {
          Object.assign(upscaleConf, v);
        });
      } catch (error) {
        return;
      }
    }

    toCreate();
    // 预加载任务
    const param = { ...upscaleConf, modelId: model_id, };
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus) {
      useAppendPreloadTask({ genParameters: param, ...task }, PRELOAD_TASK_TYPE.UPSCALE);
      clearPromptInput();
      return;
    }

    const currentTaskQueue = useCurrentTaskQueue();
    currentTaskQueue.updateReqStatus(true);

    try {
      param.continueCreate = false;

      if (upscaleConf.scale && upscaleConf.scale > 1) {
        param.scale = upscaleConf.scale;
      }
      if (!param.denoise) {
        param.denoise = 0.3;
      }
      const { status, message, data } = await hiresFix(param);
      currentTaskQueue.updateReqStatus(false);
      clearPromptInput();

      // 处理长任务
      if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
        const hasReSubmit = await longTaskDialog();
        hasReSubmit && (await hiresFixTask(base, upscaleConf));
        return;
      }

      if (status !== 0) {
        showError(status, {
          triggerEl: isFluxDevModel(upscaleConf) ? SUB_EL.FLUX_1_DEV : "other",
        });
        return;
      }

      const list = mergeArraysById(currentTaskQueue.taskQueue, [
        {
          ...task,
          originCreate,
          markId: data.markId,
          status: "pending",
          featureName: data.featureName,
          fastHour: data.fastHour,
          index: data.index,
          createTimestamp: getCurrentISO8601Time(),
        },
      ]);
      currentTaskQueue.updateTaskQueue(list);
      //  
    } catch (error) {
      openToast.error(error.message);
      currentTaskQueue.updateReqStatus(false);
    }
  };

  return {
    hiresFixTask,
  };
};

// remix 查询当前模型是否仍然存在
const getModelConfig = (modelList, model_id) => {
  const model = modelList.find(item => item.value === model_id);
  if (!model) {
    openToast.error(t("TOAST_REMIX_FAILED"));
    return null;
  }
  return model;
};

const getDefaultBatchSize = (batch_size, resolution) => {
  if (!batch_size) {
    return resolution.batch_size || 1;
  }
  return Math.min(batch_size, 4);
};

const getFinalSizeConfig = (batch_size) => {
  const { checkPermissionNotModal } = useSubPermission();
  if (!checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER)) {
    if (batch_size > 2) {
      batch_size = 2;
    }
  }
  return { batch_size };
};
export const buildRemixConfig = (conf, model, batch_size, resolution, defPs) => {
  const { width, height, size } = autoMatchResolution(resolution);
  return removeNulls({
    ...conf,
    batch_size,
    model_id: model.value,
    seed: -1, // 产品要求remix 不携带原本的seed
    steps: conf.steps || defPs.steps,
    negative_prompt: conf.negative_prompt || defPs.negative_prompt,
    cfg: conf.cfg || defPs.cfg,
    scheduler: conf.scheduler || defPs.scheduler,
    sampler_name: conf.sampler_name || defPs.sampler_name,
    size,
    resolution: {
      ...resolution,
      width,
      height,
    },
  });
};

// Remix
const remixModelError = async (params) => {
  const { model_id = "" } = params || {};
  if (!model_id) return;
  const localePath = useLocalePath();
  const { showMessage } = useModal();
  const createStore = useCreateStore();
  const { checkPermissionNotModal } = useSubPermission();
  const isMember = await checkPermissionNotModal(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
  if (!isVipModel({ model_id }) || isMember) return;
  window.trackEvent("Create", { el: "member_model_popup_show" });
  showMessage({
    style: { width: "500px", padding: 0 },
    showCancel: false,
    showConfirm: false,
    content: h(BusinessModal, {
      base: {
        title: t("CREATE.VIP_ONLY_MODEL.TITLE"),
        description: t("CREATE.VIP_ONLY_MODEL.DESC"),
        cancelText: t("MODEL_SWITCH_1"),
        confirmText: t("SUBSCRIBE_UPGRADE"),
      },
    }),
  })
    .then(async () => {
      window.trackEvent("Create", { el: "member_model_popup_upgrade" });
      const subscribePage = localePath("/user/subscribe");
      await navigateTo({ path: subscribePage });
    })
    .catch(() => {
      createStore.setModel(defaultModel.id) // 切换为默认模型
    });
};
export const useRemix = async (conf, isRerun) => {
  conf = handleResetParams(conf)
  const { toCreate } = useGoToCreate();
  toCreate();

  const createStore = useCreateStore();
  const { modelList } = useSupportModelList();

  const model = getModelConfig(modelList, conf.model_id);
  if (!model) return;

  let batch_size = getDefaultBatchSize(conf.batch_size, conf.resolution); //处理用户remix传入的batch_size
  const { batch_size: finalBatchSize } = getFinalSizeConfig(batch_size); //根据会员权限调整batch_size

  conf.batch_size = finalBatchSize;
  conf.highPixels = false;

  // 获取模型的默认参数
  const defPs = model.defaultParams;

  // 创建配置
  const newConf = buildRemixConfig(conf, model, finalBatchSize, conf.resolution, defPs);

  remixModelError(conf) //校验模型当前用户是否可用
  //  const {}
  if (isRerun) {
    return newConf;
  }
  console.log(newConf, 'newConf')
  // 更新 store 并跳转到创建页面
  createStore.setGenerateConfig(newConf);
  //remix 图生图的prompt magic
  const { mainCategory, subCategory } = newConf
  if (mainCategory && subCategory) {
    const finalMagicOption = magicItems?.find((item) => item.type === mainCategory && item.name === subCategory);
    const list = [{
      ...finalMagicOption, displayType: REFER_TYPES.PROMPT_MAGIC
      , id: getUUID()
    }]
    createStore.updateReferStore(list)
  }
};

// describe 点击存储参数跳转describe页面
export const useDescribe = async (fileLink) => {
  const localePath = useLocalePath();
  const shareDescribe = useShareDescribe();
  shareDescribe.setLink(fileLink);
  await navigateTo({ path: localePath("/tools/describe") });
};

// edit 编辑生图结果 点击弹窗交互
export const useImageEdit = createModalHook(ImageEdit, { style: { width: "auto", padding: 0, borderRadius: 0}, zIndex: 2000, raw: true, escCloseable: true }, "create_ImageEdit");

// colorize 点击 弹窗交互
export const useColorize = createModalHook(Colorize, { style: { width: "auto", padding: 0, borderRadius: 0 }, zIndex: 2000, raw: true, escCloseable: true }, "create_Colorize");

// inpaint 点击弹窗交互
export const useInpaint = createModalHook(Inpaint, { style: { width: "auto", padding: 0, borderRadius: 0 }, zIndex: 2000, raw: true, escCloseable: true }, "create_Inpaint");

// outpaint 点击弹窗交互
export const useOutpaint = createModalHook(Outpaint, { style: { width: "auto", padding: 0, borderRadius: 0 }, zIndex: 2000, raw: true, escCloseable: true }, "create_Outpaint");


export const usePhotoCrop = createModalHook(PhotoCrop, { style: { width: "auto", padding: 0, borderRadius: 0 }, zIndex: 2000, raw: true, escCloseable: true }, "create_PhotoCrop");




