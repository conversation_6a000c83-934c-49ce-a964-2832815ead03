<template>
    <div class="flex flex-col h-full bg-bg-1">
        <template v-if="isMobile">
            <MobileHeader :title="t('TOOLS_CENTER_TITLE')" />
        </template>

        <div class="relative text-text-4 overflow-hidden font-medium p-6 overflow-y-auto">
            <template v-if="!isMobile">
                <h1 class="text-center mt-8 font-bold text-[28px] text-text-1">{{ $t("TOOLS_CENTER_TITLE") }}</h1>
                <p class="text-center mt-2 text-base max-w-7xl mx-auto">
                    <span>{{ $t("TOOLS_CENTER_DES") }}</span>
                    <NuxtLinkLocale to="/tools/lumen-table" class="text-primary-6 ml-1 cursor-pointer">
                        <span>{{ $t("SEE_LUMEN_TABLE") }}</span>
                    </NuxtLinkLocale>
                </p>
            </template>

            <div class="mt-14 flex flex-col xs:flex-row justify-center gap-6 items-center xs:items-stretch xs:p-0">
                <NuxtLinkLocale to="/tools/remove-background" @click="gaTrackEvent('removeBg')" class="rounded-xl overflow-hidden bg-bg-2 w-full flex-1 max-w-[372px] cursor-pointer hover:shadow-lg">
                    <img src="@/assets/images/toolsCover/remove_bg_cover.webp" />
                    <div class="p-4 mt-auto">
                        <div class="text-base flex items-center gap-2 text-text-2">
                            <n-icon size="20"><IconsRemoveBg /></n-icon>
                            <span>{{ $t("FEATURE_BG_REMOVER_FEATURE_TITLE") }}</span>
                            <div class="h-5 bg-fill-wd-1 text-text-3 flex items-center justify-center gpa-0.5 px-2 min-w-8 rounded-full text-xs">
                                <IconsLumenFill class="w-2 h-3" />
                                <span class="ml-0.5">1</span>
                            </div>
                        </div>
                        <div class="mt-2 text-xs">{{ $t("BG_REMOVER_DES") }}</div>
                    </div>
                </NuxtLinkLocale>
                <NuxtLinkLocale to="/tools/describe" @click="gaTrackEvent('description')" class="rounded-xl overflow-hidden bg-bg-2 w-full flex-1 max-w-[372px] cursor-pointer hover:shadow-lg">
                    <img src="@/assets/images/toolsCover/describe_cover.webp" />
                    <div class="p-4 mt-auto">
                        <div class="text-base flex items-center gap-2 text-text-2">
                            <n-icon size="20"><IconsDescribe /></n-icon>
                            <span>{{ $t("FEATURE_DESCRIBE_FEATURE_TITLE") }}</span>
                            <div class="h-5 bg-fill-wd-1 text-text-3 flex items-center justify-center gpa-0.5 px-2 min-w-8 rounded-full text-xs">
                                <IconsLumenFill class="w-2 h-3" />
                                <span class="ml-0.5">1</span>
                            </div>
                        </div>
                        <div class="mt-2 text-xs">{{ $t("DESCRIBE_DES") }}</div>
                    </div>
                </NuxtLinkLocale>
                <div
                    class="rounded-xl overflow-hidden border border-dashed dark:border-dark-bg-2 dark:text-dark-desc-text border-dark-tag-bg flex items-center justify-center w-full flex-1 max-w-[372px]"
                >
                    <span>{{ $t("TOOLS_CENTER_MORE") }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
useSeoMeta({
    title: () => t("SEO_META.SEO_TOOLS_TITLE"),
    ogTitle: () => t("SEO_META.SEO_TOOLS_TITLE"),
    description: () => t("SEO_META.SEO_TOOLS_DESC"),
    ogDescription: () => t("SEO_META.SEO_TOOLS_DESC"),
});
import { useThemeStore } from "@/stores/system-config";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());

const gaTrackEvent = (el) => {
    window.trackEvent("Tools", { el: `tools_btn=${el}` });
};
</script>

<style lang="scss" scoped></style>
