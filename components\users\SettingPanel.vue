<template>
    <div class="flex flex-col">
        <div class="w-full md:w-[240px]">
            <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_CLEAR_PROMPTS_TITLE") }}</div>
            <SwitchTab v-model:value="config.clearPrompt" :options="booleanOptions" class="mt-2" @change="handleSaveUserConf" />
        </div>

        <div class="w-full md:w-[240px] mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("TASK_SUBMIT_USE_TITLE") }}</div>
            <SwitchTab v-model:value="config.shortcutKey" :options="submitShortcutKeyOptions" class="mt-2" @change="handleSaveUserConf" />
        </div>
        <div class="w-full md:w-[240px] mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("IMAGE_DELETE_TITLE") }}</div>
            <SwitchTab v-model:value="config.delConfirm" :options="booleanOptions" class="mt-2" @change="handleSaveUserConf" />
        </div>

        <!-- <div class="w-full mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("IMAGE_DISPLAY_PROMPT") }}</div>
            <SwitchTab v-model:value="config.displayPrompt" :options="booleanOptions" class="mt-2 md:w-[240px]" @change="handleSaveUserConf" />
        </div> -->
        <div class="w-full mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("IMAGE_DOWNLOAD_QUALITY_TITLE") }}</div>
            <SwitchTab v-model:value="config.downloadFileType" :options="downloadOptions" class="mt-2 md:w-[240px]" @change="handleSaveUserConf" />
        </div>
        <div class="w-full mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("GEN_STATUS_TAB_BAR_TITLE") }}</div>
            <SwitchTab v-model:value="config.changeTabStatus" :options="booleanOptions" class="mt-2 md:w-[240px]" @change="handleSaveUserConf" />
        </div>
        <!-- <div class="w-full mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("LUMEN_COST_SHOW") }}</div>
            <SwitchTab v-model:value="config.showLumenCost" :options="booleanOptions" class="mt-2 md:w-[240px]" @change="changeVal" />
        </div> -->

        <div class="w-full mt-8">
            <div class="text-sm text-text-3 font-medium">{{ t("CONFIG_SETTING_HIDDEN_PROMPT_WHEN_UPLOAD_IMG") }}</div>
            <SwitchTab v-model:value="config.hiddenPromptWhenUploadImg" :options="booleanOptions" class="mt-2 md:w-[240px]" @change="handleSaveUserConf" />
        </div>
    </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useUserProfile } from "@/stores/index";
import SwitchTab from "@/components/SwitchTab.vue";

const { t } = useI18n({ useScope: "global" });
const userProfileStore = useUserProfile();
const { userConfig } = storeToRefs(userProfileStore);

const defaultValues = {
    viewQuality: "webp_70",
    downloadOpt: "options",
    shortcutKey: "combination_key",
};
const config = ref({});

const downloadOptions = [
    { value: "png", label: "PNG" },
    { value: "jpg", label: "JPEG" },
];

const booleanOptions = computed(() => {
    return [
        { value: true, label: t("SHORT_BTN_YES") },
        { value: false, label: t("SHORT_BTN_NO") },
    ];
});
const submitShortcutKeyOptions = [
    { value: "enter", label: "Enter" },
    { value: "combination_key", label: "Ctrl + Enter" },
];

const handleSaveUserConf = () => {
    userProfileStore.syncUpdateUserConfig(config.value);
};

const changeVal = () => {
    window.trackEvent("Setting", { el: `lumen_display=${config.value.showLumenCost ? "yes" : "no"}` });
    handleSaveUserConf();
};

onMounted(() => {
    nextTick(() => {
        config.value = {
            clearPrompt: userConfig.value.clearPrompt,
            viewQuality: userConfig.value.viewQuality || defaultValues.viewQuality,
            delConfirm: userConfig.value.delConfirm,
            detailShowParameters: userConfig.value.detailShowParameters,
            shortcutKey: userConfig.value.shortcutKey || defaultValues.shortcutKey,
            displayPrompt: userConfig.value.displayPrompt,
            downloadFileType: userConfig.value.downloadFileType,
            changeTabStatus: userConfig.value.changeTabStatus,
            exportPromptToEmail: userConfig.value.exportPromptToEmail,
            showLumenCost: userConfig.value.showLumenCost,
            hiddenPromptWhenUploadImg: userConfig.value.hiddenPromptWhenUploadImg,
        };
    });
});
</script>
