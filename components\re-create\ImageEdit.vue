<template>
    <div class="re-create__container">
        <div class="re-create__close" @click="handleClose()">
            <IconsClose class="size-4 lg:size-6" />
        </div>
        <div class="image-area flex items-end justify-center overflow-hidden">
            <div
                v-show="isImgLoading"
                :animate="false"
                class="min-h-[360px] rounded-lg !bg-transparent flex items-center justify-center w-full h-full max-h-full"
                :style="
                    imgInfo.width
                        ? {
                              aspectRatio: imgInfo.width / imgInfo.height,
                              maxWidth: imgInfo.width + 'px',
                              maxHeight: imgInfo.height + 'px',
                          }
                        : {
                              width: '100%',
                              height: '100%',
                          }
                "
            >
                <n-icon size="40">
                    <IconsSpinLoading class="text-text-2" />
                </n-icon>
            </div>
            <img v-show="!isImgLoading && item?.imgUrl" :src="item.imgUrl" alt="" class="object-contain rounded-lg max-h-full" />
        </div>
        <div class="prompt-area w-full flex justify-center shrink-0">
            <div class="w-[540px] h-[140px] bg-fill-ipt-s-1 rounded-2xl flex flex-col items-center justify-between backdrop-blur-lg border border-solid border-border-t-1">
                <div class="h-[76px] w-full">
                    <n-input
                        type="textarea"
                        :placeholder="t('EDIT_IMAGE.PROMPT', currentPrompt)"
                        v-model:value="editPrompt"
                        :maxlength="2500"
                        class="edit-input !h-[76px] w-full min-w-0 text-sm text-text-4 p-4 pb-0"
                        :autosize="{ minRows: 1, maxRows: 3 }"
                        @keydown.stop="shortcutKey"
                    />
                </div>
                <div class="w-full items-center justify-between flex h-[64px] p-3">
                    <div class="flex items-center shrink-0 gap-2">
                        <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                            <template #trigger>
                                <div
                                    class="rounded-full size-10 p-2.5 bg-fill-ww-10 text-text-6 transform transition-all duration-200"
                                    @click="handleImprove('translation')"
                                    :class="{ '!text-text-2 cursor-pointer hover:text-text-1 hover:bg-fill-ww-11': allowImprove }"
                                >
                                    <n-icon size="20">
                                        <IconsSpinLoading v-if="promptTranslating.isLoading && promptTranslating.type === 'translation'" />
                                        <IconsTranslate v-else />
                                    </n-icon>
                                </div>
                            </template>
                            <div class="tips-box">
                                <div class="font-medium text-text-1 flex items-center gap-2">
                                    <span>{{ t("SHORT_PROMPT_TRANS_TITLE") }}</span>
                                </div>
                                <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_TRANS_MESSAGE") }}</div>
                            </div>
                        </n-tooltip>

                        <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                            <template #trigger>
                                <div
                                    class="rounded-full size-10 p-2.5 bg-fill-ww-10 text-text-6 transform transition-all duration-200"
                                    @click="handleImprove('enhance')"
                                    :class="{ '!text-text-2 cursor-pointer hover:text-text-1 hover:bg-fill-ww-11': allowImprove }"
                                >
                                    <n-icon size="20">
                                        <IconsSpinLoading v-if="promptTranslating.isLoading && promptTranslating.type === 'enhance'" />
                                        <IconsImprove v-else />
                                    </n-icon>
                                </div>
                            </template>

                            <div class="tips-box">
                                <div class="font-medium text-text-1 flex items-center gap-2">
                                    <span>{{ t("SHORT_PROMPT_IMPROVE_TITLE") }}</span>
                                </div>
                                <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_IMPROVE_MESSAGE") }}</div>
                            </div>
                        </n-tooltip>
                    </div>
                    <VipBtn display="none" auth @click="handleGenerate">
                        <Button class="min-w-[96px]" :disabled="isExceed || !editPrompt.length" :loading="isExceed">
                            {{ t("EDIT_IMAGE.GENERATE") }}
                            <ExpendConst class="ml-3" :class="{ 'opacity-40': isExceed || !editPrompt.length }" :feature-type="lumenCostKey" :num="1" />
                        </Button>
                    </VipBtn>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed } from "vue";
import ExpendConst from "@/components/subscribe/ExpendConst.vue";
import { useUserProfile } from "@/stores/index";
import { useCurrentTaskQueue } from "@/stores/create";
import { useThemeStore } from "@/stores/system-config";

import { loadImage, mergeArraysById, getCurrentISO8601Time, getFuncNameByModelId } from "@/utils/tools";
import { postImageEdit } from "@/api";
import { useGlobalError } from "@/hook/error";
import { useCheckMaxTask, useAppendPreloadTask, longTaskDialog, translateOrEnhanceText, useGoToCreate } from "@/hook/create";
import { ERROR_CODE_ENUM, PRELOAD_TASK_TYPE, fluxKontextModelId } from "@/utils/constant";
import { t } from "@/utils/i18n-util";
const { isPc } = storeToRefs(useThemeStore());

import KontextModel from "@/components/KontextModel.vue";
const userProfile = useUserProfile();

const emits = defineEmits(["confirm", "cancel", "close"]);

const props = defineProps({
    item: {
        type: Object,
        default: () => ({}),
    },
});
const { toCreate } = useGoToCreate();
const { showError } = useGlobalError();
const lumenCostKey = computed(() => getFuncNameByModelId({ model_id: fluxKontextModelId }));

const shortcut = computed(() => {
    return userProfile.userConfig.shortcutKey === "enter";
});
// 翻译或增强提示词
const promptTranslating = ref({
    type: "",
    isLoading: false,
});
const isImgLoading = ref(true);
const imgInfo = ref({
    width: 0,
    height: 0,
});

const editPrompt = ref("");
const allowImprove = computed(() => !!editPrompt.value && editPrompt.value.length <= 600);

const handleClose = () => {
    emits("cancel");
    emits("close");
};
const shortcutKey = (event) => {
    event.stopPropagation();
    //组合键提交任务
    if (!shortcut.value && (event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        handleGenerate();
        return;
    }
};

//提示词增强或翻译
const handleImprove = async (mode) => {
    if (mode === "translation") {
        trackEvent("translate");
    } else {
        trackEvent("enhance");
    }
    if (!allowImprove.value || promptTranslating.isLoading) {
        editPrompt.value.length > 600 && openToast.info(t("SHORT_PROMPT_TOO_LONG"));
        return;
    }

    promptTranslating.value = { type: mode, isLoading: true };
    const { data } = await translateOrEnhanceText(editPrompt.value, mode);
    promptTranslating.value = { type: "", isLoading: false };
    editPrompt.value = data;
    promptTranslating.value = {};
};

const currentTaskQueue = useCurrentTaskQueue();
const isExceed = computed(() => {
    return currentTaskQueue.taskQueue.length >= currentTaskQueue.maxTask || currentTaskQueue.isLoading;
});
const handleGenerate = async () => {
    trackEvent("generate");
    if (isExceed.value) {
        return;
    }
    const { promptId, imgUrl, realWidth, realHeight, id, img_url = "" } = props.item;
    console.log(props.item, "props.item");
    const genParameters = {
        model_id: fluxKontextModelId,
        promptId,
        prompt: editPrompt.value,
        resolution: {
            width: realWidth || "",
            height: realHeight || "",
            batch_size: 1,
        },
        imgEditPara: {
            imgUrl: imgUrl || img_url,
            fileId: id,
        },
    };
    // debugger;
    console.log("genParameters", genParameters);
    // return;
    startEditTask(genParameters);
};

//任务开启
const startEditTask = async (conf) => {
    if (currentTaskQueue.isLoading) {
        openToast.error(t("TOAST_TASK_LIMIT"), { duration: 5e3 });
        return;
    }
    // 检查当前是否允许生图
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus && !checked.preloadStatus) {
        showError(ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR);
        return;
    }
    // 并发已满 提交预载
    if (!checked.concurrencyStatus) {
        useAppendPreloadTask({ genParameters: conf }, PRELOAD_TASK_TYPE.EDIT_IMAGE);
        handleClose();
        return;
    }
    currentTaskQueue.updateReqStatus(true);

    try {
        const { data, status, message } = await postImageEdit(conf);
        currentTaskQueue.updateReqStatus(false);
        //关闭 操作窗口，显示 原页面
        if (status === ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR) {
            showError(ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR);
            return;
        }
        // 耗时任务 二次确认 重新提交
        if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
            const hasReSubmit = await longTaskDialog();
            hasReSubmit && startEditTask(conf, true);
            return;
        }

        if (status !== 0) {
            showError(status);
            return;
        }
        let originCreate = "edit";
        const list = mergeArraysById(currentTaskQueue.taskQueue, [
            {
                ...conf,
                originCreate,
                markId: data.markId,
                status: "pending",
                fastHour: data.fastHour,
                index: data.index,
                createTimestamp: getCurrentISO8601Time(),
            },
        ]);
        currentTaskQueue.updateTaskQueue(list);
        isImgLoading.value = true;

        toCreate(); //任务创建成功 跳转到create页面或者关闭create的详情预览
        handleClose();
    } catch (error) {
        currentTaskQueue.updateReqStatus(false);
        openToast.error(error.message);
    }
};
const currentPrompt = ref(1);
let timer = null;
// prompt三选一 3秒切换
const handlePromptChange = () => {
    // 初始化值（可选）
    currentPrompt.value = Math.floor(Math.random() * 2);

    // 清除旧的定时器，避免重复注册
    if (timer) {
        clearInterval(timer);
    }

    // 启动轮询定时器，每 2.5 秒切换一次
    timer = setInterval(() => {
        currentPrompt.value = (currentPrompt.value + 1) % 3;
        console.log(currentPrompt.value, "currentPrompt.value");
    }, 2500);
};

onMounted(async () => {
    trackEvent("show");
    const { imgUrl, realHeight, realWidth } = props.item;
    editPrompt.value = "";
    if (!imgUrl) return;
    handlePromptChange();
    try {
        isImgLoading.value = true;
        imgInfo.value = {
            width: realWidth,
            height: realHeight,
        };
        await loadImage(imgUrl);
        setTimeout(() => {
            isImgLoading.value = false;
        }, 500);
    } catch (error) {
        imgInfo.value = {
            width: 0,
            height: 0,
        };
        console.log(error, "图片加载失败");
    }

    if (isPc.value && !userProfile.checkUsedFuncByKey("kotext_model")) {
        const { showMessage } = useModal();
        userProfile.refreshUsedFuncByKey("kotext_model");
        showMessage({
            style: { width: "1100px", height: "600px", padding: "0px", borderWidth: "0px" },
            showCancel: false,
            showConfirm: false,
            showLeftTopCloseBtn: true,
            escCloseable: true,
            content: h(KontextModel, {}),
        });
    }
});

const clearTimer = () => {
    if (timer) {
        clearTimeout(timer);
        timer = null;
    }
};

watch(
    () => editPrompt.value,
    (newVal) => {
        if (newVal) {
            clearTimer();
        } else {
            if (!timer) {
                handlePromptChange();
            }
        }
    }
);
onBeforeUnmount(() => {
    clearTimer();
});

const trackEvent = (type = "") => {
    if (!type) return;
    window.trackEvent("Create", { el: `edit_details_${type}` });
};
</script>
<style lang="scss" scoped>
.edit-input {
    ::v-deep(.n-input-wrapper) {
        @apply p-0;
    }
    ::v-deep(.n-input__textarea-el) {
        @apply p-0;
    }
    ::v-deep(.n-input__placeholder) {
        @apply text-text-4 p-0;
    }
    ::v-deep(.n-input__border) {
        @apply hidden;
    }
}
.tips-box {
    @apply bg-white text-dark-bg-2 dark:text-dark-text p-3 rounded dark:bg-dark-bg-2 max-w-96;
}
</style>
