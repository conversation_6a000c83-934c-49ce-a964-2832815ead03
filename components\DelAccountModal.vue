<template>
    <div class="relative">
        <h2 class="h-8 flex items-center text-base font-semibold text-text-1">
            <span>{{ t("PROFILE_DEL_ACCOUNT") }}</span>
        </h2>
        <div class="absolute top-0 -right-2 p-4 flex items-center justify-center size-8 cursor-pointer rounded-full transition-colors text-text-2 hover:bg-fill-wd-1" @click="handleCancel">
            <n-icon size="20">
                <IconsClose />
            </n-icon>
        </div>
        <div class="mt-10 flex justify-center">
            <n-icon size="48">
                <IconsDelAccount />
            </n-icon>
        </div>
        <h3 class="mt-4 text-xl font-medium text-text-2 text-center">{{ t("PROFILE_DEL_ACCOUNT_TITLE") }}</h3>
        <div class="mt-4 text-center text-sm font-medium text-text-2">{{ t("DEL_ACC.DEL_ACCOUNT_CONTENT") }}</div>

        <div class="mt-8 pb-6 relative">
            <div class="bg-fill-ipt-1 rounded-lg px-3 flex items-center">
                <n-icon size="20" class="text-text-4">
                    <IconsEmail />
                </n-icon>
                <input
                    type="email"
                    :placeholder="t('DEL_ACC.DEL_ACCOUNT_PLACEHOLDER')"
                    class="h-10 flex-1 p-2.5 bg-transparent rounded-lg border-none outline-none w-full text-sm font-medium"
                    maxlength="70"
                    :readonly="loading"
                    v-model="email"
                    @change="checkEmail"
                />
            </div>
            <div class="absolute left-0 bottom-0 right-0 text-xs text-[#961414]" v-if="checkErr">{{ t("PROFILE_ACCOUNT_INVALID_EMAIL") }}</div>
        </div>

        <div class="mt-6 flex justify-end gap-3 flex-wrap">
            <Button type="secondary" class="min-w-32 max-md:w-full" @click="handleCancel">{{ t("DEL_ACC.DEL_ACCOUNT_CANCEL") }}</Button>
            <Button type="danger" class="min-w-32 max-md:w-full" :loading="loading" @click="handleConfirm">{{ t("DEL_ACC.DEL_ACCOUNT_CONFIRM") }}</Button>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
import { delUserAccount } from "@/api";
import { validateEmail } from "@/utils/tools";
const props = defineProps(["email"]);
const emits = defineEmits(["confirm", "cancel"]);
const email = ref("");
const checkErr = ref(false);
const loading = ref(false);
const checkEmail = () => {
    checkErr.value = !validateEmail(email.value);
    return checkErr.value;
};
const handleCancel = () => {
    emits("cancel");
};
const handleConfirm = async () => {
    if (checkEmail() || loading.value) {
        return;
    }
    try {
        loading.value = true;
        const { status, message } = await delUserAccount({ feedbackEmail: email.value });
        loading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        emits("confirm");
    } catch (error) {
        emits("confirm");
    }
};
</script>

<style lang="scss" scoped>
::v-deep(.n-input .n-input__input-el) {
    @apply dark:text-dark-text;
}

.select-group::after {
    @apply content-[''] absolute left-1 top-1 bottom-1  h-8 rounded bg-neutral-200;
    transform: translateX(calc(100% * var(--index) + 2px * var(--index)));
    transition: transform 0.15s cubic-bezier(0.42, 0.05, 0.03, 0.96);
    width: calc(calc(100% - 12px) / 4);
}
.conf-icon-bar {
    @apply opacity-40 cursor-pointer hover:opacity-70;
}
.group-item {
    @apply flex-1 flex items-center justify-center rounded cursor-pointer opacity-70 transition-all duration-300 hover:opacity-100 z-10 h-8;
    &:hover {
        background: rgba(225, 225, 225, 0.7);
    }
}
.dark .group-item {
    &:hover {
        background: rgba(255, 255, 255, 0.1);
    }
}
.act-bar {
    @apply text-sm bg-black/15 dark:bg-white/15 shrink-0 w-6 h-6 rounded-sm flex items-center justify-center dark:text-dark-text opacity-75;
}
</style>
