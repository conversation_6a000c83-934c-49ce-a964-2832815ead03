<template>
    <div class="w-screen absolute z-10 bottom-0 pb-10" :class="isMobile ? '!pb-0' : ''" @click.stop>
        <div class="dark:text-white flex justify-end items-center gap-4 px-4 py-2">
            <div
                class="w-10 h-10 rounded-full bg-black text-white flex items-center justify-center relative"
                :class="[item.sensitive ? 'cursor-not-allowed opacity-40' : 'cursor-pointer']"
                @click="handleEditImage"
            >
                <n-icon size="22"><IconsChatEdit /></n-icon>
                <div class="absolute new-tag size-3 rounded-full z-10 top-0 right-0 leading-4"></div>
            </div>
            <div class="w-10 h-10 rounded-full bg-black text-white flex items-center justify-center" @click="trigger('edit')">
                <n-icon size="22"><IconsPencil /></n-icon>
            </div>
            <div class="w-10 h-10 rounded-full bg-black text-white flex items-center justify-center" @click="trigger('more')">
                <n-icon size="24" class="rotate-90"><IconsMore /></n-icon>
            </div>
        </div>
        <div class="rounded-t-xl bg-neutral-100 dark:bg-black pt-3 pb-12 px-4 text-sm dark:text-dark-text">
            <div v-if="showComponent">
                <div v-show="!isCustomUpload(item)" class="flex items-center justify-between dark:text-dark-desc-text">
                    <span>{{ t("COMMON_PROMPT") }}</span>
                    <n-icon v-show="item.prompt" size="16" @click="handelCopyText(item.prompt)"> <IconsCopyText /> </n-icon>
                </div>
                <div v-show="!isCustomUpload(item)" class="rounded-md bg-neutral-200 dark:bg-dark-bg p-2 px-3 mt-1.5 min-h-20">
                    <n-scrollbar class="max-h-28" style="word-wrap: break-word">
                        {{ item.prompt }}
                        <span v-if="!item.prompt" class="opacity-40">{{ t("COMMON_PROMPT") }}</span>
                    </n-scrollbar>
                </div>
                <div v-show="!isCustomUpload(item)" class="flex items-center justify-between mt-3 dark:text-dark-desc-text">
                    <span>{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>
                    <n-icon v-show="item.negative_prompt" size="16" @click="handelCopyText(item.negative_prompt)"> <IconsCopyText /> </n-icon>
                </div>
                <div v-show="!isCustomUpload(item)" class="rounded-md bg-neutral-200 dark:bg-dark-bg p-2 px-3 mt-1.5 min-h-20">
                    <n-scrollbar class="max-h-28">
                        {{ item.negative_prompt }}
                        <span v-if="!item.negative_prompt" class="opacity-40">{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>
                    </n-scrollbar>
                </div>

                <div class="rounded-md bg-neutral-200 dark:bg-dark-bg p-2 px-3 mt-3">
                    <div v-if="!isCustomUpload(item)" class="h-9 flex items-center gap-2">
                        <span class="dark:text-dark-desc-text mr-auto">Model</span>
                        <span class="px-2">{{ modelInfo(item).label }}</span>
                        <img :src="modelInfo(item).icon" class="rounded w-6 h-6" />
                    </div>
                    <div class="h-9 flex items-center gap-2">
                        <span class="dark:text-dark-desc-text mr-auto">Date Created</span>
                        <span>{{ timeFormat(item) }}</span>
                    </div>
                    <div v-if="!isCustomUpload(item) && item.seed" class="h-9 flex items-center gap-2">
                        <span class="dark:text-dark-desc-text mr-auto">Seed</span>
                        <span>{{ item.seed }}</span>
                        <n-icon v-show="item.seed" size="16" class="dark:text-dark-desc-text" @click="handelCopyText(item.seed)"> <IconsCopyText /> </n-icon>
                    </div>
                    <div v-if="item.realWidth && item.realHeight" class="h-9 flex items-center gap-2">
                        <span class="dark:text-dark-desc-text mr-auto">Resolution</span>
                        <span>{{ item.realWidth }} x {{ item.realHeight }}</span>
                    </div>
                    <div v-if="!isCustomUpload(item) && item.cfg" class="h-9 flex items-center gap-2">
                        <span class="dark:text-dark-desc-text mr-auto">Guidance Scale</span>
                        <span>{{ item.cfg }}</span>
                    </div>
                </div>
            </div>
            <div v-if="isEditPanel" class="pb-6">
                <div class="text-center py-4 dark:text-dark-desc-text">Select a tool to edit this image</div>
                <div class="grid grid-cols-3 gap-2 mt-4">
                    <div class="flex flex-col gap-2 items-center justify-center p-3 rounded-md bg-white dark:bg-dark-bg-3 relative" @click="handleToRemix">
                        <n-icon size="24">
                            <IconsRemix />
                        </n-icon>
                        <span>{{ t("TOOLBAR_REMIX") }}</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': disabledFn('removeBg') }"
                        @click="handleFnClick('removeBg')"
                    >
                        <n-tooltip placement="top" :show-arrow="false" raw>
                            <template #trigger>
                                <div class="flex flex-col justify-center items-center gap-2">
                                    <n-icon size="24"> <IconsRemoveBg /> </n-icon>
                                    <span>{{ $t("FEATURE_BG_REMOVER_FEATURE_TITLE_SHORT") }}</span>
                                </div>
                            </template>
                            <div class="text-sm text-nowrap text-text-2 bg-bg-5 rounded-lg py-2 px-3">
                                {{
                                    $t(
                                        "ESTIMATE_COST_LUMENS",
                                        {
                                            num: 1,
                                        },
                                        3
                                    )
                                }}
                            </div>
                        </n-tooltip>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': disabledFn('upscale') }"
                        @click="handleFnClick('upscale')"
                    >
                        <n-icon size="24">
                            <IconsHD />
                        </n-icon>
                        <span>{{ t("FEATURE_HIR_FIX_TITLE") }}</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': disabledFn('inpaint') }"
                        @click="handleFnClick('inpaint')"
                    >
                        <n-icon size="24">
                            <IconsCreate />
                        </n-icon>
                        <span>{{ t("TOOLBAR_INPAINT") }}</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': disabledFn('colorize') }"
                        @click="handleFnClick('colorize')"
                    >
                        <n-icon size="24">
                            <IconsColorize />
                        </n-icon>
                        <span>{{ t("LINE_ART") }}</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': disabledFn('outpaint') }"
                        @click="handleFnClick('outpaint')"
                    >
                        <n-icon size="24">
                            <IconsExpand />
                        </n-icon>
                        <span>{{ t("FEATURE_OUTPAINT_TITLE") }}</span>
                    </div>
                </div>
            </div>
            <div v-if="isMorePanel" class="pb-6">
                <div class="pt-4 dark:text-dark-desc-text">Share to</div>
                <div class="grid grid-cols-4 gap-2 mt-1.5 text-xs">
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3.5 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': isSensitive || isCustomUpload(item) }"
                        @click="shareTo('X')"
                    >
                        <n-icon size="24">
                            <IconsShareTwitter />
                        </n-icon>
                        <span>X</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3.5 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': isSensitive || isCustomUpload(item) }"
                        @click="shareTo('Pinterest')"
                    >
                        <n-icon size="24">
                            <IconsSharePinterest />
                        </n-icon>
                        <span>Pinterest</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3.5 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': isSensitive || isCustomUpload(item) }"
                        @click="shareTo('Facebook')"
                    >
                        <n-icon size="24">
                            <IconsShareFacebook />
                        </n-icon>
                        <span>Facebook</span>
                    </div>
                    <div
                        class="flex flex-col gap-2 items-center justify-center p-3.5 rounded-md bg-white dark:bg-dark-bg-3 relative"
                        :class="{ 'opacity-40': isSensitive || isCustomUpload(item) }"
                        @click="shareTo('Reddit')"
                    >
                        <n-icon size="24">
                            <IconsShareReddit />
                        </n-icon>
                        <span>Reddit</span>
                    </div>
                </div>
                <div class="mt-4 bg-white dark:bg-dark-bg-3 rounded-lg px-4">
                    <div
                        @click="postToCommunity"
                        class="border-b border-solid dark:border-white/5 border-neutral-100 flex items-center py-4 gap-2"
                        :class="{ 'opacity-40': item.isPublic !== 0 || isSensitive || isCustomUpload(item) }"
                    >
                        <n-icon size="20">
                            <IconsCommunities />
                        </n-icon>
                        <span>{{ publicStateTxt(item.isPublic) }}</span>
                        <n-icon v-if="item.isPublic === 3" size="20" class="ml-auto">
                            <IconsAlert />
                        </n-icon>
                    </div>

                    <DownLoadPopover :link="item.highThumbnailUrl || item.imgUrl" :thumbnail="item.thumbnailUrl || item.highThumbnailUrl || item.imgUrl" :isSensitive="isSensitive">
                        <div class="border-b border-solid dark:border-white/5 border-neutral-100 flex items-center py-4 gap-2">
                            <n-icon size="20">
                                <IconsDownload />
                            </n-icon>
                            <span>{{ t("TOOLBAR_DOWNLOAD") }}</span>
                        </div>
                    </DownLoadPopover>
                    <div v-if="!fromCollect" class="flex items-center py-4 gap-2" :class="{ 'opacity-40': isSensitive }" @click="handleAddToCollection">
                        <n-icon size="20">
                            <IconsCollectionMark />
                        </n-icon>
                        <span>{{ t("MENU_COLLECTION_TIPS") }}</span>
                    </div>
                </div>
                <div class="mt-4 bg-white dark:bg-dark-bg-3 rounded-lg px-4">
                    <div
                        class="border-b border-solid dark:border-white/5 border-neutral-100 flex items-center py-4 gap-2"
                        :class="{ 'opacity-40': isSensitive || isCustomUpload(item) }"
                        @click="shareTo()"
                    >
                        <n-icon size="20">
                            <IconsShareLink />
                        </n-icon>
                        <span>{{ t("TOOLBAR_COPY_LINK") }}</span>
                    </div>
                    <div class="flex items-center py-4 gap-2" @click="setCopyToClipboard()">
                        <n-icon size="20"> <IconsCopyText /> </n-icon>
                        <span>{{ t("TOOLBAR_COPY_FULL_INFO") }}</span>
                    </div>
                </div>
                <div class="mt-4 bg-white dark:bg-dark-bg-3 rounded-lg px-4 text-error">
                    <div v-if="!fromCollect" class="flex items-center py-4 gap-2" @click="handleDelItem">
                        <n-icon size="20">
                            <IconsDele />
                        </n-icon>
                        <span>{{ t("TOOLBAR_DELETE") }}</span>
                    </div>
                    <div v-if="fromCollect" class="flex items-center py-4 gap-2" @click="handleDelItemFromCollect">
                        <n-icon size="20">
                            <IconsDele />
                        </n-icon>
                        <span>Remove from Collections</span>
                    </div>
                </div>

                <n-drawer v-model:show="showCollection" width="100vw" height="90vh" class="bg-neutral-100 dark:bg-dark-bg p-0" placement="bottom">
                    <n-drawer-content :native-scrollbar="false">
                        <div class="flex justify-between items-center">
                            <div class="w-8 h-8 flex justify-center items-center rounded-full bg-neutral-200 dark:bg-dark-bg-2" @click="showCollection = false">
                                <n-icon :size="20">
                                    <IconsClose />
                                </n-icon>
                            </div>
                            <span class="text-base">{{ t("MENU_COLLECTION_TIPS") }}</span>
                            <p class="text-base text-primary" @click="handleAddColl">{{ t("COMMON_BTN_OK") }}</p>
                        </div>

                        <div class="mt-6 rounded-xl bg-white dark:bg-dark-bg-2 px-4">
                            <n-scrollbar class="h-[79vh]">
                                <div
                                    v-for="item in shareCollect.collectList || []"
                                    :key="item.id"
                                    class="border-b border-solid dark:border-white/5 border-neutral-100 flex justify-between items-center py-4 gap-2 space-between"
                                    @click="handleCheckCollection(item)"
                                >
                                    <span> {{ item.collectName }}</span>
                                    <n-icon size="20" class="text-primary" v-if="checkCollectionId === item.id">
                                        <IconsSuccess />
                                    </n-icon>
                                </div>
                            </n-scrollbar>
                        </div>
                    </n-drawer-content>
                </n-drawer>
            </div>
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    item: {
        type: Object,
        required: true,
    },
    fromCollect: {
        type: Boolean,
        default: false,
    },
});
const localePath = useLocalePath();
const { t } = useI18n({ useScope: "global" });
import { formatDate, copyToClipboard, copyAllParams, isMjModel, isFluxDevModel, isFluxKontextModel, handleResetParams } from "@/utils/tools";
import { useMaintainPostCount, useDeleteImageConfirm, useInitCollection, useUpdateCloudStorage } from "@/hook/updateAccount";
import { renderModelIcon } from "@/utils/tools";
import { NIcon } from "naive-ui";
import { Alert } from "@/icons/index.js";
import { useSyncAction } from "@/stores/syncAction";
import { useSubPermission } from "@/hook/subscribe";
import { SUBSCRIBE_PERMISSION } from "@/utils/constant.js";
import { publishToCommunity, deleteResult, collectionImg } from "@/api";
import { useDailyPostPicLimit, useForceUpdatePageState } from "@/stores";
import PublishModal from "@/components/PublishModal.vue";

import { useTheme } from "@/hook/system.js";
import { useGetModelInfo, useImageEdit, useRemix, useUpscale, useRemoveBg, useInpaint, useOutpaint, useColorize, usePhotoCrop } from "@/hook/create";

const { isMobile } = storeToRefs(useThemeStore());
const dailyPostPicLimit = useDailyPostPicLimit();
const maintainPostCount = useMaintainPostCount();
const { checkPermission, checkPermissionNotModal } = useSubPermission();
const syncAction = useSyncAction();

const emits = defineEmits(["update:showPrompt", "removeItem"]);

const isCustomUpload = computed(() => {
    return (item) => {
        return item.originCreate === "customUpload" || item.originCreate === "crop";
    };
});
import { communityHotTags } from "@/utils/tools";

const isEditPanel = ref(false);
const isMorePanel = ref(false);
const showComponent = computed(() => !isEditPanel.value && !isMorePanel.value);
//模型选择组件渲染函数
const modelInfo = computed(() => {
    return (item) => {
        const option = useGetModelInfo(item.model_id);
        const model_id = option.value;
        let icon = renderModelIcon(model_id);
        return { ...option, icon };
    };
});
const timeFormat = computed(
    () =>
        ({ createTimestamp, createTime }) =>
            formatDate(createTimestamp || createTime)
);
//isPublic 当前图片审核状态  0未提交  1已公开  2审核中  3已拒绝
const stateMap = ["COMMUNITY_PUBLISH", "COMMUNITY_PUBLISHED", "COMMUNITY_PUBLIC_REVIEW", "COMMUNITY_PUBLIC_REJECT"];
const publicStateTxt = (status) => {
    return t(stateMap[status] || "COMMUNITY_PUBLISH");
};
const { showMessage, clearMessageBox } = useModal();
const isSensitive = computed(() => !!props.item.sensitive);
//发布到社区 或者 提示被拒绝的信息
const postToCommunity = () => {
    const isReject = props.item.isPublic === 3;
    if (isCustomUpload.value(props.item) || isSensitive.value) {
        return;
    }
    if (isReject) {
        clearMessageBox();
        showMessage({
            style: { width: "420px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, props.item.rejectionContent),
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
            title: t("INFO_TITLE"),
        })
            .then()
            .catch(() => {});
        return;
    }
    if (props.item.isPublic !== 0) {
        return;
    }

    publicConf.value = {
        publicType: "everyone",
        brief: "",
    };

    const { thumbnailUrl, promptId, imgName } = props.item;

    showMessage({
        style: { width: "343px" },
        showCancel: false,
        showConfirm: false,
        zIndex: 5000,
        content: h(PublishModal, {
            base: {
                imgUrl: thumbnailUrl,
                imgName: imgName,
                promptId: promptId,
            },
        }),
    }).then((_) => {
        props.item.isPublic = 1; //强制设置为已公开
    });
};

const publicConf = ref({
    publicType: "everyone",
    brief: "",
});
const chooseTag = (tag) => {
    publicConf.value.brief = `${publicConf.value.brief} #${tag}`;
};

const trigger = (type) => {
    isEditPanel.value = type === "edit";
    isMorePanel.value = type === "more";
    if (isMorePanel.value) {
        maintainPostCount.asyncUpdateLimit();
    }
};
const { openModal: openImageEdit } = useImageEdit();
const { openModal: openInpaint } = useInpaint();
const { openModal: openOutpaint } = useOutpaint();
const { openModal: openColorize } = useColorize();
const { openModal: openCroper } = usePhotoCrop();

const handleEditImage = () => {
    window.trackEvent("Create", { el: `H5_detail_edit` });
    let isEditingImg = props.item;
    //二次编辑图片
    if (isEditingImg.sensitive) return;
    openImageEdit({ item: isEditingImg });
};

const handelCopyText = (text) => {
    copyToClipboard(text);
    openToast.success(t("TOAST_COPY_SUCCESS"));
};

//全参数复制
const setCopyToClipboard = () => {
    const task = { ...props.item };
    const modelName = useGetModelInfo(task.model_id)?.label;
    copyAllParams(task, modelName);
    openToast.success(t("TOAST_COPY_SUCCESS"));
};
//分享
const shareTo = async (platform) => {
    if (isSensitive.value || isCustomUpload.value(props.item)) {
        return;
    }

    window.trackEvent("APP_SHARE", { el: `share=${platform}` });
    let href = null;
    // if (props.community) {
    //     href = encodeURIComponent(window.location.host + `/app/community/detail/${props.id}`);
    // } else {
    href = encodeURIComponent(window.location.host + `/app${localePath("/image/share")}?promptId=${props.item.promptId}&imageName=${props.item.imgName}&gId=${btoa(props.item.loginName)}`);
    // }
    const pageTitle = encodeURIComponent(document.title);
    let link = "";
    const imgUrl = encodeURIComponent(props.item.highMiniUrl || props.item.thumbnailUrl || props.item.highThumbnailUrl || props.item.imgUrl);

    switch (platform) {
        case "Facebook":
            link = `https://www.facebook.com/sharer/sharer.php?u=${href}&id=61562588510786`;
            break;
        case "X":
            link = `https://twitter.com/intent/tweet?url=${href}&text=${pageTitle}&hashtags=PicLumen,PicLumenArt,PicLumenAI`;
            break;
        case "Pinterest":
            link = `https://pinterest.com/pin/create/button/?url=${href}&media=${imgUrl}&description=${pageTitle}`;
            break;
        case "Reddit":
            link = `https://reddit.com/submit?url=${href}&title=${pageTitle}`;
            break;
        default:
            link = href;
    }
    if (link === href) {
        link = decodeURIComponent(href);
        await copyToClipboard(link);
        openToast.success(t("TOAST_COPY_SUCCESS"));
        return;
    }
    window.open(link, "_blank");
};

// 提交到社区
const submitLoading = ref(false);
const handlePostSubmit = async () => {
    if (submitLoading.value) {
        return;
    }
    if (dailyPostPicLimit.limit <= 0) {
        clearMessageBox();
        showMessage({
            style: { width: "420px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, t("COMMUNITY_PUBLIC_LIMIT")),
            icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_COMMUNITY_BETA"),
        })
            .then()
            .catch(() => {});

        return;
    }
    submitLoading.value = true;
    const { imgName, promptId } = props.item;
    const { status, message } = await publishToCommunity({
        imgName,
        promptId,
        ...publicConf.value,
    });
    submitLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    maintainPostCount.updateCount();
    showPostToCommunity.value = false;
    syncAction.publish("updateImg", {
        updateKey: "isPublic",
        updateValue: 2,
        promptId,
        imgName,
    });
};
//删除图片
const delLoading = ref(false);
const handleDelItem = async () => {
    if (delLoading.value) {
        openToast.info(t("DEL_TIPS"));
        return;
    }
    window.trackEvent("APP_DELETE_IMG", { el: `delete_btn` });
    const delCheck = await useDeleteImageConfirm();
    if (!delCheck) {
        return;
    }
    emits("update:showPrompt", false);
    try {
        delLoading.value = true;
        const { imgName, promptId, loginName } = props.item;
        const { status, message } = await deleteResult({ promptId, imgName, loginName });
        delLoading.value = false;
        if (status != 0) {
            openToast.error(message);
            return;
        }
        const toDelete = new Set([imgName]);
        syncAction.publish("batchDelImg", toDelete);
        openToast.success(t("ACTION_SUCCESS_TIPS"));
    } catch (error) {
        delLoading.value = false;
    }
};
const showCollection = ref(false);
const checkCollectionId = ref("");
import { useShareCollect } from "@/stores";
const shareCollect = useShareCollect();
const handleAddToCollection = async () => {
    if (isSensitive.value) {
        return;
    }
    const { success, data } = await useInitCollection();
    const totalCount = data.reduce((accumulator, currentValue) => {
        return accumulator + Number(currentValue.collectNums || 0);
    }, 0);
    const allowCollection = checkPermissionNotModal(SUBSCRIBE_PERMISSION.CLOUD_STORAGE_COUNT, totalCount + 1);
    if (!allowCollection) {
        showMessage({
            showCancel: false,
            style: { width: "420px" },
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, [h("p", { class: "tracking-wider" }, t("COLLECTION_STORAGE_LIMIT_MESSAGE"))]),
            icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
            title: t("DIALOG_TITLE_NOTICE"),
        });
        return;
    }
    checkCollectionId.value = data[0]?.id;
    showCollection.value = true;
};
const handleCheckCollection = ({ id }) => {
    checkCollectionId.value = id;
};
//确认收藏
const handleAddColl = async () => {
    showCollection.value = false;
    const param = { promptId: props.item.promptId, imgName: props.item.imgName, classifyId: checkCollectionId.value };
    const { status, data = {}, message } = await collectionImg(param);
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    useUpdateCloudStorage(data);
    openToast.success(t("COLLECTION_ADD_SUCCESS"));
};

//更多操作
// remix
const handleToRemix = () => {
    useRemix(props.item);
};
// 去背景
const { removeBg } = useRemoveBg();
const handleToRemoveBg = () => {
    const base = handleResetParams(props.item);
    const { imgUrl, promptId, model_id, loginName } = props.item;
    removeBg(base, {
        imgUrl,
        promptId: promptId,
        modelId: model_id,
        loginName: loginName,
    });
};
// upscale
const { hiresFixTask } = useUpscale();
const handleToHireFix = async () => {
    const base = handleResetParams(props.item);
    const { realWidth, realHeight } = props.item;

    const { imgUrl, promptId, model_id, loginName } = props.item;
    const expandConf = {
        imgUrl,
        promptId,
        model_id,
        loginName,
        realWidth,
        realHeight,
    };

    hiresFixTask(base, {
        ...expandConf,
    });
};

// 局部重绘
//cropper
//inpaint
const handleInpaint = () => {
    const { item } = props;
    if (item.sensitive) return;
    openInpaint({ item });
};
//Colorize
const handleColorize = () => {
    const { item } = props;
    if (item.sensitive) return;
    openColorize({ item });
};
//Expand
const handleOutpaint = () => {
    const { item } = props;
    if (item.sensitive) return;
    openOutpaint({ item });
};

const handleFnClick = (type) => {
    if (disabledFn(type)) return;
    switch (type) {
        case "removeBg":
            handleToRemoveBg();
            break;
        case "upscale":
            handleToHireFix();
            break;
        case "outpaint":
            handleOutpaint();
            break;
        case "inpaint":
            handleInpaint();
            break;
        case "colorize":
            handleColorize();
            break;
    }
};

const modelNotSupport = computed(() => isMjModel(props.item) || isFluxDevModel(props.item) || isFluxKontextModel(props.item) || isFluxKreaModel(props.item));

const disabledFn = (type) => {
    const { model_id } = props.item;
    switch (type) {
        case "inpaint":
        case "colorize":
        case "outpaint":
            return isSensitive.value || modelNotSupport.value;
        case "removeBg":
            return isSensitive.value;
        case "upscale":
            return isSensitive.value || isFluxKreaModel(props.item); // upscale krea暂时不可用

        default:
            return false;
    }
};
// 从收藏夹移除
const handleDelItemFromCollect = () => {
    emits("removeItem");
};
</script>
<style scoped></style>
