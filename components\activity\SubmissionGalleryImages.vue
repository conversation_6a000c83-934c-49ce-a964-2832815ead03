<template>
    <div class="flex1 grid grid-cols-6 gap-2 ">
        <div v-for="(item, index) in list" :key="item.promptId" class="flex relative justify-between items-center rounded-lg overflow-hidden"  @click="handleImg(item, index)">
            <img
                loading="lazy"
                :src="item.highMiniUrl || item.thumbnailUrl"
                @error="item.loaded = true"
                @load="item.loaded = true"
                alt=""
                class="w-[132px] h-[132px] object-cover cursor-pointer"
                :class="{ loading_bg_anime: !item.loaded }"
            />
            <div class="absolute w-6 h-6 top-2 right-2 flex items-center justify-center rounded-full bg-fill-t-2 text-[#D9D9D9] backdrop-blur-lg" :class="{ 'bg-primary-6': selectedImagesUrls.includes(item.imgUrl) }">
                <n-icon size="20">
                    <IconsSuccess />
                </n-icon>
            </div>
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    selectedList: {
        type: Array,
        default: () => [],
    },
})

const selectedImagesUrls = computed(() => {
    return props.selectedList.map(item => item.imgUrl)
})
const emits = defineEmits(['select'])

const handleImg = (item) => {
    emits('select', item)
}


</script>

<style lang="scss" scoped>
.swiper-slide{
    width: auto !important;
    background: none !important;
}
</style>

