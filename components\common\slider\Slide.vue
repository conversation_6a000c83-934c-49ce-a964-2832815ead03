<template>
    <div class="relative w-full h-full no-drag">
        <input
            type="range"
            :min="min"
            :max="max"
            :step="step"
            :disabled="disabled"
            :value="modelValue"
            @input="handleInput"
            @change="handleChange"
            @blur="$emit('blur')"
            class="slide-range w-full absolute top-0 left-0 bottom-0 right-0"
            :class="{ '!cursor-not-allowed opacity-40 slide-range__disabled': disabled }"
            :style="{
                height: `${height}px`,
            }"
        />
        <div
            class="active-thumb w-full absolute pointer-events-none"
            :style="{
                width: modelValue < 0 ? `calc(${size}% - 12px)` : `${size > 100 ? 100 : size}%`,
                left: backgroundPositionX,
                transform: modelValue < 0 ? 'rotateY(180deg)' : '' /* 水平翻转 */,
                height: `${height}px`,
            }"
        ></div>
    </div>
</template>
<script setup>
const emit = defineEmits(["update:modelValue", "change", "input", "blur"]);
const props = defineProps({
    modelValue: {
        type: Number,
        default: 1,
    },
    min: {
        type: Number,
        default: 0,
    },
    max: {
        type: Number,
        default: 1,
    },
    step: {
        type: Number,
        default: 0.1,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    height: {
        type: Number,
        default: 8,
    },
});
const size = computed(() => {
    let { modelValue, min, max } = props;
    modelValue = Math.abs(modelValue);
    let finalSize = 1;
    if (min >= 0) {
        finalSize = (modelValue - min) / (max - min);
    } else {
        finalSize = modelValue / (max + Math.abs(min));
    }
    return Math.floor(Math.abs(finalSize) * 100);
});
const backgroundPositionX = computed(() => {
    let { min, max } = props;
    let absMin = Math.abs(min);
    const allLen = absMin + max;
    const ratio = absMin / allLen;
    if (min > 0 && max > 0) {
        return 0;
    } else {
        return `${ratio * 100}%`; // 确保background-position-x在变化时从固定位置开始
    }
});

const handleInput = ({ target }) => {
    emit("update:modelValue", Number(target.value));
    emit("input", Number(target.value));
};
const handleChange = ({ target }) => {
    emit("update:modelValue", Number(target.value));
    emit("change", Number(target.value));
};
</script>
<style lang="scss" scoped>
//默认轨道
input[type="range"] {
    @apply outline-none appearance-none w-full bg-fill-slider-3 m-0 rounded-full;
}
// 滑块
input[type="range"]::-webkit-slider-thumb {
    fill: var(----fill-slider-5, #fff);
    filter: drop-shadow(0 8px 24px rgba(0, 0, 0, 0.16)) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.12));
    @apply appearance-none size-6 bg-fill-slider-5 rounded-full cursor-pointer  z-10 relative;
}
input[type="range"]::-moz-range-thumb {
    @apply appearance-none h-4 w-1 bg-fill-slider-1 rounded-full cursor-pointer z-10 relative border-[2px];
}
.slide-range__disabled {
    cursor: not-allowed !important;
    &::-webkit-slider-thumb,
    &::-moz-range-thumb {
        cursor: not-allowed !important;
    }
}
// 已经激活的轨道
.active-thumb {
    transform-origin: left;
    @apply z-[5] bg-primary rounded-l-full top-0;
}
</style>
