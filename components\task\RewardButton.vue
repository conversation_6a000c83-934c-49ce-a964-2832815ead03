<template>
    <div>
        <!-- 有可以领取奖励的，就展示领取按钮 -->
        <Button v-if="canClaimTaskIds.length" class="min-w-[88px]" @click="handleClaim()" :loading="loading">
            {{ $t("TASKS_CENTER_CLAIM") }}
        </Button>
        <!-- 所有任务均已完成 且已领取奖励 -->
        <Button v-if="allFinished && !canClaimTaskIds.length" class="min-w-[88px] cursor-default" type="secondary">
            <n-icon size="20">
                <IconsSuccess />
            </n-icon>
        </Button>
        <Button v-if="!allFinished" type="secondary" @click="handleToPage()" class="min-w-[88px]">
            {{ $t("TASKS_CENTER_GO") }}
        </Button>
    </div>
</template>
<script setup>
import { useTaskStore } from "@/stores/task";

import { TASK_STATE_MAP } from "@/constants/taskCenter";
import { computed } from "vue";

const taskStore = useTaskStore();
const props = defineProps({
    tasks: {
        type: Array,
        default: () => [],
    },
});

const loading = ref(false);

/** 可领取lumen的任务 */
const canClaimTasks = computed(() => {
    return props.tasks.filter((item) => !item.beReward && item.beFinish);
});
/** 可领取lumen的任务ID */
const canClaimTaskIds = computed(() => {
    return canClaimTasks.value.map((item) => item.taskId);
});
/** 所有任务均已完成 */
const allFinished = computed(() => {
    return props.tasks.every((item) => item.beFinish);
});
/** 任务ID，取首个即可，用于单个个任务使用 */
const firstTaskId = computed(() => {
    return props.tasks[0].taskId;
});
/** 待完成任务 */
const toDoTask = computed(() => {
    return props.tasks.find((item) => !item.beFinish);
});
/** 待完成任务Id */
const toDoTaskId = computed(() => {
    return toDoTask.value?.taskId;
});

/** 跳转页面 */
const localePath = useLocalePath();
const handleToPage = async () => {
    const taskId = toDoTaskId.value;
    const { path, externalLink, jumpedAutoFinish } = TASK_STATE_MAP[taskId] || {};
    trackTaskByIds([taskId], "go");
    // 站内链接
    if (path) {
        await navigateTo(localePath(path));
    }
    // 站外链接
    else if (externalLink) {
        window.open(externalLink, "_blank");
        // 跳转后自动完成任务
        if (jumpedAutoFinish) {
            autoFinishedTask();
        }
    }
};

/** 自动完成任务 */
const autoFinishedTask = async () => {
    await taskStore.finishTask(firstTaskId.value);
    taskStore.getTaskList();
};
/** 领取任务奖励 */
const handleClaim = async () => {
    if (loading.value || !canClaimTaskIds.value.length) {
        return;
    }
    loading.value = true;
    await taskStore.getTaskReward(canClaimTaskIds.value).finally(() => {
        loading.value = false;
    });
    trackTaskByIds(canClaimTaskIds.value, "claim");
    taskStore.getTaskList();
};
/** 按钮埋点，点击领取可以同时领取多个 */
const trackTaskByIds = (ids, behave) => {
    try {
        ids.forEach((id) => {
            const taskMap = TASK_STATE_MAP[id] || {};
            const { trackEvent, trackEventTask } = taskMap;
            if (trackEvent && trackEventTask) {
                taskStore.trackEvent(`${trackEvent}#task=${trackEventTask}#behave=${behave}`);
            }
        });
    } catch {}
};
</script>

<style lang="scss" scoped></style>
