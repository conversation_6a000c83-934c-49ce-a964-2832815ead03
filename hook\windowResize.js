import { onMounted, onUnmounted, ref } from "vue";
function debounce(func, wait) {
    let timeout;
    return function () {
        const context = this,
            args = arguments;
        timeout && clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}
function useWindowResize() {
    const width = ref(1920);
    const height = ref(1080);

    const onResize = debounce(function () {
        width.value = window.innerWidth;
        height.value = window.innerHeight;
    }, 60);

    onMounted(() => {
        window.addEventListener("resize", onResize);
        onResize();
    });

    onUnmounted(() => {
        window.removeEventListener("resize", onResize);
    });

    return { width, height };
}

export default useWindowResize;
