<template>
    <div class="re-create__container">
        <div class="re-create__close" @click="handleClose()">
            <IconsClose class="size-4 lg:size-6" />
        </div>
        <div class="flex-1 pb-[88px] relative">
            <div ref="wapperRef" class="absolute top-6 right-6 bottom-[88px] left-6 overflow-hidden">
                <canvas ref="canvasRef" class="w-full h-full"></canvas>
                <ShapeCreateCanvas
                    v-if="handlerControl !== 'rect'"
                    :type="handlerControl"
                    :isLasso="isLasso"
                    :isEraser="isEraser"
                    :pencil-width="strokeWidth"
                    ref="svgCreateRef"
                    @createShape="createPath"
                />
            </div>
            <div class="absolute bottom-4 left-1/2 -translate-x-1/2 text-text-2 max-w-full">
                <div class="mx-auto p-2 rounded-xl bg-bg-2 border border-border-1 h-14 max-w-max flex justify-center items-center gap-3">
                    <div
                        class="h-10 w-10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-fill-wd-1"
                        :class="{ 'bg-fill-wd-1 text-primary-6': 'brush' === handlerType }"
                        @click="changeHandlerType('brush')"
                    >
                        <n-icon size="24">
                            <IconsBrush />
                        </n-icon>
                    </div>
                    <div
                        class="h-10 w-10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-fill-wd-1"
                        :class="{ 'bg-fill-wd-1 text-danger-6': 'eraser' === handlerType }"
                        @click="changeHandlerType('eraser')"
                    >
                        <n-icon size="24">
                            <IconsEraser />
                        </n-icon>
                    </div>

                    <div class="w-[1px] h-6 bg-border-2"></div>
                    <div
                        class="h-10 w-10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-fill-wd-1"
                        :class="{ 'bg-fill-wd-1 text-primary-6': 'rect' === handlerControl }"
                        @click="changeControlType('rect')"
                    >
                        <n-icon size="24">
                            <IconsSizeLarge />
                        </n-icon>
                    </div>
                    <div
                        class="h-10 w-10 rounded-lg items-center justify-center cursor-pointer hover:bg-fill-wd-1 hidden temp:flex"
                        :class="{ 'bg-fill-wd-1 text-primary-6': 'lasso' === handlerControl }"
                        @click="changeControlType('lasso')"
                    >
                        <n-icon size="24">
                            <IconsLasso />
                        </n-icon>
                    </div>
                    <div
                        class="h-10 w-10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-fill-wd-1"
                        :class="{ 'bg-fill-wd-1 text-primary-6': 'pencil' === handlerControl }"
                        @click="changeControlType('pencil')"
                    >
                        <n-icon size="24">
                            <IconsPencil />
                        </n-icon>
                    </div>
                    <n-slider :disabled="'pencil' !== handlerControl" class="w-44 text-primary-6" v-model:value="brushDiameter" :step="1" :max="100" :min="10" />

                    <div
                        class="px-3 min-w-16 text-center rounded-full h-[30px] items-center justify-center text-text-6 shrink-0 hidden temp:flex"
                        :class="{ 'bg-fill-wd-1 !text-text-2': 'pencil' === handlerControl }"
                    >
                        <span>{{ brushDiameter }} px</span>
                    </div>

                    <div class="w-[1px] h-6 bg-border-2"></div>

                    <div class="h-10 w-10 rounded-lg flex items-center justify-center cursor-pointer hover:bg-fill-wd-1" @click="handleUndo">
                        <n-icon size="24">
                            <IconsUndo />
                        </n-icon>
                    </div>

                    <div class="px-2 text-center cursor-pointer rounded-full hover:bg-fill-wd-1 h-[30px] flex items-center justify-center" @click="handelResetCanvas">
                        <span>{{ t("CONFIG_BASE_RESET_BTN") }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-[72px] bg-bg-2 px-6 flex items-center gap-4 rounded-lg">
            <ModelDrop :models="[fluxDevModelId, realisticModelId]" :modelId="checkedModelId" @chooseModel="chooseModel" v-if="false" />

            <div class="flex-1 bg-fill-ipt-1 rounded-lg h-11 flex items-center gap-4 pr-4 text-text-4">
                <n-input type="text" :placeholder="t('COMMON_PROMPT')" v-model:value="inpaintPrompt" :maxlength="2500" class="flex-1 min-w-0 text-sm text-text-2" @keydown.stop="shortcutKey" />

                <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <n-icon size="24" class="shrink-0" :class="{ 'text-text-2 cursor-pointer hover:text-text-1': allowImprove }" @click="handleImprove('translation')">
                            <IconsSpinLoading v-if="promptTranslating.isLoading && promptTranslating.type === 'translation'" class="text-primary" />
                            <IconsTranslate v-else />
                        </n-icon>
                    </template>
                    <div class="tips-box">
                        <div class="font-medium text-text-1 flex items-center gap-2">
                            <span>{{ t("SHORT_PROMPT_TRANS_TITLE") }}</span>
                        </div>
                        <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_TRANS_MESSAGE") }}</div>
                    </div>
                </n-tooltip>

                <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <n-icon size="24" class="shrink-0" :class="{ 'text-text-2 cursor-pointer hover:text-text-1': allowImprove }" @click="handleImprove('enhance')">
                            <IconsSpinLoading v-if="promptTranslating.isLoading && promptTranslating.type === 'enhance'" class="text-primary" />
                            <IconsImprove v-else />
                        </n-icon>
                    </template>

                    <div class="tips-box">
                        <div class="font-medium text-text-1 flex items-center gap-2">
                            <span>{{ t("SHORT_PROMPT_IMPROVE_TITLE") }}</span>
                        </div>
                        <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_IMPROVE_MESSAGE") }}</div>
                    </div>
                </n-tooltip>
            </div>

            <n-tooltip placement="bottom" trigger="hover" :delay="100" :show-arrow="false" raw>
                <template #trigger>
                    <n-button
                        type="primary"
                        :bordered="false"
                        :loading="isExceed"
                        :disabled="isExceed"
                        class="shrink-0 px-4 py-2.5 h-11 bg-primary-6 hover:disabled:!bg-primary-3 hover:!bg-primary-7 focus:!bg-primary-7 disabled:bg-primary-3 disabled:!text-text-t-5 !text-text-white !opacity-100"
                        @click="handleNextProcess"
                    >
                        <div class="flex items-center gap-3">
                            <span>{{ t("CONFIG_BASE_SUBMIT_BTN") }}</span>
                            <ExpendConst :feature-type="lumenCostKey" :num="1" class="ml-1" />
                        </div>
                    </n-button>
                </template>
                <div v-if="!isMobile" class="bg-white text-dark-bg-2 dark:text-dark-text p-3 rounded dark:bg-dark-bg-2 max-w-96">
                    <div>{{ t("ESTIMATE_COST_LUMENS") }}</div>
                </div>
            </n-tooltip>
        </div>
    </div>
</template>

<script setup>
import { EaseCanvas, EaseImage, Group, Path, ClippingGroup, eraseObject, StaticCanvas, workSpaceId, getExtensionKey, HistoryType, queryElement } from "@easeus/editor-core";
import { genInpaint } from "@/api";
import { uploadToCos, getFuncNameByModelId } from "@/utils/tools.js";
import { fluxDevModelId, realisticModelId } from "@/utils/constant.js";
import { useUserProfile } from "@/stores";
import { useCurrentTaskQueue } from "@/stores/create";

import { useGetModelInfo, useCheckMaxTask, useAppendPreloadTask, longTaskDialog, translateOrEnhanceText, useGoToCreate } from "@/hook/create";
import { ERROR_CODE_ENUM, PRELOAD_TASK_TYPE, SUBSCRIBE_PERMISSION } from "@/utils/constant";
import { formatPonyV6Prompt, mergeArraysById, getCurrentISO8601Time } from "@/utils/tools";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
import { useSyncAction } from "@/stores/syncAction";
import { renderModelIcon } from "@/utils/tools";
import ExpendConst from "@/components/subscribe/ExpendConst.vue";
import { useGlobalError } from "@/hook/error";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { toCreate } = useGoToCreate();

const emits = defineEmits(["confirm", "cancel", "close"]);

const { isMobile } = storeToRefs(useThemeStore());
const { showError } = useGlobalError();

const { t } = useI18n({ useScope: "global" });

const syncAction = useSyncAction();
const currentTaskQueue = useCurrentTaskQueue();
const userProfile = useUserProfile();
const { checkPermission } = useSubPermission();
const { checkShowVipNotice } = useVipNotice();

const props = defineProps({
    item: {
        type: Object,
        required: true,
        default: () => ({}),
    },
});

const wapperRef = ref(null);
const canvasRef = ref(null);
const svgCreateRef = ref(null);
// const submitLoading = ref(false);
const checkedModelId = ref(props.item.model_id);
const inpaintPrompt = ref("");
// handlerType Value : brush, eraser
const handlerType = ref("brush");
const handlerControl = ref("rect");

// 笔刷直径
const brushDiameter = ref(30);
let easeCanvas = null;
let groupId = null;
const strokeWidth = computed(() => (handlerControl.value === "lasso" ? 1.5 : brushDiameter.value));
const isLasso = computed(() => handlerControl.value === "lasso");
const isEraser = computed(() => handlerType.value === "eraser");
const isExceed = computed(() => currentTaskQueue.taskQueue.length >= currentTaskQueue.maxTask || currentTaskQueue.isLoading);
const shortcut = computed(() => userProfile.userConfig.shortcutKey === "enter");
const allowImprove = computed(() => !!inpaintPrompt.value && inpaintPrompt.value.length <= 600);
//模型选择组件渲染函数
const renderLabel = computed(() => {
    const model_id = checkedModelId.value;
    const model = useGetModelInfo(model_id);
    let icon = renderModelIcon(model_id);
    return {
        label: model.label,
        icon,
    };
});
//当前图片来源是否是自定义上传
const isCustomUpload = computed(() => props.item.originCreate === "customUpload");
// const lumenCostKey = computed(() => (isFluxDevModel({ model_id: checkedModelId.value }) ? "FLUX_1_DEV" : ""));
const lumenCostKey = computed(() => getFuncNameByModelId({ model_id: checkedModelId.value }));

//切换笔刷 和橡皮模式
const changeHandlerType = (type) => {
    handlerType.value = type;
    if (type === "brush") {
        easeCanvas.set({
            selectionColor: "rgba(123, 87, 229, 0.64)",
        });
    } else {
        easeCanvas.set({
            selectionColor: "rgba(247, 105, 101, 0.64)",
        });
    }
};
//选择 工具
const changeControlType = (type) => {
    handlerControl.value = type;
    if (type === "rect") {
        easeCanvas.selection = true;
        easeCanvas.hoverCursor = "crosshair";
    }
    if (type !== "rect") {
        nextTick(() => {
            svgCreateRef.value.setCanvas(easeCanvas);
        });
    }
};

const getGroup = () => queryElement(groupId, easeCanvas);

let isDrawing = false;
let handlePoint = { startX: 0, startY: 0 };
const handleStart = ({ absolutePointer }) => {
    if (isDrawing) {
        return;
    }
    isDrawing = true;
    const startX = absolutePointer.x;
    const startY = absolutePointer.y;
    handlePoint = { startX, startY };
};

const handleEnd = ({ absolutePointer }) => {
    if (!isDrawing) {
        return;
    }
    isDrawing = false;
    handlePoint.endX = absolutePointer.x;
    handlePoint.endY = absolutePointer.y;
    if (handlerControl.value === "rect") {
        const pathString = createRectanglePath(handlePoint.startX, handlePoint.endX, handlePoint.startY, handlePoint.endY);
        createPath(pathString);
        return;
    }
};

const createRectanglePath = (x1, x2, y1, y2) => {
    const minX = Math.min(x1, x2);
    const minY = Math.min(y1, y2);
    const maxX = Math.max(x1, x2);
    const maxY = Math.max(y1, y2);
    return `M${minX} ${minY} L${maxX} ${minY} ${maxX} ${maxY} ${minX} ${maxY} Z`;
};
//创建
const createPath = async (pathString) => {
    const pathStyle = {
        fill: "rgba(123, 87, 229, 1)",
        stroke: "rgba(123, 87, 229, 1)",
        strokeWidth: handlerControl.value === "pencil" ? strokeWidth.value / easeCanvas.getZoom() : 1.5,
        strokeLineCap: "round",
        strokeLineJoin: "round",
        selectable: false,
        erasable: false,
    };
    if (handlerControl.value === "pencil") {
        pathStyle.fill = "rgba(0, 0, 0, 0)";
    }
    const mask = new Path(pathString, pathStyle);
    const { x, y } = mask.getCenterPoint();
    const left = x - mask.width / 2;
    const top = y - mask.height / 2;
    console.log(left);
    const group = getGroup();
    if (!isEraser.value) {
        easeCanvas.history.historyProcessing = true;
        const original = {
            ...group.toObject(getExtensionKey),
        };
        if (group.clipPath.getObjects().length > 0) {
            const offset = pathStyle.strokeWidth / 2;
            const clone = await mask.clone(getExtensionKey);
            clone.set({
                left: left - offset,
                top: top - offset,
                globalCompositeOperation: "source-over",
                stroke: "white",
            });
            await eraseObject(group, clone);
        }
        group.add(mask);
        easeCanvas.history.historyProcessing = false;
        const historySnapshot = {
            type: HistoryType.UPDATE,
            action: "clipping",
            models: [
                {
                    ...group.toObject(getExtensionKey),
                    original: original,
                },
            ],
        };
        easeCanvas.history.addHistory(historySnapshot);
    } else {
        const clone = await mask.clone(getExtensionKey);
        const original = {
            ...group.toObject(getExtensionKey),
        };
        clone.set({ globalCompositeOperation: "destination-out", groupId: group.id });
        // easeCanvas.addHistory({
        //     type: "add",
        //     models: [mask],
        // });
        await eraseObject(group, clone);
        const historySnapshot = {
            type: HistoryType.UPDATE,
            action: "clipping",
            models: [
                {
                    ...group.toObject(getExtensionKey),
                    original: original,
                },
            ],
        };
        easeCanvas.history.addHistory(historySnapshot);
    }

    easeCanvas.renderAll();
};
//undo
const handleUndo = () => {
    easeCanvas.undo();
};
//重置画布
const handelResetCanvas = () => {
    const group = getGroup();

    group.getObjects().forEach((child) => {
        group.remove(child); // 从组中移除子对象
        child.dispose(); // 销毁子对象（可选，根据需求）
    });
    // 更新组的尺寸和位置
    group.setCoords();
    easeCanvas.history.clear();
    // 刷新画布
    easeCanvas.renderAll();
};
//选择模型
const chooseModel = async (model) => {
    checkedModelId.value = model;
};
// 翻译或增强提示词
const promptTranslating = ref({
    type: "",
    isLoading: false,
});
//提示词增强或翻译
const handleImprove = async (mode) => {
    let permission = SUBSCRIBE_PERMISSION.ENHANCE;
    if (mode === "translation") {
        permission = SUBSCRIBE_PERMISSION.TRANSLATION;
        window.trackEvent("APP_AUTO_TRANSLATE", { el: `translate_btn` });
    } else {
        window.trackEvent("APP_AUTO_ENHANCE", { el: `enhance_btn` });
    }
    if (!allowImprove.value) {
        inpaintPrompt.value.length > 600 && openToast.info(t("SHORT_PROMPT_TOO_LONG"));
        return;
    }
    if (promptTranslating.isLoading) {
        return;
    }

    const hasPermission = await checkPermission(permission);
    if (!hasPermission) {
        return;
    }

    // checkShowVipNotice(`inpaind_${mode}`);

    promptTranslating.value = { type: mode, isLoading: true };
    const { data } = await translateOrEnhanceText(inpaintPrompt.value, mode);
    promptTranslating.value = { type: "", isLoading: false };
    inpaintPrompt.value = data;
    promptTranslating.value = false;
};

// 获取蒙版图 二值化
import workerScript from "../workers/calcImageData.js?raw";
const getMaskImage = async () => {
    const group = getGroup();
    const blob = new Blob([workerScript], { type: "application/javascript" });
    const worker = new Worker(URL.createObjectURL(blob));
    // const worker = new Worker(new URL("../workers/calcImageData.js", import.meta.url));
    const offscreenCanvas = new StaticCanvas(null, {
        width: easeCanvas.workspace.width,
        height: easeCanvas.workspace.height,
        backgroundColor: "transparent",
    });
    const clonedGroup = await group.clone(getExtensionKey);
    offscreenCanvas.add(clonedGroup);
    offscreenCanvas.renderAll();
    const tempCanvas = offscreenCanvas.toCanvasElement();
    const tempContext = tempCanvas.getContext("2d");
    let imageData = tempContext.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);
    worker.postMessage({ imageData, width: offscreenCanvas.width, height: offscreenCanvas.height });
    const maskBlob = await new Promise((resolve) => {
        worker.onmessage = function (e) {
            const processedImageData = e.data.imageData;
            tempContext.putImageData(processedImageData, 0, 0);
            tempCanvas.toBlob(
                (blob) => {
                    resolve(blob);
                },
                "image/jpeg",
                0.1
            );
        };
    });

    const { fullPath } = await uploadToCos({ file: maskBlob, originalFileName: `mask_${Date.now()}.jpg`, type: "normal" });
    return fullPath;
};
// 获取合成图
const getCompositeImage = async () => {
    const offscreenCanvas = new StaticCanvas(null, {
        width: easeCanvas.workspace.width,
        height: easeCanvas.workspace.height,
        backgroundColor: "transparent",
    });
    const { objects } = easeCanvas.toJSON();
    await offscreenCanvas.loadFromJSON({ objects: objects.filter((item) => item.id !== workSpaceId) });
    offscreenCanvas.renderAll();
    const compoundBlob = await new Promise((resolve) => {
        //图片 压缩 并resize 长边256px quality 0.1
        const scale = 256 / Math.max(offscreenCanvas.width, offscreenCanvas.height);
        const temp = offscreenCanvas.toCanvasElement();
        const tempCanvas = document.createElement("canvas");
        tempCanvas.width = temp.width * scale;
        tempCanvas.height = temp.height * scale;
        tempCanvas.getContext("2d").drawImage(temp, 0, 0, tempCanvas.width, tempCanvas.height);
        tempCanvas.toBlob(
            (blob) => {
                resolve(blob);
            },
            "image/webp",
            0.1
        );
    });
    const { fullPath } = await uploadToCos({ file: compoundBlob, originalFileName: `compound_${Date.now()}.webp`, type: "normal" });
    return fullPath;
};

//下一步处理
const handleNextProcess = async () => {
    if (currentTaskQueue.isLoading) {
        openToast.error(t("TOAST_TASK_LIMIT"), 5e3);
        return;
    }
    currentTaskQueue.updateReqStatus(true);
    // try {
    const [maskUrl, compositeUrl] = await Promise.all([getMaskImage(), getCompositeImage()]);
    const { model_id, realWidth, width, height, realHeight, imgUrl } = props.item;
    let genParameters = {
        model_id,
        prompt: inpaintPrompt.value,
        resolution: {
            width: realWidth || width,
            height: realHeight || height,
            batch_size: 1,
        },
        genLocalRedrawPara: {
            img_url: imgUrl,
            mask_img_url: maskUrl,
            draw_img_url: compositeUrl,
        },
    };

    await inpaintTask(genParameters);
    currentTaskQueue.updateReqStatus(false);
};

const inpaintTask = async (conf, longTask = false) => {
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus && !checked.preloadStatus) {
        showError(ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR);
        return Promise.resolve(false);
    }
    if (!longTask) {
        const { prompt } = formatPonyV6Prompt(conf, "input");
        conf.prompt = prompt;
    }

    // 并发已满 提交预载
    if (!checked.concurrencyStatus) {
        useAppendPreloadTask(
            {
                genParameters: conf,
            },
            PRELOAD_TASK_TYPE.INPAINT
        );
        emits("close");
        return Promise.resolve(false);
    }
    currentTaskQueue.updateReqStatus(true);
    conf.continueCreate = longTask;
    const { data, status, message } = await genInpaint(conf);
    if (status === ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR) {
        showError(ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR);
        return Promise.resolve(false);
    }
    // 耗时任务 二次确认 重新提交
    if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
        const hasReSubmit = await longTaskDialog();
        hasReSubmit && inpaintTask(conf, true);
        return Promise.resolve(false);
    }
    if (status !== 0) {
        showError(status);
        return Promise.resolve(false);
    }
    let originCreate = "localRedraw";
    const list = mergeArraysById(currentTaskQueue.taskQueue, [
        {
            ...conf,
            originCreate,
            markId: data.markId,
            status: "pending",
            fastHour: data.fastHour,
            index: data.index,
            createTimestamp: getCurrentISO8601Time(),
        },
    ]);
    currentTaskQueue.updateTaskQueue(list);
    toCreate();
    emits("close");
    return Promise.resolve(true);
};

const shortcutKey = (event) => {
    event.stopPropagation();
    // 只按enter提交任务
    if (shortcut.value && event.key === "Enter" && !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey) {
        event.preventDefault();
        handleNextProcess();
        return;
    }
    //组合键提交任务
    if (!shortcut.value && (event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        handleNextProcess();
        return;
    }
};
onMounted(async () => {
    const originURL = props.item.thumbnailUrl || props.item.imgUrl;
    const backgroundImage = await EaseImage.fromURL(originURL, {
        crossOrigin: "anonymous",
    });
    const { width, height } = wapperRef.value.getBoundingClientRect();
    easeCanvas = new EaseCanvas(canvasRef.value, {
        width,
        height,
        container: wapperRef.value,
        useWheel: false,
        selectionColor: "rgba(123, 87, 229, 0.64)",
        workspaceOptions: {
            width: backgroundImage.width,
            height: backgroundImage.height,
            fillType: "none",
            zoomRatio: 1,
            hoverCursor: "crosshair",
        },
    });
    easeCanvas.defaultCursor = "crosshair";
    const group = new Group([], {
        opacity: 0.64,
        selectable: false,
        hoverCursor: "crosshair",
        erasable: true,
        width,
        height,
        clipPath: new ClippingGroup([], {
            width: backgroundImage.width,
            height: backgroundImage.height,
        }),
    });
    backgroundImage.set({
        erasable: true,
        selectable: false,
        hoverCursor: "crosshair",
        left: 0,
        top: 0,
    });
    easeCanvas.add(backgroundImage);
    easeCanvas.add(group);
    console.log(group.id);
    groupId = group.id;

    easeCanvas.history.clear();

    easeCanvas.on("mouse:down", handleStart);
    easeCanvas.on("mouse:up", handleEnd);
});
onBeforeUnmount(() => {
    easeCanvas.dispose();
});
const handleClose = () => {
    emits("cancel");
    emits("close");
};
</script>

<style lang="scss" scoped></style>
