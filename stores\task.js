import { defineStore } from "pinia";
import {
  resetTask<PERSON><PERSON>,
  getTask<PERSON><PERSON><PERSON><PERSON>,
  getTaskR<PERSON>ard<PERSON><PERSON>,
  finishTasks<PERSON>pi,
} from "@/api/task";
import {
  DAILY_REWARDS_IDS,
  LIKES_IDS,
  FOLLOW_IDS,
  TASK_SHARE_ID,
  TASK_STATE_MAP,
} from "@/constants/taskCenter";

const gaTrackEvent = (el) => {
  window.trackEvent("Rewards_Center", { el });
};

/**
 * 对后端返回的数据进行分组归类，模板好渲染
 * @param {object[]} taskList
 * @returns
 */

const getTaskGroupData = (taskList) => {
  const dailyRewardsTasks = [];
  const likesTasks = [];
  const followTasks = [];
  const achievementRewardTasks = [];
  //修复前后端数据不一致  导致的 页面报错
  const taskIds = Object.keys(TASK_STATE_MAP);
  taskList = taskList.filter((item) => taskIds.includes(item.taskId));
  for (let item of taskList) {
    if (DAILY_REWARDS_IDS.includes(item.taskId)) {
      dailyRewardsTasks.push(item);
    } else if (LIKES_IDS.includes(item.taskId)) {
      likesTasks.push(item);
    } else if (FOLLOW_IDS.includes(item.taskId)) {
      followTasks.push(item);
    } else {
      achievementRewardTasks.push(item);
    }
  }
  // likesTasks[0].beFinish = true
  // likesTasks[1].beFinish = false
  // likesTasks[2].beFinish = true
  // likesTasks[3].beFinish = true
  return {
    dailyRewardsTasks,
    likesTasks,
    followTasks,
    achievementRewardTasks,
  };
};

//用户信息
export const useTaskStore = defineStore("taskStore", {
  state: () => ({
    allTaskList: [],
    dailyRewardsTasks: [],
    likesTasks: [],
    followTasks: [],
    achievementRewardTasks: [],
    taskListLoading: false,
    dailyModalShowLastDate: {},
  }),

  getters: {},

  actions: {
    /**
     * 重置每日任务
     */
    async resetDailyTask() {
      return await resetTaskApi();
    },
    /**
     * 设置任务列表
     * @param {Object} taskGroupData - 任务分组数据
     */
    setGroupData(taskGroupData) {
      const {
        dailyRewardsTasks,
        likesTasks,
        followTasks,
        achievementRewardTasks,
      } = taskGroupData;
      this.dailyRewardsTasks = dailyRewardsTasks;
      this.likesTasks = likesTasks;
      this.followTasks = followTasks;
      this.achievementRewardTasks = achievementRewardTasks;
    },
    /**
     * 获取任务列表
     * @returns {Promise<Object>} 任务列表
     */
    async getTaskList() {
      this.taskListLoading = true;
      const { data } = await getTaskListApi().finally(() => {
        this.taskListLoading = false;
      });
      const taskGroupData = getTaskGroupData(data || []);
      this.setGroupData(taskGroupData);
      return taskGroupData;
    },
    /**
     * 获取任务奖励
     * @param {string[]} taskIds  任务ID集合
     * @returns {Promise<Object>} 奖励领取结果
     */
    async getTaskReward(taskIds) {
      if (!taskIds?.length) {
        return;
      }

      const res = await getTaskRewardApi(taskIds);
      return res;
      // 领取后 异步更新任务列表
      // this.getTaskList()
    },
    /**
     * 完成任务
     * @param {string} taskId 任务ID
     * @returns {Promise<Object>} 完成任务结果
     */
    async finishTask(taskId) {
      const res = await finishTasksApi({ taskId });
      return res;
    },
    /**
     * 完成任务 分享
     */
    finishTaskShare() {
      return this.finishTask(TASK_SHARE_ID);
    },
    /**
     * 埋点
     */
    trackEvent(el) {
      gaTrackEvent(el);
    },
  },
});
