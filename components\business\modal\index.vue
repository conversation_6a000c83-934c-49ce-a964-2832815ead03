<template>
    <!-- <n-modal :show="visible" :mask-closable="false"> -->
    <div class="relative w-full xl:w-[500px] border border-solid border-border-1 flex flex-col rounded-2xl overflow-hidden bg-bg-3">
        <!-- 背景图 -->
        <slot name="bg">
            <div>
                <img src="@/assets/images/modal-bg/diamond_light.webp" class="w-full block dark:hidden" />
                <img src="@/assets/images/modal-bg/diamond_dark.webp" class="w-full hidden dark:block" />
            </div>
        </slot>

        <!-- 关闭按钮 -->
        <div v-if="showClose" class="absolute top-0 right-0 p-6 text-text-1" @click="handleClose">
            <n-icon size="24" class="cursor-pointer">
                <IconsClose />
            </n-icon>
        </div>

        <!-- 标题 -->
        <p class="text-text-1 text-xl font-semibold text-center">{{ base.title }}</p>

        <!-- 描述 -->
        <p class="px-6 py-4 text-sm text-text-2 font-medium text-center">{{ base.description }}</p>

        <!-- 操作按钮 -->
        <div class="flex justify-center py-6 gap-x-3">
            <Button type="secondary" round class="min-w-[140px] h-10" @click="cancel" :bordered="false">
                {{ base.cancelText }}
            </Button>
            <Button type="primary" round class="min-w-[140px] h-10" @click="confirm" :bordered="false" :loading="loading">
                <span>{{ base.confirmText }}</span>
            </Button>
        </div>
    </div>
    <!-- </n-modal> -->
</template>

<script setup>
const props = defineProps({
    // visible: Boolean,
    base: {
        type: Object,
        required: true,
    },
    showClose: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["confirm", "cancel", "close"]);

// 内部关闭 modal
const handleClose = () => {
    emit("close");
};

const confirm = () => {
    emit("confirm");
};

const cancel = () => {
    emit("cancel");
};
</script>

<style scoped></style>
