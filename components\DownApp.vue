<template>
    <n-popover trigger="click" :options="options" placement="top" :show-arrow="false" raw class="rounded-lg">
        <template #trigger>
            <div class="px-2 flex items-center gap-3 cursor-pointer">
                <n-icon size="24">
                    <IconsInstallMobile />
                </n-icon>
                <span>{{ t("INSTALL_APP") }}</span>
            </div>
        </template>

        <div class="popover-box">
            <div v-for="item in options" :key="item.key" @click="handleSelect(item)" class="popover-item-box" :class="{ 'show-divider': item.divider, 'disabled-item': item.disabled }">
                <div>
                    <span class="text-sm">{{ item.label }}</span>
                    <div class="text-xs" v-if="item.disabled">{{ t("COMING_SOON_MESSAGE") }}</div>
                </div>
            </div>
        </div>
    </n-popover>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { NIcon } from "naive-ui";
import {} from "@/icons/index.js";

const options = [
    { label: "iOS App", key: "ios", link: "https://apps.apple.com/us/app/id6720725066" },
    { label: "Android App", key: "android", disabled: true },
];

// 选择指令
const handleSelect = ({ disabled, link, key }) => {
    window.trackEvent("APP_INSTALL_APP", { el: `install_app=${key}` });
    if (disabled) {
        return;
    }
    window.open(link);
};
</script>

<style lang="scss" scoped>
.show-divider:after {
    content: "";
    height: 1px;
    @apply absolute block top-0 left-0 right-0 dark:bg-white/20 bg-black/20;
}
.popover-box {
    @apply py-2.5 px-3 bg-white text-black/70 dark:bg-[#2F3035] rounded-lg min-w-[154px] dark:text-dark-text text-sm shadow-md;
    .popover-item-box {
        @apply h-10 relative rounded-md p-2 flex items-center gap-2  cursor-pointer hover:dark:bg-[#3A3B3F] hover:bg-[#F6F6FE];
    }
    .disabled-item {
        @apply opacity-50 hover:bg-transparent hover:dark:bg-transparent;
    }
}
</style>
