export const magicTypes = [
    { label: "<PERSON><PERSON><PERSON>_TYPE_STYLE", name: "Style", },
    { label: "MA<PERSON>C_TYPE_PAINT", name: "Painting", },
    {
        label: "MAGIC_TYPE_CAMERA",
        name: "Photography",

    },
];
export const magicItems = [
    {
        type: "Style",
        label: "MA<PERSON><PERSON>_TYPE_STYLE",
        name: "Neon",
        cover: "magic/neon.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Fantasy",
        cover: "magic/fantasy.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "<PERSON> Gogh",
        cover: "magic/van_gogh.webp",
    },
    {
        type: "Style",
        label: "MA<PERSON>C_TYPE_STYLE",
        name: "<PERSON><PERSON>",
        cover: "magic/gta.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "<PERSON>",
        cover: "magic/comic.webp",
    },
    {
        type: "Style",
        label: "<PERSON><PERSON><PERSON>_TYPE_STYLE",
        name: "Pop Art",
        cover: "magic/pop_art.webp",
    },
    {
        type: "Style",
        label: "MA<PERSON><PERSON>_TYPE_STYLE",
        name: "Sci-Fi",
        cover: "magic/sci_fi.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Simple",
        cover: "magic/simple.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "1980's anime",
        cover: "magic/1980_anime.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "3D animation",
        cover: "magic/3d_ainmation.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Caricature",
        cover: "magic/caricature.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Dark fantasy",
        cover: "magic/dark_fantasy.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Poster design",
        cover: "magic/poster_design.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Pixel art",
        cover: "magic/pixel_art.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Monochrome",
        cover: "magic/monochrome.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Graffiti",
        cover: "magic/graffiti.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Surrealism",
        cover: "magic/surrealism.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Glitch",
        cover: "magic/glitch.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Silhouette",
        cover: "magic/silhouette.webp",
    },
    {
        type: "Style",
        label: "MAGIC_TYPE_STYLE",
        name: "Flat Papercraft",
        cover: "magic/flat_papercraft.webp",
    },

    {
        type: "Painting",
        label: "MAGIC_TYPE_PAINT",
        name: "Sketch",
        cover: "magic/sketch.webp",
    },
    {
        type: "Painting",
        label: "MAGIC_TYPE_PAINT",
        name: "Watercolor",
        cover: "magic/watercolor.webp",
    },
    {
        type: "Painting",
        label: "MAGIC_TYPE_PAINT",
        name: "Oil Painting",
        cover: "magic/oil_painting.webp",
    },
    {
        type: "Painting",
        label: "MAGIC_TYPE_PAINT",
        name: "Lineart",
        cover: "magic/lineart.webp",
    },

    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Depth of field",
        cover: "magic/depth_of_field.webp",
    },
    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Film noir",
        cover: "magic/film_noir.webp",
    },
    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Long exposure",
        cover: "magic/long_exposure.webp",
    },
    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Analog film",
        cover: "magic/analog_film.webp",
    },
    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Cinematic",
        cover: "magic/cinematic.webp",
    },
    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Faded Photo",
        cover: "magic/faded_photo.webp",
    },
    {
        type: "Photography",
        label: "MAGIC_TYPE_CAMERA",
        name: "Tilt Shift",
        cover: "magic/tilt_shift.webp",
    },
];
//提示词魔法 是否合法
export const promptMagicValid = (type, name) => magicItems.findIndex((item) => item.type === type && item.name === name) > -1;
