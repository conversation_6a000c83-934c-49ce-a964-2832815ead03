import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useShareDataStore = defineStore("shareData", () => {
  const list = ref({});
  const dataFrom = ref("community");

  const setList = (newList = []) => {
  // 从这里存入数据
    list.value[dataFrom.value] = newList;
  };

  const setDataFrom = (from) => {
    dataFrom.value = from;
  };

  const findNearestStatusTrue = (arr, index, isNext) => {
    // 边界检查：确保下标在有效范围内
    if (index < 0 || index >= arr.length) {
      throw new Error("Invalid index: Index out of bounds");
    }

    let target = null;
    if (!isNext) {
      // 向前查找最近的 可以展示 的元素
      for (let i = index - 1; i >= 0; i--) {
        if (arr[i]?.status === true) {
          // 使用可选链防止未定义属性
          target = arr[i];
          break; // 找到第一个后立即终止循环
        }
      }
    } else {
      // 向后查找最近的 可以展示 的元素
      for (let i = index + 1; i < arr.length; i++) {
        if (arr[i]?.status === true) {
          target = arr[i];
          break;
        }
      }
    }
    return target;
  };
  const targetItemIndex = computed(() => {
    return (id, field = "id") =>
      Math.max(
        0,
        (list.value[dataFrom.value] || []).findIndex(
          (item) => item[field] === id
        )
      );
  });
  const maxIndex = computed(() => {
    return (list.value[dataFrom.value] || []).length;
  });
  const getPreviousOrNextId = computed(() => {
    return (id, field = "id", isNext = true) => {
      const index = targetItemIndex.value(id, field);
      const arr = list.value[dataFrom.value] || [];
      if (index < 0 || index >= arr.length) {
        return null;
      }
      let target = null;
      if (!isNext) {
        // 向前查找最近的 可以展示 的元素
        for (let i = index - 1; i >= 0; i--) {
          if (
            !arr[i].hide &&
            !arr[i].isDel &&
            !arr[i].reported &&
            !arr[i].deleted
          ) {
            // 使用可选链防止未定义属性
            target = arr[i];
            break; // 找到第一个后立即终止循环
          }
        }
      } else {
        // 向后查找最近的 可以展示 的元素
        for (let i = index + 1; i < arr.length; i++) {
          if (
            !arr[i].hide &&
            !arr[i].isDel &&
            !arr[i].reported &&
            !arr[i].deleted
          ) {
            target = arr[i];
            break;
          }
        }
      }

      return target;
    };
  });
  const getCurrentDetailById = (id, field = "id") => {
    return list.value[dataFrom.value]?.find((item) => item[field] === id);
  };
  const deleteHideItem = (id, field = "id") => {
    const index = targetItemIndex.value(id, field);
    if (index !== -1) {
      list.value[dataFrom.value].splice(index, 1, {
        ...list.value[dataFrom.value][index],
        hide: true,
      });
    }
  };
  return {
    setList,
    setDataFrom,
    deleteHideItem,
    maxIndex,
    targetItemIndex,
    getPreviousOrNextId,
    getCurrentDetailById,
  };
});
