let canvas = null
let ctx = null

export const compressImageNew = async (file, width, height, type = "jpeg", quality = 1) => {
  if (!canvas) {
    canvas = document.createElement("canvas");
    ctx = canvas.getContext("2d");
  }
  const img = await createImageData(file)
  const ratio = Math.min(width / img.width, height / img.height, 1);
  const targetWidth = img.width * ratio;
  const targetHeight = img.height * ratio;
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          let compressedFile = new File([blob], file.name, { type })
          resolve({
            compressedFile,
            width: targetWidth,
            height: targetHeight,
            size: blob.size,
            type,
            url: URL.createObjectURL(compressedFile),
          });
        } else {
          reject(new Error("图片压缩失败"));
        }
      },
      type,
      quality
    );
  });
};

export const compressSingleFileOrUrl = async (
  file,
  parameter = {
    type: '', // 默认压缩时选择文件大小最佳的格式
    width: 1024,
    height: 1024,
    quality: 1,
  }
) => {
  try {
    // 获取文件对象
    const imgData = await createImageData(file);
    let processedFile;

    if (parameter.type === "webp" || parameter.type === "jpeg") {
      // 使用指定格式压缩
      const res = await compressImageNew(
        file,
        parameter.width,
        parameter.height,
        `image/${parameter.type}`,
        parameter.quality
      );
      processedFile = res.compressedFile;
    } else {
      const original = {
        width: imgData.width,
        height: imgData.height,
        size: file.size,
        type: "original",
        compressedFile: file,
      };

      let compressedResults = { original };
      // 同时生成两种格式的压缩文件
      const [webpFile, jpegFile] = await Promise.all([
        compressImageNew(file, parameter.width, parameter.height, "image/webp", parameter.quality),
        compressImageNew(file, parameter.width, parameter.height, "image/jpeg", parameter.quality),
      ]);
      compressedResults.webp = webpFile;
      compressedResults.jpeg = jpegFile;

      // 比较文件大小，选择较小的文件
      const bestFormat = getBestCompressed(compressedResults);
      processedFile = bestFormat.compressedFile;
    }

    // 返回压缩后的结果
    return {
      originalFile: file,
      compressedFile: processedFile,
    };
  } catch (error) {
    console.error(`处理文件时出错:`, error);
    throw error; // 如果需要，上抛错误
  }
};


const createImageData = async (file) => {
  const img = new Image();
  const url = URL.createObjectURL(file);
  img.src = url;
  await new Promise((resolve, reject) => {
    img.onload = resolve;
    img.onerror = reject;
  });
  URL.revokeObjectURL(url);
  return img;
};
const fetchFile = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch file from URL: ${url}`);
    }
    const blob = await response.blob();
    const fileName = url.split("/").pop(); // 根据 URL 提取文件名
    return new File([blob], fileName, { type: blob.type });
  } catch (error) {
    console.error("Error fetching file:", error);
    throw error;
  }
};

const getBestCompressed = (results) => {
  let originalResult = results.original;
  let webpResult = results.webp || null;
  let jpegResult = results.jpeg || null;

  const { width, height, size } = webpResult || jpegResult; //压缩后的文件

  const resolutionReduced = width < originalResult.width || height < originalResult.height;
  const sizeReduced = size < originalResult.size;
  let bestFormat = null

  //分辨率不变，计算压缩后是否降低文件大小 如果不降低使用原图
  let message
  if (!sizeReduced && !resolutionReduced) {
    message = "不需压缩，请直接使用原图";
    bestFormat = 'original'
  } else {
    if (webpResult.size > jpegResult.size) {
      message = "建议使用 JPEG";
      bestFormat = 'jpeg'
    } else {
      message = "建议使用 WebP 格式";
      bestFormat = 'webp'
    }
  }
  let finalResuReduce = results[bestFormat].width < originalResult.width
  let finalSizeReduce = results[bestFormat].size < originalResult.size
  let sizeChanged = Math.abs((originalResult.size - results[bestFormat].size) / 1024 / 1024).toFixed(2)
  if (finalResuReduce || finalSizeReduce) {
    trackEvent("Common", { el: `upload#vary=${finalSizeReduce ? "smaller" : "larger"}#value=${sizeChanged}` })
  }
  return results[bestFormat]
}

