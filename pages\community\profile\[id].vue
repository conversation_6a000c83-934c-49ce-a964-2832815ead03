<template>
    <div v-show="!showPreview" class="h-screen dark:text-neutral-100 relative overflow-y-auto" tabindex="0" @scroll="scrollLoadMore" ref="virtualRef">
        <section class="md:hidden h-16 flex items-center shrink-0 sticky top-0 z-50 dark:bg-dark-bg bg-white border-b border-solid dark:border-dark-bg-2 border-neutral-300">
            <div class="flex items-center gap-2 p-6 cursor-pointer" @click="handleBack">
                <n-icon size="20">
                    <IconsBack />
                </n-icon>
                <span>{{ t("ACC_SIGN_IN_BACK") }}</span>
            </div>
        </section>
        <div class="flex flex-col">
            <section id="community-header" class="flex flex-col justify-center items-center md:mt-14 mb-6 md:mb-10">
                <CommunityUserInfo
                    :is-loaded="userBaseInfo && Object.keys(userBaseInfo).length !== 0"
                    :who="userBaseInfo.isSelf ? 'self' : 'other'"
                    :avatar-url="userBaseInfo.userAvatarUrl"
                    :user-name="userBaseInfo.userName"
                    :user-desc="userBaseInfo.introduction"
                    :vip-plan="userBaseInfo.planLevel"
                    :posted="userBaseInfo.publicImgNums"
                    :likes="userBaseInfo.likeNums"
                    :following="userBaseInfo.fansNums"
                    :followers="userBaseInfo.followNums"
                    :is-followed="userBaseInfo?.followed"
                    @onFollowOtherClick="handleUpdateFollow"
                />
            </section>
        </div>
        <!-- <section class="dark:bg-dark-bg rounded-lg bg-white m-4 p-6 text-xs border border-solid dark:border-dark-bg-2 border-neutral-300">
            <div class="max-w-2xl mx-auto flex flex-col items-center">
                <div class="flex items-center gap-6">
                    <div class="w-20 h-20 rounded-full overflow-hidden">
                        <img v-if="!!userBaseInfo.userAvatarUrl" :src="userBaseInfo.userAvatarUrl" class="w-full h-full object-contain" />
                        <n-icon v-else class="opacity-70" size="80">
                            <IconsPerson />
                        </n-icon>
                    </div>
                    <div>
                        <div class="flex items-center gap-2">
                            <span class="text-sm">{{ userBaseInfo.userName }}</span>
                            <div v-if="!userBaseInfo.isSelf">
                                <Button v-if="userBaseInfo.followed" type="secondary" size="small" @click="handleUpdateFollow">
                                    <span class="text-xs">
                                        {{ t("COMMUNITY_FOLLOWING") }}
                                    </span>
                                </Button>
                                <Button type="tertiary" size="small" v-else round @click="handleUpdateFollow">
                                    <span class="text-xs">
                                        {{ t("COMMUNITY_FOLLOW_BTN") }}
                                    </span>
                                </Button>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 mt-4">
                            <div>
                                <span>{{ formatLike(userBaseInfo.likeNums) }}</span> <span class="ml-1 opacity-70">{{ t("SHORT_LIKES") }}</span>
                            </div>
                            <div>
                                <span>{{ formatLike(userBaseInfo.fansNums) }}</span> <span class="ml-1 opacity-70">{{ t("COMMUNITY_FANS") }}</span>
                            </div>
                            <div>
                                <span>{{ formatLike(userBaseInfo.followNums) }}</span> <span class="ml-1 opacity-70">{{ t("COMMUNITY_FOLLOWED") }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="h-[1px] bg-black/20 dark:bg-white/20 mt-6 mb-4 w-full"></div>

                <p v-if="!!userBaseInfo.introduction" class="text-center px-16 opacity-80 leading-5 break-all">{{ userBaseInfo.introduction }}</p>
                <p v-else class="opacity-50">{{ t("COMMUNITY_EMPTY_DESC") }}</p>
                <p v-if="userBaseInfo.joined > 0" class="opacity-50 mt-2">
                    {{
                        t("COMMUNITY_JOIN_DAY", {
                            day: userBaseInfo.joined,
                        })
                    }}
                </p>
                <p v-else class="opacity-50 mt-2">{{ t("COMMUNITY_JOIN_TODAY") }}</p>
            </div>
        </section> -->

        <div v-if="publicImgs.length == 0 && !pageLoading" class="flex items-center justify-center flex-col h-screen text-black/40 dark:text-dark-text/40">
            <img class="dark:block hidden" src="@/assets/images/data_empty_dark.svg" alt="" />
            <img class="dark:hidden block" src="@/assets/images/data_empty_light.svg" alt="" />
            <span class="mt-4 opacity-70">{{ t("EMPTY_DATA") }}</span>
        </div>

        <VirtualWaterfall
            padding="16px"
            style="box-sizing: content-box; min-height: 30vh"
            rowKey="id"
            :gap="16"
            :virtual="true"
            :items="publicImgs"
            :calcItemHeight="calcItemHeight"
            :itemMinWidth="128"
            :preload-screen-count="[1, 2]"
            :columnCount="waterfallColCount"
            :maxColumnCount="waterfallColCount"
        >
            <template #default="{ item, index }">
                <div :key="item.id" class="overflow-hidden relative h-full rounded-2xl">
                    <NuxtLinkLocale
                        :to="`/community/detail/${item.id}`"
                        v-if="!item.reported"
                        @click="setShareList()"
                        class="w-full h-full backdrop-blur overflow-hidden relative waterfall-item box-border block"
                    >
                        <img
                            loading="lazy"
                            :src="item.highMiniUrl || item.thumbnailUrl"
                            @error="item.loaded = true"
                            @load="item.loaded = true"
                            alt=""
                            class="w-full h-full object-contain cursor-pointer"
                            :class="{ loading_bg_anime: !item.loaded }"
                        />

                        <FeaturedTips class="featured-tips" v-if="item.featured" />

                        <div class="mask">
                            <div v-if="item.showPrompt" class="prompt-text">{{ item.prompt }}</div>
                            <div v-else class="flex flex-col items-center text-center">
                                <div>
                                    <n-icon size="18" class="text-info-6">
                                        <IconsLock />
                                    </n-icon>
                                </div>
                                <div>{{ t("COMMUNITY_AUTHOR_HIDE_PROMPT") }}</div>
                            </div>

                            <div class="flex items-center h-6 gap-1" @click.stop.prevent>

                                <UsersAvatar
                                    :icon-size="24"
                                    :src="item?.accountInfo.userAvatarUrl"
                                    :user-id="item?.accountInfo?.userId"
                                    :plan-level="item?.accountInfo?.planLevel"
                                />

<!-- 
                                <n-avatar v-if="item.accountInfo.userAvatarUrl" round :size="24" class="shrink-0" :src="item.accountInfo.userAvatarUrl"></n-avatar>
                                <n-icon v-else :size="24" class="shrink-0">
                                    <IconsPerson />
                                </n-icon> -->
                                <div class="flex-1 mr-1 overflow-hidden whitespace-nowrap text-ellipsis text-xs text-dark-active-text">
                                    {{ item.accountInfo.userName || "" }}
                                </div>
                                <div class="shrink-0 flex items-center gap-1 cursor-pointer" @click="handleLike(item)">
                                    <n-icon size="18" class="text-dark-active-text">
                                        <HeartLike :checked="item.liked" />
                                    </n-icon>
                                    <span class="text-dark-active-text text-xs">{{ formatLike(item.fileLikeNums) }}</span>
                                </div>
                                <div class="shrink-0 flex items-center gap-1 cursor-pointer ml-3" @click="quickComment(item)">
                                    <n-icon size="18" class="text-dark-active-text">
                                        <IconsNoticeComments />
                                    </n-icon>
                                    <span class="text-dark-active-text text-xs">{{ formatLike(item.fileCommentNums) }}</span>
                                </div>
                            </div>
                        </div>
                    </NuxtLinkLocale>
                    <div v-else class="absolute top-0 left-0 right-0 bottom-0 bg-white dark:bg-dark-bg-2 flex flex-col items-center justify-center dark:text-dark-text text-black opacity-70">
                        <n-icon size="24">
                            <IconsNoEye />
                        </n-icon>
                        <p class="mt-4">{{ t("COMMUNITY_REPORT_IMG_TXT") }}</p>
                    </div>
                </div>
            </template>
        </VirtualWaterfall>

        <div v-if="pages.isDone && !pageLoading && publicImgs.length > 0" class="text-center mt-auto">
            <span class="text-black/40 dark:text-dark-text/40 end-msg">{{ t("SHORT_NO_MORE") }}</span>
        </div>
        <div v-if="pageLoading" class="sticky left-0 bottom-0 right-0 py-10 flex items-center justify-center">
            <n-icon size="32" class="text-primary">
                <IconsSpinLoading />
            </n-icon>
        </div>
    </div>
    <BackToTop v-if="virtualRefScrollTop > 200" @click="gotoTop" />
</template>

<script setup>
import { onActivated, onBeforeUnmount, onDeactivated, onMounted, watch } from "vue";
import { VirtualWaterfall } from "@lhlyu/vue-virtual-waterfall";
import { getCommunityPersonalById, getCommunityPersonalImgsById, addLikeByFileId, reduceLikeByFileId, followUserById, unfollowUserById } from "@/api";
import useWindowResize from "@/hook/windowResize";
import { formatNumber, isScrolledToBottom, debounce, formatPonyV6Prompt, dateToLocal, getDayDifference, decryptResult } from "@/utils/tools";
import { KEEPALIVE_PAGES } from "@/utils/constant";
import { useUserProfile, useShareDataStore } from "@/stores";
import UsersAvatar from "@/components/users/Avatar.vue";

import CommunityUserInfo from "@/components/CommunityUserInfo.vue";
import BackToTop from "@/components/BackToTop.vue";
defineOptions({
    name: KEEPALIVE_PAGES.COMMUNITY_PROFILE,
});
const userBaseInfo = ref({});

const { user } = useUserProfile();
const router = useRouter();
const route = useRoute();
const { t } = useI18n({ useScope: "global" });

const formatLike = computed(() => {
    return (num = 0) => formatNumber(num);
});
//高度计算
const calcItemHeight = (item, w) => {
    let { width, realWidth, height, realHeight } = item;
    realWidth = realWidth || width;
    realHeight = realHeight || height;
    return w / (realWidth / realHeight);
};
const windowResize = useWindowResize();
const waterfallColCount = computed(() => {
    let base = 6;
    if (windowResize.width.value <= 1660) {
        base = 5;
    }
    if (windowResize.width.value <= 1440) {
        base = 4;
    }
    if (windowResize.width.value <= 1144) {
        base = 3;
    }
    if (windowResize.width.value <= 768) {
        base = 2;
    }
    return base;
});
const localePath = useLocalePath();
//返回
const handleBack = async () => {
    if (!history.state.back) {
        await navigateTo({ path: localePath("/community/explore") });
    } else {
        shareData.setDataFrom("community");
        router.back();
    }
};

//关注/取关 指定用户
let followLoading = false;
const handleUpdateFollow = async () => {
    if (followLoading) return;
    followLoading = true;
    let updateFollowStatus = followUserById;
    const isFollowed = userBaseInfo.value.followed;
    if (isFollowed) {
        updateFollowStatus = unfollowUserById;
    }
    const { status, message } = await updateFollowStatus({ userId: userBaseInfo.value.userId });
    followLoading = false;

    if (status === 0) {
        userBaseInfo.value.followed = !isFollowed;
        queryUserBaseInfo();
        return;
    }
    openToast.error(message);
};

// 详情
const showPreview = ref(false);
const viewItem = ref({});
const previewRef = ref(null);

//计算时间差值(天)
const dayDiff = (str) => {
    let { year, month, day } = dateToLocal(str);
    const diff = getDayDifference(`${year}/${month}/${day}`);
    return Math.abs(diff);
};

//删除当前预览的图片
const delCurrent = (id) => {
    publicImgs.value = publicImgs.value.filter((item) => item.id !== id);
};

//查询指定用户的基础信息
let targetUserId = "";
//页面初始化
const initPageData = debounce(() => {
    publicImgs.value = [];
    pages.value = {
        pageNum: 0,
        pageSize: 20,
        isDone: false,
    };
    console.log("初始化页面数据...");
    targetUserId = route.params.id;
    queryUserBaseInfo();
    queryUserPublicImgs();
}, 300);
const queryUserBaseInfo = async () => {
    const { status, data, message } = await getCommunityPersonalById({ userId: targetUserId });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    userBaseInfo.value = {
        ...data,
        ...data.accountInfo,
        isSelf: data.accountInfo.userId == user.userId,
        joined: dayDiff(data.createTime),
    };
    const Username = userBaseInfo.value?.userName;
    useSeoMeta({
        title: () => t("SEO_META.SEO_OTHERS_PROFILE_TITLE", { Username }),
        ogTitle: () => t("SEO_META.SEO_OTHERS_PROFILE_TITLE", { Username }),
        description: () => t("SEO_META.SEO_OTHERS_PROFILE_DESC", { Username }),
        ogDescription: () => t("SEO_META.SEO_OTHERS_PROFILE_DESC", { Username }),
    });
};
//查询指定用户公开的图片
const publicImgs = ref([]);
const pages = ref({
    pageNum: 0,
    pageSize: 20,
    isDone: false,
});
const pageLoading = ref(false);
const queryUserPublicImgs = async () => {
    if (pageLoading.value) {
        return;
    }
    let { pageNum, pageSize, isDone, lastFileId = "" } = pages.value;
    if (isDone) {
        return;
    }
    pageNum += 1;
    pageLoading.value = true;
    //游标分页  当前数据最后一条数据ID
    if (pageNum == 1) {
        lastFileId = "";
    }

    const { status, data, message } = await getCommunityPersonalImgsById({ userId: targetUserId, pageSize, lastFileId });
    pageLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    pages.value.lastFileId = data.lastId;
    const list = (decryptResult(data.encryptResult) || []).map((item) => {
        const genInfo = JSON.parse(item.genInfo) || {};
        const { resolution = {} } = genInfo;
        const { prompt } = formatPonyV6Prompt(genInfo, "output");
        return {
            ...genInfo,
            ...item,
            prompt,
            width: resolution.width,
            height: resolution.height,
            showDel: true,
            dataUserId: item.accountInfo.userId,
            showPrompt: user.userId == item.accountInfo.userId || item.publicType !== "myself",
        };
    });
    if (pageNum === 1) {
        publicImgs.value = list;
    } else {
        publicImgs.value = [...publicImgs.value, ...list];
    }
    pages.value.isDone = list.length == 0 || list.length < pages.value.pageSize;
    pages.value.pageNum = pageNum;
};

const virtualRef = ref();
const virtualRefScrollTop = ref(0);
const gotoTop = () => {
    virtualRefScrollTop.value = 0;
    restoreScrollTop();
};
const restoreScrollTop = () => {
    virtualRef.value?.scrollTo({ top: virtualRefScrollTop.value });
};
const scrollLoadMore = debounce(async (e) => {
    virtualRefScrollTop.value = e.target.scrollTop;
    const isBottom = isScrolledToBottom(e.target);
    //获取元素滚动的高度
    if (isBottom && !pageLoading.value) {
        queryUserPublicImgs();
    }
}, 100);

const shareData = useShareDataStore();
const setShareList = () => {
    shareData.setList(publicImgs.value);
};

//快速评论
const quickComment = async (item) => {
    setShareList();
    await navigateTo({ path: localePath(`/community/detail/${item.id}`), query: { comment: 1 } });
};

//更新总评论数量
const changeCommentCount = () => {
    const current = publicImgs.value.find((item) => viewItem.value.id === item.id);
    current.fileCommentNums += 1;
    if (showPreview.value) {
        previewRef.value?.forceUpdatePreview(current);
    }
};

//更新图片举报状态
const handleUpdateReportState = () => {
    const current = publicImgs.value.find((item) => viewItem.value.id === item.id);
    current.reported = true;
    if (showPreview.value) {
        previewRef.value?.forceUpdatePreview(current);
    }
};

//点赞或取消
const likeLoading = ref(false);
const handleLike = async (row) => {
    let { id, fileLikeNums, liked } = row;
    if (likeLoading.value) {
        return;
    }
    likeLoading.value = true;
    let callback = addLikeByFileId;
    let step = 1;
    if (liked) {
        callback = reduceLikeByFileId;
        step = -1;
    }
    fileLikeNums = Math.max(0, (fileLikeNums += step));
    try {
        const { status, message } = await callback({ commFileId: id });
        likeLoading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        const index = publicImgs.value.findIndex((item) => item.id === id);
        if (index > -1) {
            publicImgs.value[index].fileLikeNums = fileLikeNums;
            publicImgs.value[index].liked = !liked;
        }
        if (showPreview.value) {
            previewRef.value?.forceUpdatePreview(publicImgs.value[index]);
        }
    } catch (error) {
        likeLoading.value = false;
    }
};

const currentId = ref();
const unWatch = ref();
onActivated(() => {
    shareData.setDataFrom("socialHome");
    unWatch.value = watch(
        () => route.params.id,
        (newId, oldId) => {
            if (route.path === localePath(`/community/profile/${newId}`)) {
                if (newId !== currentId.value) {
                    initPageData();
                    currentId.value = newId;
                } else {
                    restoreScrollTop();
                }
            }
        },
        {
            immediate: true,
        }
    );
});
onDeactivated(() => {
    unWatch.value();
});
</script>

<style lang="scss" scoped>
.waterfall-item {
    cursor: pointer;
    &::after {
        @apply absolute top-0 right-0 bottom-0 left-0 z-0  opacity-0 pointer-events-none bg-gradient-to-b from-black/70 via-white/0 to-black/70;
        content: "";
    }
    &:hover {
        &::after {
            opacity: 1;
        }
        .mask {
            display: flex;
        }
        .featured-tips {
            display: none;
        }
    }
}

.mask {
    @apply absolute top-0 right-0 bottom-0 left-0 p-2 z-10 hidden flex-col justify-between text-dark-active-text;
}
.end-msg {
    border-top: 1px solid;
    @apply pt-6 px-3 my-7 inline-block dark:border-white/20 border-black/20;
}
.prompt-text {
    height: 62px;
    transition: all 0.1s ease-in-out;
    pointer-events: none;
    display: -webkit-box; /* 启用弹性盒子布局 */
    -webkit-box-orient: vertical; /* 设置盒子方向为垂直 */
    -webkit-line-clamp: 3;
    overflow: hidden;
}
</style>
