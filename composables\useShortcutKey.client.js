import { ref, onMounted, onUnmounted, onActivated, onDeactivated } from "vue";
export const useShortcutKey = (opts = {}) => {
    const { onUp, onDown, onEsc, target = import.meta.client ? window : undefined } = opts;
    // 监听器是否已注册
    const isActive = ref(false);
    const keyHandler = (e) => {
        let handledEvent = null;
        switch (e.key) {
            case "ArrowUp":
                handledEvent = onUp;
                break;
            case "ArrowDown":
                handledEvent = onDown;
                break;
            case "Escape":
                handledEvent = onEsc;
                break;
        }
        if (handledEvent) {
            e.stopPropagation();
            e.preventDefault();
            handledEvent(e);
        }
    };

    const addEvent = () => {
        if (!isActive.value && target) {
            target.addEventListener("keydown", keyHandler);
            isActive.value = true;
        }
        console.log("-------------addEvent");
    };
    const removeEvent = () => {
        if (isActive.value && target) {
            target.removeEventListener("keydown", keyHandler);
            isActive.value = false;
        }
        console.log("-------------removeEvent");
    };

    onMounted(addEvent);
    onUnmounted(removeEvent);
    //兼容页面 keep-alive 时，B页面keydown触发A页面的keyDown事件
    onActivated(addEvent);
    onDeactivated(removeEvent);
};
