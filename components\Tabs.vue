<template>
    <div class="tabs text-sm flex gap-2 h-full overflow-auto no-scroll" ref="tabRootRef" @wheel="onScroll">
        <div
            class="bg-fill-tab-5 text-text-tab-5 hover:text-text-tab-7 h-9 px-3 rounded-full text-center font-medium text-sm leading-5 relative cursor-pointer whitespace-nowrap flex justify-center items-center"
            :class="{ '!bg-fill-tab-7 !text-text-tab-7': activeItem == v.value }"
            v-for="(v, k) in tabs"
            :key="v.value"
            @click="change(v, k)"
        >
            <span class="min-w-10">{{ v.label }}</span>
        </div>
    </div>
</template>
<script setup>
import { rafThrottle } from "@/utils/tools";
const props = defineProps({
    tabs: {
        type: Array,
        default: () => [
            { label: "Default", value: 0 },
            { label: "Custom", value: 1 },
        ],
    },
    value: {
        type: [String, Number],
        default: 1,
    },
});
const tabRootRef = ref();
const activeItem = ref(props.value);
const emit = defineEmits(["update:value", "change"]);
const change = (item, index) => {
    activeItem.value = item.value;
    emit("update:value", item.value);
    emit("change", item.value, index);
    setScroll(index);
};
const onScroll = rafThrottle((e) => {
    e.preventDefault();
    e.stopPropagation();
    const target = tabRootRef.value;
    const left = target.scrollLeft + e.deltaY / 5;
    target.scrollTo({ left });
}, 16);
const setScroll = (index) => {
    nextTick(() => {
        const target = tabRootRef.value;
        const item = target.children[index];
        const left = item.offsetLeft - target.offsetLeft - (target.clientWidth - item.clientWidth) / 2;
        target.scrollTo({ left, behavior: "smooth" });
    });
};
watch(
    () => props.value,
    () => {
        activeItem.value = props.value;
        const index = props.tabs.findIndex((v) => v.value == props.value);
        if (index > -1) {
            setScroll(index);
        }
    }
);
</script>
<style scoped lang="scss">
.tabs {
    overscroll-behavior: contain;
}
</style>
