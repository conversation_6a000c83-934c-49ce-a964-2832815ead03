//
const iconModules = {
    ...import.meta.glob("@/assets/images/magic/*", { eager: true, query: "?url", import: "default" }),
    ...import.meta.glob("@/assets/images/modIcon/*", { eager: true, query: "?url", import: "default" }),
    ...import.meta.glob("@/assets/images/models-example/*", { eager: true, query: "?url", import: "default" }),
};
export const renderStaticImage = (imgPath) => {
    if (import.meta.server) {
        return "";
    }
    const key = `/assets/images/${imgPath}`;
    return iconModules[key] || "";
};

//naiveUI 主题配置
export const naiveUIConfig = {
    dark: {
        common: {
            primaryColor: "#ffffff",
            primaryColorHover: "#ffffff",
            hoverColor: "#ffffff0d",
            popoverColor: "#2e2e2f",
            primaryColorPressed: "#fff",
            primaryColorSuppl: "transparent",
            heightMedium: "40px",
            borderRadius: "6px",
        },

        Scrollbar: {
            width: "8px",
            railInsetHorizontal: "4px 4px 4px auto",
            color: "rgba(255,255,255,0.7)",
            colorHover: "rgba(255,255,255,1)",
        },
        Input: {
            textColor: "rgba(255,255,255,0.7)",
        },
        Form: {
            feedbackFontSizeMedium: "12px",
        },
    },
    light: {
        common: {
            primaryColor: "#000",
            primaryColorHover: "#000",
            hoverColor: "#f2f2f2",
            popoverColor: "#fff",
            primaryColorPressed: "#000",
            primaryColorSuppl: "transparent",
            heightMedium: "40px",
            borderRadius: "6px",
        },
        Select: {
            border: "none",
        },
        Scrollbar: {
            width: "8px",
            railInsetHorizontal: "4px 4px 4px auto",
            color: "rgba(0,0,0,0.7)",
            colorHover: "rgba(0,0,0,1)",
        },
        Input: {
            border: "none",
        },
        Form: {
            feedbackFontSizeMedium: "12px",
        },
    },
};

/**
 * 获取支付回调(携带多语言前缀)
 * @returns {string} 支付回调地址
 */
export const getLocalePrefix = (langs) => {
    try {
        const { pathname } = new URL(location.href);
        langs = langs.map((item) => item.key).filter((item) => item !== "en");
        // 匹配 /app/后面的第一个非空段
        const match = pathname.match(/^\/app\/([^\/]+)/);
        if (match && langs.includes(match[1])) {
            return `${match[1]}`;
        }
        return null;
    } catch (e) {
        return null;
    }
};
export const createLocaleUrl = (url, langs) => {
    try {
        const prefix = getLocalePrefix(langs);
        if (!prefix) {
            return url;
        }
        const { origin, pathname } = new URL(url);
        //不是当前域名下的链接
        if (origin !== location.origin) {
            return url;
        }
        langs = langs.map((item) => item.key).filter((item) => item !== "en");
        const match = pathname.match(/^\/app\/([^\/]+)/);
        // 已经包含了前缀了 或者 不是工作台的路径
        if ((match && langs.includes(match[1])) || !match) {
            return url;
        }
        //给当前域名下的链接拼接前缀
        return origin + pathname.replace("/app/", `/app/${prefix}/`);
    } catch {
        return url;
    }
};

