<template>
    <template v-if="isMobile">
        <n-drawer :width="200" :show="isShow" :style="customStyle" @on-mask-click="handleDrawerClick" @update:show="handleUpdateShow">
            <n-drawer-content>
                <div class="flex"><PicNav /></div>
            </n-drawer-content>
        </n-drawer>
    </template>
    <template></template>
</template>
<script setup>
import { useThemeStore } from "@/stores/system-config";
import { useCurrentTheme } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());
const { isShowMoDrawer, setShowMoDrawer, getShowMoDrawer } = useCurrentTheme();

const isShow = computed(() => {
    return getShowMoDrawer();
});

const customStyle = ref({
    "--n-border-radius": "0px",
    "--n-body-padding": "0px",
});

watch(isShowMoDrawer, (val) => {
    // 强制修改遮罩层样式，解决deep查找不起效的问题
    if (val) {
        nextTick(() => {
            const mask = document.querySelector(".n-drawer-container .n-drawer-mask");
            if (mask) {
                mask.style.backdropFilter = "blur(8px)";
            }
        });
    }
});

const handleUpdateShow = (value) => {
    setShowMoDrawer(value);
};

const handleDrawerClick = () => {
    setShowMoDrawer(false);
};
</script>

<style lang="scss" scoped></style>
