<template>
    <div
        class="w-[400px] min-h-[476px] bg-bg-6 rounded-2xl px-6 pt-8 pb-6 flex flex-col items-center gap-4 relative shadow-[0px_4px_8px_-2px_rgba(0,0,0,0.12),0px_8px_24px_0px_rgba(0,0,0,0.16)]"
        v-if="currSelectModel"
    >
        <!-- 名字 {{ models.map((item) => item.label) }} -->
        <div class="absolute top-4 right-4 group">
            <div class="size-8 cursor-pointer hover:bg-fill-wd-1 rounded-full flex items-center justify-center" @click="handleCloseClick">
                <n-icon :size="20">
                    <IconsClose class="text-text-4 group-hover:text-text-2" />
                </n-icon>
            </div>
        </div>
        <!-- 图标 -->
        <img :src="currSelectModel?.icon || ''" alt="" class="size-12 object-cover transform z-[1] rounded-lg" />

        <!-- [模型名字] is alive -->
        <div class="flex flex-col items-center gap-1">
            <div class="text-text-1 text-base font-semibold">{{ $t("NEW_MODEL_PREVIEW.XXX_ALIVE", { name: currSelectModel.modelDisplay }) }}</div>
            <div class="text-text-4 text-xs font-medium">{{ subTitle }}</div>
        </div>

        <!-- 介绍 -->
        <div class="flex flex-col items-start w-full gap-1">
            <template v-if="descriptionArray?.length">
                <div class="text-text-3 text-sm" v-for="(item, index) in descriptionArray" :key="index">{{ item }}</div>
            </template>
        </div>

        <!-- 图片展示 -->
        <div class="flex w-full h-[234px] relative">
            <img class="size-full object-cover pointer-events-none rounded-lg" :src="exampleImage" />
        </div>

        <div v-if="unreadModels.length > 1" class="w-full flex items-center justify-between relative cursor-default">
            <div class="min-w-[96px] px-4"></div>

            <span class="text-text-4 flex-1 text-center"> {{ currSelectIndex + 1 }} / {{ unreadModels.length }} </span>

            <Button type="primary" class="min-w-[96px] h-8 px-4" @click="handleNextClick">
                {{ $t("NEW_MODEL_PREVIEW.NEXT_MODEL") }}
            </Button>
        </div>
    </div>
</template>

<script setup>
import { modelIconMapping, NamiyaModelId, fluxKontextModelId, fluxKreaModelId } from "@/utils/constant";
import { useGetModelInfo } from "@/hook/create";

const { t } = useI18n({ useScope: "global" });
const emit = defineEmits(["close"]);

const unreadModels = ref([]); // 可用模型列表
const currSelectIndex = ref(0); // 当前选中模型id

const currSelectModel = computed(() => {
    let model = unreadModels.value[currSelectIndex.value];
    const modelInfo = useGetModelInfo(model?.id);

    return (
        {
            ...model,
            ...modelInfo,
        } ?? null
    );
});
const exampleImage = computed(() => currSelectModel.value?.newModelSplashImgs?.[0] || currSelectModel.value?.exampleImgs?.[0] || ""); //模型示例图片列表

const subTitle = computed(() => parseSubTitleByModeId(currSelectModel.value?.id));
const descriptionArray = computed(() => filterDescriptArrayByModelId(currSelectModel.value?.id));

onMounted(() => {
    const { userConfig } = useUserProfile();
    const { readNewModelIds = [] } = userConfig; //已读modelId列表

    let allModels = modelIconMapping.flatMap((group) => Object.values(group.model)); // 所有模型列表
    unreadModels.value = allModels.filter((model) => model.isNew && !readNewModelIds.includes(model.id)); // 所有未读的模型
});

const parseSubTitleByModeId = (modelId) => {
    // 获取次级标题文案
    switch (modelId) {
        case NamiyaModelId: // namiya
            return t("NEW_MODEL_PREVIEW.CREATE_STUNNNING_ANIME_SCENES");
        case fluxKontextModelId: // FLUX.1 Kontext
            return t("NEW_MODEL_PREVIEW.CREATE_IMAGE_WITH", { name: currSelectModel.value.modelDisplay });
        case fluxKreaModelId: // FLUX.1 Krea
            return t("NEW_MODEL_PREVIEW.DESC_KREA_0");
        // default:
        // return t("NEW_MODEL_PREVIEW.CREATE_IMAGE_WITH", { name: currSelectModel.value.modelDisplay }); //TODO
    }
};

const filterDescriptArrayByModelId = (modelId) => {
    // 根据模型id筛选对应描述文字
    switch (modelId) {
        case NamiyaModelId: // namiya
            return [t("NEW_MODEL_PREVIEW.DESC_NAMIYA_1"), t("NEW_MODEL_PREVIEW.DESC_NAMIYA_2")];
        case fluxKontextModelId: // FLUX.1 Kontext
            return [t("NEW_MODEL_PREVIEW.DESC_KONTEXT_1"), t("NEW_MODEL_PREVIEW.DESC_KONTEXT_2")];
        case fluxKreaModelId: // FLUX.1 Krea
            return [t("NEW_MODEL_PREVIEW.DESC_KREA_1"), t("NEW_MODEL_PREVIEW.DESC_KREA_2")];
        // default: // FLUX.1 Kontext
        //     return [t("NEW_MODEL_PREVIEW.DESC_KONTEXT_1"), t("NEW_MODEL_PREVIEW.DESC_KONTEXT_2")];
    }
    return [];
};

const handleNextClick = () => {
    // 切换下一张模型
    currSelectIndex.value = (currSelectIndex.value + 1) % unreadModels.value.length;
};

// 关闭弹窗 触发上传已读事件
const handleCloseClick = () => {
    const newModelIds = unreadModels.value.map((item) => item.id);
    const userProfile = useUserProfile();
    userProfile.syncUpdateUserConfig({
        readNewModelIds: newModelIds,
    });

    emit("close");
};
</script>
<style lang="scss" scoped>
.pic {
    animation: fadeCycle 7s infinite linear;
    animation-fill-mode: both;
    opacity: 0;
}

.pic:nth-child(1) {
    animation-delay: 0s;
}
.pic:nth-child(2) {
    animation-delay: 5s;
}
.pic:nth-child(3) {
    animation-delay: 10s;
}
.pic:nth-child(4) {
    animation-delay: 15s;
}

@keyframes fadeCycle {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    60% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}
</style>
