<template>
    <MobileHeader :disableBack="true">
        <template #right>
            <div class="flex gap-2 justify-end">
                <div class="size-8 rounded-full flex justify-center items-center bg-bg-2 relative" @click="handleHeaderClick('notice')">
                    <n-icon size="20" class="text-text-2"> <IconsNotice /> </n-icon>
                    <!-- 红点提示 -->
                    <span v-if="unreadMessages.unReadCount > 0" class="size-2 bg-red-600 rounded-full absolute top-0.5 right-0.5"></span>
                </div>
                <div class="size-8 rounded-full flex justify-center items-center bg-bg-2" @click="handleHeaderClick('setting')">
                    <n-icon size="20" class="text-text-2">
                        <IconsSetting />
                    </n-icon>
                </div>
            </div>
        </template>
    </MobileHeader>

    <section class="h-full bg-bg-1 text-sm font-medium relative overflow-y-auto" ref="virtualRef" @scroll="scrollLoadMore">
        <div class="flex flex-col">
            <section id="community-header" class="flex flex-col justify-center items-center md:mt-14 mb-6 md:mb-10">
                <CommunityUserInfo
                    :is-loaded="userCommunity?.communityUser && Object.keys(userCommunity?.communityUser).length != 0"
                    who="self"
                    :avatar-url="userCommunity?.communityUser?.userAvatarUrl"
                    :user-name="userCommunity?.communityUser?.userName"
                    :user-desc="userCommunity.communityUser.introduction"
                    :vip-plan="vipInfo.plan"
                    :posted="userCommunity.communityUser.posted"
                    :likes="userCommunity.communityUser.likes"
                    :following="userCommunity.communityUser.following"
                    :followers="userCommunity.communityUser.followers"
                />
            </section>

            <section class="py-2 flex items-center justify-center w-full md:py-4 top-0 z-10 overflow-x-auto scrollbar-hide">
                <div class="min-w-0 sticky flex items-center gap-2 text-text-tab-5">
                    <div
                        class="bg-fill-tab-5 h-10 px-4 min-w-[104px] rounded-full flex items-center justify-center cursor-pointer md:hover:text-text-tab-7"
                        :class="{ 'bg-fill-tab-7 text-text-tab-7': tabs === 'Posted' }"
                        @click="switchTab(`Posted`)"
                    >
                        <span>{{ t("COMMUNITY_PUBLISHED") }}</span>
                    </div>
                    <div
                        class="bg-fill-tab-5 h-10 px-4 min-w-[104px] rounded-full flex items-center justify-center cursor-pointer md:hover:text-text-tab-7"
                        :class="{ 'bg-fill-tab-7 text-text-tab-7': tabs === 'Likes' }"
                        @click="switchTab(`Likes`)"
                    >
                        <span>{{ t("SHORT_LIKES", 2) }}</span>
                    </div>
                    <div
                        class="bg-fill-tab-5 h-10 px-4 min-w-[104px] rounded-full flex items-center justify-center cursor-pointer md:hover:text-text-tab-7"
                        :class="{ 'bg-fill-tab-7 text-text-tab-7': tabs === 'follow' }"
                        @click="switchTab(`follow`)"
                    >
                        <span>{{ t("COMMUNITY_FOLLOWING") }}</span>
                    </div>
                    <div
                        class="bg-fill-tab-5 h-10 px-4 min-w-[104px] rounded-full flex items-center justify-center cursor-pointer md:hover:text-text-tab-7"
                        :class="{ 'bg-fill-tab-7 text-text-tab-7': tabs === 'fans' }"
                        @click="switchTab(`fans`)"
                    >
                        <span>{{ t("COMMUNITY_FANS") }}</span>
                    </div>
                </div>
            </section>
        </div>
        <div class="tab-content mt-4 mb-12 pb-10 px-6" :style="{ height: contentViewHightStyle }">
            <component :is="componentsMap[tabs]" :tabs="tabs" :key="tabs" ref="tabListRefer"></component>
            <div v-if="tabbarHeight" :style="{ height: `${tabbarHeight}px` }" />
        </div>
    </section>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
const updateSeo = () => {
    useSeoMeta({
        title: () => t("SEO_META.SEO_SELF_PROFILE_TITLE"),
        ogTitle: () => t("SEO_META.SEO_SELF_PROFILE_TITLE"),
        description: () => t("SEO_META.SEO_SELF_PROFILE_DESC"),
        ogDescription: () => t("SEO_META.SEO_SELF_PROFILE_DESC"),
    });
};
import { getCommunityPersonalById,updateUserProfile } from "@/api";
import { useUserCommunity, useUnreadMessage } from "@/stores";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { debounce, isScrolledToBottom } from "@/utils/tools";
import { KEEPALIVE_PAGES } from "@/utils/constant";
import PostedList from "@/components/users/PostedList.vue";
import LikesList from "@/components/users/LikesList.vue";
import FollowList from "@/components/users/FollowList.vue";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
import { loopUnreadNotice } from "@/api";
import { useCurrentTheme } from "@/stores/system-config";
import CommunityUserInfo from "@/components/CommunityUserInfo.vue";

const { getTabBarHeight } = useCurrentTheme();
const tabbarHeight = computed(() => {
    if (isMobile.value) return 0;
    return getTabBarHeight() || 0;
});
defineOptions({
    name: KEEPALIVE_PAGES.USER_WORKS,
});
const unreadMessages = useUnreadMessage();
const { isMobile } = storeToRefs(useThemeStore());
const userCommunity = useUserCommunity();
// 会员相关
const subscribeStore = useSubscribeStore();
const { vipInfo } = storeToRefs(subscribeStore);

const componentsMap = {
    Posted: PostedList,
    Likes: LikesList,
    follow: FollowList,
    fans: FollowList,
};
const tabs = ref("Posted");

const virtualRef = ref(null);
const virtualRefScrollTop = ref(0);

const tabListRefer = ref(null);

const switchTab = (tab) => {
    if (tab !== tabs.value) {
        tabs.value = tab;
        queryUserBaseInfo();
    }
};

const scrollLoadMore = debounce(async (e) => {
    const isBottom = isScrolledToBottom(e.target, 300);
    virtualRefScrollTop.value = e.target.scrollTop;
    //获取元素滚动的高度
    if (isBottom) {
        tabListRefer.value.loadMoreData();
    }
}, 100);

//修复切换路由时，偏移量不正确
onActivated(() => {
    nextTick(updateSeo);
    queryUserBaseInfo();
    restoreScrollTop();
});
const restoreScrollTop = () => {
    virtualRef.value?.scrollTo({ top: virtualRefScrollTop.value });
};
const queryUserBaseInfo = async () => {
    const { status, data, message } = await getCommunityPersonalById();
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    const { fansNums, followNums, likeNums, publicImgNums, introduction } = data;
    userCommunity.updateCommunityUser({
        followers: fansNums,
        following: followNums,
        likes: likeNums,
        posted: publicImgNums,
        introduction,
        userAvatarUrl: data.accountInfo.userAvatarUrl,
        userName: data.accountInfo.userName,
    });
};

const tabList = ref([
    { label: "COMMUNITY_MY_IMAGES", key: "img" },
    { label: "COMMUNITY_MY_FOLLOWING", key: "follow" },
]);
const localePath = useLocalePath();
const handleHeaderClick = async (eventType) => {
    if (eventType === "notice") {
        await navigateTo({ path: localePath("/message-center") });
        return;
    }
    if (eventType === "setting") {
        await navigateTo({ path: localePath("/account/") });
        return;
    }
};
// 刷新提示信息
const refreshNoticeDatas = async () => {
    if (isMobile.value) {
        const { status, data } = await loopUnreadNotice();
        if (status === 0) {
            unreadMessages.updateUnreadList(data);
        }
    }
};
const headerHeight = ref(0);

onMounted(() => {
    refreshNoticeDatas();

    const headerView = document.getElementById("community-header");
    if (!headerView) return;
    headerHeight.value = headerView.offsetHeight;
});

const contentViewHightStyle = computed(() => {
    return `calc(100vh - ${headerHeight.value ?? 0}px`;
});
</script>

<style lang="scss" scoped>
.tab-content {
    // min-height: calc(100vh - 72px);
    // height: fit-content;
}

::-webkit-scrollbar {
    display: none;
}
</style>
