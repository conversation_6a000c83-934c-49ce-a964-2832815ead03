<template>
    <div class="dark:text-dark-text relative z-[999] h-full dark:bg-black overflow-auto">
        <RecycleScroller @scroll="pageScroll" class="h-full px-0.5" :items="currentCollect.imgList || []" key-field="imgName" :item-size="itemWidth" :grid-items="colCount" :prerender="10">
            <template #default="{ item, index }">
                <div class="w-full aspect-square p-0.5">
                    <div class="bg-dark-tag-bg w-full h-full relative" @click="handleChooseItem(item)">
                        <div v-if="hasSelectItem(item.imgName)" class="w-6 h-6 border-2 border-solid rounded-full bg-primary flex items-center justify-center absolute bottom-2 right-2">
                            <n-icon class="shrink-0 text-white" size="18"><IconsSuccess /></n-icon>
                        </div>
                        <img :src="item.renderUrl" loading="lazy" class="w-full h-full" :class="[chooseLayoutType === 'Square' ? 'object-cover' : 'object-contain']" alt="" />
                    </div>
                </div>
            </template>
            <template #before>
                <MobileHeader :title="currentCollect.collectName">
                    <template #right>
                        <n-icon v-if="!selectModel" size="18" class="text-text-2 rotate-90" @click="moreActive = true">
                            <IconsMore />
                        </n-icon>
                    </template>
                </MobileHeader>

                <!-- <div class="px-4 py-4 flex items-center justify-between fixed w-full top-0 z-10 dark:bg-black/80 bg-neutral-300/90">
                  <n-icon size="18" class="rotate-90" @click="handleBack">
                      <IconsArrowLine />
                  </n-icon>
                  <div>{{ currentCollect.collectName }}</div>
                  <n-icon v-if="!selectModel" size="18" class="rotate-90" @click="moreActive = true">
                      <IconsMore />
                  </n-icon>
                  <span v-else class="text-primary" @click="handleCancelSelect"> {{ t("COMMON_BTN_CANCEL") }} </span>
              </div>
              <div class="h-14"></div> -->
            </template>
            <template #after>
                <div v-if="isLoading" class="py-10 text-center">
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </div>
                <div v-if="currentCollect.imgList && currentCollect.imgList.length === 0 && !isLoading" class="flex flex-col items-center justify-center min-h-96">
                    <img class="dark:block hidden w-1/3" src="@/assets/images/data_empty_dark.svg" alt="" />
                    <img class="dark:hidden block w-1/3" src="@/assets/images/data_empty_light.svg" alt="" />

                    <span class="mt-4 opacity-70">{{ t("EMPTY_DATA") }}</span>
                </div>
                <div class="h-36"></div>
            </template>
        </RecycleScroller>

        <div v-if="selectModel" class="rounded-xl dark:bg-dark-bg/70 backdrop-blur-sm bg-neutral-300/90 fixed bottom-0 w-full p-4 pb-10 dark:text-dark-text">
            <div class="text-center">{{ t("COLLECTION.X_PHOTO_SELECTED", selectIds.size) }}</div>

            <div class="mt-3 flex gap-3 h-10 items-center justify-center text-xs">
                <div class="h-full px-4 flex items-center justify-center gap-2 rounded-full dark:bg-dark-bg-2 bg-white" @click="handleBatchMove">
                    <n-icon size="18">
                        <IconsMoveTo />
                    </n-icon>
                    <span>{{ t("COLLECTIONS_MOVE_TITLE") }}</span>
                </div>
                <div class="text-error h-full px-4 flex items-center justify-center gap-2 rounded-full dark:bg-dark-bg-2 bg-white" @click="handleBatchUncollect">
                    <n-icon size="18">
                        <IconsUnFavorite />
                    </n-icon>
                    <span>{{ t("COLLECTION_UN_COLLECTION") }}</span>
                </div>
            </div>
        </div>
    </div>
    <n-drawer v-model:show="moreActive" height="80vh" class="!rounded-t-2xl dark:!bg-dark-bg dark:text-dark-text" placement="bottom">
        <n-drawer-content class="pt-3">
            <div class="flex items-center gap-4">
                <div
                    class="w-1/2 py-3 rounded-lg dark:bg-dark-bg-3 bg-neutral-100 flex flex-col items-center justify-center gap-1 text-xs"
                    @click="
                        selectModel = true;
                        moreActive = false;
                    "
                >
                    <IconsHistory class="text-lg" />
                    <span>{{ t("COLLECTION.SELECT_IMAGE") }}</span>
                </div>
                <div
                    class="w-1/2 py-3 rounded-lg dark:bg-dark-bg-3 bg-neutral-100 flex flex-col items-center justify-center gap-1 text-xs"
                    @click="
                        showEditCollection = true;
                        moreActive = false;
                    "
                >
                    <IconsPencil class="text-lg" />
                    <span>{{ t("COLLECTION_MENU_EDIT") }}</span>
                </div>
            </div>
            <div class="mt-4 px-4 rounded-lg dark:bg-dark-bg-3 bg-neutral-100">
                <H5Radio :options="sortOps" v-model:hidden="moreActive" v-model:value="sortType" />
            </div>
            <div class="mt-4 px-4 rounded-lg dark:bg-dark-bg-3 bg-neutral-100">
                <H5Radio :options="layoutOps" v-model:hidden="moreActive" v-model:value="chooseLayoutType" />
            </div>
            <div class="mt-4 px-4 rounded-lg dark:bg-dark-bg-3 bg-neutral-100">
                <H5Radio :options="layoutSizeOps" v-model:hidden="moreActive" v-model:value="chooseLayoutSize" />
            </div>
            <div class="mt-4 px-4 rounded-lg dark:bg-dark-bg-3 bg-neutral-100" @click="handleDel">
                <div class="flex gap-2 items-center py-4 text-error">
                    <n-icon class="shrink-0" size="20"><IconsDele /></n-icon>
                    <div class="flex-1">{{ t("COLLECTION_MENU_DEL") }}</div>
                </div>
            </div>
        </n-drawer-content>
    </n-drawer>
    <n-drawer v-model:show="showCollections" height="80vh" class="!rounded-t-2xl dark:!bg-dark-bg dark:text-dark-text" placement="bottom">
        <n-drawer-content>
            <template #header>
                <div class="flex items-center justify-between font-medium text-base">
                    <n-icon size="24" @click="showCollections = false">
                        <IconsClose />
                    </n-icon>
                    <span>{{ t("COLLECTION_MOVE_TITLE") }}</span>
                    <span class="text-primary" @click="handleSubmitMoveTo">Done</span>
                </div>
            </template>

            <div class="rounded-lg bg-bg-3 px-4 mb-20">
                <div
                    v-for="(item, index) in AllCollections"
                    :key="item.id"
                    class="py-4 flex items-center gap-2"
                    :class="{ 'border-t border-solid dark:border-dark-bg-3 border-neutral-200': index !== 0 }"
                    @click="targetCollection = item.id"
                >
                    <n-icon class="shrink-0" size="20"><IconsBook /></n-icon>
                    <div class="flex-1 text-ellipsis overflow-hidden text-nowrap">{{ item.collectName }}</div>
                    <n-icon class="shrink-0 text-primary" size="20"><IconsSuccess v-if="targetCollection === item.id" /></n-icon>
                </div>
            </div>
        </n-drawer-content>
    </n-drawer>

    <EditCollectionDrawer v-model:show="showEditCollection" :currentCollect="currentCollect" @change="handleRename" />
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { RecycleScroller } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import { useWindowSize } from "@vueuse/core";
import { useShareCollect, useDefUnCollectionsConfirm } from "@/stores/index";
import { delCollectById, queryImgByCollect, batchReduceCollection, batchMoveToCollections } from "@/api";
import { formatPonyV6Prompt, debounce, isScrolledToBottom } from "@/utils/tools";
import { IconArrowTop, IconSquare, IconEqualHeight, IconSizeSmall, IconSizeMedium, IconSizeLarge, Alert } from "@/icons/index.js";
import { NIcon, NCheckbox } from "naive-ui";
import EditCollectionDrawer from "@/components/collections/EditCollectionDrawer.vue";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";

const { showMessage } = useModal();
const defUnCollectionsConfirm = useDefUnCollectionsConfirm();
const localePath = useLocalePath();

const shareCollect = useShareCollect();
const { width } = useWindowSize();
const route = useRoute();
const router = useRouter();
const sortType = ref("Descending");
const chooseLayoutType = ref("Square");
const chooseLayoutSize = ref("Medium");
const currentCollect = ref({});
const moreActive = ref(false);
const selectModel = ref(false);
const showCollections = ref(false);
const showEditCollection = ref(false);
const targetCollection = ref(null);
const colCount = computed(() => {
    return chooseLayoutSize.value === "Small" ? 4 : chooseLayoutSize.value === "Medium" ? 2 : 1;
});
const itemWidth = computed(() => (width.value - 8) / colCount.value);
const AllCollections = computed(() => shareCollect.collectList.filter((item) => item.id !== route.params.id));
watch(
    () => shareCollect.collectList,
    (val) => {
        currentCollect.value = val.find((item) => item.id === route.params.id);
        console.log(val);
    }
);

watch(sortType, (v) => {
    currentPage = 1;
    queryCollectionList();
});

const sortOps = [
    {
        label: "Descending",
        value: "Descending",
        icon: () => h(NIcon, { class: "rotate-180 shrink-0", size: 20 }, { default: () => h(IconArrowTop) }),
    },
    {
        label: "Ascending",
        value: "Ascending",
        icon: () => h(NIcon, { class: "shrink-0", size: 20 }, { default: () => h(IconArrowTop) }),
    },
];
const layoutOps = [
    {
        label: "Full",
        value: "Full",
        icon: () => h(NIcon, { class: "shrink-0", size: 20 }, { default: () => h(IconEqualHeight) }),
    },
    {
        label: "Square",
        value: "Square",
        icon: () => h(NIcon, { class: "shrink-0", size: 20 }, { default: () => h(IconSquare) }),
    },
];
const layoutSizeOps = [
    {
        label: "Small",
        value: "Small",
        icon: () => h(NIcon, { class: "shrink-0", size: 20 }, { default: () => h(IconSizeSmall) }),
    },
    {
        label: "Medium",
        value: "Medium",
        icon: () => h(NIcon, { class: "shrink-0", size: 20 }, { default: () => h(IconSizeMedium) }),
    },
    {
        label: "Large",
        value: "Large",
        icon: () => h(NIcon, { class: "shrink-0", size: 20 }, { default: () => h(IconSizeLarge) }),
    },
];
onMounted(async () => {
    // 假如没有ID 则退回
    const id = route.params.id;
    if (!id || shareCollect.collectList.length === 0) {
        await navigateTo(localePath("/m/collection"), { replace: true });
        return;
    }
    currentCollect.value = shareCollect.collectList.find((item) => item.id === id);
    queryCollectionList();
});
const isLoading = ref(false);
const handleBack = () => {
    router.back();
};
let currentPage = 1;
const queryCollectionList = async () => {
    if (isLoading.value) {
        return;
    }
    const { pageSize = 100, maxPage = 1, id } = currentCollect.value;
    const param = { pageSize, pageNum: currentPage, classifyId: id, collationName: sortType.value };

    if (param.pageNum > maxPage && maxPage != 0) {
        return;
    }
    isLoading.value = true;

    const { status, data = {}, message } = await queryImgByCollect(param);
    isLoading.value = false;

    if (status !== 0) {
        openToast.error(message);
        return false;
    }
    if (!data) {
        return;
    }
    const newArray = data?.resultList || [];
    const list = newArray.map((item) => {
        const { prompt } = formatPonyV6Prompt(item.genInfo, "output");
        return {
            ...item.genInfo,
            ...item,
            prompt,
            imgName: item.fileName,
            imgUrl: item.fileUrl,
            realWidth: item.width,
            realHeight: item.height,
            renderUrl: item.miniThumbnailUrl || item.highMiniUrl || item.thumbnailUrl || item.highThumbnailUrl || item.fileUrl,
            largeUrl: item.highMiniUrl || item.thumbnailUrl || item.highThumbnailUrl || item.fileUrl,
        };
    });
    const newCurrent = { ...currentCollect.value };
    if (currentPage === 1) {
        newCurrent.cover = list[0]?.renderUrl;
        newCurrent.imgList = list;
        newCurrent.total = data.total;
        newCurrent.maxPage = Math.ceil(data.total / newCurrent.pageSize);
    } else {
        newCurrent.imgList.push(...list);
    }
    currentPage += 1;
    const index = shareCollect.collectList.findIndex((item) => item.id === id);
    const newArr = [...shareCollect.collectList];
    newArr.splice(index, 1, newCurrent);
    shareCollect.setCollectList([...newArr]);
};

const pageScroll = debounce((e) => {
    const isBottom = isScrolledToBottom(e.target);
    if (!isBottom) {
        return;
    }
    queryCollectionList();
}, 60);

//删除收藏夹
const handleDel = async () => {
    const allowDel = await showMessage({
        style: { width: "480px" },
        confirmBtn: t("COMMON_BTN_CONTINUE"),
        content: h("div", null, [h("p", null, t("COLLECTION_DEL_TIPS"))]),
        icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_ATTEN"),
    })
        .then(() => true)
        .catch(() => false);

    // console.log(allowDel);
    if (!allowDel) {
        return;
    }
    const { status, data = {}, message } = await delCollectById({ id: currentCollect.value.id });
    if (status !== 0) {
        openToast.error(message);
        return;
    }

    const newArr = shareCollect.collectList.filter((item) => item.id !== currentCollect.value.id);
    shareCollect.setCollectList(newArr);

    await navigateTo(localePath("/m/collection"), { replace: true });
};

const selectIds = ref(new Set());
const hasSelectItem = computed(() => {
    return (imgName) => selectIds.value.has(imgName);
});
//取消当前
const handleCancelSelect = () => {
    selectIds.value.clear();
    selectModel.value = false;
};
//选择当前项目
const handleChooseItem = async (item) => {
    // const { imgName } = item;
    if (!selectModel.value) {
        shareCollect.setCollectItem({ ...item, classifyId: currentCollect.value.id });
        await navigateTo({ path: localePath(`/m/collection/detail`) });
        return;
    }
    const { imgName } = item;
    const hasChecked = hasSelectItem.value(imgName);
    if (hasChecked) {
        selectIds.value.delete(imgName);
    } else {
        selectIds.value.add(imgName);
    }
};
//批量 移动收藏夹
const handleBatchMove = async () => {
    if (selectIds.value.size === 0) {
        return;
    }
    targetCollection.value = AllCollections.value[0]?.id;
    showCollections.value = true;
};
//提交批量移动
const handleSubmitMoveTo = async () => {
    const collectBatchList = currentCollect.value.imgList.filter((item) => selectIds.value.has(item.imgName)).map(({ imgName, promptId }) => ({ fileName: imgName, promptId }));
    if (collectBatchList.length === 0) {
        openToast.error(t("COLLECTION.NO_SELECT_IMAGE"));
        return;
    }
    if (!targetCollection.value) {
        openToast.error(t("COLLECTION.NO_COLLECTION_SELECTED"));
        return;
    }

    const param = {
        classifyId: targetCollection.value,
        oldClassifyId: currentCollect.value.id,
        collectBatchList,
    };
    const { status, message } = await batchMoveToCollections(param);
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    showCollections.value = false;

    const imgList = currentCollect.value.imgList.filter((item) => !selectIds.value.has(item.imgName));
    currentCollect.value.imgList = imgList;
    currentCollect.value.cover = imgList[0]?.renderUrl;
    currentCollect.value.total = currentCollect.value.total - selectIds.value.size;
    currentCollect.value.maxPage = Math.ceil(currentCollect.value.total / currentCollect.value.pageSize);
    selectIds.value.clear();

    const index = shareCollect.collectList.findIndex((item) => item.id === currentCollect.value.id);
    const newArr = [...shareCollect.collectList];
    newArr.splice(index, 1, { ...currentCollect.value });
    shareCollect.setCollectList([...newArr]);
};

const handleCancelFavorite = async () => {
    const collectBatchList = currentCollect.value.imgList.filter((item) => selectIds.value.has(item.imgName)).map(({ imgName, promptId }) => ({ fileName: imgName, promptId }));
    const param = {
        classifyId: currentCollect.value.id,
        collectBatchList,
    };
    const { status, message } = await batchReduceCollection(param);
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    const imgList = currentCollect.value.imgList.filter((item) => !selectIds.value.has(item.imgName));
    currentCollect.value.imgList = imgList;
    currentCollect.value.cover = imgList[0]?.renderUrl;
    currentCollect.value.total = currentCollect.value.total - selectIds.value.size;
    currentCollect.value.maxPage = Math.ceil(currentCollect.value.total / currentCollect.value.pageSize);
    selectIds.value.clear();
    const index = shareCollect.collectList.findIndex((item) => item.id === currentCollect.value.id);
    const newArr = [...shareCollect.collectList];
    newArr.splice(index, 1, { ...currentCollect.value });
    shareCollect.setCollectList([...newArr]);
};

//批量 取消收藏
const handleBatchUncollect = async () => {
    if (selectIds.value.size === 0) {
        return;
    }
    if (defUnCollectionsConfirm.isConfirm) {
        handleCancelFavorite();
        return;
    }
    let hasChecked = false;
    showMessage({
        style: { width: "480px" },
        confirmBtn: t("COLLECTION_UN_COLLECTION"),
        content: h("div", null, [
            h("p", null, t("COLLECTION_REMOVE_CONTENT")),
            h(
                NCheckbox,
                {
                    class: "mt-4",
                    style: {
                        "--n-color-checked": "#6904E9", // 修改选中时的颜色
                        "--n-border-checked": "1px solid #6904E9", // 修改选中时的颜色
                        "--n-border-focus": "1px solid #6904E9", // 修改选中时的颜色
                        "--n-box-shadow-focus": "none", // 修改选中时的颜色
                        "--n-border": "1px solid #6904E9", // 修改选中时的颜色
                    },
                    "on-update:checked": (val) => {
                        hasChecked = val;
                    },
                },
                {
                    default: () =>
                        h(
                            "span",
                            {
                                class: " dark:text-dark-text",
                            },
                            t("COLLECTION_REMOVE_NOT_CONFIRM")
                        ),
                }
            ),
        ]),
        icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_ATTEN"),
    }).then(() => {
        defUnCollectionsConfirm.setDefConfirm(hasChecked);
        handleCancelFavorite();
    });
};
//修改收藏夹名称
const handleRename = ({ collectName, description, id }) => {
    const index = shareCollect.collectList.findIndex((item) => item.id === id);
    const newArr = [...shareCollect.collectList];
    Object.assign(currentCollect.value, { collectName, description });
    newArr.splice(index, 1, currentCollect.value);
    shareCollect.setCollectList(newArr);
};
</script>

<style lang="scss" scoped></style>
