<template>
    <!-- h5中的生图设置 -->
    <div>
        <div
            class="flex justify-center size-10 items-center bg-fill-wd-1 rounded-full text-text-2 font-medium hover:text-text-1 cursor-pointer hover:!bg-fill-wd-2"
            :class="{
                '!text-text-1 !bg-fill-wd-2': show,
            }"
            @click="show = true"
        >
            <span class="p-2">
                <IconsControl class="size-5" />
            </span>
        </div>
        <CommonH5Popup v-model:show="show">
            <div class="w-full relative h-[70dvh] flex flex-col gap-4">
                <div class="w-full flex justify-between items-center mt-5 mb-2">
                    <span class="rounded-full size-8 bg-fill-wd-1 hover:bg-fill-wd-2 flex items-center justify-center cursor-pointer" @click="show = false">
                        <IconsClose />
                    </span>
                    <p class="text-text-1 font-medium text-base">{{ $t("CONFIG_BASE_TITLE") }}</p>
                    <span class="text-primary-6 font-medium cursor-pointer text-base" @click="show = false">{{ $t("DONE") }}</span>
                </div>
                <!-- 生图模型 -->
                <CreateModelPanel :modelId="createStore.model_id" v-model:showNewTag="showNewTag" @change="commonDispatch($event, 'setModelId')" @forbid="commonDispatch($event, 'modelForbid')">
                    <template #trigger>
                        <div class="group w-full transition-all duration-[250]">
                            <div class="w-full flex items-center text-text-4 text-sm mb-2 gap-1">
                                <span>{{ $t("CONFIG_BASE_MODEL") }}</span>
                            </div>
                            <div class="w-full cursor-pointer h-12 p-2 bg-fill-wd-1 hover:bg-fill-wd-2 rounded-lg flex items-center g-node-model gap-2 group text-sm font-medium relative">
                                <img v-if="realSelectedItem.icon" :src="realSelectedItem.icon || defaultModel.icon" class="w-8 h-8 rounded-lg overflow-hidden shrink-0" />
                                <span class="text-text-2 group-hover:text-text-1">{{ realSelectedItem.label }}</span>
                                <img src="@/assets/images/subscribe/icon_fo_all_member.webp" class="size-4" alt="" v-if="realSelectedItem.isVipModel" />
                                <div v-if="showNewTag" class="new-tag absolute text-white transform translate-y-[-100%] right-0 text-xs px-2 py-0.5 rounded-[2px_12px_2px_12px] z-[100]">NEW</div>
                            </div>
                        </div>
                    </template>
                </CreateModelPanel>
                <!-- 图像比例 -->
                <CreateResolution
                    :defaultCheckRatioId="createStore.defaultCheckRatioId"
                    :disabled="disableResolutionChange"
                    :showAuto="disableResolutionChange"
                    @change="commonDispatch($event, 'setResolution')"
                />
                <!-- 生图张数 -->
                <CreateBatchSizeSelect :batch_size="createStore.batch_size" :disabled="disabledBatchSizeChange" @change="commonDispatch($event, 'setBatchSize')" />
                <!-- 高级设置 cfg steps等 -->
                <CreateAdvancedSettings />
            </div>
        </CommonH5Popup>
    </div>
</template>

<script setup>
import { defaultModel } from "@/utils/constant.js";
import { useCreateStore } from "@/stores/create";

const createStore = useCreateStore();
const emit = defineEmits(["setConfigs"]);
const show = ref(false);
const showNewTag = ref(false); //是否有模型上新

const props = defineProps({
    disableResolutionChange: {
        type: Boolean,
        default: false,
    },
    realSelectedItem: {
        type: Object,
        default: () => {},
    },
    disabledBatchSizeChange: {
        type: Boolean,
        default: false,
    },
});

const commonDispatch = (payload, type) => {
    emit("setConfigs", { payload, type });
};
</script>
