<template>
    <div class="re-create__container">
        <div class="re-create__close" @click="handleClose()">
            <IconsClose class="size-4 lg:size-6" />
        </div>
        <div ref="wrapperRef" class="flex-1 m-6 overflow-hidden">
            <canvas ref="canvasRef" />
        </div>
        <div class="py-4 px-6 flex justify-center gap-4">
            <div class="h-14 p-2 bg-bg-2 rounded-xl flex items-center gap-3 border border-solid border-border-1">
                <n-popover :show-arrow="false" raw trigger="click" zIndex="9999" ref="aspectPopoverRef">
                    <template #trigger>
                        <div class="h-full px-3 bg-bg-6 rounded-lg flex gap-1.5 items-center justify-center cursor-pointer select-none">
                            <IconsAutoSize v-if="includeContentRefer" class="text-2xl" />
                            <Aspect v-else :height="renderSize.height" :width="renderSize.width" />
                            <span class="shrink-0 text-base mt-0.5 text-text-2">{{ renderSize.label }}</span>
                        </div>
                    </template>
                    <AspectRatioList :shapeList="supportShapeList" @change="handleScaleChange" />
                </n-popover>
                <div class="w-0.5 h-6 bg-border-2"></div>
                <div class="h-full aspect-square flex justify-center items-center rounded-lg" :class="{ 'hover:bg-fill-wd-1': !disableUndoOrReset }">
                    <IconsUndo :class="{ 'text-text-6': disableUndoOrReset }" class="text-2xl text-text-2 cursor-pointer select-none" @click="handleUndo" />
                </div>
                <div class="h-full px-2 flex justify-center items-center rounded-lg" :class="{ 'hover:bg-fill-wd-1': !disableUndoOrReset }">
                    <span :class="{ 'text-text-6': disableUndoOrReset }" class="text-base text-text-2 font-medium cursor-pointer select-none" @click="handleReset">Reset</span>
                </div>
            </div>
            <div class="h-14 p-2 bg-bg-2 rounded-xl flex items-center gap-3 border border-solid border-border-1">
                <IconsMinus class="text-2xl text-text-2 cursor-pointer select-none" @click="handleZoom('sub')" />
                <div class="min-w-10 text-center text-text-2">{{ showZoom }}%</div>
                <IconsAdd class="text-2xl text-text-2 cursor-pointer select-none" @click="handleZoom('add')" />
            </div>
        </div>
        <div class="py-5 px-6 bg-bg-2 flex items-center gap-4 border-t border-solid border-border-1 max-w-full rounded-lg">
            <!-- 模型 -->
            <ModelDrop :models="[fluxDevModelId, realisticModelId]" :modelId="modelId" @chooseModel="chooseModel" v-if="false" />
            <!-- 输入框 -->
            <div class="flex-1 h-11 bg-fill-ipt-1 rounded-lg overflow-hidden flex items-center gap-4 pr-4">
                <n-input ref="textareaRef" v-model:value="outPaintPrompt" :maxlength="2500" class="w-full min-w-0 text-sm" :placeholder="$t('SHORT_DRAW_WHAT')" type="text" />

                <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <div class="w-6 h-6 shrink-0">
                            <IconsSpinLoading v-if="promptTranslateOrEnhance.isLoading && isTranslate" class="text-2xl text-primary" />
                            <IconsTranslate
                                v-else
                                :class="{ '!opacity-40': !isAllowTranslateOrEnhance }"
                                class="text-2xl text-text-2 cursor-pointer select-none"
                                @click="handleTranslateOrEnhance('translation')"
                            />
                        </div>
                    </template>
                    <div class="tips-box">
                        <div class="font-medium text-text-1 flex items-center gap-2">
                            <span>{{ t("SHORT_PROMPT_TRANS_TITLE") }}</span>
                            <!-- <n-icon size="18" class="text-info-6" v-show-plan><IconsDiamond /></n-icon> -->
                        </div>
                        <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_TRANS_MESSAGE") }}</div>
                    </div>
                </n-tooltip>

                <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <div class="w-6 h-6 shrink-0">
                            <IconsSpinLoading v-if="promptTranslateOrEnhance.isLoading && isEnhance" class="text-2xl text-primary" />
                            <IconsImprove
                                v-else
                                :class="{ '!opacity-40': !isAllowTranslateOrEnhance }"
                                class="text-2xl text-text-2 cursor-pointer select-none"
                                @click="handleTranslateOrEnhance('enhance')"
                            />
                        </div>
                    </template>

                    <div class="tips-box">
                        <div class="font-medium text-text-1 flex items-center gap-2">
                            <span>{{ t("SHORT_PROMPT_IMPROVE_TITLE") }}</span>
                            <!-- <n-icon size="18" class="text-info-6" v-show-plan><IconsDiamond /></n-icon> -->
                        </div>
                        <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_IMPROVE_MESSAGE") }}</div>
                    </div>
                </n-tooltip>
            </div>

            <n-tooltip placement="bottom" trigger="hover" :delay="100" :show-arrow="false" raw>
                <template #trigger>
                    <n-button
                        :bordered="false"
                        :disabled="isExceed || !loadComplete"
                        :loading="isExceed"
                        class="px-4 py-2.5 h-11 rounded-md !bg-primary-6 shrink-0 !text-dark-active-text"
                        @click="handleGenerate"
                    >
                        <span>{{ $t("CONFIG_BASE_SUBMIT_BTN") }}</span>
                        <ExpendConst :feature-type="lumenCostKey" :num="1" :size="generateConfigSize" class="ml-1" />
                    </n-button>
                </template>
                <div v-if="!isMobile" class="bg-white text-dark-bg-2 dark:text-dark-text p-3 rounded dark:bg-dark-bg-2 max-w-96">
                    <div>{{ t("ESTIMATE_COST_LUMENS") }}</div>
                </div>
            </n-tooltip>
        </div>
    </div>
</template>
<script setup>
import IconTranslate from "@/components/icons/Translate.vue";
import Minus from "@/components/icons/Minus.vue";
import Add from "@/components/icons/Add.vue";
import AutoSize from "@/components/icons/AutoSize.vue";
import Aspect from "@/components/Aspect.vue";
import SpinLoading from "@/components/icons/SpinLoading.vue";
import { useThemeStore } from "@/stores/system-config";

import {
    BackgroundFillType,
    controlsUtils,
    EaseCanvas,
    Rect,
    EaseImage,
    originGroupKey,
    getExtensionKey,
    iMatrix,
    Pattern,
    Point,
    util,
    Group,
    queryElement,
    HistoryType,
    StaticCanvas,
} from "@easeus/editor-core";
import { useSupportModelList, useCurrentTaskQueue } from "@/stores/create";
import { uploadToCos, mergeArraysById, getFuncNameByModelId, getCurrentISO8601Time, isFluxDevModel, formatPonyV6Prompt } from "@/utils/tools.js";
import { fluxDevModelId, realisticModelId } from "@/utils/constant.js";
import AspectRatioList from "@/components/generator/AspectRatioList.vue";
import { longTaskDialog, translateOrEnhanceText, useAppendPreloadTask, useCheckMaxTask, useGetModelInfo, useGoToCreate } from "@/hook/create.js";
import { renderModelIcon } from "@/utils/tools.js";
import { SHAPE_ALL, ERROR_CODE_ENUM, PRELOAD_TASK_TYPE, SUBSCRIBE_PERMISSION } from "@/utils/constant.js";
import { useSubPermission, useVipNotice } from "@/hook/subscribe.js";
import { genExpand } from "@/api/index.js";
import ModelDrop from "@/components/ModelDrop.vue";
import ExpendConst from "@/components/subscribe/ExpendConst.vue";
import { useGlobalError } from "@/hook/error";
import { useCreateStore } from "@/stores/create";
import { storeToRefs } from "pinia";
const { toCreate } = useGoToCreate();

const emits = defineEmits(["confirm", "cancel", "close"]);
const { showError } = useGlobalError();
const { isMobile } = storeToRefs(useThemeStore());
const props = defineProps({
    item: {
        type: Object,
        required: true,
        default: () => ({}),
    },
});

const { t } = useI18n({ useScope: "global" });

const isCustomUpload = computed(() => props.item.originCreate === "customUpload"); // 是否是上传的图片
const lumenCostKey = computed(() => getFuncNameByModelId({ model_id: modelId.value }));

const { checkShowVipNotice } = useVipNotice();
const supportModelListStore = useSupportModelList();

const scale = ref("");
const aspectPopoverRef = ref();
const renderSize = computed(() => {
    let size = scale.value || "";
    if (!size) {
        size = "1024 x 1024";
    }
    const checked = supportShapeList.value.find((item) => item.value === size);
    if (!checked) {
        return { width: 1, height: 1 };
    }
    const label = checked.label.replace(":", " : ");
    return {
        label: label,
        width: checked.width,
        height: checked.height,
    };
});

// 当前模型支持的尺寸
const supportShapeList = ref(SHAPE_ALL);
// 选择的refer是否包含contentRefer
// const includeContentRefer = computed(() => {
//     const list = createStore.generateConfig.style_list || [];
//     return list.some((item) => item.style === "contentRefer");
// });

const generateConfigSize = computed(() => {
    const { realHeight, realWidth } = props.item;
    return `${realWidth}x${realHeight}}`;
});

/**
 * 设置图片相对Group居中
 * @param image
 * @param group
 */
const setGroupCenterFromObject = (image, group) => {
    const imageCenter = image.getCenterPoint();
    const groupCenter = group.getCenterPoint();
    let offsetX = 0;
    let offsetY = 0;
    if (group.scaleX > image.scaleX) {
        offsetX = groupCenter.x - imageCenter.x;
    }
    if (group.scaleY > image.scaleY) {
        offsetY = groupCenter.y - imageCenter.y;
    }
    image.set({
        left: image.left + offsetX,
        top: image.top + offsetY,
    });
    // 更新坐标
    image.setCoords();
};

/**
 * 选择比例
 * @param item
 * @returns {Promise<void>}
 */
const handleScaleChange = async (item) => {
    setRatio(item, true);
    const { scaleX, scaleY } = DEGREE_WITH_SCALES.find((pItem) => pItem.label === item.label);
    const group = queryElement(groupID, opCanvas);
    const image = queryElement(imageID, opCanvas);

    const groupObject = group.toObject(getExtensionKey);
    const imageObject = image.toObject(getExtensionKey);
    const original1 = {};
    const original2 = {
        ...imageObject,
    };
    originGroupKey.forEach((v) => {
        original1[v] = groupObject[v];
    });
    group.set({ left: 0, top: 0, scaleX: 1, scaleY: 1 });
    group.set({ left: 0, top: 0, scaleX, scaleY });
    image.set({ left: 0, top: 0 });
    group.setCoords();
    image.setCoords();

    setGroupCenterFromObject(image, group); // 在Group中居中显示图片

    await autoResize(group);
    opCanvas.renderAll();
    aspectPopoverRef.value.setShow(false);
    const historySnapShot = {
        type: HistoryType.UPDATE,
        action: "resizing",
        models: [
            {
                ...group.toObject(getExtensionKey),
                original: original1,
            },
            {
                ...image.toObject(getExtensionKey),
                original: original2,
            },
        ],
    };
    opCanvas.history.addHistory(historySnapShot);
};

const disableUndoOrReset = ref(true); // 禁用撤销与恢复

/**
 * 设置比例
 * @param item
 * @param isUpdateStack
 */
const setRatio = (item, isUpdateStack = false) => {
    scale.value = `${item.width} x ${item.height}`;
    if (isUpdateStack) {
        disableUndoOrReset.value = false;
    }
};

/**
 * 撤销
 */
const handleUndo = () => {
    if (disableUndoOrReset.value) return;
    console.log("撤销", opCanvas.history);
    opCanvas.history.undo();
    const groupObj = queryElement(groupID, opCanvas);
    autoResize(groupObj);
    disableUndoOrReset.value = opCanvas.history.stackIndex <= 0;
};

/**
 * 重置
 */
const handleReset = async () => {
    if (disableUndoOrReset.value) return;
    const group = queryElement(groupID, opCanvas);
    const image = queryElement(imageID, opCanvas);
    opCanvas.history.clear();
    // approximateProportion为真则不在预设比例内
    if (approximateProportion) {
        group.set({ left: 0, top: 0, scaleX: approximateProportion.scaleX, scaleY: approximateProportion.scaleY });
        image.set({ left: 0, top: 0 });
    } else {
        group.set({ left: 0, top: 0, scaleX: 1, scaleY: 1 });
        image.set({ left: 0, top: 0 });
    }
    group.setCoords();
    setGroupCenterFromObject(image, group); // 在Group中居中显示图片
    autoResize(group);
    opCanvas.renderAll();
    setRatio(defaultRatio);
    disableUndoOrReset.value = true;
};

const zoom = ref(0);
let zoomRatio;
const showZoom = computed(() => parseInt(zoom.value * 100));
const handleZoom = (type) => {
    const currentZoom = zoom.value / zoomRatio;
    const zoomStep = 0.1 / zoomRatio;
    const minZoom = 0.1 / zoomRatio;
    const maxZoom = 1 / zoomRatio;
    const newZoom = type === "add" ? Math.min(currentZoom + zoomStep, maxZoom) : Math.max(currentZoom - zoomStep, minZoom);
    opCanvas.setZoom(newZoom);
    opCanvas.renderAll();
};

const { checkPermission, checkPermissionNotModal } = useSubPermission();
const modelId = ref(props.item.model_id || realisticModelId); // 默认真实模型
// 模型选择组件渲染函数
const renderLabel = computed(() => {
    const model = useGetModelInfo(modelId.value);
    const icon = renderModelIcon.value(modelId.value);
    return { label: model.label, icon };
});
const chooseModel = async (model) => {
    const isFluxDev = isFluxDevModel({ model_id: model });
    if (isFluxDev) {
        const res = await checkPermission(SUBSCRIBE_PERMISSION.NOT_BASIC_MEMBER);
        if (!res) {
            return;
        }
    }
    modelId.value = model;
};

const outPaintPrompt = ref("");

// 翻译或增强提示词
const promptTranslateOrEnhance = ref({ type: "", isLoading: false });
const isAllowTranslateOrEnhance = computed(() => outPaintPrompt.value && outPaintPrompt.value.length <= 600);
const isTranslate = computed(() => promptTranslateOrEnhance.value.type === "translation");
const isEnhance = computed(() => promptTranslateOrEnhance.value.type === "enhance");
/**
 * 提示词翻译或提示词增强
 * @param mode 类型
 */
const handleTranslateOrEnhance = async (mode) => {
    if (!isAllowTranslateOrEnhance.value) {
        outPaintPrompt.value.length > 600 && openToast.info(t("SHORT_PROMPT_TOO_LONG"));
        return;
    }

    let permission = SUBSCRIBE_PERMISSION.ENHANCE;
    if (mode === "translation") {
        permission = SUBSCRIBE_PERMISSION.TRANSLATION;
        window.trackEvent("APP_AUTO_TRANSLATE", { el: `translate_btn` });
    } else {
        window.trackEvent("APP_AUTO_ENHANCE", { el: `enhance_btn` });
    }
    if (promptTranslateOrEnhance.value.isLoading) {
        return;
    }
    const hasPermission = await checkPermission(permission);
    if (!hasPermission) {
        return;
    }

    // checkShowVipNotice(`outpaint_${mode}`);

    promptTranslateOrEnhance.value = { type: mode, isLoading: true };
    const { success, data } = await translateOrEnhanceText(outPaintPrompt.value, mode);
    promptTranslateOrEnhance.value = { type: "", isLoading: false };
    if (success) {
        outPaintPrompt.value = data;
        promptTranslateOrEnhance.value = false;
    }
};

const createImage = (src) => {
    const dpi = window.devicePixelRatio;
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.crossOrigin = true;
        image.onload = () => {
            image.width = image.width / dpi;
            image.height = image.height / dpi;
            resolve(image);
        };
        image.error = () => {
            reject();
        };
        image.src = src;
    });
};

const canvasToBlob = (canvas) => {
    return new Promise((resolve, reject) => {
        canvas.toBlob(
            (blob) => {
                console.log(URL.createObjectURL(blob));
                resolve(blob);
            },
            "image/webp",
            0.9
        );
    });
};

function getImageSizeFromRatio(imgWidth, imgHeight, width, height, rWidth, rHeight) {
    const imageWidthRatio = imgWidth / width;
    const imageHeightRatio = imgHeight / height;
    const finalImageWidth = imageWidthRatio * rWidth;
    const finalImageHeight = imageHeightRatio * rHeight;
    return { finalImageHeight: Math.round(finalImageHeight), finalImageWidth: Math.round(finalImageWidth) };
}
const getBlob = (canvas, ...arg) =>
    new Promise((res) => {
        canvas.toBlob((blob) => {
            res(blob);
        }, ...arg);
    });
const getUploadInfo = () => {
    return new Promise(async (resolve, reject) => {
        const group = queryElement(groupID, opCanvas),
            groupWidth = (group.width - 1) * group.scaleX,
            groupHeight = (group.height - 1) * group.scaleY,
            image = queryElement(imageID, opCanvas),
            imageWidth = image.getScaledWidth(),
            imageHeight = image.getScaledHeight(),
            remainderX = groupWidth - imageWidth + group.left - image.left,
            remainderY = groupHeight - imageHeight + group.top - image.top;
        let clipedWidth = imageWidth,
            clipedHeight = imageHeight,
            offsetX = group.left - image.left,
            offsetY = group.top - image.top,
            newTop = 0,
            newLeft = 0,
            result = {};
        if (remainderX < 0 || offsetX > 0) {
            newLeft = offsetX > 0 ? offsetX : 0;
            const remainOffset = remainderX < 0 ? remainderX : 0;
            clipedWidth = imageWidth + remainOffset - newLeft;
        }
        if (remainderY < 0 || offsetY > 0) {
            newTop = offsetY > 0 ? offsetY : 0;
            const remainOffset = remainderY < 0 ? remainderY : 0;
            clipedHeight = imageHeight + remainOffset - newTop;
        }
        if (clipedWidth <= 0 || clipedHeight <= 0) {
            resolve(null);
            return;
        }
        const tempCanvas = new StaticCanvas(null, {
            width: clipedWidth,
            height: clipedHeight,
        });
        const groupCanvas = new StaticCanvas(null, {
            width: groupWidth,
            height: groupHeight,
            backgroundColor: "rgba(123, 87, 229, 0.64)",
        });
        const tempImage = await image.clone(getExtensionKey);
        const groupImage = await image.clone(getExtensionKey);
        groupImage.set({ left: -offsetX, top: -offsetY });
        const blackRect = new Rect({
            width: groupImage.width,
            height: groupImage.height,
            fill: "black",
            left: -offsetX,
            top: -offsetY,
        });
        tempImage.set({ left: -newLeft, top: -newTop });
        tempCanvas.add(tempImage);
        groupCanvas.add(blackRect);
        groupCanvas.add(groupImage);
        tempCanvas.renderAll();
        groupCanvas.renderAll();
        const blob = await getBlob(tempCanvas.getElement());
        const groupBlob = await getBlob(groupCanvas.getElement(), "image/webp", 0.15);
        result = {
            left: offsetX > 0 ? 0 : -offsetX,
            top: offsetY > 0 ? 0 : -offsetY,
            right: remainderX < 0 ? 0 : remainderX,
            bottom: remainderY < 0 ? 0 : remainderY,
            width: groupWidth,
            height: groupHeight,
            blob,
            groupBlob,
        };
        resolve(result);
    });
};

const isUploading = ref(false); // 是否正在上传
const handleGenerate = async () => {
    isUploading.value = true;
    try {
        const info = await getUploadInfo();
        if (!info) {
            isUploading.value = false;
            openToast.error(t("OUTPAINT_ERROR"));
            return;
        }
        let url = URL.createObjectURL(info.blob);
        const cropImg = await createImage(url);
        const { width, height, left: originalLeft, top: originalTop, right: originalRight, bottom: originalBottom } = info;
        const originalXRatio = width / cropImg.width;
        const originalYRatio = height / cropImg.height;

        // 计算四边相对于原图实际扩了多少像素
        const top = originalTop / originalYRatio;
        const right = originalRight / originalXRatio;
        const bottom = originalBottom / originalYRatio;
        const left = originalLeft / originalXRatio;

        const checked = supportShapeList.value.find((item) => item.value === scale.value);
        let newTop = Math.abs(parseInt(top / (cropImg.height / checked.height)));
        let newRight = Math.abs(parseInt(right / (cropImg.width / checked.width)));
        let newBottom = Math.abs(parseInt(bottom / (cropImg.height / checked.height)));
        let newLeft = Math.abs(parseInt(left / (cropImg.width / checked.width)));

        // 获取缩放后的原图
        const reSize = getImageSizeFromRatio(cropImg.width, cropImg.height, width, height, checked.width, checked.height);

        // 补齐差值
        const reTotalWidth = reSize.finalImageWidth + newRight + newLeft;
        const reTotalHeight = reSize.finalImageHeight + newTop + newBottom;
        if (reTotalWidth < checked.width) {
            const diffWidth = checked.width - reTotalWidth;
            newRight += diffWidth;
        }
        if (reTotalWidth > checked.width) {
            const diffWidth = reTotalWidth - checked.width;
            if (newRight > diffWidth) {
                newRight -= diffWidth;
            }
            if (newLeft > diffWidth) {
                newLeft -= diffWidth;
            }
        }
        if (reTotalHeight < checked.height) {
            const diffHeight = checked.height - reTotalHeight;
            newBottom += diffHeight;
        }
        if (reTotalHeight > checked.height) {
            const diffHeight = reTotalHeight - checked.height;
            if (newBottom > diffHeight) {
                newBottom -= diffHeight;
            }
            if (newTop > diffHeight) {
                newTop -= diffHeight;
            }
        }

        const resetSizeWidth = checked.width - Math.abs(newLeft) - Math.abs(newRight);
        const resetSizeHeight = checked.height - Math.abs(newTop) - Math.abs(newBottom);
        // resize图片
        const canvas = document.createElement("canvas");
        canvas.width = resetSizeWidth;
        canvas.height = resetSizeHeight;
        const ctx = canvas.getContext("2d");
        const img = await createImage(URL.createObjectURL(info.blob));
        ctx.drawImage(img, 0, 0, resetSizeWidth, resetSizeHeight);
        const blob = await canvasToBlob(canvas);

        // 上传到对象存储
        const res = await uploadToCos({ file: blob, originalFileName: Date.now() + "_.webp" });
        const origin = await uploadToCos({ file: info.groupBlob, originalFileName: Date.now() + "_origin.webp" });
        const genParameters = {
            model_id: modelId.value,
            prompt: outPaintPrompt.value,
            resolution: {
                width: checked.width,
                height: checked.height,
                batch_size: 1,
            },
            enlargeImagePara: {
                img_url: res.fullPath,
                draw_img_url: origin.fullPath,
                left: Math.abs(newLeft),
                top: Math.abs(newTop),
                right: Math.abs(newRight),
                bottom: Math.abs(newBottom),
            },
        };
        console.log("提交参数：", genParameters);
        expandTask(genParameters);
        isUploading.value = false;
    } catch (error) {
        openToast.error(error.message);
        isUploading.value = false;
    }
};

//扩图任务开启
const handleBack = () => {
    emits("close");
};
const currentTaskQueue = useCurrentTaskQueue();
const syncAction = useSyncAction();
const isExceed = computed(() => {
    return currentTaskQueue.taskQueue.length >= currentTaskQueue.maxTask || currentTaskQueue.isLoading || isUploading.value;
});
const expandTask = async (conf, longTask = false) => {
    if (currentTaskQueue.isLoading) {
        openToast.error(t("TOAST_TASK_LIMIT"), 5e3);
        return;
    }
    // 检查当期是否允许生图
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus && !checked.preloadStatus) {
        showError(ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR);
        return;
    }
    if (!longTask) {
        conf = formatPonyV6Prompt(conf, "input");
    }

    // 并发已满 提交预载
    if (!checked.concurrencyStatus) {
        useAppendPreloadTask({ genParameters: conf }, PRELOAD_TASK_TYPE.EXTEND);
        handleBack();
        return;
    }
    currentTaskQueue.updateReqStatus(true);
    try {
        conf.continueCreate = longTask;
        const { data, status, message } = await genExpand(conf);
        currentTaskQueue.updateReqStatus(false);
        //关闭 操作窗口，显示 原页面
        if (status === ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR) {
            showError(ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR);
            return;
        }
        // 耗时任务 二次确认 重新提交
        if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
            const hasReSubmit = await longTaskDialog();
            hasReSubmit && expandTask(conf, true);
            return;
        }
        if (status !== 0) {
            showError(status);
            return;
        }
        let originCreate = "enlargeImage";
        const list = mergeArraysById(currentTaskQueue.taskQueue, [
            {
                ...conf,
                originCreate,
                markId: data.markId,
                status: "pending",
                fastHour: data.fastHour,
                index: data.index,
                createTimestamp: getCurrentISO8601Time(),
            },
        ]);
        currentTaskQueue.updateTaskQueue(list);
        toCreate();
        handleBack();
    } catch (error) {
        currentTaskQueue.updateReqStatus(false);
        isUploading.value = false;
        openToast.error(error.message);
    }
};

const setCenterFromObject = (obj) => {
    const objCenter = obj.getCenterPoint();
    const viewportTransform = opCanvas.viewportTransform;
    if (opCanvas.width === undefined || opCanvas.height === undefined || !viewportTransform) return;
    viewportTransform[4] = opCanvas.width / 2 - objCenter.x * viewportTransform[0];
    viewportTransform[5] = opCanvas.height / 2 - objCenter.y * viewportTransform[3];
    opCanvas.setViewportTransform(viewportTransform);
    opCanvas.renderAll();
};

const setCanvasTransfrom = async (obj, scale) => {
    return new Promise(async (resolve) => {
        if (!opCanvas) return;
        const center = opCanvas.getCenterPoint();
        opCanvas.setViewportTransform(iMatrix);
        // 重新计算所有对象的位置偏移量
        opCanvas.zoomToPoint(new Point(center.x, center.y), scale);
        setCenterFromObject(obj);
        opCanvas.fire("zoom:change", opCanvas.getZoom());
        opCanvas.renderAll();
        resolve(true);
    });
};

// 自动调整画布大小
const autoResize = async (obj) => {
    if (!opCanvas) return;
    const { width, height } = opCanvas;
    opCanvas.fire("resize", { width, height });
    const objWidth = obj.getScaledWidth();
    const objHeight = obj.getScaledHeight();
    const scale = util.findScaleToFit({ width: objWidth, height: objHeight }, { width, height });
    await setCanvasTransfrom(obj, scale * 0.8);
    obj.setCoords();
};

/**
 * 创建棋盘格纹理
 *
 * @param size 棋盘格大小
 * @param scaleX 棋盘格横向缩放
 * @param scaleY 棋盘格纵向缩放
 * @param color1 棋盘格颜色1
 * @param color2 棋盘格颜色2
 * @returns Pattern
 */
const createCheckerPattern = (size = 20, scaleX = 1, scaleY = 1, color1 = "#fff", color2 = "#e0e0e0") => {
    const patternCanvas = document.createElement("canvas");
    patternCanvas.width = size * 2;
    patternCanvas.height = size * 2;
    const ctx = patternCanvas.getContext("2d");
    if (!ctx) {
        throw new Error("Failed to get 2D context");
    }
    // 绘制棋盘格
    ctx.fillStyle = color1;
    ctx.fillRect(0, 0, size * 2, size * 2);
    ctx.fillStyle = color2;
    ctx.fillRect(0, 0, size, size);
    ctx.fillRect(size, size, size, size);

    // 创建一个纹理对象
    return new Pattern({
        source: patternCanvas, // 传入 Canvas 元素
        repeat: "repeat",
        patternTransform: [1 / scaleX, 0, 0, 1 / scaleY, 0, 0], // 反向缩放补偿 - 拖动保持格子大小不变
    });
};

/**
 * 计算对应比例的缩放值
 *
 * @param originalWidth 图片宽度
 * @param originalHeight 图片高度
 * @param width 比例对应的宽度
 * @param height 比例对应的高度
 * @returns {{label, ratio: number, width: number, height: number, scaleX: number, scaleY: number}}
 */
const relativeScalingCal = (originalWidth, originalHeight, width, height) => {
    const ratio = width / height;
    let targetWidth, targetHeight, scaleX, scaleY;
    if (ratio >= 1) {
        // 横向比例：宽 >= 高
        targetWidth = originalWidth;
        targetHeight = originalWidth / ratio;
        scaleX = 1;
        scaleY = targetHeight / originalHeight;
    } else {
        // 竖向比例：高 > 宽
        targetHeight = originalHeight;
        targetWidth = originalHeight * ratio;
        scaleY = 1;
        scaleX = targetWidth / originalWidth;
    }
    // 保证 scaleX/scaleY ≥ 1（向上放大）
    if (scaleX < 1) {
        scaleX = 1;
        targetWidth = originalWidth;
        targetHeight = targetWidth / ratio;
        scaleY = targetHeight / originalHeight;
    }
    if (scaleY < 1) {
        scaleY = 1;
        targetHeight = originalHeight;
        targetWidth = targetHeight * ratio;
        scaleX = targetWidth / originalWidth;
    }
    return {
        ratio: parseFloat(ratio.toFixed(4)),
        width: Math.round(targetWidth),
        height: Math.round(targetHeight),
        scaleX: parseFloat(scaleX.toFixed(4)),
        scaleY: parseFloat(scaleY.toFixed(4)),
    };
};

/**
 * 计算内置比例的宽高比
 * @returns {*}
 */
const getAspectRatio = () => SHAPE_ALL.map((item) => ({ ...item, ratio: item.width / item.height }));

/**
 * 查找相近的两个比例
 * @param width
 * @param height
 * @returns {{prev: *, next: *}}
 */
const findAdjacentAspects = (width, height) => {
    // 计算目标比例（自动处理方向）
    const targetRatio = width / height;

    // 筛选同方向的比例项并按比例值排序
    const filtered = getAspectRatio().sort((a, b) => a.ratio - b.ratio);
    // 寻找插入位置以确定相邻项
    let low = 0;
    let high = filtered.length;
    while (low < high) {
        const mid = (low + high) >>> 1;
        if (filtered[mid].ratio < targetRatio) low = mid + 1;
        else high = mid;
    }

    // 确定相邻项索引
    const prevIndex = low - 1;
    const nextIndex = low;

    // 返回有效相邻项
    return {
        prev: prevIndex >= 0 ? filtered[prevIndex] : filtered[0],
        next: nextIndex < filtered.length ? filtered[nextIndex] : filtered[filtered.length - 1],
    };
};

/**
 * 计算当前宽高是否在比例指定比例内
 * @param width
 * @param height
 * @returns {boolean}
 */
function isAspectRatioValid(width, height) {
    const maxRatio = 1536 / 640;
    const minRatio = 640 / 1536;
    const currentRatio = width / height;
    return currentRatio >= minRatio && currentRatio <= maxRatio;
}

/**
 * 计算内置比例的缩放值
 */
const initPreScaling = (originalWidth, originalHeight) => {
    // 计算内置比例相对于图片的缩放值
    DEGREE_WITH_SCALES = SHAPE_ALL.map((degree) => {
        const targetItemScaling = relativeScalingCal(originalWidth, originalHeight, degree.width, degree.height);
        return { label: degree.label, ...targetItemScaling };
    });
};

// 公共缩放处理方法
const handleScaling = (direction, eventData, transform, x, y) => {
    const { target } = transform;
    const width = transform.target.getScaledWidth();
    const height = transform.target.getScaledHeight();

    // 计算实际宽度/高度
    let realDimension;
    if (direction === "ml") {
        const remainderX = width - target.width - -target.left;
        realDimension = -x + remainderX + target.width;
    } else if (direction === "mr") {
        const remainderX = -target.left;
        realDimension = x + remainderX;
    } else if (direction === "mt") {
        const remainderY = height - target.height - -target.top;
        realDimension = -y + remainderY + target.height;
    } else if (direction === "mb") {
        const remainderY = -target.top;
        realDimension = y + remainderY;
    }

    if (realDimension < 0) return false;

    // 计算相邻宽高比
    const adj = direction === "ml" || direction === "mr" ? findAdjacentAspects(realDimension, height) : findAdjacentAspects(width, realDimension);

    // 计算缩放比例
    let scale, ratio;
    if (direction === "ml" || direction === "mr") {
        const nextScale = (adj.next.ratio * height) / target.width;
        const prevScale = (adj.prev.ratio * height) / target.width;
        const next = Math.abs(nextScale - realDimension / target.width);
        const prev = Math.abs(prevScale - realDimension / target.width);

        scale = next > prev ? prevScale : nextScale;
        ratio = next > prev ? adj.prev : adj.next;
    } else {
        const nextScale = width / adj.next.ratio / target.height;
        const prevScale = width / adj.prev.ratio / target.height;
        const next = Math.abs(nextScale - realDimension / target.height);
        const prev = Math.abs(prevScale - realDimension / target.height);

        scale = next > prev ? prevScale : nextScale;
        ratio = next > prev ? adj.prev : adj.next;
    }

    // 应用缩放
    const originScale = direction === "ml" || direction === "mr" ? target.scaleX : target.scaleY;

    const newPoint = controlsUtils.getLocalPoint(transform, transform.originX, transform.originY, x, y);
    const signKey = direction === "ml" || direction === "mr" ? "signX" : "signY";
    const sign = Math.sign(direction === "ml" || direction === "mr" ? newPoint.x : newPoint.y) || transform[signKey] || 1;

    if (!transform[signKey]) {
        transform[signKey] = sign;
    }

    const scaleProp = direction === "ml" || direction === "mr" ? "scaleX" : "scaleY";
    target.set({ [scaleProp]: scale });

    const isAccept = originScale !== scale;
    if (isAccept) setRatio(ratio, true);
    return isAccept;
};

// 四个方向的缩放处理
const createScalingHandler = (direction) => {
    return (eventData, transform, x, y) => {
        const scaling = (eventData, transform, x, y) => handleScaling(direction, eventData, transform, x, y);
        const execute = controlsUtils.wrapWithFireEvent("scaling", controlsUtils.wrapWithFixedAnchor(scaling));
        return execute(eventData, transform, x, y);
    };
};

// 角点缩放处理
const createCornerHandler = () => {
    return (eventData, transform, x, y) => {
        disableUndoOrReset.value = false;
        return controlsUtils.scalingEqually(eventData, transform, x, y);
    };
};

const loadComplete = ref(false); // 画布是否加载完成
const wrapperRef = ref();
const canvasRef = ref();
let opCanvas, DEGREE_WITH_SCALES, groupID, imageID;
const initCanvas = async () => {
    const { previewUrl, thumbnailUrl } = props.item;
    // 添加背景图片
    const image = await EaseImage.fromURL(
        previewUrl || thumbnailUrl,
        {
            crossOrigin: "Anonymous",
        },
        {
            selectable: false,
        }
    );

    // 创建棋盘格
    const checkBoard = createCheckerPattern(20, 1, 1, "#0c0c0d", "#252526");

    opCanvas = new EaseCanvas(canvasRef.value, {
        width: wrapperRef.value.offsetWidth,
        height: wrapperRef.value.offsetHeight,
        container: wrapperRef.value,
        addToCenter: false,
        useWorkSpace: true,
        backgroundColor: checkBoard,
        useGuideLine: false,
        useSimplyHotkey: true,
        useHotkeys: false,
        selection: false,
        useWheel: false,
        maxZoom: 2,
        minZoom: 0.1,
        workspaceOptions: {
            fillType: BackgroundFillType.Color,
            fill: "transparent",
            zoomRatio: 0.8,
            isClip: false,
            width: image.width,
            height: image.height,
            lockMovementX: true,
            lockMovementY: true,
        },
    });

    const clipPath = new Rect({ width: image.width, height: image.height, top: 0, left: 0, fill: "transparent" }); // 创建一个裁剪路径
    const relRect = new Rect({ width: image.width, height: image.height, top: 0, left: 0, fill: "transparent" });
    const group = new Group([clipPath, relRect], {
        left: 0,
        top: 0,
        centeredScaling: false,
        lockMovementX: true,
        lockMovementY: true,
        lockRotation: true, // 禁止旋转
        lockScalingFlip: true, // 禁止翻转
        hoverCursor: "default",
        borderColor: "#7B57E5",
        cornerStrokeColor: "white",
        minScaleLimit: 0.05,
        scaleX: approximateProportion ? approximateProportion.scaleX : 1, // 如果不是内置比例折设置内置比例
        scaleY: approximateProportion ? approximateProportion.scaleY : 1,
    });

    // 设置Workspace的宽高
    const groupRealWidth = group.getScaledWidth();
    const groupRealHeight = group.getScaledHeight();
    opCanvas.workspace.resetSize({ width: groupRealWidth, height: groupRealHeight });

    // 应用处理函数
    group.controls.ml.actionHandler = createScalingHandler("ml");
    group.controls.mr.actionHandler = createScalingHandler("mr");
    group.controls.mt.actionHandler = createScalingHandler("mt");
    group.controls.mb.actionHandler = createScalingHandler("mb");

    group.controls.tl.actionHandler = createCornerHandler();
    group.controls.tr.actionHandler = createCornerHandler();
    group.controls.bl.actionHandler = createCornerHandler();
    group.controls.br.actionHandler = createCornerHandler();

    opCanvas.clipPath = clipPath;
    opCanvas.add(image, group); // 添加图片和group

    groupID = group.id; // 缓存Group ID
    imageID = image.id;
    setGroupCenterFromObject(image, group);

    opCanvas.setActiveObject(group); // 设置group为激活对象
    opCanvas.history.clear(); // 清除add时的历史记录

    initPreScaling(image.width, image.height);

    opCanvas.workspace.on("scaling", async () => {
        opCanvas.clipPath = await opCanvas.workspace.clone(getExtensionKey);
    });

    opCanvas.on("zoom:change", (value) => {
        if (!zoomRatio) zoomRatio = 0.8 / value;
        zoom.value = zoomRatio * value;
    });

    opCanvas.on("mouse:wheel", (opt) => {
        const delta = opt.e.deltaY;
        let canvasZoom = opCanvas.getZoom();
        canvasZoom *= 0.999 ** delta;
        if (canvasZoom > 1 / zoomRatio) {
            canvasZoom = 1 / zoomRatio;
        }
        if (canvasZoom < 0.1 / zoomRatio) {
            canvasZoom = 0.1 / zoomRatio;
        }
        let center;
        if (delta > 0) {
            center = opCanvas.getCenterPoint();
        } else {
            center = opt.pointer;
        }
        opCanvas.zoomToPoint(center, canvasZoom);
        opCanvas.fire("zoom:change", canvasZoom);
        opt.e.preventDefault();
        opt.e.stopPropagation();
        opCanvas.renderAll();
    });

    opCanvas.on("selection:cleared", () => {
        opCanvas.setActiveObject(group);
        opCanvas.renderAll();
    });
    loadComplete.value = true;
};

let approximateProportion, defaultRatio;
const initRatio = () => {
    const sourceImageWidth = props.item.realWidth || props.item.width;
    const sourceImageHeight = props.item.realHeight || props.item.height;
    defaultRatio = SHAPE_ALL.find((item) => item.width === sourceImageWidth && item.height === sourceImageHeight);
    if (!defaultRatio) {
        const isAspectRatio = isAspectRatioValid(sourceImageWidth, sourceImageHeight); // 判断图片是否SHAPE_ALL在比例内
        if (isAspectRatio) {
            const imgRatio = sourceImageWidth / sourceImageHeight;
            const preRatioArray = getAspectRatio();
            // 查找最接近的比例
            defaultRatio = preRatioArray.reduce((prev, curr) => {
                return Math.abs(curr.ratio - imgRatio) < Math.abs(prev.ratio - imgRatio) ? curr : prev;
            });
        } else {
            defaultRatio = SHAPE_ALL.find((item) => item.width === 1024 && item.height === 1024);
        }
        const relativeScalingCalRes = relativeScalingCal(sourceImageWidth, sourceImageHeight, defaultRatio.width, defaultRatio.height);
        approximateProportion = { label: defaultRatio.label, ...relativeScalingCalRes };
    }
    setRatio(defaultRatio);
    console.log("当前匹配到的比例：", defaultRatio);
};

onMounted(() => {
    // 设置默认比例
    initRatio();
    initCanvas();
});

onBeforeUnmount(() => {
    opCanvas && opCanvas.dispose();
});
const handleClose = () => {
    emits("cancel");
    emits("close");
};
</script>

<style lang="scss" scoped></style>
