import { useCurrentTheme } from "@/stores/system-config";
import { ref, computed, watchEffect } from "vue";

export const useTheme = () => {
    const currentTheme = useCurrentTheme();
    const systemDefTheme = useCookie("theme").value || "light";

    const systemThemeDark = ref(systemDefTheme === "dark");

    const themeOptions = computed(() => {
        return [
            { label: "MODE_LIGHT", key: "light", icon: "Sun" },
            { label: "MODE_DARK", key: "dark", icon: "Moon" },
            {
                label: systemThemeDark.value ? "MODE_SYSTEM_DARK" : "MODE_SYSTEM_LIGHT",
                key: "system",
                icon: "IconSystem",
            },
        ];
    });
    const updateThemeToCookie = (isDark) => {
        if (import.meta.server) {
            return;
        }
        const themeCookie = useCookie("theme");
        if (isDark) {
            document.body.classList.add("dark");
            themeCookie.value = "dark";
        } else {
            document.body.classList.remove("dark");
            themeCookie.value = "light";
        }
    };

    // 主题色跟随系统
    const autoTheme = () => {
        if (import.meta.client) {
            const systemDark = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
            systemThemeDark.value = systemDark;
            if (currentTheme.theme === "system") {
                updateThemeToCookie(systemDark);
            }
        }
    };

    const theme = computed(() => themeOptions.value.find((item) => item.key === currentTheme.theme));
    // 当前使用的主题色
    const themeLabel = computed(() => (theme.value ? theme.value.label : `MODE_SYSTEM_${currentTheme.theme === "dark" ? "DARK" : "LIGHT"}`));

    const isDark = computed(() => {
        if (currentTheme.theme === "system") {
            return systemThemeDark.value;
        }
        return currentTheme.theme === "dark";
    });

    onMounted(() => {
        autoTheme();
        updateThemeToCookie(isDark.value);
        window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", autoTheme);
    });

    // 选择指令
    const handleSelect = async (theme) => {
        window.trackEvent("Personal_Popup", {
            el: `personal_popup_theme=${theme}`,
        });
        currentTheme.setTheme(theme);
        await nextTick();
        updateThemeToCookie(isDark.value);
    };

    onUnmounted(() => {
        window.matchMedia("(prefers-color-scheme: dark)").removeEventListener("change", autoTheme);
    });

    return {
        isDark,
        themeOptions,
        theme,
        themeLabel,
        autoTheme,
        handleSelect,
    };
};
