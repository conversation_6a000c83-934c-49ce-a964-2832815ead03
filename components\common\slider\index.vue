<template>
    <div class="flex items-center no-drag">
        <Slide :min="min" :max="max" :step="step" v-bind="$attrs" :disabled="disabled" v-model="myValue" @input="handleModelValueInput" @change="handleModelValueChange" class="flex-1 mb-2" />
        <Stepper :min="min" :max="max" :step="step" :disabled="disabled" v-model="myValue" @change="handleModelValueChange" class="ml-4 w-[58px]" />
    </div>
</template>
<script setup>
import Slide from "./Slide.vue";
import Stepper from "./Stepper.vue";
const emit = defineEmits(["update:modelValue", "change", "input"]);
const route = useRoute();
const props = defineProps({
    modelValue: {
        type: Number,
        default: 1,
    },
    min: {
        type: Number,
        default: 0,
    },
    max: {
        type: Number,
        default: 1,
    },
    step: {
        type: Number,
        default: 0.1,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    itemKey: {
        type: String,
        default: "",
    },
});
// const myValue = computed({
//     get() {
//         return props.modelValue;
//     },
//     set(value) {
//         emit("update:modelValue", value);
//         emit("input", value);
//     },
// });
const myValue = computed(() => props.modelValue);
const handleModelValueInput = (value) => {
    emit("update:modelValue", value);
    emit("input", value);
};
const handleModelValueChange = (value) => {
    emit("update:modelValue", value);
    emit("change", value);
};
</script>
<style scoped lang="scss"></style>
