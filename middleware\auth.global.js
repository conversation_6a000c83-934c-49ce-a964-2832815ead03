export default defineNuxtRouteMiddleware(async (to, form) => {
    const localePath = useLocalePath();
    const oldPath = to.path;
    const authToken = useCookie("authToken");
    const isAuthenticated = Boolean(authToken.value);
    const isServer = import.meta.server;
    if (oldPath === "/account/login" || oldPath === localePath("/account/login")) {
        authToken.value = null;
        return;
    }
    // 如果用户未认证且尝试访问需要认证的页面
    const whiteRoutes = ["/account/login", "/image/share", "/community/share", "/image/detail"];
    let newPath = "";
    // 定义精确匹配的重定向规则
    // 注意：这里不包含动态参数的路由
    const exactRedirects = {
        "/image-generator/explore": "/community/explore",
        "/image-generator/create": "/image/create",
        "/image-generator/gallery": "/image/gallery",
        "/user/setting": "/account",
        "/image-generator/collections": "/image/gallery",
        "/image-generator/m-collections": "/m/collection",
        "/image-generator/m-image-detail": "/m/collection/detail",
        "/message-center/details": "/message-center",
        "/image-generator/share": "/image/share",
    };

    // 1. 尝试精确匹配
    if (exactRedirects[oldPath]) {
        newPath = exactRedirects[oldPath];
    } else {
        // 2. 处理动态路由重定向
        // 动态路由的匹配顺序很重要，更具体的规则应放在前面
        // 例如：/image-generator/m-collections-list/:id 应在 /image-generator/community/:id 之前匹配

        // /community-detail/:id  to  /community/detail/:id
        if (oldPath.match("/community-detail/")) {
            const id = oldPath.split("/").pop();
            newPath = `/community/detail/${id}`;
        }
        // /image-generator/m-collections-list/:id  to  /m/collection/:id
        else if (oldPath.match("/image-generator/m-collections-list/")) {
            const id = oldPath.split("/").pop(); // 获取最后一个段作为ID
            if (id) {
                newPath = `/m/collection/${id}`;
            }
        }
        // /activity/detail/:activityId  to  /activity/:activityId
        else if (oldPath.match("/activity/detail/")) {
            const activityId = oldPath.split("/").pop();
            if (activityId) {
                newPath = `/activity/${activityId}`;
            }
        }
        // /image-generator/community/:id  to  /community/share/:id
        else if (oldPath.match("/image-generator/community/")) {
            const id = oldPath.split("/").pop();
            if (id) {
                newPath = `/community/share/${id}`;
            }
        }
        // /user/social-home/:id  to  /community/profile/:id
        else if (oldPath.match("/user/social-home/")) {
            const id = oldPath.split("/").pop();
            if (id) {
                newPath = `/community/profile/${id}`;
            }
        }
    }
    // 检查当前路由是否在白名单中
    const isPublicRoute = whiteRoutes.some((pattern) => {
        // 正则表达式匹配动态路由
        return oldPath.match(pattern) || newPath.match(pattern);
    });
    //接口鉴权
    if (isServer && isAuthenticated && !isPublicRoute) {
        const config = useRuntimeConfig();
        const baseURL = config.public.apiBase;
        try {
            const { data } = await $fetch("/user/token-verify", {
                baseURL,
                method: "GET",
                headers: { Authorization: authToken.value },
            });
            if (!data) {
                authToken.value = null;
                return navigateTo(localePath("/account/login"), { redirectCode: 302 });
            }
        } catch (error) {
            return;
        }
    }

    if (oldPath === "/" && isAuthenticated) {
        return navigateTo(localePath("/community/explore"), {
            redirectCode: 302,
        });
    }
    if (oldPath === "/" && !isAuthenticated) {
        return navigateTo(localePath("/account/login"), {
            redirectCode: 302,
        });
    }

    // 获取用户认证状态
    const i18n = useNuxtApp().$i18n;
    const locale = i18n.locale;
    if (isAuthenticated && !isServer) {
        const userStore = useUserProfile();
        // 路由菜单红点
        const newRouters = userStore.userConfig.newRouters || [];
        const currentPath = oldPath.replace(`/${locale.value}`, "");
        if (newRouters.includes(currentPath)) {
            userStore.syncUpdateUserConfig({
                newRouters: newRouters.filter((item) => item !== currentPath),
            });
        }
    }

    if (!isAuthenticated && !isPublicRoute) {
        return navigateTo(localePath("/account/login"), { redirectCode: 302 });
    }

    // 如果找到了新的路径，则执行重定向并保留查询参数
    if (newPath) {
        // 构建包含查询参数的新 URL
        const finalRedirectPath = {
            path: newPath,
            query: to.query, // 完整保留所有查询参数
        };
        // 使用 301 永久重定向，对 SEO 有利
        return navigateTo(localePath(finalRedirectPath), { redirectCode: 301 });
    }
});
