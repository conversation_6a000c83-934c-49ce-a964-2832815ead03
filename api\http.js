import { useUserProfile } from "@/stores";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useForceReload, useNewFeaturePopup } from "@/hook/updateAccount";
import { throttle } from "@/utils/tools.js";
const fetchClient = async (options) => {
    // const { $runConfig } = useNuxtApp();
    const config = useRuntimeConfig();
    const baseUrl = config.public.apiBase;
    const authToken = useCookie("authToken", { maxAge: 60 * 60 * 24 * 365 }); // 设置cookie过期时间为365天

    // const baseUrl = $runConfig.getApiBase();
    const isProduction = config.public.env === "production";

    // 创建 AbortController 实例
    // const controller = new AbortController();
    // const signal = controller.signal;
    let customErr = {
        status: -1,
        message: "",
    };
    try {
        let Authorization = useUserProfile().user.token || authToken.value;
        const key = baseUrl + options.url + JSON.stringify(options.params || options.data || {}) + Authorization;

        const response = await $fetch.raw(baseUrl + options.url, {
            ...options,
            key,
            onRequest({ options }) {
                let h = useRequestHeaders(["x-forwarded-for", "x-real-ip", "user-agent"]);
                const headers = {
                    contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                    Platform: "Web",
                    ...h,
                };
                if (options.isForm) {
                    delete headers.contentType;
                    if (options.data && typeof options.data === "object" && !(options.data instanceof FormData)) {
                        const fd = new FormData();
                        Object.entries(options.data).forEach(([k, v]) => fd.append(k, v));
                        options.body = fd;
                    }
                } else if (options.method === "POST") {
                    headers["Content-Type"] = "application/json;charset=UTF-8";
                }
                headers.Authorization = Authorization;
                options.headers = headers;
            },
            onRequestError({ request, options, error }) {
                customErr = {
                    status: 500,
                    message: "A small system hiccup. Please refresh or try again later.",
                };
                errorToast(customErr.message);
                return customErr;
            },
            onResponseError({ response }) {
                if (response.status === 403 || response.status === 401) {
                    authToken.value = null;
                    customErr = {
                        status: response.status,
                        message: "Your session has expired. Please login again to continue.",
                    };
                    return;
                }
            },
        });
        const res = response._data;
        if (res.status === 0 && Authorization) {
            authToken.value = Authorization;
        }

        if (res.status !== 0 || customErr.status !== -1) {
            let errCode = res.status || customErr.status;
            let message = null;
            if (errCode == 403 || errCode == 401) {
                message = "Your session has expired. Please login again to continue.";
                authToken.value = null;
                noAccessPermission();
                return;
            } else if (errCode == 429) {
                message = "You are operating too quickly, please try again later!";
            } else {
                message = res.message || `A small system hiccup. Please refresh or try again later.`;
            }
            return {
                status: errCode,
                message,
            };
        }
        if (isProduction && import.meta.client) {
            const resHeaders = response.headers;
            const originVersion = resHeaders.get("web-version");
            const updateSystemId = resHeaders.get("suspension-version");
            originVersion && updateAppVersion(originVersion, updateSystemId);
        }
        return res;
    } catch (error) {
        if (error.status === 403 || error.statusCode === 401) {
            authToken.value = null;
            noAccessPermission();
            return;
        }
        return {
            status: error.status || error.statusCode,
            message: "A small system hiccup. Please refresh or try again later.",
        };
    }
};

const fetch = async (options) => {
    // const { $runConfig } = useNuxtApp();
    const authToken = useCookie("authToken", { maxAge: 60 * 60 * 24 * 365 }); // 设置cookie过期时间为365天

    // const baseUrl = $runConfig.getApiBase();

    const config = useRuntimeConfig();
    const baseUrl = config.public.apiBase;

    // 创建 AbortController 实例
    // const controller = new AbortController();
    // const signal = controller.signal;
    let customErr = {
        code: -1,
        message: "",
    };
    const key = baseUrl + options.url + JSON.stringify(options.params || options.data || {}) + authToken.value;
    const { data, error, status } = await useFetch(baseUrl + options.url, {
        key,
        ...options,
        // signal,
        onRequest({ options }) {
            let Authorization = authToken.value;
            let h = useRequestHeaders(["x-forwarded-for", "x-real-ip", "user-agent"]);
            const headers = {
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                Platform: "Web",
                ...h,
            };
            if (options.isForm) {
                delete headers.contentType;
                if (options.data && typeof options.data === "object" && !(options.data instanceof FormData)) {
                    const fd = new FormData();
                    Object.entries(options.data).forEach(([k, v]) => fd.append(k, v));
                    options.body = fd;
                }
            } else if (options.method === "POST") {
                headers["Content-Type"] = "application/json;charset=UTF-8";
            }
            headers.Authorization = Authorization;
            options.headers = headers;
        },
        onRequestError({ request, options, error }) {
            customErr = {
                status: 500,
                message: `A small system hiccup. Please refresh or try again later.`,
            };
        },
    });
    if (!error.value) {
        return toRaw(data.value);
    }
    let errCode = error.value.statusCode || 500;
    let message = null;
    if (errCode == 403 || errCode == 401) {
        message = "Your session has expired. Please login again to continue.";
        authToken.value = null;
        noAccessPermission();
        return {
            status: 0,
            message,
        };
    } else if (error.value.statusCode == 429) {
        message = "You are operating too quickly, please try again later!";
    } else {
        message = `A small system hiccup. Please refresh or try again later.`;
    }
    return {
        status: 0,
        message,
    };
};
const reqMethodClient = new (class Http {
    get(opt) {
        return fetchClient({
            method: "get",
            ...opt,
            params: opt.params || opt.data || {},
        });
    }
    post(opt) {
        return fetchClient({ method: "post", ...opt, body: opt.data || {} });
    }
    put(opt) {
        return fetchClient({ method: "put", ...opt, body: opt.data || {} });
    }
    delete(opt) {
        return fetchClient({ method: "delete", ...opt, body: opt.data || {} });
    }
})();
const reqMethod = new (class Http {
    get(opt) {
        return fetch({
            method: "get",
            ...opt,
            params: opt.params || opt.data || {},
        });
    }
    post(opt) {
        return fetch({ method: "post", ...opt, body: opt.data || {} });
    }
    put(opt) {
        return fetch({ method: "put", ...opt, body: opt.data || {} });
    }
    delete(opt) {
        return fetch({ method: "delete", ...opt, body: opt.data || {} });
    }
})();
//这些接口不能带token
const whiteList = [
    "/user/login",
    "/user/apple/login",
    "/user/register",
    "/user/register-send-code",
    "/user/reset-password",
    "/user/reset-password-send-code",
    "/img/img-detail",
    "/user/login-oauth",
    "/common/getSysTime",
    "/common/hello",
    "/comm-img/img-detail",
];
const reqHttp = ({ method, ...conf }) => {
    method = method || "get";
    if (import.meta.client) {
        const hasAllow = whiteList.findIndex((item) => conf.url.startsWith(item)) > -1;
        const token = useCookie("authToken");
        if (!hasAllow && !token.value) {
            noAccessPermission();
            return Promise.resolve({ status: 0 });
        }
        return reqMethodClient[method](conf);
    }
    return reqMethod[method](conf);
};

const noAccessPermission = throttle(async () => {
    if (import.meta.client) {
        useSubscribeStore().resetVipInfo();
        useUserProfile().clearUserInfo();
    }
    await navigateTo("/account/login", { redirectCode: 302 });
}, 1000);

const errorToast = throttle((message) => {
    openToast.error(message, 5e3);
}, 1000);

export default reqHttp;

const updateAppVersion = throttle((version, noticeId) => {
    const { $runConfig } = useNuxtApp();
    const localVersion = $runConfig.getVersion();

    if (version !== localVersion && !window.hasUpdate && version !== "unknown") {
        window.hasUpdate = true;
        useForceReload();
        return;
    }
    const localeNoticeId = Number(localStorage.getItem("noticeId") || 0);
    if (noticeId && noticeId != 0 && localeNoticeId !== noticeId) {
        useNewFeaturePopup(noticeId);
        return;
    }
}, 1000);
