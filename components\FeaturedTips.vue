<template>
    <div @click.stop="showFeaturedTips" class="hover-action-btn">
        <n-icon size="22">
            <IconsChoice />
        </n-icon>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { Alert } from "@/icons/index.js";
import { NIcon } from "naive-ui";

//精选提示
const showFeaturedTips = () => {
    const { showMessage } = useModal();
    showMessage({
        showCancel: false,
        style: { width: "420px" },
        confirmBtn: t("COMMON_BTN_OK"),
        content: h("div", null, [h("p", { class: "tracking-wider" }, t("COMMUNITY_IMG_FEATURE"))]),
        icon: h(NIcon, { size: 32, class: "text-primary" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_FEATURED"),
    });
};
</script>

<style lang="scss" scoped>
.hover-action-btn {
    @apply bg-white/30 w-9 h-9 rounded-full backdrop-blur flex items-center justify-center cursor-pointer hover:text-dark-active-text  text-dark-active-text absolute top-2 left-2 z-50;
}
</style>
