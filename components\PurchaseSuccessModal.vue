<script setup>
import { I18nT } from "vue-i18n";
import { useSubscribeStore } from "@/stores/subscribe.js";
const subscribeStore = useSubscribeStore();
subscribeStore.initUserPromotionStatus(); // 订阅成功，更新最新套餐信息
const emits = defineEmits(["close"]);
const handleOk = () => {
    emits("close");
};
const restoreUrl = () => {
    const url = new URL(window.location); // 创建一个 URL 对象
    url.searchParams.delete("type"); // 删除 type 参数
    url.searchParams.delete("paymentMethod"); // 删除 type 参数
    url.searchParams.delete("action"); // 删除 type 参数
    // paypal 返回的参数
    url.searchParams.delete("subscription_id"); // 删除 type 参数
    url.searchParams.delete("ba_token"); // 删除 type 参数
    url.searchParams.delete("token"); // 删除 type 参数
    window.history.replaceState({}, "", url.href); // 替换当前的 URL 但不刷新页面
};
onMounted(() => {
    restoreUrl();
});
</script>

<template>
    <div class="space-y-6">
        <div class="flex justify-between items-center gap-2 pb-4">
            <span class="text-2xl text-text-1 font-semibold">{{ $t("SUBSCRIBE_MODAL_PURCHASE") }}</span>
            <div class="w-8 h-8 rounded-full text-text-4 hover:text-text-2 flex items-center justify-center hover:bg-fill-wd-1 cursor-pointer" @click="handleOk">
                <IconsClose class="w-5 h-5" />
            </div>
        </div>
        <div class="text-sm font-medium leading-5.5 text-text-2">
            <i18n-t keypath="SUBSCRIBE_MODAL_PURCHASE_TEXT">
                <template v-slot:email>
                    <span class="text-primary-6"><EMAIL></span>
                </template>
            </i18n-t>
        </div>
        <div class="p-2 pt-6">
            <Button class="min-w-28 ml-auto" type="primary" @click="handleOk">{{ $t("COMMON_BTN_OK") }}</Button>
        </div>
    </div>
</template>
