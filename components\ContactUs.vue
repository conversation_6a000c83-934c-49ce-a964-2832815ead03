<template>
    <div v-if="showTrigger" class="flex gap-3 items-center px-2 cursor-pointer" v-bind:="$attrs" @click="changeShowPopover(true)">
        <slot>
            <n-icon size="24">
                <svg fill="currentColor" height="1em" viewBox="0 -960 960 960" width="1em" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M480-360q17 0 28.5-11.5T520-400q0-17-11.5-28.5T480-440q-17 0-28.5 11.5T440-400q0 17 11.5 28.5T480-360Zm-40-160h80v-240h-80v240ZM80-80v-720q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240L80-80Zm126-240h594v-480H160v525l46-45Zm-46 0v-480 480Z"
                    />
                </svg>
            </n-icon>
            <div>{{ t("MENU_FEEDBACK") }}</div>
        </slot>
    </div>

    <n-modal v-model:show="showPopover" :maskClosable="false">
        <n-card style="width: 600px" :bordered="false" size="huge" role="dialog" aria-modal="true">
            <div class="relative">
                <h2 class="text-xl font-bold text-center">{{ t("FORM_CONTACT_US_TITLE") }}</h2>
                <n-icon size="24" class="cursor-pointer hover:text-[#8961FF] absolute -top-1 -right-1" @click="changeShowPopover(false)">
                    <IconsClose />
                </n-icon>
            </div>
            <n-scrollbar style="max-height: 65vh" class="pr-4">
                <n-form class="py-8" ref="formRef" :model="formValue" :rules="rules">
                    <n-form-item require-mark-placement="left" :label="t('FORM_CONTACT_US_ISSUE_LABEL')" path="reason">
                        <n-select
                            class="rounded-md border border-solid border-black/15 dark:bg-white/5 w-full"
                            v-model:value="formValue.reason"
                            :options="issueList"
                            :render-label="renderLabel"
                            :placeholder="t('FORM_CONTACT_US_ISSUE_PLACEHOLDER')"
                        >
                        </n-select>
                    </n-form-item>
                    <n-form-item require-mark-placement="left" :label="t('FORM_CONTACT_US_FULL_NAME_LABEL')" path="fullName">
                        <div class="rounded-md border border-solid border-black/15 dark:bg-white/5 w-full">
                            <n-input type="text" v-model:value="formValue.fullName" :placeholder="t('FORM_CONTACT_US_FULL_NAME_PLACEHOLDER')" :maxlength="100" />
                        </div>
                    </n-form-item>
                    <n-form-item require-mark-placement="left" :label="t('FORM_CONTACT_US_EMAIL_LABEL')" path="email">
                        <div class="rounded-md border border-solid border-black/15 dark:bg-white/5 w-full">
                            <n-input type="email" v-model:value="formValue.email" placeholder="<EMAIL>" :maxlength="100" />
                        </div>
                    </n-form-item>
                    <n-form-item require-mark-placement="left" :label="t('FORM_CONTACT_US_MESSAGE_LABEL')" path="message">
                        <div class="rounded-md border border-solid border-black/15 dark:bg-white/5 w-full">
                            <n-input
                                :maxlength="2500"
                                type="textarea"
                                :autosize="{
                                    minRows: 3,
                                    maxRows: 3,
                                }"
                                v-model:value="formValue.message"
                                :placeholder="t('FORM_CONTACT_US_MESSAGE_PLACEHOLDER')"
                            />
                        </div>
                    </n-form-item>
                    <n-form-item require-mark-placement="left" :label="t('FORM_CONTACT_US_UPLOAD_TITLE')" path="feedbackFile">
                        <n-upload
                            accept=".jpeg,.jpg,.png,.webp"
                            list-type="image-card"
                            :disabled="uploading"
                            :max="5"
                            :file-list="feedbackFiles"
                            :on-remove="removeFeedbackFile"
                            :on-change="chooseFile"
                            :show-preview-button="false"
                            file-list-class="!grid-cols-5 custom-list"
                            :custom-request="customRequest"
                        >
                            <IconsUpload class="text-xl" />
                        </n-upload>
                    </n-form-item>
                </n-form>
            </n-scrollbar>

            <Button type="primary" rounded="lg" class="mt-4" block :disabled="hasLoading" @click="handleValidateClick"
                >{{ hasLoading ? t("FORM_CONTACT_US_SENDING") : t("FORM_CONTACT_US_SEND_BTN") }}
            </Button>
        </n-card>
    </n-modal>
</template>

<script setup>
defineProps({
    style: {
        type: Object,
        default: () => ({}),
    },
    showTrigger: {
        type: Boolean,
        default: true,
    },
});
const { t } = useI18n({ useScope: "global" });
import { contactUs } from "@/api";
import { useUserProfile } from "@/stores/index";
import { uploadToCos, validateEmail } from "@/utils/tools";
const userProfile = useUserProfile();
const showPopover = ref(false);
const hasLoading = ref(false);
const uploading = ref(false); //上传loading
const formRef = ref(null);
const formValue = ref({
    email: "",
});
const issueList = [
    { label: "FORM_CONTACT_US_ISSUE_TECHNICAL", value: "1" },
    { label: "FORM_CONTACT_US_ISSUE_FEATURES", value: "2" },
    { label: "FORM_CONTACT_US_ISSUE_ACCOUNT", value: "3" },
    { label: "FORM_CONTACT_US_ISSUE_DEL_ACCOUNT", value: "4" },
    { label: "FORM_CONTACT_US_ISSUE_BUSINESS", value: "5" },
    { label: "FORM_CONTACT_US_ISSUE_LEGAL_PRIVACY", value: "6" },
    { label: "PAYMENT_ISSUES_TITLE", value: "7" },
];
const renderLabel = (option) => {
    return t(option.label);
};

//上传列表
const feedbackFiles = ref([]);
const rules = {
    reason: {
        required: true,
        trigger: "blur",
        renderMessage: () => t("FORM_CONTACT_US_ISSUE_EMPTY"),
    },
    fullName: {
        required: true,
        trigger: "blur",
        renderMessage: () => t("FORM_CONTACT_US_FULL_NAME_EMPTY"),
    },
    email: {
        required: true,
        trigger: "blur",
        validator: (rule, value) => {
            return new Promise((resolve, reject) => {
                if (value == "" || value == undefined) {
                    reject(new Error(t("FORM_CONTACT_US_EMAIL_EMPTY")));
                } else if (!validateEmail(value)) {
                    reject(new Error(t("FORM_CONTACT_US_EMAIL_ERROR")));
                } else {
                    resolve();
                }
            });
        },
    },
    message: {
        required: true,
        trigger: "blur",
        renderMessage: () => t("FORM_CONTACT_US_MESSAGE_EMPTY"),
    },
};

const changeShowPopover = (status) => {
    showPopover.value = status;
    if (status) {
        hasLoading.value = false;
        formValue.value = {
            email: "",
        };
        formRef.value?.restoreValidation();
        feedbackFiles.value = [];
        return;
    }
};
defineExpose({ changeShowPopover });

//选择文件
const chooseFile = ({ fileList, file }) => {
    return new Promise((resolve, reject) => {
        const maxSize = 5 * 1024 * 1024;
        const supperTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
        const res = file.file;
        if (res.size > maxSize || !supperTypes.includes(res.type)) {
            openToast.error(t("FORM_CONTACT_US_UPLOAD_TYPE_ERR"), 5e3);
            return reject(t("FORM_CONTACT_US_UPLOAD_TYPE_ERR"));
        }
        feedbackFiles.value = fileList;
        return resolve(file);
    });
};
//自定义上传
const customRequest = async ({ file, onFinish, onError, onProgress }) => {
    try {
        const res = await uploadToCos({ file: file.file, originalFileName: file.file.name, updateProgress: onProgress, type: "suggest" });
        if (res.statusCode === 200) {
            onFinish(Promise.resolve(file));
            const index = feedbackFiles.value.findIndex((item) => item.id === file.id);
            feedbackFiles.value[index].url = res.fullPath;
        } else {
            onError(Promise.reject());
        }
    } catch (error) {
        onError(Promise.reject());
    }
};
//删除
const removeFeedbackFile = (file) => {
    feedbackFiles.value = feedbackFiles.value.filter((item) => item.id !== file.id);
};

//提交表单
const handleValidateClick = async () => {
    if (hasLoading.value) {
        return;
    }
    try {
        await formRef.value?.validate();
        hasLoading.value = true;
        const loginName = userProfile.user.loginName;
        const urlList = feedbackFiles.value.map((item) => item.url).filter((item) => !!item);
        const { status, message } = await contactUs({ ...formValue.value, loginName, urlList });
        hasLoading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        changeShowPopover(false);
        openToast.success(t("FORM_CONTACT_US_SEND_SUCCESS"));
    } catch (error) {}
};
</script>

<style lang="scss" scoped>
::v-deep(.custom-list .n-upload-file.n-upload-file--image-card-type),
::v-deep(.custom-list .n-upload-trigger.n-upload-trigger--image-card) {
    width: 94px;
    height: 94px;
}
::v-deep(.custom-list .n-upload-file.n-upload-file--image-card-type .n-image > img) {
    object-fit: contain !important;
}
</style>
