<template>
    <div class="flex h-full p-0.5 rounded-full gap-0.5 items-center bg-fill-tab-4 text-sm font-medium">
        <template v-for="item in options" :key="item.value">
            <div
                class="flex items-center justify-center py-2 px-4 rounded-full transition-all duration-[250]"
                :class="[item.disabled || disabled ? 'text-text-6 cursor-default' : 'text-text-4 cursor-pointer', value === item.value ? '!text-text-1 bg-fill-tab-3' : '']"
                @click="handleChange(item)"
            >
                {{ item.label }}
            </div>
        </template>
    </div>
</template>
<script setup>
const emit = defineEmits(["change", "update:value"]);
const props = defineProps({
    value: {
        type: String,
        default: "",
    },
    options: {
        type: Array,
        default: () => [],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});

const handleChange = (item) => {
    const { value, disabled } = item;
    if (props.disabled) return;
    emit("change", value);
    if (disabled) return;
    emit("update:value", value);
};
</script>
