<script setup>
import { useSubscribeErrorModal } from "@/hook/subscribe.js";
import { reqCancelAutoSubscribe, reqCancelAutoSubscribeByPaypal } from "@/api/index.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { getPlatformVipToWeight } from "@/utils/tools.js";
import { PLATFORM_TYPE, SUBSCRIBE_TYPE } from "@/utils/constant.js";

const emits = defineEmits(["close"]);
const { showErrorModal } = useSubscribeErrorModal();
const subscribeStore = useSubscribeStore();
const unsubscribeLoading = ref(false);
const handleUnsubscribe = () => {
    unsubscribeLoading.value = true;
    const isPaypal = currSub.value.platform === "paypal";
    let reqCancelFn = reqCancelAutoSubscribe;
    if (isPaypal) {
        reqCancelFn = reqCancelAutoSubscribeByPaypal;
    }
    reqCancelFn()
        .then(async (res) => {
            if (res.status === 0) {
                await subscribeStore.getSubList();
                if (isPaypal) {
                    await subscribeStore.getRefreshOn();
                }
                emits("close");
            } else {
                showErrorModal("SUBSCRIBE_EX_OPERATION");
            }
        })
        .catch((err) => {
            showErrorModal("SUBSCRIBE_EX_OPERATION");
        })
        .finally(() => {
            unsubscribeLoading.value = false;
        });
};

const handleClose = () => {
    if (unsubscribeLoading.value) return;
    emits("close");
};

const PLAN_MAPPING = {
    [SUBSCRIBE_TYPE.STANDARD]: "SUBSCRIBE_PLAN_STANDARD",
    [SUBSCRIBE_TYPE.PRO]: "SUBSCRIBE_PLAN_PRO",
};
// 显示等级最高的付费订阅信息
const currSub = computed(() => {
    const webSubList = getPlatformVipToWeight(subscribeStore.subList, PLATFORM_TYPE.WEB);
    if (webSubList.length === 0) {
        return { plan: "", platform: "" };
    }
    const firstSub = webSubList[0];
    return {
        plan: PLAN_MAPPING[firstSub.planLevel] || "",
        platform: firstSub.vipPlatform,
    };
});
// 当前生效的的会员是否是免费试用
const hasFreeTrial = computed(() => subscribeStore.vipInfo.hasInTrial);
</script>

<template>
    <div v-if="!hasFreeTrial" class="space-y-6 p-6 pt-4 md:w-[640px]">
        <div class="flex justify-between items-center gap-2 pb-4">
            <span class="text-base text-text-1 font-semibold ml-2">{{ $t("SUBSCRIBE_MODAL_CANCEL_TITLE") }}</span>
            <div class="translate-x-2 w-8 h-8 rounded-full text-text-4 hover:text-text-2 flex items-center justify-center hover:bg-fill-wd-1 cursor-pointer" @click="handleClose">
                <IconsClose class="w-5 h-5" />
            </div>
        </div>
        <div class="text-sm font-medium leading-5.5 text-text-2 space-y-6">
            <!-- 只有当前是赠送会员时显示 -->
            <div v-if="subscribeStore.isGiftVip">
                <p>{{ $t("SUBSCRIBE_MODAL_CANCEL_PLATFORM") }} {{ $t(currSub.plan) }}</p>
                <p>{{ $t("SUBSCRIBE_MODAL_CANCEL_PLATFORM") }} {{ $t(currSub.platform) }}</p>
            </div>
            <p>{{ $t("SUBSCRIBE_MODAL_CANCEL_LINE1") }}</p>
            <p>{{ $t("SUBSCRIBE_MODAL_CANCEL_LINE2") }}</p>
            <p>{{ $t("SUBSCRIBE_MODAL_CANCEL_LINE3") }}</p>
            <p>{{ $t("SUBSCRIBE_MODAL_CANCEL_LINE4") }}</p>
        </div>
        <div class="flex justify-end flex-wrap gap-3 pt-6">
            <Button :loading="unsubscribeLoading" type="secondary" class="max-md:w-full md:min-w-28" @click="handleUnsubscribe">
                {{ $t("SUBSCRIBE_MODAL_CANCEL_CANCEL") }}
            </Button>
            <Button :disabled="unsubscribeLoading" type="primary" class="max-md:w-full md:min-w-28" @click="handleClose">
                {{ $t("SUBSCRIBE_MODAL_CANCEL_CONFIRM") }}
            </Button>
        </div>
    </div>

    <div v-if="hasFreeTrial" class="relative md:w-[500px] text-center bg-bg-3">
        <img src="@/assets/images/subscribe/cancel_trial.webp" class="w-full object-cover pointer-events-none hidden dark:block" />
        <img src="@/assets/images/subscribe/cancel_trial_light.webp" class="w-full object-cover pointer-events-none dark:hidden" />
        <div class="absolute z-10 top-4 right-4 w-8 h-8 rounded-full text-text-4 hover:text-text-2 flex items-center justify-center hover:bg-fill-wd-1 cursor-pointer" @click="handleClose">
            <IconsClose class="w-5 h-5" />
        </div>

        <div class="p-6 pt-0">
            <div class="text-text-1 text-xl font-semibold">{{ $t("CANCEL_TRIAL_TITLE") }}</div>
            <div class="my-4 text-text-2 font-medium">{{ $t("CANCEL_TRIAL_CONTENT") }}</div>
            <div class="flex justify-center flex-wrap gap-3 pt-6">
                <Button class="max-md:w-full md:min-w-36" type="secondary" :loading="unsubscribeLoading" @click="handleUnsubscribe">{{ $t("CANCEL_TRIAL_CONFIRM") }}</Button>
                <Button class="max-md:w-full md:min-w-36" :disabled="unsubscribeLoading" @click="handleClose">{{ $t("CANCEL_TRIAL_CLOSE") }}</Button>
            </div>
        </div>
    </div>
</template>
