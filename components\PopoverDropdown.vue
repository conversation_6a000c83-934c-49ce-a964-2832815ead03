<template>
    <n-popover trigger="hover" :placement="placement" :show-arrow="false" :on-update:show="handleStateChange" raw ref="popoverRef">
        <template #trigger>
            <slot>
                <div @click.stop="null" class="trigger-bar">
                    <n-icon size="24">
                        <IconsMore />
                    </n-icon>
                </div>
            </slot>
        </template>
        <div class="flex flex-col gap-1 popover-box">
            <div v-for="item in options" :key="item.key" @click="checkItem(item)" class="popover-item-box" :class="{ 'show-divider': item.divider, 'hover:bg-transparent opacity-50': item.disabled }">
                <slot :name="item.key" :item="item">
                    <component v-if="item.icon" :is="item.icon" class="!text-xl !size-5" />
                    <span class="text-sm" v-if="noTrans">{{ item.label }}</span>
                    <span class="text-sm" v-else>{{ $t(item.label) }}</span>
                    <component class="!text-xl text-info-6 !size-5" v-if="item.suffix" :is="item.suffix" />
                </slot>
            </div>
        </div>
    </n-popover>
</template>

<script setup>
const emits = defineEmits(["state-change", "select"]);
const props = defineProps({
    options: {
        type: Array,
        default: () => [],
    },
    placement: {
        type: String,
        default: "top",
    },
    noTrans: {
        type: Boolean,
        default: false,
    },
});
import { ref } from "vue";
const popoverRef = ref(null);
const checkItem = ({ key, disabled }) => {
    if (disabled) {
        return;
    }
    popoverRef.value.setShow(false);
    emits("select", key);
};

const handleStateChange = (state) => {
    emits("state-change", state);
};
</script>

<style lang="scss" scoped>
.trigger-bar {
    @apply bg-dark-bg-2/30 w-9 h-9 rounded-full backdrop-blur flex items-center justify-center cursor-pointer hover:text-dark-active-text;
}
.show-divider:after {
    content: "";
    height: 1px;
    @apply absolute block top-0 left-0 right-0 dark:bg-white/20 bg-black/20;
}
.popover-box {
    @apply p-2 bg-bg-6 rounded-lg min-w-[154px] text-sm border border-solid border-border-t-1 flex flex-col gap-1;
    box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.12), 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
    .popover-item-box {
        @apply relative rounded-[4px] py-2.5 px-3 flex items-center gap-2  cursor-pointer hover:bg-fill-drop-2 text-text-drop-1 text-sm;
    }
    .disabled-item {
        @apply text-text-drop-3 hover:!bg-transparent;
    }
}
</style>
