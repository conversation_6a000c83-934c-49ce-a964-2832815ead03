<template>
    <n-modal :show="show" :on-update:show="handleStateChange">
        <div class="w-[880px] tooltip-container !p-0">
            <div class="flex justify-between items-center p-6 pr-4">
                <span class="text-text-1 font-semibold tracking-wide">{{ t("SHORT_MODEL_FEAT_TITLE") }}</span>
                <IconsClose class="w-5 h-5 cursor-pointer text-text-4 hover:text-text-1" @click="handleStateChange(false)" />
            </div>
            <div class="w-full px-6">
                <div class="rounded-lg border border-solid border-border-1 border-b-0">
                    <table class="w-full rounded-lg overflow-hidden">
                        <tr v-for="(rowItem, rowIndex) in modelFeatureTableList" :key="rowIndex" class="border-b">
                            <td
                                v-for="(colItem, colIndex) in rowItem"
                                :key="colIndex"
                                :class="[modelFeatureTableList[0][colIndex].align === 'center' ? 'text-center' : '']"
                                class="py-2.5 px-4 border-b border-r border-solid border-border-1 table-td"
                            >
                                <div v-if="colItem.type === 'model'" class="w-full min-h-6 flex items-center gap-2">
                                    <img :src="renderModelIcon(colItem.modelId)" alt="" class="w-6 h-6 object-cover rounded-lg" />
                                    <span class="text-sm font-medium text-text-3 leading-5.5">{{ colItem.name }}</span>
                                </div>
                                <span v-if="colItem.type === 'text'" :class="{ '!text-text-1': rowIndex === 0 }" class="inline-block min-h-6 text-sm font-medium leading-5.5 text-text-3">{{
                                    colItem.name
                                }}</span>
                                <IconsSuccess v-if="colItem.type === 'support'" class="w-5 h-5 text-success-6 mx-auto" />
                                <IconsClose v-if="colItem.type === 'empty'" class="w-5 h-5 text-danger-6 mx-auto" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="w-full p-6">
                <Button class="w-[120px] ml-auto" type="primary" @click="handleStateChange(false)">
                    {{ t("COMMON_BTN_OK") }}
                </Button>
            </div>
        </div>
    </n-modal>
</template>
<script setup>
import { t } from "@/utils/i18n-util";
import { renderModelIcon } from "@/utils/tools.js";
import { useSupportModelList } from "@/stores/create";
import { animeModelId, fluxDevModelId, MjModelId, fluxModelId, lineArtModelId, picLumenArtV1Id, ponyV6ModelId, realisticModelId } from "@/utils/constant.js";
const emit = defineEmits(["update:show"]);
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
});
/**
 * 打开关闭
 * @param val
 */
const handleStateChange = (val) => {
    emit("update:show", val);
};
const modelStore = useSupportModelList();
const modelFeatureTable = [
    [
        { type: "text", name: t("CONFIG_BASE_MODEL"), align: "left" }, //模型名称
        { type: "text", name: t("SHORT_MODEL_FEAT_TYPE"), align: "center" }, //模型类型 通用 动画等
        { type: "text", name: t("SHORT_MODEL_FEAT_SPEED"), align: "center" },
        { type: "text", name: t("SHORT_MODEL_FEAT_CFG"), align: "center" }, //cfg控制
        { type: "text", name: t("SHORT_MODEL_FEAT_STEPS"), align: "center" }, // steps控制
        { type: "text", name: t("CONFIG_BASE_NEGATIVE_PROMPT"), align: "center" }, //负向提示词控制
    ],
    [
        { type: "model", modelId: picLumenArtV1Id, name: "Art V1" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_FAST") },
        { type: "empty" },
        { type: "empty" },
        { type: "empty" },
    ],
    [
        { type: "model", modelId: realisticModelId, name: "Realistic V2" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: animeModelId, name: "Anime V2" },
        { type: "text", name: t("SHORT_MODEL_FEAT_ANIME") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: lineArtModelId, name: "Lineart V1" },
        { type: "text", name: t("SHORT_MODEL_FEAT_ANIME") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: ponyV6ModelId, name: "Pony Diffusion V6" },
        { type: "text", name: t("SHORT_MODEL_FEAT_ANIME") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: fluxModelId, name: "FLUX.1-schnell" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_FAST") },
        { type: "empty" },
        { type: "empty" },
        { type: "empty" },
    ],
    [
        { type: "model", modelId: fluxDevModelId, name: "FLUX.1-dev" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_SLOW") },
        { type: "support" },
        { type: "support" },
        { type: "empty" },
    ],
    [
        { type: "model", modelId: NamiyaModelId, name: "Namiya" },
        { type: "text", name: t("SHORT_MODEL_FEAT_ANIME") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: NamiyaModelId, name: "Namiya" },
        { type: "text", name: t("SHORT_MODEL_FEAT_ANIME") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: MjModelId, name: "Primo" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_FAST") },
        { type: "empty" },
        { type: "empty" },
        { type: "support" },
    ],
    [
        { type: "model", modelId: fluxKontextModelId, name: "FLUX.1 Kontext" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_FAST") },
        { type: "empty" },
        { type: "empty" },
        { type: "empty" },
    ],
    [
        { type: "model", modelId: fluxKreaModelId, name: "FLUX.1 Krea" },
        { type: "text", name: t("SHORT_MODEL_FEAT_UNIVERSAL") },
        { type: "text", name: t("SHORT_MODEL_FEAT_MEDIUM") },
        { type: "support" },
        { type: "support" },
        { type: "empty" },
    ],
];

const modelFeatureTableList = computed(() => {
    const sortedModelIds = modelStore.modelList.map((item) => item.value) || [];
    const reorderedFeatures = sortedModelIds.map((id) => modelFeatureTable.find((row) => row[0]?.modelId === id)).filter(Boolean); // 过滤掉未找到的项
    return [modelFeatureTable[0], ...reorderedFeatures];
});
</script>

<style scoped lang="scss">
.table-td {
    &:nth-child(6n) {
        border-right: none;
    }
}
</style>
