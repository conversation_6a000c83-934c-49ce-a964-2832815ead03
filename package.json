{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"dev": "nuxt dev --dotenv .env.development", "build:dev": "nuxt build --dotenv .env.development", "build:test": "nuxt build --dotenv .env.test", "build:s4": "nuxt build --dotenv .env.s4", "build:s18": "nuxt build --dotenv .env.s18", "build": "nuxt build --dotenv .env.production", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@easeus/editor-core": "^3.3.4", "@lhlyu/vue-virtual-waterfall": "^1.0.6", "@nuxtjs/critters": "^0.9.0", "@nuxtjs/i18n": "^9.5.4", "@pinia/nuxt": "^0.11.0", "@vueuse/core": "^13.2.0", "cos-js-sdk-v5": "^1.10.1", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "js-yaml": "^4.1.0", "jszip": "^3.10.1", "naive-ui": "^2.41.0", "nuxt": "^3.17.3", "nuxtjs-naive-ui": "^1.0.2", "pinia-plugin-persistedstate": "^4.3.0", "swiper": "^11.2.8", "vue": "^3.5.14", "vue-router": "^4.5.1", "vue-virtual-scroller": "^2.0.0-beta.8"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.14.0", "@oxc-parser/binding-win32-x64-msvc": "^0.82.2", "@rollup/plugin-yaml": "^4.1.2", "@rollup/rollup-win32-x64-msvc": "^4.46.2", "sass": "^1.89.0", "sass-loader": "^16.0.5", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite-tsconfig-paths": "^5.1.4"}}