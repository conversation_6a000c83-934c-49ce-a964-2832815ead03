<template>
    <div class="flex flex-col gap-2">
        <div v-for="item in formateOptions" :key="item[field.value]" class="flex items-center gap-1 text-text-2 cursor-pointer item-radio" @click="chooseItem(item)">
            <div class="radio-icon w-8 h-8 rounded-full flex items-center justify-center border-[6px] border-none border-fill-wd-1 shrink-0">
                <div class="border-2 border-solid border-border-3 rounded-[4px] w-5 h-5 flex items-center justify-center" :class="{ '!border-primary-6 bg-primary-6': hasOwenItem(item[field.value]) }">
                    <n-icon size="16" class="text-white" v-if="hasOwenItem(item[field.value])">
                        <IconsSuccess />
                    </n-icon>
                </div>
            </div>
            <span>{{ item[field.label] }}</span>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    value: {
        type: [Array, null, undefined],
        default: () => [],
    },
    options: {
        type: Array,
        required: true,
        default: () => [],
    },
    field: {
        type: Object,
        default: () => ({
            label: "label",
            value: "value",
        }),
    },
});
const formateOptions = computed(() => {
    const item = props.options[0];
    if (item && typeof item === "object" && !Array.isArray(item) && item !== null) {
        return props.options;
    }
    if (item && typeof item === "string") {
        return props.options.map((i) => ({ [props.field.label]: i, [props.field.value]: i }));
    }
    return [];
});
const hasOwenItem = computed(() => {
    let values = [];
    if (props.value) {
        values = Array.isArray(props.value) ? [...props.value] : [props.value];
    }
    return (val) => {
        return values.includes(val);
    };
});
const emits = defineEmits(["update:value"]);
const chooseItem = (item) => {
    let values = [];
    if (props.value) {
        values = Array.isArray(props.value) ? [...props.value] : [props.value];
    }
    const hasOwen = hasOwenItem.value(item[props.field.value]);
    if (hasOwen) {
        values = values.filter((v) => v !== item[props.field.value]);
    } else {
        values.push(item[props.field.value]);
    }
    emits("update:value", values);
};
</script>

<style lang="scss" scoped>
.item-radio:hover .radio-icon {
    border-style: solid;
}
</style>
