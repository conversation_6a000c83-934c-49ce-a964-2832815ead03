<template>
    <div class="space-y-4">
        <div class="flex justify-between items-center gap-2">
            <span class="text-2xl dark:text-dark-text font-semibold"> error----- {{ code }} </span>
            <IconsClose class="w-6 h-6 cursor-pointer" @click="handleClose" />
        </div>
        <div class="bg-zinc-100 dark:bg-black rounded-5xl p-5 space-y-4"></div>
        <div>预载进并发---报错--错误CODE{{ code }}</div>

        <div>
            <n-button :bordered="false" class="!bg-primary rounded-full text-base font-medium !text-white py-2.5 w-full" type="primary" @click="handleClose">
                {{ t("SUBSCRIBE_BUY_LUMEN_MODAL_BUTTON") }}
            </n-button>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
const props = defineProps({
    code: {
        type: [Number, String],
        default: "",
    },
});
const emits = defineEmits(["update:show", "confirm", "cancel"]);
const handleClose = () => {
    emits("update:show", false);
};
</script>

<style lang="scss" scoped></style>
