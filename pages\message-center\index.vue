<template>
    <div v-if="!isMobile" class="flex flex-col h-screen relative bg-bg-1 font-medium overflow-y-auto max-w-[1248px] mx-auto px-6 w-full">
        <div class="my-[56px] text-text-1 text-[28px] font-semibold sticky top-0 flex justify-center items-center z-50 shrink-0">
            {{ $t("MESSAGE_CENTER_TITLE") }}
        </div>
        <div class="flex flex-1 bg-bg-2 overflow-hidden rounded-2xl mb-6">
            <div class="shrink-0 w-[304px] h-full p-6 rounded-l-2xl border-r border-solid border-border-1 flex flex-col gap-3 justify-between">
                <div class="flex flex-col gap-2">
                    <div
                        v-for="(item, index) in unreadMessages.messageGroup"
                        :key="item.label"
                        class="flex items-center h-10 rounded-full gap-2 px-3 py-2 text-text-opt-1 hover:text-text-3 hover:bg-fill-wd-1 cursor-pointer transition duration-200 text-sm"
                        :class="{ '!bg-fill-opt-3 !text-text-opt-3': checked === index }"
                        @click="handleSwitchTab(index, item.label)"
                    >
                        <n-icon size="20" class="shrink-0">
                            <component :is="icons[item.label]"></component>
                        </n-icon>
                        <span class="flex-1 text-ellipsis text-nowrap overflow-hidden" v-if="messageMap[item.label]">{{ t(messageMap[item.label]) }}</span>
                        <n-badge :value="item.value" :max="99" color="#F76965"> </n-badge>
                    </div>
                </div>

                <div class="flex items-center justify-center gap-1.5 text-text-4 h-10 rounded-lg hover:text-text-3 hover:!bg-fill-wd-1 cursor-pointer" @click="handleReadAll">
                    <n-icon size="20"> <IconsSuccess /> </n-icon>
                    <span class="pr-2">{{ t("MESSAGE_CENTER_READ_ALL") }}</span>
                </div>
            </div>
            <ClientOnly>
                <component
                    ref="messageTypeRef"
                    class="flex-1 overflow-x-hidden w-0 h-full relative p-6 pr-0"
                    v-if="unreadMessages.messageGroup[checked]?.label"
                    :key="keepAliveKey"
                    :is="messageChannel[unreadMessages.messageGroup[checked].label]"
                />
            </ClientOnly>
        </div>
    </div>

    <div v-else class="flex flex-col h-full bg-bg-1 text-sm text-text-2 font-medium gap-4 relative">
        <MobileHeader :title="t('MESSAGE_CENTER_TITLE')" />

        <template v-for="(item, index) in unreadMessages.messageGroup">
            <div :key="item.label" v-if="icons[item.label]" class="flex items-center h-10 rounded-full gap-2 pl-4 pr-3 transition duration-200" @click="handleSwitchTab(index, item.label, true)">
                <template v-if="icons[item.label]">
                    <n-icon size="20" class="shrink-0">
                        <component :is="icons[item.label]"></component>
                    </n-icon>
                    <span class="flex-1 text-ellipsis text-nowrap overflow-hidden" v-if="messageMap[item.label]">{{ t(messageMap[item.label]) }}</span>
                    <n-badge :value="item.value" :max="99" color="#F76965"> </n-badge>
                    <n-icon size="20" class="shrink-0"> <IconsArrowRight /> </n-icon>
                </template>
            </div>
        </template>

        <!-- 已读标识 -->
        <!-- <div class="flex flex-col w-full mt-auto pb-[50px]">
            <div class="flex items-center justify-center gap-1.5 h-10 text-text-4 mx-auto w-max" @click="handleReadAll">
                <n-icon size="20"> <IconsSuccess /> </n-icon>
                <span class="pr-2">{{ t("MESSAGE_CENTER_READ_ALL") }}</span>
            </div>
        </div> -->

        <div class="fixed bottom-[50px] left-0 right-0 z-10">
            <div class="flex items-center justify-center gap-1.5 h-10 text-text-4 mx-auto w-max" @click="handleReadAll">
                <n-icon size="20"> <IconsSuccess /> </n-icon>
                <span class="pr-2">{{ t("MESSAGE_CENTER_READ_ALL") }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
const updateSeo = () => {
    useSeoMeta({
        title: () => t("SEO_META.SEO_MESSAGE_TITLE"),
        ogTitle: () => t("SEO_META.SEO_MESSAGE_TITLE"),
        description: () => t("SEO_META.SEO_MESSAGE_DESC"),
        ogDescription: () => t("SEO_META.SEO_MESSAGE_DESC"),
    });
};

onActivated(() => {
    nextTick(updateSeo);
});
import { defineAsyncComponent } from "vue";
import { dealReadAll } from "@/api";
import { useUnreadMessage } from "@/stores";
import { KEEPALIVE_PAGES } from "@/utils/constant";
import { trackLabels } from "@/common/trackEventLabel";
import { useThemeStore } from "@/stores/system-config";
import { loopUnreadNotice } from "@/api";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";
import { storeToRefs } from "pinia";

defineOptions({
    name: KEEPALIVE_PAGES.MESSAGE_CENTER,
});
const unreadMessages = useUnreadMessage();
const checked = ref(0);
const keepAliveKey = ref(1);
const router = useRouter();
const { isMobile } = storeToRefs(useThemeStore());

const messageMap = Object.freeze({
    nlikeNums: "MESSAGE_CENTER_LIKE",
    ncommentNums: "MESSAGE_CENTER_COMMENTS",
    nsysUpdateNums: "MESSAGE_CENTER_UPDATE",
    nplatformMessageNums: "MESSAGE_CENTER_PERSONAL",
    nplatformActivityNums: "MESSAGE_CENTER_PUBLIC",
});
const icons = {
    nlikeNums: defineAsyncComponent(() => import("@/components/icons/NoticeLike.vue")),
    ncommentNums: defineAsyncComponent(() => import("@/components/icons/NoticeCommentsNew.vue")),
    nsysUpdateNums: defineAsyncComponent(() => import("@/components/icons/NoticeUpdate.vue")),
    nplatformMessageNums: defineAsyncComponent(() => import("@/components/icons/NoticePersonal.vue")),
    nplatformActivityNums: defineAsyncComponent(() => import("@/components/icons/NoticePublic.vue")),
};
const messageChannel = {
    nlikeNums: defineAsyncComponent(() => import("@/components/message/LikeNotice.vue")),
    ncommentNums: defineAsyncComponent(() => import("@/components/message/CommentNotice.vue")),
    nsysUpdateNums: defineAsyncComponent(() => import("@/components/message/SystemNotice.vue")),
    nplatformMessageNums: defineAsyncComponent(() => import("@/components/message/PersonalNotice.vue")),
    nplatformActivityNums: defineAsyncComponent(() => import("@/components/message/PublicNotice.vue")),
};
//阅读所有消息
const messageTypeRef = ref();
const handleReadAll = async () => {
    gaTrackEvent("read_all");
    const { status, message } = await dealReadAll();
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    unreadMessages.readAllMessage();
    messageTypeRef.value?.readAll();
};
const localePath = useLocalePath();
//切换tab/刷新
const handleSwitchTab = async (index, eventLabel, isMobile) => {
    checked.value = index;
    keepAliveKey.value = Math.random();
    gaTrackEvent(eventLabel);
    // 移动端 跳转到下级目录中
    if (isMobile) {
        await navigateTo({ path: localePath(`/m/message-center/${eventLabel}`) });
    }
};
const gaTrackEvent = (el) => {
    window.trackEvent("Message_Center", { el: trackLabels[el] });
};

watchEffect(async () => {
    // 移动端的时候 在此处获取一次
    if (isMobile.value) {
        const { status, data } = await loopUnreadNotice();
        if (status === 0) {
            unreadMessages.updateUnreadList(data);
        }
    }
});
</script>

<style lang="scss"></style>
