self.onmessage = function (e) {
    const imageData = e.data.imageData;
    const data = imageData.data;

    const uint32 = new Uint32Array(data.buffer);

    // 黑色不透明和白色不透明的 32 位表示（小端字节序）
    const WHITE = 0xffffffff; // RGBA: 255,255,255,255
    const BLACK = 0xff000000; // RGBA: 0,0,0,255
    const ALPHA_THRESHOLD = 1; // 根据抗锯齿效果调整阈值

    // 遍历所有像素（长度减少为四分之一）
    for (let i = 0; i < uint32.length; i++) {
        // 获取当前像素的 alpha 通道（右移 24 位获取最高字节）
        const alpha = (uint32[i] >>> 24) & 0xff;
        // 使用位运算直接设置像素值
        uint32[i] = alpha > ALPHA_THRESHOLD ? WHITE : BLACK;
    }

    self.postMessage({ imageData });
};
