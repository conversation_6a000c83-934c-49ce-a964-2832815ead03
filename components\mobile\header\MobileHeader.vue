<!--
 * @Author: HuangQS
 * @Description: 移动端自定义头部组件
 * @Date: 2025-05-13 10:22:42
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-24 19:58:30
-->
<template>
    <template v-if="isMobile">
        <div id="mbHeader" ref="mbHeaderRef" :class="isFixedClass ? 'fixed top-0 w-full z-30' : ' absolute top-0 w-full z-30'">
            <!-- <slot v-if="$slots.banner" name="banner"></slot> -->
            <!-- <AnniversaryBanner /> -->

            <div class="flex w-full justify-between items-center h-14 bg-bg-1" :style="`padding: 4px ${customPX}px`">
                <div class="w-20 flex items-center">
                    <template v-if="$slots.left">
                        <slot name="left"></slot>
                    </template>
                    <n-icon v-else-if="!disableBack" class="rotate-180 flex items-center text-text-2" size="24" @click="handleBack">
                        <IconsArrowRight />
                    </n-icon>
                </div>
                <div class="flex-1 text-center items-center text-text-1 text-base">
                    <template v-if="$slots.title">
                        <slot name="title"></slot>
                    </template>
                    <span v-else> {{ title }}</span>
                </div>
                <div class="w-20 text-text-white text-base flex justify-end">
                    <slot v-if="$slots.right" name="right"></slot>
                </div>
            </div>

            <template v-if="$slots.extra">
                <slot name="extra" />
            </template>
        </div>
        <!-- <div class="h-[300px] w-full bg-blue-400 border border-[10px] border-green-400"></div> -->
        <!-- 标题占位空间 -->
        <div v-if="!isFixedClass" class="flex w-full justify-between items-center" :style="{ height: `${viewHeight}px` }"></div>
    </template>
</template>
<script setup>
import { ref, watchEffect } from "vue";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());

const emits = defineEmits([
    "onCalcFixedHeight", // 回到方法，计算组件高度
    "update:mobileHeaderHeight", // 更新移动端头部高度
]);

const props = defineProps({
    title: { type: String, default: "" },
    disableBack: { type: Boolean, default: false },
    customPX: { type: Number, default: 16 },
    customBackPath: { type: String, default: "" },
    mobileHeaderHeight: { type: Number, default: 0 },
    isFixedClass: { type: Boolean, default: false },
});

const mbHeaderRef = ref(null);
let resizeObserver = null;
const viewHeight = ref(92);

onMounted(() => {
    nextTick(() => {
        // 计算mbHeader组件高度
        // const mbHeaderEl = document.getElementById("mbHeader");
        // if (mbHeaderEl) {
        //     const height = mbHeaderEl.offsetHeight;
        //     viewHeight.value = height;
        // }

        if ("ResizeObserver" in window) {
            resizeObserver = new ResizeObserver(handleResize);
            if (mbHeaderRef.value) {
                resizeObserver.observe(mbHeaderRef.value);
            }
        }
    });
});

const handleResize = (entries) => {
    for (let entry of entries) {
        const height = entry.contentRect.bottom + entry.contentRect.top;
        viewHeight.value = height;
        // emits("onCalcFixedHeight", height);
        emits("onCalcFixedHeight", height);
        emits("update:mobileHeaderHeight", height);
    }
};

onBeforeUnmount(() => {
    if (resizeObserver && mbHeaderRef.value) {
        resizeObserver.unobserve(mbHeaderRef.value);
        resizeObserver.disconnect();
    }
});

const router = useRouter();
const route = useRoute();

const localePath = useLocalePath();
const handleBack = async () => {
    if (props.customBackPath) {
        await navigateTo({ path: localePath(props.customBackPath) });
        return;
    }

    router.back();
};

onMounted(() => {
    // 记录访问的页面
});

const title = ref(props.title);

watchEffect(() => {
    title.value = props.title;
});
</script>
<style lang="scss" scoped></style>
