<template>
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="Google Play logo">
            <path id="Vector" d="M6.82258 4.91595C6.56498 5.18966 6.41272 5.61445 6.41272 6.16454V25.8186C6.41272 26.3696 6.56498 26.7935 6.82258 27.0672L6.88809 27.1312L17.8551 16.1213V15.9916V15.8618L6.88809 4.85107L6.82258 4.91595Z" fill="url(#paint0_linear_166_8352)"/>
            <g id="Vector_2">
                <path d="M21.5101 19.7915L17.855 16.1204V15.9907V15.8609L21.511 12.1907L21.5933 12.2378L25.9247 14.7083C27.1613 15.4139 27.1613 16.5683 25.9247 17.2748L21.5933 19.7453L21.5101 19.7915Z" fill="#4A3AFF"/>
                <path d="M21.5101 19.7915L17.855 16.1204V15.9907V15.8609L21.511 12.1907L21.5933 12.2378L25.9247 14.7083C27.1613 15.4139 27.1613 16.5683 25.9247 17.2748L21.5933 19.7453L21.5101 19.7915Z" fill="url(#paint1_linear_166_8352)"/>
            </g>
            <g id="Vector_3" filter="url(#filter0_i_166_8352)">
                <path d="M21.5932 19.7449L17.8541 15.9911L6.82239 27.0667C7.22959 27.5004 7.90325 27.5537 8.66188 27.1218L21.5932 19.7449Z" fill="#4A3AFF"/>
                <path d="M21.5932 19.7449L17.8541 15.9911L6.82239 27.0667C7.22959 27.5004 7.90325 27.5537 8.66188 27.1218L21.5932 19.7449Z" fill="url(#paint2_linear_166_8352)"/>
            </g>
            <g id="Vector_4">
                <path d="M21.5932 12.2376L8.66188 4.86153C7.90325 4.42874 7.22959 4.48295 6.82239 4.91663L17.8549 15.9922L21.5932 12.2376Z" fill="#4A3AFF"/>
                <path d="M21.5932 12.2376L8.66188 4.86153C7.90325 4.42874 7.22959 4.48295 6.82239 4.91663L17.8549 15.9922L21.5932 12.2376Z" fill="url(#paint3_linear_166_8352)"/>
            </g>
        </g>
        <defs>
            <filter id="filter0_i_166_8352" x="6.82239" y="15.9911" width="14.7709" height="11.4291" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="-0.133302"/>
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow_166_8352"/>
            </filter>
            <linearGradient id="paint0_linear_166_8352" x1="16.883" y1="5.95659" x2="1.96927" y2="20.8123" gradientUnits="userSpaceOnUse">
                <stop stop-color="#00A0FF"/>
                <stop offset="0.0066" stop-color="#00A1FF"/>
                <stop offset="0.2601" stop-color="#00BEFF"/>
                <stop offset="0.5122" stop-color="#00D2FF"/>
                <stop offset="0.7604" stop-color="#00DFFF"/>
                <stop offset="1" stop-color="#00E3FF"/>
            </linearGradient>
            <linearGradient id="paint1_linear_166_8352" x1="27.5345" y1="15.9907" x2="6.11567" y2="15.9907" gradientUnits="userSpaceOnUse">
                <stop stop-color="#FFE000"/>
                <stop offset="0.4087" stop-color="#FFBD00"/>
                <stop offset="0.7754" stop-color="#FFA500"/>
                <stop offset="1" stop-color="#FF9C00"/>
            </linearGradient>
            <linearGradient id="paint2_linear_166_8352" x1="19.5626" y1="18.0296" x2="-0.662231" y2="38.1759" gradientUnits="userSpaceOnUse">
                <stop stop-color="#FF3A44"/>
                <stop offset="1" stop-color="#C31162"/>
            </linearGradient>
            <linearGradient id="paint3_linear_166_8352" x1="4.04402" y1="-1.62601" x2="13.075" y2="7.36984" gradientUnits="userSpaceOnUse">
                <stop stop-color="#32A071"/>
                <stop offset="0.0685" stop-color="#2DA771"/>
                <stop offset="0.4762" stop-color="#15CF74"/>
                <stop offset="0.8009" stop-color="#06E775"/>
                <stop offset="1" stop-color="#00F076"/>
            </linearGradient>
        </defs>
    </svg>
</template>