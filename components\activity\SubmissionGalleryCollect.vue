<template>
    <div class="flex">
        <Tabs :tabs="collectList" v-model:value="currentCollect" @change="changeTab" />

        <!-- <div class="flex-1 overflow-hidden">
            <Swiper class="w-full"
                @swiper="onSwiper"
                :resizeObserver="true"
                slides-per-view="auto"
                :space-between="10"
                :watch-overflow="true" 
            >
                <SwiperSlide v-for="(item, index) in collectList" :key="item.id"  @click="handleSwiperSlide(item, index)" class="swiper-slide">
                    <div
                        class="h-9 min-w-10 px-3 cursor-pointer text-text-4 bg-fill-btn-4 flex items-center justify-center rounded-full"
                        :class='{"text-text-opt-3": item.id === currentCollect.id}'
                    >{{ item.collectName }}</div>
                </SwiperSlide>
            </Swiper>
        </div>
        <div v-if="!canFitInOneScreen" class="w-9 h-9 flex items-center justify-center bg-fill-btn-4 rounded-full ml-2" @click="handleNextCollect">
            <n-icon size="24">
                <IconsArrowRight />
            </n-icon>
        </div> -->
    </div>
</template>
<script setup>
import { useShareCollect } from "@/stores";
import { useInitCollection } from "@/hook/updateAccount";
const shareCollect = useShareCollect();

onMounted(() => {
    useInitCollection();
});
const props = defineProps({
    defaultCollect: {
        type: Object,
    },
});
const emits = defineEmits(["change"]);

const collectList = computed(() => {
    return [props.defaultCollect, ...shareCollect.collectList].map(({ collectName, id }) => ({ label: collectName, value: id }));
});

const currentCollect = ref(props.defaultCollect.id);

const changeTab = (id) => {
    const current = collectList.value.find((item) => item.value === id);
    if (current) {
        emits("change", { id: id, collectName: current.label });
    }
};
// const handleSwiperSlide = (item, index) => {
//     currentCollect.value = item;
//     swiperRef.value.slideTo(index);
//     emits("change", item);
// };

// const handleNextCollect = () => {
//     if (!swiperRef.value.isEnd) {
//         swiperRef.value.slideNext();
//     }
// };
</script>

<style lang="scss" scoped>
.swiper-slide {
    width: auto !important;
    background: none !important;
}
</style>
