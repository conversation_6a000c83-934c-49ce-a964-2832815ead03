<template>
    <!-- 用户信息 -->
    <div class="flex flex-col text-sm">
        <div>
            <n-avatar v-if="userInfo.user.avatarUrl" :size="80" :src="userInfo.user.avatarUrl" class="cursor-pointer hover:opacity-90" round @click="updateAvatar"></n-avatar>
            <n-icon v-else :size="80" class="cursor-pointer hover:opacity-90" @click="updateAvatar">
                <IconsPerson />
            </n-icon>
        </div>

        <div class="w-full md:w-[430px] mt-6">
            <div :style="{ '--error-msg': `'${nicknameForm.errorMsg}'` }" class="pb-8 relative form-item">
                <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_NICKNAME") }}</div>
                <div class="flex items-center gap-4 mt-3">
                    <div :class="{ '!border-red-500': nicknameForm.status === 'error' }" class="custom-input-border">
                        <input
                            ref="nicknameRef"
                            v-model="nicknameForm.value"
                            :placeholder="t('PROFILE_ACCOUNT_NICKNAME')"
                            :readonly="nicknameForm.isLoading"
                            class="!caret-primary h-full border-none outline-none w-full bg-transparent dark:placeholder:text-dark-active-text/40 font-medium dark:!text-dark-active-text !text-zinc-800 peer"
                            :maxlength="maxLen"
                            type="text"
                            @input="nicknameForm.status = ''"
                        />
                        <span class="text-xs font-medium text-text-4">{{ curLen }}/{{ maxLen }}</span>
                    </div>
                </div>
            </div>

            <div class="pb-8 max-w-[356px]">
                <div class="text-sm text-text-3 font-medium">{{ t("COMMUNITY_BIO") }}</div>
                <div class="bg-fill-ipt-1 rounded-lg mt-3 min-h-20">
                    <ClientOnly>
                        <n-input
                            type="textarea"
                            :placeholder="t('COMMUNITY_EMPTY_DESC')"
                            class="h-auto min-h-20"
                            :bordered="false"
                            v-model:value="userBio"
                            :maxlength="250"
                            round
                            :rows="3"
                            :autosize="{ minRows: 3, maxRows: 5 }"
                        />
                    </ClientOnly>
                </div>
                <div class="mt-2 text-text-4">{{ t("PROFILE_ACCOUNT_CHARTS_LIMIT") }}</div>
            </div>

            <!-- 平台 -->
            <template v-if="isMobile">
                <div v-if="userInfo.user.googleId" class="pb-8 max-w-[356px] flex items-center">
                    <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_PLATFORM") }}</div>
                    <div class="ml-2">
                        <n-icon size="20"> <IconsGoogle /> </n-icon>
                    </div>
                </div>
                <div v-if="userInfo.user.appleId" class="pb-8 max-w-[356px] flex items-center">
                    <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_PLATFORM") }}</div>
                    <div class="ml-2">
                        <n-icon size="20"> <IconsApple /> </n-icon>
                    </div>
                </div>
            </template>
            <template v-else>
                <div v-if="userInfo.user.googleId" class="pb-8 max-w-[356px]">
                    <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_PLATFORM") }}</div>
                    <div class="mt-3">
                        <n-icon size="36">
                            <IconsGoogle />
                        </n-icon>
                    </div>
                </div>
                <div v-if="userInfo.user.appleId" class="pb-8 max-w-[356px]">
                    <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_PLATFORM") }}</div>
                    <div class="mt-3">
                        <n-icon size="36">
                            <IconsApple />
                        </n-icon>
                    </div>
                </div>
            </template>

            <!-- 修改密码 -->
            <div v-if="!userInfo.user.googleId && !userInfo.user.appleId" class="pb-8 max-w-[356px]">
                <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_PASSWORD") }}</div>
                <div class="mt-3 h-10">
                    <Button type="secondary" rounded="lg" @click="showResetModal = true">
                        <div class="h-full flex items-center justify-center gap-2">
                            <n-icon size="20">
                                <IconsLock />
                            </n-icon>
                            <span>{{ t("PROFILE_ACCOUNT_CHANGE_PASSWORD") }}</span>
                        </div>
                    </Button>
                </div>
            </div>

            <div v-show="!!userInfo.user.email" class="pb-8 max-w-[356px]">
                <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_EMAIL") }}</div>
                <div class="mt-3 h-10 rounded-lg bg-fill-ipt-1 px-3">
                    <div class="h-full w-full font-medium flex items-center">
                        <span>{{ userInfo.user.email }}</span>
                    </div>
                </div>
            </div>

            <!-- 性别 -->
            <div class="pb-8 max-w-[356px] hidden md:block">
                <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_GENDER") }}</div>
                <n-select v-model:value="gender" :options="genderList" :render-label="renderLabel" :to="false" class="gender-select bg-fill-ipt-1 rounded-lg mt-3 h-10" />
            </div>
            <div class="pb-8 max-w-[356px] block md:hidden">
                <div class="text-sm text-text-3 font-medium">{{ t("PROFILE_ACCOUNT_GENDER") }}</div>
                <div class="mt-2">
                    <n-radio-group v-model:value="gender" name="radiogroup">
                        <n-space>
                            <n-radio v-for="item in genderList" :key="item.value" :value="item.value">
                                {{ t(item.label) }}
                            </n-radio>
                        </n-space>
                    </n-radio-group>
                </div>
            </div>

            <div class="pb-8 max-w-[356px] flex gap-4">
                <Button :loading="nicknameForm.isLoading" class="min-w-32 px-4" @click="handleSave">
                    <span>{{ t("COMMON_BTN_SAVE") }}</span>
                </Button>
                <Button type="danger" @click="handleDelAccount">
                    <span>{{ t("PROFILE_DEL_ACCOUNT") }}</span>
                </Button>
            </div>
        </div>

        <CropModal ref="clipAvatarRef" />
        <ResetPasswordModal v-model:show="showResetModal" />
    </div>
</template>

<script setup>
import { useUserProfile } from "@/stores/index.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useClearUserInfo } from "@/hook/updateAccount.js";
import { checkNickname, updateUserProfile } from "@/api/index.js";
import { immediateDebounce } from "@/utils/tools.js";
import { NIcon } from "naive-ui";
import ResetPasswordModal from "@/components/users/ResetPasswordModal.vue";
import DelAccountModal from "@/components/DelAccountModal.vue";
import CropModal from "@/components/CropModal.vue";
import { useThemeStore } from "@/stores/system-config";
import { PLATFORM_TYPE } from "@/utils/constant.js";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());
const localePath = useLocalePath();
const { t } = useI18n({ useScope: "global" });
const userInfo = useUserProfile();
const subscribeStore = useSubscribeStore();
const { showMessage } = useModal();

// 选择头像
const clipAvatarRef = ref(null);
const updateAvatar = () => {
    clipAvatarRef.value.chooseLocalPic();
};

//个人描述：
const userBio = ref(userInfo.user.introduction || "");

const nicknameForm = ref({
    value: userInfo.user.userName,
    errorMsg: "",
    status: "",
    isLoading: false,
});

const gender = ref(userInfo.user.sex || "2");
const genderList = [
    { value: "0", label: "PROFILE_ACCOUNT_GENDER_MALE" },
    { value: "1", label: "PROFILE_ACCOUNT_GENDER_FEMALE" },
    { value: "2", label: "PROFILE_ACCOUNT_GENDER_NOT_SAY" },
];
const renderLabel = (option) => {
    return t(option.label);
};

// 修改昵称 自动聚焦
const maxLen = 30;
const curLen = computed(() => nicknameForm.value.value?.length);
const nicknameRef = ref(null);

// 重置密碼弹窗
const showResetModal = ref(false);

const formDataFn = async () => {
    // 判断是否提交中
    if (nicknameForm.value.isLoading) {
        return;
    }

    try {
        nicknameForm.value.isLoading = true;

        const params = {};
        // 判断是否修改了用户名
        if (nicknameForm.value.value === userInfo.user.userName) {
            nicknameForm.value.status = "";
            nicknameForm.value.errorMsg = "";
        } else {
            // 判断昵称是否为空
            if (nicknameForm.value.value === "" || !nicknameForm.value.value) {
                nicknameForm.value.status = "error";
                nicknameForm.value.errorMsg = t("PROFILE_ACCOUNT_NICKNAME_INPUT");
                nicknameForm.value.isLoading = false;
                return;
            }
            // 判断昵称是否符合正则
            const nicknameReg = /^[^\\/:*?"<>|]{4,30}$/;
            if (!nicknameReg.test(nicknameForm.value.value) || nicknameForm.value.value.length > maxLen) {
                nicknameForm.value.status = "error";
                nicknameForm.value.errorMsg = t("PROFILE_ACCOUNT_NICKNAME_INVALID");
                nicknameForm.value.isLoading = false;
                return;
            }

            nicknameForm.value.value = nicknameForm.value.value.trim();

            // 昵称检测
            const checkRes = await checkNickname({ userName: nicknameForm.value.value });
            if (checkRes.status === 1) {
                nicknameForm.value.status = "error";
                nicknameForm.value.errorMsg = t("PROFILE_ACCOUNT_NICKNAME_REGISTER");
                nicknameForm.value.isLoading = false;
                return;
            }
            if (checkRes.status !== 0) {
                openToast.error(checkRes.message);
                nicknameForm.value.isLoading = false;
                return;
            }
            nicknameForm.value.status = "checked";
            nicknameForm.value.errorMsg = "";
            params.userName = nicknameForm.value.value;
        }

        // 修改了性别
        if (gender.value !== userInfo.user.sex) {
            params.sex = gender.value;
        }
        // 修改了个人描述
        const introduction = userBio.value.trim();
        if (introduction !== "" && introduction !== userInfo.user.introduction) {
            params.introduction = introduction;
        }

        // 没有任何更改
        if (!Object.keys(params).length) {
            nicknameForm.value.isLoading = false;
            return;
        }

        // 更新用户信息
        const updateRes = await updateUserProfile(params);
        if (updateRes.status !== 0) {
            openToast.error(updateRes.message);
            return;
        }
        nicknameForm.value.isLoading = false;
        userInfo.updateUser(params);
        openToast.success(t("SAVE_SUCCESS"));
    } catch (error) {
        nicknameForm.value.isLoading = false;
        console.error(error);
    }
};

// 保存
const handleSave = immediateDebounce(formDataFn, 500, true);

// 删除账户
const handleDelAccount = () => {
    const subList = subscribeStore.subList || [];
    //当前用户的订阅列表存在 自动续订 并且 不是赠送的会员时 不允许删除账户
    const isPaymentVip = subList.some((item) => item.autoRenewStatus !== 0 && item.vipPlatform !== PLATFORM_TYPE.GIFT);
    if (isPaymentVip) {
        showMessage({
            style: { width: "500px", padding: "16px 24px 24px" },
            showCloseBtn: true,
            cancelBtn: t("DEL_ACC.BTN_CLOSE"),
            confirmBtn: t("DEL_ACC.GO_SUBSCRIPTION"),
            content: h("div", { class: "text-text-2 mt-10" }, [
                h("p", null, t("DEL_ACC.DEL_ACCOUNT_1")),
                h("p", null, t("DEL_ACC.DEL_ACCOUNT_2")),
                h("p", { class: "mt-6 mb-12" }, t("DEL_ACC.DEL_ACCOUNT_3")),
            ]),
            title: t("DIALOG_TITLE_NOTICE"),
        })
            .then(async (_) => {
                await navigateTo(localePath("/user/subscribe"));
            })
            .catch((_) => {});
        return;
    }
    let feedbackEmail = userInfo.user;
    showMessage({
        style: { width: "592px", padding: "16px 24px 24px" },
        showCancel: false,
        showConfirm: false,
        content: h(DelAccountModal, {
            email: feedbackEmail,
        }),
    })
        .then(async (_) => {
            useClearUserInfo();
            await navigateTo(localePath("/account/login"));
        })
        .catch((_) => {});
};
</script>

<style lang="scss" scoped>
.form-item::after {
    @apply absolute left-1 bottom-3 text-xs text-error;
    content: var(--error-msg);
}

.custom-input-border {
    @apply dark:bg-dark-bg-2 bg-neutral-100 h-10 rounded-lg border border-solid flex-1 max-w-[356px] border-neutral-100 dark:border-dark-bg-2  dark:focus-within:border-primary focus-within:border-primary px-3 bg-fill-ipt-1 flex items-center gap-2;
}

:deep(.gender-select) {
    .n-base-select-menu {
        @apply border-border-t-1 p-2;
        background-color: var(--p-bg-6);

        .n-base-select-option::before {
            border-radius: 8px !important;
        }
    }
}

:deep(.n-radio__dot) {
    --radio-color: #7b57e5;
    --n-box-shadow-active: inset 0 0 0 1px var(--radio-color) !important;
    --n-box-shadow-focus: inset 0 0 0 1px var(--radio-color) !important ;
    --n-box-shadow-hover: inset 0 0 0 1px var(--radio-color) !important ;
    --n-dot-color-active: var(--radio-color) !important  ;
}
</style>
