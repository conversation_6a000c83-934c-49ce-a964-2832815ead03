<template>
    <div>
        <DynamicScroller :items="messageList" :min-item-size="120" :emit-update="true" keyField="id" tabindex="1" class="h-full pr-3 scroll-container" @scroll="pageScroll">
            <template #default="{ item, index, active }">
                <DynamicScrollerItem :key="item.id" :item="item" :active="active" :size-dependencies="[item.message]" :data-index="index" :data-active="active" class="pb-4">
                    <template v-if="isMobile">
                        <div @click="readMessage(item)" class="rounded-2xl p-4 flex items-center gap-3 text-text-4">
                            <NoReadDot v-if="!item.read" />
                            <NuxtLinkLocale :to="`/community/profile/${item.ownerAcc.userId}`" @click.stop>
                                <img v-if="item.ownerAcc?.userAvatarUrl" :src="item.ownerAcc.userAvatarUrl" class="h-11 w-11 rounded-full overflow-hidden shrink-0" />
                                <n-icon v-else size="48" class="shrink-0 text-text-3">
                                    <IconsPerson />
                                </n-icon>
                            </NuxtLinkLocale>
                            <div class="font-normal flex-1 min-w-0">
                                <div class="flex gap-2 items-center">
                                    <span class="min-w-0 text-nowrap text-ellipsis truncate first-letter:font-medium text-sm text-text-2">
                                        {{ item.ownerAcc.userName }}
                                    </span>
                                    <span class="text-nowrap text-ellipsis truncate text-xs">
                                        {{ t("MESSAGE_CENTER_LIKE_TITLE") }}
                                    </span>
                                </div>
                                <div class="mt-1 text-xs text-text-4">{{ item.actionTime }}</div>
                            </div>

                            <img :src="item.miniThumbnailUrl" class="size-12 rounded-lg object-cover shrink-0" />
                        </div>
                    </template>
                    <template v-else>
                        <div @click="readMessage(item)" class="rounded-2xl p-4 pl-6 flex items-center gap-3 bg-transparent hover:bg-fill-wd-1 cursor-pointer">
                            <NoReadDot v-if="!item.read" />
                            <NuxtLinkLocale :to="`/community/profile/${item.ownerAcc.userId}`" @click.stop>
                                <img v-if="item.ownerAcc?.userAvatarUrl" :src="item.ownerAcc.userAvatarUrl" class="h-11 w-11 rounded-full overflow-hidden shrink-0" />
                                <n-icon v-else size="44" class="shrink-0 text-text-3">
                                    <IconsPerson />
                                </n-icon>
                            </NuxtLinkLocale>
                            <div class="font-normal flex-1">
                                <div class="flex gap-2 items-center">
                                    <span class="text-nowrap text-ellipsis truncate max-w-[50%] font-medium text-sm text-text-2">
                                        {{ item.ownerAcc.userName }}
                                    </span>
                                    <span class="text-nowrap text-ellipsis truncate max-w-[40%] text-xs text-text-4">
                                        {{ t("MESSAGE_CENTER_LIKE_TITLE") }}
                                    </span>
                                </div>
                                <div class="mt-1 text-xs text-text-4">{{ item.actionTime }}</div>
                            </div>

                            <img :src="item.miniThumbnailUrl" class="size-[72px] rounded-lg object-cover shrink-0" />
                        </div>
                    </template>
                </DynamicScrollerItem>
            </template>
            <template #after>
                <div v-if="loading" class="flex justify-center py-10">
                    <n-icon size="32" class="text-primary">
                        <IconsSpinLoading />
                    </n-icon>
                </div>
                <div v-if="noMore && messageList.length > 0" class="flex justify-center py-10">
                    <div class="pt-4 px-2 border-t border-solid text-text-4 border-border-1">There's no more</div>
                </div>
            </template>
        </DynamicScroller>
        <div v-if="noMore && messageList.length === 0" class="absolute left-0 top-0 right-0 h-full flex items-center justify-center flex-col z-10">
            <img src="@/assets/images/notice_empty.webp" class="w-36 aspect-square hidden dark:block" />
            <img src="@/assets/images/notice_empty_light.webp" class="w-36 aspect-square dark:hidden" />
            <div class="mt-4 text-text-4">{{ t("MESSAGE_CENTER_EMPTY") }}</div>
        </div>
    </div>
</template>

<script setup>
import { formatDate, debounce, isScrolledToBottom } from "@/utils/tools";
import { getLikesMessage } from "@/api";
import { useUnreadMessage, useShareDataStore } from "@/stores";

import { useThemeStore } from "@/stores/system-config";

import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import NoReadDot from "@/components/message/NoReadDot.vue";
import { storeToRefs } from "pinia";

const { t } = useI18n({ useScope: "global" });
const router = useRouter();
const unreadMessages = useUnreadMessage();
const { isMobile } = storeToRefs(useThemeStore());

const messageList = ref([]);
const loading = ref(false);
const noMore = ref(false);
//加载数据
const pageSize = 30;
let lastLikeId = null;
const handleLoad = async () => {
    if (loading.value || noMore.value) return;
    loading.value = true;
    const { status, data, message } = await getLikesMessage({ pageSize, lastLikeId });
    loading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    lastLikeId = data.lastId;
    const newArray = data?.resultList || [];
    if (!data.lastId || newArray.length < pageSize) {
        noMore.value = true;
    }
    const list = newArray.map((item) => {
        item.actionTime = formatDate(item.createTime);
        return item;
    });
    messageList.value.push(...list);
};
handleLoad();
const pageScroll = debounce((e) => {
    const isBottom = isScrolledToBottom(e.target);
    if (!isBottom) {
        return;
    }
    handleLoad();
}, 60);
//阅读信息
let fetchLoading = false;
const messageType = "nlikeNums";
const readMessage = async ({ id, fileId, read }) => {
    toCommunityDetail(fileId);
    if (fetchLoading || read) return;
    fetchLoading = true;
    const res = await unreadMessages.updateNoticeReadStatus(id, messageType);
    fetchLoading = false;
    if (!res) {
        return;
    }
    const index = messageList.value.findIndex((item) => item.id === id);
    messageList.value[index].read = true;
};
const shareData = useShareDataStore();
const localePath = useLocalePath();
const toCommunityDetail = async (id) => {
    shareData.setList([]);
    await navigateTo({ path: localePath(`/community/detail/${id}`) });
};
const readAll = () => {
    messageList.value.forEach((item) => {
        item.read = true;
    });
    console.log("父组件点击了全部已读", messageList.value);
};
defineExpose({
    readAll,
});
</script>

<style lang="scss" scoped></style>
