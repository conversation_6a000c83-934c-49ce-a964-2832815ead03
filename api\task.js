import http from "./http";

/**
 * 更新每日任务
 * @param {} data 
 * @returns 
 */
export const resetTaskApi = (data) =>
    http({
        url: "/lumen-task/reset-tasks",
        method: "post",
    });
/**
 * 获取任务中心的任务列表
 * @param {} data 
 * @returns 
 */
export const getTaskListApi = (data) =>
    http({
        url: "/lumen-task/select-tasks",
        method: "get",
    });
/**
 * 获取任务中心领取记录
 * @param {} data 
 * @returns 
 */
export const getTaskRewardLogApi = (data) =>
    http({
        url: "/lumen-task/get-task-reward-log",
        method: "post",
        data,
        isForm: true,
    });

/**
 * 领取任务奖励
 * @param {} data 
 * @returns 
 */
export const getTaskRewardApi = (data) =>
    http({
        url: "/lumen-task/receive-task-reward",
        method: "post",
        data,
    });

/**
 * 完成加入社区或者分享图片的任务
 * @param {} data 
 * @returns 
 */
export const finishTasksApi = (data) =>
    http({
        url: "/lumen-task/finish-tasks",
        method: "post",
        data,
        isForm: true,
    });