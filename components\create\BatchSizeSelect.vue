<template>
    <!-- 一批次生图数量选择 3、4为会员专享 -->
    <div
        class="w-full"
        :class="{
            'cursor-not-allowed': disabled,
        }"
    >
        <p class="w-full text-text-4 text-sm mb-2">{{ $t("SUBSCRIBE_FEATURES_TABLE_5_1") }}</p>
        <div class="flex items-center gap-2">
            <template v-for="item in batchSizeList" :key="item.value">
                <VipBtn
                    v-if="item.vipOnly"
                    class="batch-item justify-center"
                    :class="[
                        disabled ? 'cursor-not-allowed text-text-tab-8 bg-fill-tab-5' : 'cursor-pointer bg-fill-tab-5 text-text-tab-5 hover:text-text-tab-6 hover:!bg-fill-tab-6',
                        myBatchSize === item.value ? 'active' : '',
                    ]"
                    display="after"
                    :icon-size="16"
                    :disabled="disabled && item.value !== myBatchSize"
                    auth
                    @click="handleBatchSizeClick(item)"
                >
                    <span>{{ item.value }}</span>
                </VipBtn>
                <div
                    class="batch-item"
                    :class="[
                        disabled ? 'cursor-not-allowed text-text-tab-8 bg-fill-tab-5' : 'cursor-pointer bg-fill-tab-5 text-text-tab-5 hover:text-text-tab-6  hover:!bg-fill-tab-6',
                        myBatchSize === item.value ? 'active' : '',
                    ]"
                    v-else
                    @click="handleBatchSizeClick(item)"
                >
                    <span>{{ item.value }}</span>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup>
import { useVipNotice } from "@/hook/subscribe";
const { checkShowVipNotice } = useVipNotice();
const emit = defineEmits(["change"]);
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
    batch_size: {
        type: Number,
        default: 2,
    },
});
const batchSizeList = [
    { value: 1, vipOnly: false },
    { value: 2, vipOnly: false },
    { value: 3, vipOnly: true },
    { value: 4, vipOnly: true },
];

const myBatchSize = computed(() => props.batch_size);
const handleBatchSizeClick = (item) => {
    if (props.disabled) return;
    if (item.vipOnly) {
        checkShowVipNotice(`switch_batchSize_${item.value}`);
    }
    emit("change", item.value);
};
</script>
<style scoped lang="scss">
.batch-item {
    @apply w-[23%] lg:w-[76px]  h-10  gap-1.5 !text-[14px] text-xs rounded-full py-3 flex  justify-center items-center flex-grow lg:flex-grow-0 transition-all duration-[250] !font-medium;
}
.active {
    @apply text-text-tab-7 !bg-fill-tab-7 opacity-100;
}
</style>
