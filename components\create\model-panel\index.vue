<template>
    <div>
        <div @click="handleStateChange(true)">
            <slot name="trigger"> </slot>
        </div>
        <!-- Pc 中间弹窗 -->
        <n-modal v-model:show="showModels" placement="right" raw trigger="click" v-if="!isIpad" class="!bg-bg-2 !rounded-2xl">
            <div class="w-full tooltip-container max-w-[1200px] h-[80vh] flex flex-col relative mx-6 lg:mx-auto !p-0 overflow-y-auto no-scroll">
                <div class="flex px-6 items-center justify-between h-[64px] shrink-0">
                    <span class="text-text-1 text-base font-bold">{{ $t("CONFIG_BASE_MODEL") }}</span>
                    <IconsClose class="w-5 h-5 text-text-4 hover:text-text-1 cursor-pointer" @click="handleStateChange(false)" />
                </div>
                <div class="flex w-full flex-1 pt-4 pb-6 pr-6 h-[calc(100%-64px)]">
                    <div class="w-full pl-6 scroll-container flex-1 !pr-0">
                        <div v-for="(lineItem, lineIndex) in modelGroupList" :key="lineIndex">
                            <div class="flex items-center gap-1 h-10">
                                <span class="text-sm text-text-4 font-medium">{{ t(lineItem.name) }}</span>
                                <n-tooltip :show-arrow="false" placement="right" raw trigger="hover">
                                    <template #trigger>
                                        <IconsAlert class="text-text-4 cursor-pointer select-none" />
                                    </template>
                                    <div class="max-w-72 p-2 bg-bg-5 border border-solid border-border-t-1 rounded-lg text-sm text-text-3 font-medium">{{ t(lineItem.tooltip) }}</div>
                                </n-tooltip>
                                <span class="text-xs bg-clip-text model-tip font-medium ml-3">{{ lineItem.model_tip ? $t(lineItem.model_tip) : "" }}</span>
                            </div>

                            <div class="flex gap-3 my-2 flex-wrap">
                                <div
                                    v-for="(item, index) in lineItem.model"
                                    :key="index"
                                    class="flex justify-between items-center gap-3 rounded-lg relative cursor-pointer select-none w-[126px] h-[160px] overflow-hidden shrink-0"
                                    :class="{ 'selected-border': item.id === selectedItem.modelId }"
                                    @click="checkModel(item, item.id === selectedItem.modelId)"
                                    @dblclick="checkModel(item, true)"
                                >
                                    <div v-if="item.isNew" class="new-tag absolute top-0 left-0 text-xs px-2 py-0.5 rounded-[8px_2px_12px_2px] z-[100] text-white">NEW</div>
                                    <div class="flex flex-col items-center">
                                        <div class="shrink-0">
                                            <img :src="item.cover" alt="" class="size-[126px] object-cover" :class="[item.id === selectedItem.modelId ? 'rounded-t-[4px]' : 'rounded-t-lg']" />
                                        </div>
                                        <div class="w-full flex items-center justify-center gap-1 py-2 bg-fill-wd-0 rounded-b-lg">
                                            <p class="text-xs text-text-2">{{ item.label ? item.label : " " }}</p>
                                            <img src="@/assets/images/subscribe/icon_fo_all_member.webp" class="size-4" alt="" v-if="item.isVipModel" />
                                        </div>
                                    </div>
                                    <div class="selected-mask rounded-[6px]" :class="[item.id === selectedItem.modelId ? 'opacity-100' : 'opacity-0']"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="model-display w-[316px] rounded-xl border-[2px] border-dotted border-border-1 h-full relative z-[0] bg-fill-dw-10 overflow-hidden flex flex-col">
                        <div class="w-[calc(100%-2px)] absolute h-[208px] overflow-hidden left-[1px] top-[1px] rounded-t-xl">
                            <img :src="selectedItem.icon || ''" alt="" class="object-cover opacity-40 w-full transform translate-y-[-20%] z-[1]" />
                            <div class="img-mask absolute top-0 bottom-0 left-0 right-0 z-[2]"></div>
                        </div>
                        <div class="w-full p-6 pb-4 z-[10] relative">
                            <div class="flex gap-2 items-center">
                                <span class="text-text-1 text-xl font-semibold mb-2">{{ selectedItem.label }}</span>
                                <img src="@/assets/images/subscribe/icon_fo_all_member.webp" class="size-4 mb-2" alt="" v-if="selectedItem.isVipModel" />
                            </div>
                            <p v-if="selectedItem.descLangKey" class="text-text-3 font-medium text-sm mb-2 leading-[22px]">{{ t(selectedItem.descLangKey) }}</p>
                            <div class="flex gap-1.5 items-center text-xs font-medium">
                                <template v-for="tag in selectedItem.tags">
                                    <span
                                        class="py-1 px-1.5 rounded-[4px] backdrop-blur-sm"
                                        :class="[tag.type === 'primary' ? 'bg-fill-tag-green-t-3 text-success-6' : 'bg-fill-tag-gray-t-2 text-text-4']"
                                        >{{ tag.text }}</span
                                    >
                                </template>
                            </div>
                        </div>
                        <div class="flex flex-col gap-3 w-full items-center relative z-[5] flex-1 overflow-auto no-scroll">
                            <template v-for="imgItem in selectedItem.exampleImgs" :key="imgItem">
                                <img :src="imgItem" class="w-[264px] h-[176px] rounded-xl" alt="" />
                            </template>
                        </div>
                        <div class="w-full shrink-0 p-6 pt-4 h-20">
                            <Button class="w-full" @click="handleConfirmChoose">{{ $t("MODEL_SELECT") }}</Button>
                        </div>
                    </div>
                </div>
            </div>
        </n-modal>
        <!-- H5 底部弹窗 -->
        <CommonH5Popup v-model:show="showModels">
            <div class="gap-4 w-full h-[90dvh] pb-20 overflow-hidden">
                <div class="w-full flex justify-between items-center my-5">
                    <span class="rounded-full size-8 bg-fill-wd-1 hover:bg-fill-wd-2 flex items-center justify-center cursor-pointer" @click.stop="showModels = false">
                        <IconsArrowLeft />
                    </span>
                    <p class="text-text-1 font-medium text-base">{{ $t("CONFIG_BASE_MODEL") }}</p>
                    <span class="text-primary-6 font-medium cursor-pointer text-base" @click.stop="handleConfirmChoose">{{ $t("DONE") }}</span>
                </div>
                <div class="w-full scroll-container max-h-full pb-4 scroll-container no-scroll">
                    <div v-for="(lineItem, lineIndex) in modelGroupList" :key="lineIndex">
                        <div class="flex items-center gap-1 h-10">
                            <span class="text-sm text-text-4 font-medium">{{ t(lineItem.name) }}</span>
                            <n-tooltip :show-arrow="false" placement="bottom" raw trigger="hover">
                                <template #trigger>
                                    <IconsAlert class="text-text-4 cursor-pointer select-none" />
                                </template>
                                <div class="max-w-72 p-2 bg-bg-5 border border-solid border-border-t-1 rounded-lg text-sm text-text-3 font-medium">{{ t(lineItem.tooltip) }}</div>
                            </n-tooltip>
                        </div>

                        <div class="flex flex-col gap-4 my-2">
                            <div
                                v-for="(item, index) in lineItem.model"
                                :key="index"
                                class="flex justify-between items-center gap-3 rounded-lg relative cursor-pointer select-none overflow-hidden"
                                :class="{ '!bg-fill-wd-1': item.id === selectedItem.modelId }"
                                @click="checkModel(item, false)"
                                @dblclick="checkModel(item, true)"
                            >
                                <div class="flex gap-3 items-center">
                                    <div class="size-20 relative shrink-0">
                                        <img :src="item.cover" class="size-full object-cover rounded-lg" />
                                    </div>
                                    <div class="flex-col">
                                        <div class="flex items-center gap-2">
                                            <span class="text-text-2"> {{ item.label ? item.label : " " }}</span>
                                            <img src="@/assets/images/subscribe/icon_fo_all_member.webp" class="size-4" alt="" v-if="item.isVipModel" />
                                            <div v-if="item.isNew" class="px-1 py-0.5 bg-danger-6 rounded-full text-xs text-text-white font-medium group-hover:opacity-0 transition-all">NEW</div>
                                        </div>
                                        <div class="text-text-4 text-sm">
                                            <span>{{ item.descLangKey ? $t(item.descLangKey) : " " }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </CommonH5Popup>
    </div>
</template>

<script setup>
import { modelIconMapping } from "@/utils/constant";
import { debounce, renderStaticImage } from "@/utils/tools";
import { useUserProfile } from "@/stores";
import { useSupportModelList } from "@/stores/create";
import { useGetModelInfo, isModelValidForMe } from "@/hook/create.js";

import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";
import { t } from "@/utils/i18n-util";
import ModelFeatureModal from "./ModelFeatureModal.vue";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
const { isIpad } = storeToRefs(useThemeStore());
const emit = defineEmits(["forbid", "change", "update:showNewTag"]);
const props = defineProps({
    modelId: {
        type: String,
        default: "",
    },
    showNewTag: {
        type: Boolean,
        default: false,
    },
});
const showModels = ref(false);
let allNewRead = true;

/**
 * 打开 关闭弹窗
 * @param val
 */
const handleStateChange = (val) => {
    showModels.value = val;
    if (val) {
        myId.value = props.modelId;
        window.trackEvent("Create", { el: "model_menu_click" });
        if (!allNewRead) {
            const userProfile = useUserProfile();
            userProfile.syncUpdateUserConfig({
                readNewModelIds: newModelIds,
            });
            emit("update:showNewTag", false);
        }
    }
};
const supportModelList = useSupportModelList();
// 模型分组列表数据填充
const modelGroupList = computed(() => {
    const list = supportModelList.modelList;
    return modelIconMapping.reduce((result, line) => {
        const listKey = Object.keys(line.model);
        const targetIdList = list.filter((item) => listKey.includes(item.value));
        const model = targetIdList.map((item) => ({
            label: item.label,
            id: item.value,
            ...line.model[item.value],
        }));
        return result.concat({ ...line, model });
    }, []);
});

const { checkShowVipNotice } = useVipNotice();
/**
 * 检测模型权限 并选中模型 进行预览
 * @param {object} item 模型对象
 * @param {boolean} confirm 是否直接确认 confirm为false代表 仅在组件中预览 不触发确认
 * @param {string} forbidAction 模型被阻止使用后的动作类型 modal弹出弹框 switchModel 切换默认模型 （组件内部不做处理 组件外部可自行决定是否使用）
 *
 */
const checkModel = debounce((item, confirm = false, forbidAction = "modal") => {
    const { id, isVipModel } = item;
    const isModelCanUse = isModelValidForMe(id);
    console.log("checkModel", item, isModelCanUse);
    myId.value = id;
    if (isModelCanUse) {
        confirm && handleConfirm();
        if (isVipModel && confirm) {
            checkShowVipNotice(`model_${id}`);
        }
    } else if (!isModelCanUse && confirm) {
        emit("forbid", forbidAction);
    }
}, 50);

/**
 * 确认模型选中 保存按钮的操作
 */
const handleConfirmChoose = () => {
    let currentItem = useGetModelInfo(myId.value);
    window.trackEvent("Create", { el: "model_select_popup_select" });
    checkModel(
        {
            ...currentItem,
            id: currentItem.value,
        },
        true
    );
};
//确认选中模型 双击直接确认的操作
const handleConfirm = debounce(() => {
    if (import.meta.client) {
        window.trackEvent("Create", { el: `model=${myId.value}` });
    }
    emit("change", myId.value);
    handleStateChange(false);
}, 150);

let newModelIds = [];
onMounted(() => {
    //更新new tag展示状态
    const { userConfig } = useUserProfile();
    const { readNewModelIds = [] } = userConfig;
    // 提取所有模型的 id
    modelIconMapping.flatMap((group) =>
        Object.values(group.model)
            .map((model) => {
                if (model.isNew) {
                    newModelIds.push(model.id);
                }
            })
            ?.filter(Boolean)
    );
    allNewRead = newModelIds.every((item) => readNewModelIds.includes(item));
    emit("update:showNewTag", !allNewRead);
});

// 当前正在查看的 选中模型
const selectedItem = computed(() => {
    const model = useGetModelInfo(myId.value);
    return model;
});
const myId = ref(props.modelId);
watch(
    () => props.modelId,
    (val) => {
        // if (val) {
        myId.value = val;
        const planToCheckItem = useGetModelInfo(val);
        checkModel(planToCheckItem, true, "switchModel");
        // } else {
        // checkModel(defaultModel, true, "switchModel");
        // }
    },
    {
        immediate: true,
    }
);
</script>

<style lang="scss" scoped>
.model-info {
    @apply flex items-center justify-center absolute left-0 right-0 bottom-0 backdrop-blur-lg gap-1;
}

.model-tip {
    background-image: linear-gradient(90deg, #00c19a 0%, #20b761 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.img-mask {
    background-image: linear-gradient(to bottom, var(--p-fill-gradient-9) 40%, var(--p-fill-gradient-10)); /* 设置30%分界点 */
}
</style>
