<!--
 * @Author: HuangQS
 * @Description:Kontext提示弹出框
 * @Date: 2025-08-05 14:34:33
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-08-13 17:43:56
-->

<template>
    <div class="w-[1100px] h-[600px] flex relative overflow-hidden">
        <!-- 左侧 文字描述 步骤选择 -->
        <div class="flex flex-col w-[300px] p-6 gap-4">
            <div class="text-base font-semibold cursor-default">
                <span>{{ t("KONTEXT_MODEL.TITLE", 1) }}</span>
                <span>&nbsp</span>
                <span class="text-primary-6"> {{ t("KONTEXT_MODEL.TITLE", 2) }}</span>
            </div>
            <div class="text-sm font-medium cursor-default">{{ t("KONTEXT_MODEL.DESC") }}</div>

            <div class="flex-1 w-full flex flex-col gap-[1px]">
                <div
                    v-for="(item, index) in steps"
                    :key="index"
                    class="flex font-medium px-3 py-2.5 hover:bg-fill-wd-1 hover:rounded-lg cursor-pointer gap-1"
                    :class="index === currSelectStep && 'bg-fill-wd-1 rounded-lg'"
                    @click="handleStepClick(index)"
                >
                    <div class="text-text-4 min-w-3 text-start">{{ item.step }}.</div>
                    <div class="text-text-2">{{ item.description }}</div>
                </div>
            </div>
            <Button type="primary" class="w-full px-4 bg-primary-6 rounded-lg" @click="handleStartClick()">{{ t("KONTEXT_MODEL.GET_STARTED") }}</Button>
        </div>

        <!-- 右侧 图片展示 -->
        <div class="flex flex-1 h-full relative">
            <template v-for="(item, index) in steps">
                <img v-if="index === currSelectStep" :src="item.cover" class="absolute inset-0 size-full object-cover" />
            </template>

            <!-- 输入框 -->
            <div v-if="currInputValue" :key="currSelectStep" class="absolute bg-bg-2 left-6 right-6 bottom-6 px-4 py-2 rounded-lg">
                <!-- <span class="text-text-2 text-sm font-medium cursor-pointer">{{ currInputValue }}</span> -->

                <Typewriter :fullText="currInputValue" :is-word-out="false" :speed="20" />
            </div>
        </div>
    </div>
</template>

<script setup>
const currSelectStep = ref(0);

const iconModules = {
    ...import.meta.glob("@/assets/images/kontext/*", { eager: true, query: "?url", import: "default" }),
};
const getModelIcon = (imgPath) => {
    if (import.meta.server) {
        return "";
    }

    const key = `/assets/images/${imgPath}`;
    return iconModules[key] || "";
};

const steps = ref([
    { step: 1, cover: getModelIcon("kontext/img_kontext_1.webp"), description: "Your images" },
    { step: 2, cover: getModelIcon("kontext/img_kontext_2.webp"), description: "“Put both hands into one's pockets.”", input: "Put both hands into one's pockets." },
    { step: 3, cover: getModelIcon("kontext/img_kontext_3.webp"), description: "“Two different people.”", input: "Two different people." },
    { step: 4, cover: getModelIcon("kontext/img_kontext_4.webp"), description: "“To change clothes for two people.”", input: "To change clothes for two people." },
    { step: 5, cover: getModelIcon("kontext/img_kontext_5.webp"), description: "“They are drinking at the bar.”", input: "They are drinking at the bar." },
]);

const currInputValue = computed(() => {
    return steps.value[currSelectStep.value]?.input || "";
});
const autoStepTimer = ref(null);
const emits = defineEmits(["cancel"]);

onMounted(async () => {
    await new Promise((resloe) => setTimeout(resloe, 1000));
    autoStepTimer.value = setInterval(refreshAutoStep, 3000); // 自动切换图片
});

const refreshAutoStep = () => {
    currSelectStep.value = (currSelectStep.value + 1) % steps.value.length;
};

const handleStepClick = (index) => {
    currSelectStep.value = index;
    clearStepTimer();
};

const handleStartClick = () => {
    clearStepTimer();
    emits("cancel");
};
const clearStepTimer = () => {
    if (autoStepTimer.value) {
        clearInterval(autoStepTimer.value);
        autoStepTimer.value = null;
    }
};

onBeforeUnmount(() => {
    clearStepTimer();
});
</script>

<style lang="scss" scoped>
.to-show {
    animation: fadeIn 0.3s ease-in-out;
    animation-fill-mode: forwards;
    // 设置图片禁止被点击、拖动
    pointer-events: none;
}
.to-hide {
    animation: fadeOut 0.3s ease-in-out;
    animation-fill-mode: forwards;
}
// to-show

@keyframes fadeIn {
    from {
        opacity: 0.6;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
</style>
