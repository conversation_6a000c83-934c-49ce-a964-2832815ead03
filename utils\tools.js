export * from "./subscribeTools.js";
export * from "./common.js";
import {
    fluxModelId,
    fluxDevModelId,
    lineArtModelId,
    realisticModelId,
    animeModelId,
    ponyV6ModelId,
    MjModelId,
    fluxKontextModelId,
    fluxKreaModelId,
    picLumenArtV1Id,
    NamiyaModelId,
    modelIconMapping,
    SHAPE_ALL,
    SAMPLER_LIST,
    SCHEDULER_LIST,
    LUMEN_EXPEND_DICT,
    defaultModel,
    defaultRatio,
    MAX_SEED,
} from "@/utils/constant";

//返回当前模型版本支持的比例
export const getSupportResolution = (baseVersion) => {
    // console.log(baseVersion, 'baseVersion')
    return SHAPE_ALL.filter((item) => item.baseVersion?.includes(baseVersion)).map((item) => ({ ...item, key: item.label }));
};

//copy
export const copyToClipboard = (text) => {
    return new Promise((resolve, reject) => {
        if (navigator.clipboard) {
            navigator.clipboard
                .writeText(text)
                .then(() => {
                    resolve();
                })
                .catch(() => {
                    reject();
                });
        } else {
            try {
                const textarea = document.createElement("textarea");
                textarea.value = text;
                textarea.style.position = "absolute";
                textarea.style.zIndex = -1;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                document.body.removeChild(textarea);
                resolve();
            } catch (error) {
                reject();
            }
        }
    });
};
//加载外部script
export const loadScript = (link, options = {}, dataset = {}) => {
    return new Promise((resolve, reject) => {
        if (options.id && document.getElementById(options.id)) {
            resolve();
            return;
        }
        const outScript = document.createElement("script");
        outScript.type = "text/javascript";
        outScript.src = link;
        for (const key in dataset) {
            outScript.dataset[key] = dataset[key];
        }
        for (const key in options) {
            outScript[key] = options[key];
        }
        outScript.onload = resolve;
        outScript.onerror = reject;
        document.body.appendChild(outScript);
    });
};

//防抖
export const debounce = (fn, delay = 300) => {
    let timer = null;
    return function (...args) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            fn.apply(this, args);
        }, delay);
    };
};

// 防抖-立即执行
export const immediateDebounce = (func, wait, immediate) => {
    let timeout;

    return function () {
        let context = this;
        let args = arguments;

        if (timeout) clearTimeout(timeout); // timeout 不为null
        if (immediate) {
            let callNow = !timeout; // 第一次会立即执行，以后只有事件执行后才会再次触发
            timeout = setTimeout(function () {
                timeout = null;
            }, wait);
            if (callNow) {
                func.apply(context, args);
            }
        } else {
            timeout = setTimeout(function () {
                func.apply(context, args);
            }, wait);
        }
    };
};

//节流---raf
export const rafThrottle = (fn) => {
    let lock = false;
    return function (...args) {
        if (lock) return;
        lock = true;
        window.requestAnimationFrame(() => {
            fn.apply(this, args);
            lock = false;
        });
    };
};
//节流 --- 服务端兼容的简单
export const throttle = (fn, delay = 100) => {
    let last = 0;
    return function (...args) {
        const now = Date.now();
        if (now - last > delay) {
            last = now;
            fn.apply(this, args);
        }
    };
};
//判断触底（触2/3）
export const isScrolledToBottom = (element, distance = 1000) => {
    return element.scrollHeight - element.scrollTop <= element.clientHeight + Number(distance);
};

import { useUserProfile } from "@/stores";
import { useSupportModelList } from "@/stores/create";
//根据模型名称 获取模型ID
const getModelByName = (name) => {
    const { modelList } = useSupportModelList();
    const nameLowercase = name.toLowerCase();
    const info = modelList.find((item) => item.label.toLowerCase() === nameLowercase) || {};
    return info;
};

// 参数解析
import { promptMagicValid } from "./magic.js";
export const parsePrompt = (str = "") => {
    try {
        if (!str || typeof str !== "string" || !str.includes("--piclumen")) return false;
        const params = str.split("--piclumen");
        let prompt = params[0] || "";
        prompt = prompt.slice(0, 2500);
        str = params[1] || str;
        //prompt 解析
        const keyMap = [
            {
                fullName: "--resolution",
                simpleName: "-rs",
                key: "aspect_pixel",
            },
            {
                fullName: "--negative-prompt",
                simpleName: "-np",
                key: "negative_prompt",
            },
            {
                fullName: "--guidance",
                simpleName: "-g",
                key: "cfg",
            },
            {
                fullName: "--steps",
                simpleName: "-s",
                key: "steps",
            },
            {
                fullName: "--seed",
                simpleName: "-e",
                key: "seed",
            },
            {
                fullName: "--batch-size",
                simpleName: "-bs",
                key: "batch_size",
            },
            {
                fullName: "--model-name",
                simpleName: "-mn",
                key: "model_name",
            },
            {
                fullName: "--source-type",
                simpleName: "-st",
                key: "source_type",
            },
            {
                fullName: "--prompt-magic",
                simpleName: "-pm",
                key: "magic",
            },
        ];
        const result = {};
        if (prompt) {
            result["prompt"] = prompt;
        }
        keyMap.forEach((item) => {
            // 用正则匹配完整和简写的参数
            const fullPattern = new RegExp(`${item.fullName} "([^"]+)"|${item.fullName} ([^ ]+)`);
            const simplePattern = new RegExp(`${item.simpleName} "([^"]+)"|${item.simpleName} ([^ ]+)`);
            // 尝试匹配 fullName 和 simpleName
            const fullMatch = str.match(fullPattern);
            const simpleMatch = str.match(simplePattern);
            if (fullMatch) {
                result[item.key] = fullMatch[1] || fullMatch[2]; // 获取匹配的值
            } else if (simpleMatch) {
                result[item.key] = simpleMatch[1] || simpleMatch[2];
            }
        });
        let batch_size = Math.max(Math.min(Number(result.batch_size || 1), 4), 1);
        if (isNaN(batch_size)) {
            batch_size = 1;
        }
        if (result.aspect_pixel) {
            const ar = result.aspect_pixel.split(":");
            const res = autoMatchResolution({ width: ar[0], height: ar[1] });
            const resolution = {
                batch_size: batch_size,
                width: res.width || 1024,
                height: res.height || 1024,
            };
            delete result.aspect_pixel;
            result.resolution = resolution;
            result.size = `${resolution.width} x ${resolution.height}`;
        }
        result.batch_size = batch_size;

        if (result.cfg) {
            let cfg = Math.max(Math.min(Number(result.cfg), 30), 1);
            if (isNaN(cfg)) {
                delete result.cfg;
            } else {
                result.cfg = cfg;
            }
        }
        if (result.steps) {
            let steps = Math.max(Math.min(Number(result.steps), 60), 1);
            if (isNaN(steps)) {
                delete result.steps;
            } else {
                result.steps = steps;
            }
        }
        if (result.seed) {
            let seed = Number(result.seed);
            if (isNaN(seed) || seed > MAX_SEED || seed == 0) {
                seed = -1;
            }
            result.seed = seed;
        }

        if (result.magic) {
            const [mainCategory, subCategory] = result.magic.split(":");
            if (promptMagicValid(mainCategory, subCategory)) {
                delete result.magic;
                result.mainCategory = mainCategory;
                result.subCategory = subCategory;
            }
        }

        if (result.model_name) {
            const { defaultParams, value } = getModelByName(result.model_name);
            if (value) {
                delete result.model_name;
                result.model_id = value;
            }
            if (!result.cfg && value) {
                result.cfg = defaultParams.cfg;
            }
            if (!result.steps && value) {
                result.steps = defaultParams.steps;
            }
        }

        if (result.negative_prompt == "''" || result.negative_prompt == '""') {
            result.negative_prompt = "";
        }
        result.negative_prompt = (result.negative_prompt || "").slice(0, 2500);
        return result;
    } catch (error) {
        return false;
    }
};
//复制全部参数
export const copyAllParams = (task, modelName) => {
    let str = `${task.prompt || ""} --piclumen -rs ${task.realWidth}:${task.realHeight} -np "${task.negative_prompt || ""}"`;
    if (task.cfg) {
        str += ` -g ${task.cfg}`;
    }
    if (task.steps) {
        str += ` -s ${task.steps}`;
    }
    if (task.resolution?.batch_size) {
        str += ` -bs ${task.resolution.batch_size}`;
    }
    if (task.seed) {
        let finalSeed = task.seed > MAX_SEED ? -1 : task.seed;
        str += ` -e ${finalSeed}`;
    }
    if (task.originCreate) {
        str += ` -st "${task.originCreate}"`;
    }
    if (task.mainCategory && task.subCategory) {
        str += ` -pm "${task.mainCategory}:${task.subCategory}"`;
    }
    if (modelName) {
        str += ` -mn "${modelName}"`;
    }
    copyToClipboard(str);
};

import { t } from "@/utils/i18n-util.js";
//下载跨域图片
export const downloadImage = (url, type = "jpg") => {
    openToast.success(t("TOAST_DOWNLOAD"), 5e3);
    return new Promise((resolve, reject) => {
        const imgType = {
            jpg: {
                type: "image/jpeg",
                ext: ".jpg",
            },
            png: {
                type: "image/png",
                ext: ".png",
            },
        };
        const xhr = new XMLHttpRequest();
        xhr.open("GET", url, true);
        xhr.responseType = "blob";
        xhr.onprogress = function (event) {
            if (event.lengthComputable) {
                const percentComplete = (event.loaded / event.total) * 100;
                // 更新进度条等UI元素
            } else {
            }
        };
        xhr.onload = function () {
            if (this.status == 200) {
                const blob = new Blob([this.response], {
                    type: "application/octet-stream",
                });
                const downloadUrl = URL.createObjectURL(blob);
                const img = new Image();
                img.src = downloadUrl;
                img.crossOrigin = "anonymous";
                const canvas = document.createElement("canvas");
                let ctx = canvas.getContext("2d");
                img.onload = async () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    //PNG 100% quality  jpg 75% quality
                    let dataURL = canvas.toDataURL(imgType[type].type, type === "png" ? 1 : 0.75);
                    const a = document.createElement("a");
                    a.href = dataURL;
                    a.download = "piclumen-" + Date.now() + imgType[type].ext; // 设定下载文件名
                    a.click();
                    URL.revokeObjectURL(downloadUrl);
                    resolve();
                };
            } else {
                reject();
            }
        };
        xhr.send();
    });
};

//合并两个任务数组
export const mergeArraysById = (array1, array2) => {
    const mergedMap = new Map();
    // 将两个数组合并然后遍历，以减少遍历次数
    [...array1, ...array2].forEach((item) => {
        if (mergedMap.has(item.markId)) {
            // 如果已存在，直接在原对象上更新属性
            const existingItem = mergedMap.get(item.markId);
            //取出非-1 最小的索引
            let index;
            const indexNums = [existingItem.index, item.index].filter((i) => i != -1);
            if (indexNums.length === 0) {
                index = -1;
            }
            index = Math.min(...indexNums);
            for (const key in item) {
                existingItem[key] = item[key];
            }
            existingItem.index = index;
        } else {
            // 如果不存在，则直接添加
            mergedMap.set(item.markId, item);
        }
    });

    // 从Map中提取合并后的结果数组
    return Array.from(mergedMap.values());
};

//时间本地化
export const dateToLocal = (dateString, isGMT = false) => {
    if (!dateString) {
        return "";
    }
    //获取本地时间与0时区的时差 unit=> ms
    const diffTime = new Date().getTimezoneOffset() * 60 * 1000;
    //将服务器时间(东八区)处理为0时区时间
    let date = new Date(dateString);
    let def_diff = -8 * 60 * 60 * 1000 - diffTime;
    if (isGMT) {
        def_diff = 0; // 如果是GMT，则时区不发生偏移
    }
    date.setTime(date.getTime() + def_diff);
    // 获取年、月、日、小时、分钟
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, "0"); // 月份是从0开始的
    let day = date.getDate().toString().padStart(2, "0");
    let hours = date.getHours();
    let minutes = date.getMinutes().toString().padStart(2, "0");
    // 时间戳
    const timestamp = date.getTime();
    return { year, month, day, hours, minutes, timestamp };
};

/**
 * 转换时间戳为正常时间展示
 * @param {*} timestampInt int类型的时间戳
 * @param {*} isSec 是否为毫秒， false:自动x1000
 */
export const converTimeByTimestamp = (timestampInt, isSec = false) => {
    const sec = isSec ? 1 : 1000;
    try {
        const date = new Date(Number(timestampInt) * sec); // 将秒转换为毫秒
        const year = date.getFullYear(); // 获取年份
        const month = (date.getMonth() + 1).toString().padStart(2, "0"); // 月份是从0开始的，所以要加1并补齐为两位数
        const day = date.getDate().toString().padStart(2, "0"); // 日期补齐为两位数
        const hours = date.getHours().toString().padStart(2, "0"); // 小时补齐为两位数
        const minutes = date.getMinutes().toString().padStart(2, "0"); // 分钟补齐为两位数
        const seconds = date.getSeconds().toString().padStart(2, "0"); // 秒数补齐为两位数
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (e) {
        return "-";
    }
};

//获取东八区时间
export const getCurrentISO8601Time = () => {
    let now = new Date();
    //为了和数据库统一，此处需将时间处理为东八的 UTC+8:00
    let diff = now.getTimezoneOffset() * 60 * 1000; // 获取时区偏移量（ms）
    let gmt_8 = now.getTime() + diff + 8 * 60 * 60 * 1000; // 东八区时间 ms
    now.setTime(gmt_8);
    let iso8601Time = now.toISOString();
    return iso8601Time;
};
//日期格式化
export const formatDate = (dateString) => {
    if (!dateString) {
        return "";
    }
    let { year, month, day, hours, minutes } = dateToLocal(dateString);
    // 确定上午或下午
    let period = hours >= 12 ? "PM" : "AM";
    // 如果是下午且小时数不是12，则小时数减去12
    if (period === "PM" && hours !== 12) {
        hours -= 12;
    }
    // 如果小时数是0（午夜），则显示为12
    hours = hours === 0 ? 12 : hours;
    // 格式化日期和时间字符串
    let formattedDate = `${month}/${day}/${year.toString().slice(2)} at ${hours}:${minutes} ${period}`;
    return formattedDate;
};

/**
 * 日期格式 简写 格式化
 * @param timestamp 日期 或时间戳格式
 * @param isGMT Boolean 标记是否是 GMT 时间 默认值false
 * @returns {Object: {
 * year, 年
 * month, 月份 从1开始的
 * day, 日
 * diff, 和今天相差几天
 * }}
 */
const monthsAbbreviation = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
export const formatDateShort = (timestamp, isGMT = false) => {
    let { year, month, day } = dateToLocal(timestamp, isGMT);
    const diff = getDayDifference(`${year}/${month}/${day}`);
    if (diff === 0) {
        return {
            year,
            month,
            day,
            diff,
            shortStr: null,
            shortStrKey: `SHORT_TODAY`,
        };
    }
    if (diff === -1) {
        return {
            year,
            month,
            day,
            diff,
            shortStr: null,
            shortStrKey: `SHORT_YESTERDAY`,
        };
    }
    if (year && month && day) {
        return {
            year,
            month,
            day,
            diff,
            shortStr: `${monthsAbbreviation[Number(month) - 1]} ${day}, ${year}`,
            shortStrKey: null,
        };
    }
    return {
        year,
        month,
        day,
        diff,
        shortStr: `${timestamp}`,
        shortStrKey: null,
    };
};

//获取比例近似值
const findClosestElement = (arr, target) => {
    return arr.reduce((closest, current) => (Math.abs(current.ratio - target.ratio) < Math.abs(closest.ratio - target.ratio) ? current : closest));
};
export const autoMatchResolution = (resolution) => {
    const list = getSupportResolution("SDXL");
    const ratioList = list.map((item) => ({
        ...item,
        ratio: item.width / item.height,
    }));
    const {
        width = 1024,
        height = 1024,
        value = "1024 x 1024",
        label = "1:1",
    } = findClosestElement(ratioList, {
        ratio: resolution.width / resolution.height,
    });
    return {
        width,
        height,
        size: value,
        label,
    };
};

// 格式化Remix 参数
export const handleResetParams = (item) => {
    let { model_ability, batch_size, resolution, model_id, steps, seed, scheduler, sampler_name, negative_prompt, cfg, prompt, highPixels = false, mainCategory = null, subCategory = null } = item;
    let { animeStyleControl, anime_style_control } = model_ability || {};
    const res = {
        anime: animeStyleControl || anime_style_control || null,
        size: `${resolution.width} x ${resolution.height}`,
        batch_size: batch_size || resolution.batch_size || 1,
        resolution,
        model_id,
        steps,
        seed,
        scheduler,
        sampler_name,
        negative_prompt,
        cfg,
        prompt,
        highPixels,
        mainCategory,
        subCategory,
    };
    return res;
};

// 获取指定日期和当前相差几天
export const getDayDifference = (dateString) => {
    // 获取当前日期
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置时间为当天的 00:00:00
    // 解析传入的日期字符串
    const givenDate = new Date(dateString);
    givenDate.setHours(0, 0, 0, 0); // 设置时间为给定日期的 00:00:00
    // 计算两个日期之间的毫秒数差异
    const differenceInTime = givenDate.getTime() - today.getTime();
    // 将毫秒数转换为天数
    const differenceInDays = Math.round(differenceInTime / (1000 * 3600 * 24));
    return differenceInDays;
};

// 获取此时此刻与指定时间倒计时字符串
/**
 * format
 * @param {*} endTimeInt
 * @param {*} format yyyy-MM-dd HH:mm:ss
 * @returns
 */
export const getCountDownTime = (endTimeInt) => {
    const now = new Date().getTime(); // 当前时间

    const diff = endTimeInt - now; // 时间差（毫秒）
    if (diff <= 0) {
        // 如果时间已过，返回 0
        return {
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0,
        };
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24)); // 天数
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)); // 小时
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)); // 分钟
    const seconds = Math.floor((diff % (1000 * 60)) / 1000); // 秒
    // return `${customFormat(hours)}:${customFormat(minutes)}:${customFormat(seconds)}`;
    return {
        days: days,
        hours: hours,
        minutes: minutes,
        seconds: seconds,
    };
};

export const countDonwTimeFormat = (timeInt) => {
    return timeInt.toString().padStart(2, "0"); // 确保
};

//数据时间分组(第一个不一样的时间的数据添加标记)
export const processData = (list, key = "createTimestamp") => {
    let lastDate = null;
    return list.map((item) => {
        // let { year, month, day } = dateToLocal(item[key]);
        let { year, month, day, diff, shortStr = null, shortStrKey = null } = formatDateShort(item[key]);
        const currentDate = `${year}/${month}/${day}`;
        const newItem = { ...item, showTime: false };
        if (currentDate !== lastDate) {
            newItem.showTime = true;
            // const diff = getDayDifference(currentDate);
            newItem.showDay = {
                year,
                month,
                day,
                diff,
                shortStr,
                shortStrKey,
            };
            lastDate = currentDate;
        }
        return newItem;
    });
};
import { getCosTmpAuth, getCosTmpAuthForRembg } from "@/api";
export const uploadToCos = (options = {}) => {
    return new Promise(async (resolve, reject) => {
        let { file, originalFileName, updateProgress, type = null } = options;
        try {
            const COS = (await import("cos-js-sdk-v5")).default;
            // console.trace("cos config:", COS);
            if (typeof originalFileName !== "string") {
                originalFileName = file.name;
            }
            const fileExt = originalFileName.split(".").pop().toLowerCase();
            const { data, status, message } = await getCosTmpAuth({
                fileExt,
                type,
                name: originalFileName,
            });
            if (status !== 0) {
                reject(message);
                return;
            }
            const { bucket, region, fileName, response, fullPath } = data;
            const { credentials, startTime, expiredTime } = response || {};
            const cos = new COS({
                SecretId: credentials.tmpSecretId, // sts服务下发的临时 secretId
                SecretKey: credentials.tmpSecretKey, // sts服务下发的临时 secretKey
                SecurityToken: credentials.sessionToken, // sts服务下发的临时 SessionToken
                StartTime: startTime, // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
                ExpiredTime: expiredTime, // 临时密钥过期时间
            });
            const uploadConf = {
                Bucket: bucket /* 填写自己的 bucket，必须字段 */,
                Region: region /* 存储桶所在地域，必须字段 */,
                Key: fileName /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */,
                Body: file, // 上传文件对象
                SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
                onProgress: function ({ loaded, total }) {
                    if (typeof variable === "function") {
                        updateProgress({ percent: Math.floor((loaded / total) * 100) });
                    }
                },
            };
            cos.uploadFile(uploadConf, (err, res) => {
                console.log("上传结束", err, res);
                if (err) {
                    reject();
                } else {
                    resolve({ ...res, fullPath });
                }
            });
        } catch (error) {
            console.log(error);
            reject();
        }
    });
};
export const uploadToCosNew = (options = {}, apiType = "common") => {
    const apiMap = {
        common: getCosTmpAuth,
        batch_rmbg: getCosTmpAuthForRembg,
    };
    return new Promise(async (resolve, reject) => {
        let { file, originalFileName, updateProgress } = options;
        try {
            const COS = (await import("cos-js-sdk-v5")).default;
            // console.trace("cos config:", COS);
            if (typeof originalFileName !== "string") {
                originalFileName = file.name;
            }
            const fileExt = originalFileName.split(".").pop().toLowerCase();
            const getMappedAuth = apiMap[apiType] || apiMap.common;
            const res = await getMappedAuth({
                fileExt,
                name: originalFileName,
                ...options,
            });
            const { data, status, message } = res;
            if (status !== 0) {
                reject(res);
                return;
            }
            const { bucket, region, fileName, response, fullPath } = data;
            const { credentials, startTime, expiredTime } = response || {};
            const cos = new COS({
                SecretId: credentials.tmpSecretId, // sts服务下发的临时 secretId
                SecretKey: credentials.tmpSecretKey, // sts服务下发的临时 secretKey
                SecurityToken: credentials.sessionToken, // sts服务下发的临时 SessionToken
                StartTime: startTime, // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
                ExpiredTime: expiredTime, // 临时密钥过期时间
            });
            const uploadConf = {
                Bucket: bucket /* 填写自己的 bucket，必须字段 */,
                Region: region /* 存储桶所在地域，必须字段 */,
                Key: fileName /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */,
                Body: file, // 上传文件对象
                SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
                onProgress: function ({ loaded, total }) {
                    if (typeof variable === "function") {
                        updateProgress({ percent: Math.floor((loaded / total) * 100) });
                    }
                },
            };
            cos.uploadFile(uploadConf, (err, res) => {
                console.log("上传结束", err, res);
                if (err) {
                    reject(err);
                } else {
                    resolve({ ...res, fullPath });
                }
            });
        } catch (error) {
            console.log(error);
            reject(error);
        }
    });
};

// 判断是否为Flux模型
export const isFluxSeries = ({ model_id }) => {
    if (!model_id) return false;
    return [fluxModelId, fluxDevModelId, picLumenArtV1Id, fluxKontextModelId]?.includes(model_id);
};
export const isFluxModel = ({ model_id }) => {
    return fluxModelId === model_id;
};
// 判断是否为FluxDev模型
export const isFluxDevModel = ({ model_id }) => {
    return fluxDevModelId === model_id;
};
// 判断是否为PicLumen Art v1模型
export const isPicLumenArtV1 = ({ model_id }) => {
    return picLumenArtV1Id === model_id;
};
// 判断是否为Line Art模型
export const isLineArtModel = ({ model_id }) => {
    return lineArtModelId === model_id;
};
// 判断是否为真实模型
export const isRealisticModel = ({ model_id }) => {
    return realisticModelId === model_id;
};
// 判断是否为动漫模型
export const isAnimeModel = ({ model_id }) => {
    return animeModelId === model_id;
};
// 判断是否为pony v6模型
export const isPonyV6 = ({ model_id }) => {
    return ponyV6ModelId === model_id;
};
// 判断是否为MJ模型
export const isMjModel = ({ model_id }) => {
    return MjModelId === model_id;
};
// 判断是否为Kontext模型
export const isFluxKontextModel = ({ model_id }) => {
    return fluxKontextModelId === model_id;
};
// 判断是否为Krea模型
export const isFluxKreaModel = ({ model_id }) => {
    return fluxKreaModelId === model_id;
};
// 判断是否为Namiya模型
export const isNamiyaModel = ({ model_id }) => {
    return NamiyaModelId === model_id;
};

export const isVipModel = ({ model_id }) => {
    return getStaticModelById(model_id)?.isVipModel || false;
};

// 获取key匹配constant中的lumen消耗基数
export const getFuncNameByModelId = ({ model_id }) => {
    if (isFluxModel({ model_id })) return "FLUX_1_SCHNELL";
    if (isFluxDevModel({ model_id })) return "FLUX_1_DEV";
    if (isMjModel({ model_id })) return "MJ_MODEL";
    // if (isLineArtModel({ model_id })) return "OTHER";
    // if (isRealisticModel({ model_id })) return "OTHER";
    // if (isAnimeModel({ model_id })) return "OTHER";
    if (isPonyV6({ model_id })) return "PONY_DIFFUSION_V6";
    if (isFluxKontextModel({ model_id })) return "FLUX_KONTEXT";
    if (isFluxKreaModel({ model_id })) return "KREA_MODEL";

    return "OTHER";
};
export const renderModelIcon = (model_id) => {
    const icon = getStaticModelById(model_id)?.icon;
    return icon || defaultModel.icon; // 未找到
};
export const getStaticModelById = (model_id) => {
    for (const group of modelIconMapping) {
        if (group.model && model_id in group.model) {
            return group.model[model_id];
        }
    }
};

export const getResolution = ({ width, height }) => {
    let result = SHAPE_ALL.find((item) => item.width === width && item.height === height);
    return result || defaultRatio;
};

//有效的seed
export const isValidSeed = (seed) => {
    let result = /^[0-9]\d{0,10}$/.test(seed);
    console.log(seed, "seed", result);
    return result;
};

/* ponyV6模型 参数处理
   @param {object} conf - 生图参数
   @param {string} type - 处理类型 ["input", "output"]
*/
export const formatPonyV6Prompt = (conf, type = "input") => {
    if (!isPonyV6(conf)) {
        return conf; // 不是ponyV6模型，返回原来的conf
    }
    const sign = "score_9, score_8_up, score_7_up, score_6_up, score_5_up, score_4_up, ";
    const { prompt = "", ponyTags = {} } = conf;
    if (type === "input") {
        // 后缀
        const suffix = Object.keys(ponyTags).join(", ");
        conf.prompt = `${sign}${prompt}${suffix ? ", " + suffix : ""}`;
    }
    if (prompt && type === "output") {
        conf.prompt = prompt.replace(sign, "");
    }
    return conf;
};
export const browserInfo = () => {
    const userAgent = navigator.userAgent;

    if (userAgent.indexOf("Edg") > -1) {
        return "Edge";
    } else if (userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("OPR") === -1) {
        return "Chrome";
    } else if (userAgent.indexOf("Firefox") > -1) {
        return "Firefox";
    } else if (userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") === -1) {
        return "Safari";
    } else if (userAgent.indexOf("OPR") > -1 || userAgent.indexOf("Opera") > -1) {
        return "Opera";
    } else if (userAgent.indexOf("Trident") > -1) {
        return "Internet Explorer";
    } else {
        return "Other";
    }
};
export const trackEvent = (eventname, { ps = "", el = "" }) => {
    try {
        if (!eventname) return;
        const { loginName } = useUserProfile().user;
        const br = browserInfo();
        window.gtag("event", eventname, {
            uid_custom: loginName,
            pv: import.meta.env.VITE_VERSION, // 版本
            br, // 浏览器
            ps, // 产品来源
            el,
            // debug_mode: true,
        });
    } catch (error) {
        console.error("trackEvent error:", error);
    }
};
// 千分位格式化
export const formatNumber = (num) => {
    if (num === 0 || !num) return "0"; // 特殊处理 0
    const units = ["K", "M", "B"];
    const magnitude = Math.floor(Math.log10(Math.abs(num)) / 3); // 获取数值的千位等级
    if (magnitude === 0) return num.toString(); // 小于 1000 的直接返回
    const scaledNum = (num / Math.pow(1000, magnitude)).toFixed(1); // 按等级缩放数值
    return scaledNum.replace(/\.0$/, "") + units[magnitude - 1]; // 格式化并附上单位
};
// 社区 热门标签集合
export const communityHotTags = [
    `photography`,
    `anime`,
    `clothing`,
    `outdoors`,
    `comics`,
    `woman`,
    `costume`,
    `man`,
    `animal`,
    `armor`,
    `transportation`,
    `architecture`,
    `city`,
    `cartoon`,
    `car`,
    `food`,
    `astronomy`,
    `modern_art`,
    `robot`,
    `landscape`,
    `fantasy`,
    `photorealistic`,
    `game_character`,
    `sci-fi`,
    "nature",
    "object",
    "text",
    "flowers",
    "mineral",
    "indoors",
    "sculpture",
    "portrait",
    "holiday",
    "steampunk",
    "abstract",
    "lineart",
];
// 社区 热门 emoji
export const communityHotEmojis = [
    "🥰",
    "💖",
    "😀",
    "🥳",
    "😮",
    "😁",
    "😪",
    "😊",
    "🙂",
    "🙃",
    "😉",
    "😌",
    "😒",
    "😍",
    "🤩",
    "😘",
    "😗",
    "😚",
    "😙",
    "🤗",
    "🤭",
    "🤫",
    "😋",
    "😛",
    "😜",
    "😝",
    "🤪",
    "🥸",
    "🤠",
    "😎",
    "🤓",
    "😺",
    "😸",
    "😹",
    "😻",
    "😽",
    "😼",
    "🥱",
    "😏",
    "😶",
    "😐",
    "😑",
    "😬",
    "🙄",
    "😯",
    "😲",
    "😏",
    "😱",
    "😳",
    "🥵",
    "🥶",
    "😰",
    "😥",
    "😓",
    "🤤",
    "💯",
    "🔥",
    "🤧",
    "🤒",
    "🤕",
    "😵",
    "🤯",
    "😇",
    "👍",
];
// 评论时间 格式化
export const formatCommentDate = (commentDate) => {
    // 获取本地化时间戳
    const nowTimeStamp = Date.now();
    const realTime = dateToLocal(commentDate);
    const commentTime = new Date(realTime.timestamp);
    const diffInSeconds = Math.floor((nowTimeStamp - realTime.timestamp) / 1000);
    const timeFormats = [
        {
            limit: 60,
            format: (diff) => ({
                diff,
                unit: "COMMUNITY_REPLY_SECOND",
                units: "COMMUNITY_REPLY_SECOND_S",
            }),
        },
        {
            limit: 3600,
            format: (diff) => ({
                diff: Math.floor(diff / 60),
                unit: "COMMUNITY_REPLY_MINUTE",
                units: "COMMUNITY_REPLY_MINUTE_S",
            }),
        },
        {
            limit: 86400,
            format: (diff) => ({
                diff: Math.floor(diff / 3600),
                unit: "COMMUNITY_REPLY_HOUR",
                units: "COMMUNITY_REPLY_HOUR_S",
            }),
        },
        {
            limit: 259200,
            format: (diff) => ({
                diff: Math.floor(diff / 86400),
                unit: "COMMUNITY_REPLY_DAY",
                units: "COMMUNITY_REPLY_DAY_S",
            }),
        },
        {
            limit: Infinity,
            format: () => {
                const year = commentTime.getFullYear();
                const monthAlias = monthsAbbreviation[commentTime.getMonth()];
                const day = String(commentTime.getDate()).padStart(2, "0");
                return {
                    diff: null,
                    unit: null,
                    units: null,
                    label: `${day} ${monthAlias} ${year}`,
                };
            },
        },
    ];
    for (const { limit, format } of timeFormats) {
        if (diffInSeconds < limit) {
            return format(diffInSeconds);
        }
    }
};

import CryptoJS from "crypto-js";
export const strToMd5 = (password) => {
    return CryptoJS.MD5(`${password}HifjoaWIKLejgh`).toString();
};

export const decryptResult = (encryptedData, responseType = "Array") => {
    const key = CryptoJS.enc.Utf8.parse("fclzCSIsBUV1Xk1T"); // 16字节密钥
    const bytes = CryptoJS.AES.decrypt(encryptedData, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    });
    const decrypted = bytes.toString(CryptoJS.enc.Utf8);
    try {
        return JSON.parse(decrypted);
    } catch (error) {
        if (responseType === "Array") {
            return [];
        }
        return {};
    }
};

//邮箱正则校验
export const validateEmail = (email) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
};
//删除对象中为null的属性
export const removeNulls = (obj) => Object.fromEntries(Object.entries(obj).filter(([_, v]) => v !== null));

/**
 * 数字国际化
 * @param  {number} num 要格式化的数字
 * @returns {string} 格式化后的数字
 */
export const numberFormat = (num) => {
    return new Intl.NumberFormat().format(num);
};

/**
 * 比较两个日期
 * @param {Date|string} date1 - 时间（Date对象或ISO字符串）
 * @param {Date|string} date2 - 时间（Date对象或ISO字符串）
 * @returns {Boolean} start 是否在 end
 *
 */
export function dateIsAfter(date1, date2) {
    const d1 = date1 instanceof Date ? date1 : new Date(date1);
    const d2 = date2 instanceof Date ? date2 : new Date(date2);

    if (isNaN(d1) || isNaN(d2)) {
        throw new Error("Invalid date input");
    }

    return d1 > d2;
}

/**
 * 将北京时间字符串转换为本地时间Date对象
 * @param {string} beijingTimeStr - 格式："YYYY-MM-DDTHH:mm:ss"（北京时间）
 * @returns {Date} 转换后的本地时间Date对象
 */
export function convertBeijingToLocal(beijingTimeStr) {
    // 添加UTC+8时区信息并创建Date对象
    const dateWithTimezone = new Date(beijingTimeStr + "+08:00");

    // 验证时间有效性
    if (isNaN(dateWithTimezone.getTime())) {
        throw new Error("Invalid Beijing time format");
    }

    return dateWithTimezone;
}

/**
 * 计算两个时间之间的差值并格式化输出
 * @param {Date|string} start - 开始时间（Date对象或ISO字符串）
 * @param {Date|string} end - 结束时间（Date对象或ISO字符串）
 * @param {Object} [options] - 配置选项
 * @param {string} [options.format] - 格式模板（默认：'{d}d {h}h {m}min'）
 * @param {number} [options.minUnit] - 最小单位（毫秒，默认：60000=1分钟）
 * @param {boolean} [options.roundUp] - 是否向上取整（默认：true）
 * @returns {string} 格式化后的时间差
 */
export function timeDifference(start, end, options = {}) {
    // 参数处理
    const {
        format = "{d}d {h}h {m}min",
        minUnit = 60000, // 默认最小单位1分钟
        roundUp = true,
    } = options;

    // 转换为Date对象
    const startDate = typeof start === "string" ? new Date(start) : start;
    const endDate = typeof end === "string" ? new Date(end) : end;

    // 验证日期有效性
    if (isNaN(startDate)) throw new Error("Invalid start date");
    if (isNaN(endDate)) throw new Error("Invalid end date");

    // 计算差值（毫秒）
    let diffMs = Math.abs(endDate - startDate);

    // 按最小单位处理
    if (minUnit > 1) {
        diffMs = roundUp ? Math.ceil(diffMs / minUnit) * minUnit : Math.floor(diffMs / minUnit) * minUnit;
    }

    // 计算各个单位
    const units = {
        d: Math.floor(diffMs / 86400000), // 天
        h: Math.floor((diffMs % 86400000) / 3600000), // 小时
        m: Math.floor((diffMs % 3600000) / 60000), // 分钟
        s: Math.floor((diffMs % 60000) / 1000), // 秒
        ms: diffMs % 1000, // 毫秒
    };

    // 修复点1：正确转义正则表达式
    const formatted = format.replace(/\{(\w+)\}/g, (_, key) => {
        return units[key] ?? "0"; // 修复点2：处理undefined值
    });

    // 修复点3：优化零值过滤逻辑
    return (
        formatted
            .replace(/\b0[^\s]+\b/g, "") // 移除0值单位
            .replace(/\s+/g, " ") // 合并多个空格
            .trim() || "0m"
    );
}
/**预加载图片 */
export const loadImage = (src) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = "anonymous"; // For cross-origin requests
        img.onload = () => resolve(img);
        img.onerror = (e) => reject(new Error(`Image loading failed: ${src}`));
        img.src = src;
    });
};

export const getUUID = () => {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = crypto.getRandomValues(new Uint8Array(1))[0] % 16;
        const v = c === "x" ? r : (r & 0x3) | 0x8; // 确保符合 UUID v4 格式
        return v.toString(16);
    });
};

/**
 * 计算 lumen 消耗成本
 *
 * @param {string} type - lumen 消耗的类型
 * @param {number} num - 计算成本的单位数量
 * @returns {number} - lumen 消耗的总成本。如果类型在 LUMEN_EXPEND_DICT 中未找到，则返回 0。
 */
export const expendCostLumen = (type, num, imgShapeValue) => {
    // 兼容性处理
    if (typeof type !== "string") return 0;
    if (typeof num !== "number" || num < 0) return 1;
    if (typeof imgShapeValue !== "string") return 1;

    let radio = 1;

    // [移除背景]消耗lumen都为1
    if (type !== "REMOVE_BG_IMAGE") {
        try {
            let [width, height] = imgShapeValue.split("x"); // 解析宽高
            const pixelRate = (width * height) / 1000000; // 总像素(x百万像素) / 100W

            if (pixelRate > 4) radio = 4;
            else if (pixelRate <= 2) radio = 1;
            else if (pixelRate <= 3) radio = 2;
            else if (pixelRate <= 4) radio = 3;
            else radio = 1;
        } catch (e) { }
    }

    const expend = LUMEN_EXPEND_DICT?.[type] ?? 0;

    const lumen = parseInt(expend * num * radio);

    console.log(`lumen转换： [功能基础积分${expend}] X [当前${num}张图] * [使用${imgShapeValue}像素${radio}倍率耗损] = [${lumen} Lumen]`);
    return lumen;
};

/**
 * 将系统中以前的语言代码处理成当前最新的语言代码
 * @param {string} locale - 以前的老语言代码
 * @returns {string} 转化之后的新代码
 */
const BCP_47_LOCALMap = {
    de_DE: "de", // 德语（German）
    en: "en", // 英语（美国，常见默认）
    es: "eS", // 西班牙语（西班牙）
    fr_FR: "fr", // 法语（法国）
    it: "it", // 意大利语（意大利）
    ja_JP: "ja", // 日语（日本）
    ko: "ko", // 韩语（韩国）
    pt_BR: "pt", // 葡萄牙语（葡萄牙）
    "zh-TW": "zhTW", // 繁体中文（台湾）
};
export const transformLocaleToBCP47 = (locale) => {
    return BCP_47_LOCALMap[locale] || locale; // 如果没有匹配到，则返回原始值
};

/**
 * 读取文件信息
 * @param {*} file
 */
export const getImageInfo = (file) => {
    console.log(file, "filefilefile");
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = function (event) {
            const img = new Image();

            img.onload = function () {
                const width = img.width;
                const height = img.height;
                resolve({ width, height, size: file.size }); // 返回宽高
            };

            img.onerror = function () {
                reject(new Error("无法加载图片")); // 图片加载失败时拒绝
            };

            img.src = event.target.result; // 将文件内容作为图片源
        };

        reader.onerror = function () {
            reject(new Error("读取文件失败")); // 读取文件失败时拒绝
        };

        reader.readAsDataURL(file); // 读取文件作为 Data URL
    });
};
