<template>
    <div class="h-full" :class="[menuClass, hideMenu ? '!hidden' : '']">
        <nav class="w-full h-full relative box-border border-r border-solid border-border-1 bg-bg-2 text-sm text-text-opt-1 min-h-screen transition-all">
            <div class="w-full px-4 h-full">
                <div class="py-6 flex items-center text-text-3 hover:text-text-1" :class="[isFold ? 'justify-center' : 'justify-start px-3']">
                    <a href="/" target="_blank" class="">
                        <IconsLogoWithText class="h-8" :class="{ '!hidden': isFold }" />
                        <IconsLogo class="h-8 hidden" :class="{ '!block': isFold }" />
                    </a>
                </div>
                <div class="space-y-3 mt-6 pt-4 h-[50%] scroll-container no-scroll !overflow-x-hidden">
                    <div v-for="item in menuList" :key="item.label" @click="handleMenuClick(item)" class="relative">
                        <NuxtLinkLocale
                            :to="item.path"
                            class="menu-items h-10 z-10 p-3 flex gap-2 font-medium items-center relative rounded-full overflow-hidden border border-solid border-transparent transition-all hover:text-text-opt-2"
                            :class="{ 'active-link': isActiveLink(item.path), '!w-10 px-2': isFold }"
                        >
                            <component :is="item.icon" class="text-xl shrink-0" />
                            <component :is="item.label" class="transition-all ease-in-out delay-150" :class="{ 'opacity-0': isFold }" />
                        </NuxtLinkLocale>
                        <template v-if="item.path === '/image/create'">
                            <JobQueue :isFold="isFold" />
                        </template>
                    </div>
                </div>
            </div>

            <div class="sticky bottom-0 z-50" v-show="!isMobile">
                <!-- 用户提升 用户 -->
                <ClientOnly>
                    <!-- 活动 -->
                    <UserPromotion v-if="!isFold" />
                    <div class="bg-bg-2">
                        <!-- 社媒 -->
                        <SocialMedia v-if="!isFold" :social="['discord', 'facebook', 'twitter', 'apple', 'youtube']" class="flex justify-between px-4 h-16 py-4 text-3.5xl" />
                        <!-- 用户信息 功能列表 -->
                        <PicNavUserInfo :isFold="isFold" />
                    </div>
                </ClientOnly>
            </div>
        </nav>
    </div>
</template>

<script setup>
import { useUserProfile, useForceUpdatePageState, useUnreadMessage } from "@/stores";

import { IconExplore, IconCreate, IconHistory, IconTools, IconActivity } from "@/icons/index.js";
import { HIDE_MENU_PATH } from "@/utils/constant.js";

import JobQueue from "@/components/JobQueue.vue";
import LangText from "@/components/LangText.vue";
import SocialMedia from "@/components/SocialMedia.vue";

import IconTask from "@/components/icons/Task.vue";
import { computed } from "vue";
import { useThemeStore } from "@/stores/system-config";

const { isMobile, isPc, isIpad } = storeToRefs(useThemeStore());

const { setShowMoDrawer } = useCurrentTheme();
const localePath = useLocalePath();

const { t } = useI18n({ useScope: "global" });
const route = useRoute();
const userProfile = useUserProfile();
const systemStore = useSystemConfig();
const unreadMessages = useUnreadMessage();

const isActiveLink = computed(() => {
    return (link) => route.path.includes(link);
});

// 用户上线之后，获取当前积分 和未完成的生图任务
const forceUpdatePageState = useForceUpdatePageState();
const forceReload = {
    dbClickReload: (path) => {
        if (route.path === localePath(path)) {
            forceUpdatePageState.updatePageKey(path, Date.now());
        }
        setShowMoDrawer(false); //关闭侧边栏
    },
};

const handleMenuClick = ({ path, trackKey }) => {
    const { path: currentPath } = route;
    //判断当前路由是否是menuList里面的其中一个
    let isTabbar = menuList.value?.some((item) => currentPath.includes(item.path));
    forceReload.dbClickReload(path);
    trackKey && trackEvents("Navigation", `nav_menu=${trackKey}`);
};
// 根据路由设置菜单的展开/折叠状态
const menuClass = computed(() => {
    const { systemConfig } = systemStore;
    if (isMobile.value) return "w-full";
    switch (systemConfig.menuState) {
        case MENU_STATE.HIDE:
            return "hidden";
        case MENU_STATE.FOLD:
            return "w-[73px]"; //有单独1px的有边框
        default:
            return "w-[220px]";
    }
});
// 菜单的展开/折叠状态
const isFold = computed(() => {
    const { systemConfig } = systemStore;
    return systemConfig.menuState === MENU_STATE.FOLD || isIpad.value;
});

const { userConfig } = storeToRefs(userProfile);
const showBadge = computed(() => {
    return (path) => {
        if (!userConfig.value?.newRouters) {
            return "";
        }
        if (userConfig.value.newRouters.includes(path)) {
            return "badge-dot";
        }
        return "";
    };
});
//有新的活动
const hasNewActivity = computed(() => {
    if (unreadMessages.newActivityCount > 0 && route.path !== localePath("/activity")) {
        return "badge-dot";
    }
    return "";
});

const menuList = computed(() => {
    return [
        {
            path: "/community/explore",
            icon: IconExplore,
            label: h(LangText, { labelKey: "MENU_COMMUNITY" }),
            trackKey: "explore",
            isBlank: false,
        },
        {
            path: "/image/create",
            icon: IconCreate,
            label: h("div", { class: ["flex", "items-center", "justify-between", "w-full"] }, [h(LangText, { labelKey: "MENU_CREATE" })]),
            trackKey: "create",
            isBlank: false,
        },
        {
            path: "/image/gallery",
            icon: IconHistory,
            label: h("div", { class: ["flex", "items-center", "justify-between", "w-full"] }, [h(LangText, { labelKey: "MENU_GALLERY" })]),
            trackKey: "gallery",
            isBlank: false,
        },
        {
            path: "/tools",
            icon: IconTools,
            label: h("div", { class: ["flex", "items-center", "justify-between", "w-full"] }, [h(LangText, { labelKey: "MENU_TOOLS", class: showBadge.value("/tools") })]),
            trackKey: "tools",
            isBlank: false,
        },
        {
            path: "/tasks",
            icon: IconTask,
            label: h("div", { class: ["flex", "items-center", "justify-between", "w-full"] }, [h(LangText, { labelKey: "MENU_TASKS", class: showBadge.value("/tasks") })]),
            trackKey: "rewards_center",
            isBlank: false,
        },
        {
            path: "/activity",
            icon: IconActivity,
            label: h("div", { class: ["flex", "items-center", "justify-between", "w-full"] }, [
                h(LangText, { labelKey: "ACTIVITY.MENU", class: [showBadge.value("/activity"), hasNewActivity.value] }),
            ]),
            trackKey: "challenge_center",
            isBlank: false,
        },
    ];
});

// 多语言相关

const router = useRouter();

//监听路由变化 更新折叠状态
watch(
    () => [route.path, isPc.value],
    ([newPath, currentIsPc]) => {
        let finalState = MENU_STATE.EXPAND;
        if (currentIsPc) {
            finalState = MENU_STATE.EXPAND;
        } else {
            finalState = MENU_STATE.FOLD;
        }
        if (newPath.includes("/image/create")) {
            finalState = MENU_STATE.FOLD;
        }

        systemStore.setSystemConfig({
            menuState: finalState,
        });
    },
    {
        immediate: true,
    }
);
const hideMenu = computed(() => {
    return HIDE_MENU_PATH.map((item) => localePath(item)).includes(route.path);
});

// 触发Google事件
const trackEvents = (event, el) => {
    try {
        window.trackEvent(event, { el });
    } catch (error) {
        console.error("Error tracking event:", error.message);
    }
};
</script>

<style lang="scss" scoped>
.menu-items.active-link {
    @apply text-text-opt-3 bg-fill-opt-3 border-border-t-1;
}
</style>
<style>
.badge-dot::after {
    content: " ";
    @apply w-1.5 h-1.5 bg-danger-6 rounded-full absolute ml-2;
}

.no-scrollbar::-webkit-scrollbar {
    display: none; /* 隐藏 Chrome, Safari 和 Opera 的滚动条 */
}
.no-scrollbar {
    -ms-overflow-style: none; /* 隐藏 IE 和 Edge 的滚动条 */
    scrollbar-width: none; /* 隐藏 Firefox 的滚动条 */
}
</style>
