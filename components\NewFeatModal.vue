<template>
    <div class="py-1 px-4">
        <div class="flex items-center justify-between mb-3">
            <div class="text-2xl font-semibold">{{ t("MAINTENANCE_TITLE") }}</div>
            <n-icon size="32" class="cursor-pointer hover:scale-105" @click="handleOk"><IconsClose /></n-icon>
        </div>
        <div v-for="(item, index) in noticeContent" :key="index" class="text-sm font-medium min-h-5 leading-5.5">{{ item }}</div>

        <div class="mt-3 flex items-center justify-end">
            <n-button round class="!bg-primary !text-white hover:opacity-90 h-11 w-44" :bordered="false" @click="handleOk">
                {{ t("COMMON_BTN_OK") }}
            </n-button>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
const props = defineProps({
    start: {
        type: Number,
        required: true,
    },
    end: {
        type: Number,
        required: true,
    },
});
const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}`;
};
const getMinutesDifference = (timestamp1, timestamp2) => {
    // 计算两个时间戳之间的毫秒差
    const millisecondsDifference = Math.abs(timestamp1 - timestamp2);
    console.log("millisecondsDifference:", millisecondsDifference);
    // 将毫秒转换为分钟，并四舍五入取整
    const minutesDifference = Math.round(millisecondsDifference / (1000 * 60));
    return minutesDifference;
};

const noticeContent = computed(() => {
    return [
        t("MAINTENANCE_CONTENT_1"),
        "",
        t("MAINTENANCE_CONTENT_2", { startTime: formatTimestamp(props.start), endTime: formatTimestamp(props.end), maintenanceMinutes: getMinutesDifference(props.start, props.end) }),
        "",
        t("MAINTENANCE_CONTENT_3"),
        "",
        t("MAINTENANCE_CONTENT_4"),
        t("MAINTENANCE_CONTENT_5"),
    ];
});

const emits = defineEmits(["confirm"]);
const handleOk = () => {
    emits("confirm", false);
};
</script>
