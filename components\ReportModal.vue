<template>
    <div class="text-text-2">
        <h2 class="text-base font-semibold flex items-center justify-between pl-2">
            <span>{{ t(`REPORT`) }}</span>
            <div class="w-8 h-8 rounded-full hover:bg-fill-wd-1 hover:text-text-2 text-text-4 flex items-center justify-center cursor-pointer transition-colors" @click="handleCancel">
                <IconsClose class="text-xl" />
            </div>
        </h2>
        <div class="relative py-4 px-2 mt-4">
            <h2 class="text-base font-medium">{{ t(`REPORT_TITLE`) }}</h2>

            <RadioGroup class="mt-6 !gap-6" :options="options" v-model:value="auditType" />

            <div v-if="auditType === 5" class="relative pb-4 mt-2">
                <div class="custom-scrollbar">
                    <n-input
                        type="textarea"
                        class="textarea-box"
                        :autosize="{ minRows: 3, maxRows: 3 }"
                        :bordered="false"
                        :placeholder="t('REPORT_OPTION_OTHER_PLACEHOLDER')"
                        v-model:value="otherContent"
                        rows="3"
                        maxlength="200"
                    />
                </div>
                <div class="absolute left-0 bottom-0 text-xs text-error">{{ errText }}</div>
            </div>
        </div>

        <div class="flex gap-3 justify-end">
            <Button class="min-w-32" type="secondary" @click="handleCancel">{{ t("COMMON_BTN_CANCEL") }}</Button>
            <Button class="min-w-32" type="primary" :loading="loading" @click="handleConfirm">{{ t("CONFIG_BASE_SUBMIT_BTN") }}</Button>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/utils/i18n-util";
import { reportCommunityGenContent, reportComment } from "@/api";
const props = defineProps(["info"]);
const emits = defineEmits(["confirm", "cancel"]);
const loading = ref(false);
const auditType = ref(1);
const otherContent = ref("");
const errText = ref("");
const handleCancel = () => {
    emits("cancel");
};

const options = [
    { label: t("REPORT_OPTION_VIOLENCE"), value: 1 },
    { label: t("REPORT_OPTION_PORNOGRAPHY"), value: 2 },
    { label: t("REPORT_OPTION_DISCRIMINATION"), value: 3 },
    { label: t("REPORT_OPTION_INFRINGEMENT"), value: 4 },
    { label: t("REPORT_OPTION_OTHER"), value: 5 },
];
const checkOtherContent = computed(() => {
    return auditType.value === 5 && otherContent.value.trim() === "";
});

const handleConfirm = async () => {
    if (!auditType.value) {
        return;
    }
    if (loading.value) {
        return;
    }
    if (checkOtherContent.value) {
        errText.value = t("REPORT_OPTION_OTHER_PLACEHOLDER");
        return; // 文本为空时，不提交
    }
    try {
        // TYPE:  COMMENT 举报评论   GEN_CONTENT 举报生成内容
        const { commFileId, commentId, type } = props.info;
        loading.value = true;
        let reqCallback = reportCommunityGenContent;
        const params = { commFileId };
        if (type === "COMMENT") {
            reqCallback = reportComment;
            params.commentId = commentId;
        }
        const { status, message } = await reqCallback({
            ...params,
            auditType: auditType.value,
            otherContent: otherContent.value,
        });
        loading.value = false;
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        emits("confirm");
    } catch (error) {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
::v-deep(.n-input .n-input__input-el) {
    @apply dark:text-dark-text;
}

.custom-scrollbar {
    @apply border-none outline-none w-full  bg-fill-ipt-1 rounded-lg p-2 resize-none text-sm;
}
.dark .custom-scrollbar .textarea-box {
    --n-text-color: #fff !important;
}

::v-deep(.n-input__textarea-el),
::v-deep(.n-input__placeholder) {
    padding: 0 !important;
    caret-color: #6f47ff !important;
}
::v-deep(.n-input__placeholder) {
    opacity: 0.6;
}
::v-deep(.n-input .n-input-wrapper) {
    padding: 0 10px;
}

::v-deep(.n-input.n-input--textarea .n-input__textarea-mirror) {
    padding: 0;
}
</style>
