@tailwind base;
/* @tailwind components; */
@tailwind utilities;

@layer base {
    @font-face {
        font-family: "Bebas Neue";
        src: url("@/assets/fonts/BebasNeue-Regular.ttf") format("truetype");
    }
}

@layer utilities {
    /* @variants responsive { } */

    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none;
        /* IE and Edge */
        scrollbar-width: none;
        /* Firefox */
    }
}

@font-face {
    font-family: "Poppins";
    src: url("@/assets/fonts/Poppins-Regular.ttf");
}

*,
pre {
    font-family: "Poppins", sans-serif;
    outline: none;
}

body {
    overflow-x: hidden;
    overscroll-behavior-y: none;
}

/* 全局禁用弹性滚动 */
html,
body {
    overscroll-behavior: none;
    /* 可选：防止页面内容被橡皮筋效果拉伸 */
    height: 100%;
    overflow: hidden;
}

img {
    user-select: none;
}

.n-base-selection__state-border,
.n-input__state-border {
    box-shadow: none !important;
    border: none !important;
}

.n-modal-mask {
    backdrop-filter: blur(2px);
}

.n-slider .n-slider-rail .n-slider-rail__fill {
    background: linear-gradient(180deg, #6f47ff 0%, #6f47ff 99%);
}

.dark .n-slider .n-slider-rail {
    background: var("--p-fill-slider-3");
}

.dark .n-slider .n-slider-rail .n-slider-rail__fill {
    background: var("--p-primary-7");
}

.n-input,
.n-base-selection-label {
    background-color: transparent !important;
}

.dark .n-base-select-menu {
    background-color: #2f3035 !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

body,
.no-scroll {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

.no-scroll::-webkit-scrollbar,
body::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, and Opera */
}

@media (max-width: 768px) {
    .h5-no-scroll {
        scrollbar-width: none;
        /* Firefox */
        -ms-overflow-style: none;
        /* IE and Edge */
    }

    body::-webkit-scrollbar,
    .h5-no-scroll::-webkit-scrollbar {
        display: none;
    }
}

.n-base-select-menu .n-base-select-option::before {
    border-radius: 0px !important;
    left: 0 !important;
    right: 0 !important;
}

.n-base-select-menu .n-base-select-option {
    --n-option-font-size: 12px !important;
}

.n-base-selection .n-base-selection__border,
.n-base-selection .n-base-selection__state-border {
    border: none !important;
}

.loading_bg_anime {
    background: linear-gradient(90deg, #eaeaea 25%, #f5f5f5 37%, #eaeaea 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1s ease infinite;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.dark .loading_bg_anime {
    background: linear-gradient(90deg, #242425 25%, #2c2d31 37%, #242425 63%);
    background-size: 400% 100%;
    animation: custom-skeleton-loading 1s ease infinite;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

@keyframes custom-skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}

body .n-message-container.n-message-container--top {
    top: 100px;
}

/* .h-screen {
  height: calc(var(--dvh, 1vh) * 100);
}
.min-h-screen{
  min-height: calc(var(--dvh, 1vh) * 100);
} */

@media (max-width: 768px) {
    .h-screen {
        height: calc(100vh - constant(safe-area-inset-bottom));
        height: calc(100vh - env(safe-area-inset-bottom));
    }
}

/* Firefox专用设置 */
@supports (-moz-appearance: none) {
    * {
        scrollbar-color: var(--p-fill-wd-3);
        /* 滑块颜色 + 透明轨道 */
        scrollbar-width: thin;
    }
}

/* 可选：特定容器设置示例 */
.scroll-container {
    overflow: auto;
    scrollbar-gutter: stable;
    /* 防止内容跳动 */
    /* 通用设置（影响所有滚动条） */
}

.scroll-container::-webkit-scrollbar {
    width: 10px;
    /* 垂直滚动条宽度 */
}

/* WebKit浏览器核心设置 */
.scroll-container::-webkit-scrollbar-track {
    background-color: transparent;
    /* 轨道背景透明 */
}


.scroll-container::-webkit-scrollbar-button {
    display: none;
    /* 隐藏箭头按钮 */
}

.scroll-container::-webkit-scrollbar-thumb {
    background-color: var(--p-fill-wd-3);
    /* 滑块半透明 */
    border-radius: 4px;
    cursor: pointer;
}

/* 悬停状态 */
.scroll-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--p-fill-wd-4);
}

/*公共scrollBar样式*/
.operation-bar-box::-webkit-scrollbar {
    width: 4px;
    height: 3px;
    margin-left: 4px;
}

.operation-bar-box::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.1);
}

.operation-bar-box::-webkit-scrollbar-track {
    border-radius: 10px;
    background: transparent;
}

/*隐藏scrollbar*/
.no-scroll-bar {
    scrollbar-width: none;
}

.no-scroll-bar::-webkit-scrollbar {
    display: none;
    -ms-overflow-style: none;
    width: 0;
    height: 0;
}

.no-scroll-bar::-webkit-scrollbar-thumb {
    display: none;
    width: 0;
    height: 0;
}

.no-scroll-bar::-webkit-scrollbar-track {
    display: none;
    width: 0;
    height: 0;
}

.h-screen {
    height: 100vh;
    height: 100dvh;
    /* fallback for old browsers */
}

.n-dropdown-menu {
    border-radius: 8px;
    border: 1px solid var(--p-border-t-1);
    background: var(--p-bg-6);
    /* 菜单容器投影 */
    box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.12), 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.n-dropdown-menu:not(.n-dropdown-menu--scrollable) {
    padding: 8px;
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body .n-dropdown-option-body__prefix.n-dropdown-option-body__prefix--show-icon {
    width: max-content;
    aspect-ratio: 1;
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending {
    color: var(--p-text-drop-1);
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending:hover {
    color: var(--p-text-drop-2);
}

.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before {
    border-radius: 4px;
}

.n-scrollbar-rail__scrollbar {
    --n-scrollbar-color: var(--p-fill-wd-3) !important;
    --n-scrollbar-color-hover: var(--p-fill-wd-4) !important;
}