<template>
    <n-modal v-if="isIpad" :z-index="20" :show="true" placement="right" raw trigger="click" class="!bg-bg-2 !rounded-2xl"> <NewModelContent @close="handleClose" /> </n-modal>
    <n-tooltip v-else-if="showModel" :z-index="20" raw placement="right" :show="true" :show-arrow="false">
        <template #trigger>
            <div class="new-tag absolute text-white transform translate-y-[-100%] right-0 text-xs px-2 py-0.5 rounded-[2px_12px_2px_12px]">NEW</div>
        </template>
        <NewModelContent @close="handleClose" />
    </n-tooltip>
</template>
<script setup>
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
import NewModelContent from "./components/NewModelContent.vue";
const { isIpad } = storeToRefs(useThemeStore());

const emit = defineEmits(["update:showNewTag"]);

const showModel = ref(false); // 解决网页端Create中tooltip提前渲染
onMounted(async () => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    showModel.value = true;
});

const handleClose = () => {
    emit("update:showNewTag", false);
};
</script>
