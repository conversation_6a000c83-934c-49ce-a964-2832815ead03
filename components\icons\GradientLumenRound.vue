<template>
    <div class="flex items-center justify-center rounded-full gradient-lumen relative overflow-hidden">
        <div class="bg-bg-6 w-full h-full rounded-full absolute top-[1px] left-[1px]"></div>
        <IconsLumenFill class="size-[60%] relative z-10" />
    </div>
</template>
<style lang="scss" scoped>
.gradient-lumen {
    background: linear-gradient(145deg, var(--p-fill-wd-4) 20%, var(--p-fill-wd-1) 100%);
}
</style>
