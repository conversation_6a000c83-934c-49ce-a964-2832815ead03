<template>
    <div class="flex bg-fill-btn-1 items-center rounded-full py-1 px-2 gap-x-1 text-xs text-text-3 overflow-hidden">
        <n-icon size="14">
            <component :is="icon" />
        </n-icon>
        <label v-if="!!props.text" class="font-medium min-w-4 overflow-hidden text-ellipsis">{{ props.text }}</label>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });

const props = defineProps({
    icon: {
        type: Object,
    },
    text: {
        type: String,
    },
});
</script>
