<script setup>
import { useSubscribeErrorModal } from "@/hook/subscribe.js";
import { reqCancelUpgradeSubscribe, reqCancelAutoSubscribeByPaypal } from "@/api/index.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { getPlatformVipToWeight } from "@/utils/tools.js";
import { PLATFORM_TYPE, SUBSCRIBE_TYPE } from "@/utils/constant.js";

const emits = defineEmits(["close"]);
const { showErrorModal } = useSubscribeErrorModal();
const subscribeStore = useSubscribeStore();

const cancelChangeLoading = ref(false);
const PLAN_MAPPING = {
    [SUBSCRIBE_TYPE.STANDARD]: "SUBSCRIBE_PLAN_STANDARD",
    [SUBSCRIBE_TYPE.PRO]: "SUBSCRIBE_PLAN_PRO",
};
// 显示等级最高的付费订阅信息
const currSub = computed(() => {
    const webSubList = getPlatformVipToWeight(subscribeStore.subList, PLATFORM_TYPE.WEB);
    if (webSubList.length === 0) {
        return { plan: "", platform: "" };
    }
    const firstSub = webSubList[0];
    return {
        plan: PLAN_MAPPING[firstSub.planLevel] || "",
        platform: firstSub.vipPlatform,
    };
});
const handleUnsubscribe = () => {
    cancelChangeLoading.value = true;
    const isPaypal = currSub.value.platform === "paypal";
    let reqCancelFn = reqCancelUpgradeSubscribe;
    if (isPaypal) {
        reqCancelFn = reqCancelAutoSubscribeByPaypal;
    }
    reqCancelFn()
        .then(async (res) => {
            if (res.status === 0) {
                await subscribeStore.getRefreshOn();
                emits("close");
            } else {
                showErrorModal("SUBSCRIBE_EX_OPERATION");
            }
        })
        .catch((err) => {
            showErrorModal("SUBSCRIBE_EX_OPERATION");
        })
        .finally(() => {
            cancelChangeLoading.value = false;
        });
};

const handleClose = () => {
    if (cancelChangeLoading.value) return;
    emits("close");
};
</script>

<template>
    <div class="space-y-6">
        <div class="flex justify-between items-center gap-2 pb-4">
            <span class="text-base text-text-1 font-semibold ml-2">{{ $t("SUBSCRIBE_MODAL_CHANGE_CONFIRM") }}</span>
            <div class="w-8 h-8 rounded-full text-text-4 hover:text-text-2 flex items-center justify-center hover:bg-fill-wd-1 cursor-pointer" @click="handleClose">
                <IconsClose class="w-5 h-5" />
            </div>
        </div>
        <div class="text-sm font-medium leading-5.5 text-text-2 px-2">{{ $t("SUBSCRIBE_MODAL_CHANGE_CONTENT") }}</div>
        <div class="flex justify-end gap-x-3 p-2 pt-6">
            <Button :loading="cancelChangeLoading" type="secondary" @click="handleUnsubscribe">{{ $t("SUBSCRIBE_MODAL_CHANGE_CONFIRM") }}</Button>
            <Button :disabled="cancelChangeLoading" type="primary" @click="handleClose">{{ $t("SUBSCRIBE_MODAL_CHANGE_CANCEL") }}</Button>
        </div>
    </div>
</template>
