<!--
 * @Author: HuangQS
 * @Description: Banner区域 组件顶部的banner都统一用这个组件管理
 * @Date: 2025-07-16 14:41:56
 * @LastEditors: HuangQ<PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-30 14:19:43
-->

<template>
    <div ref="bannerRef" class="w-full">
        <AnniversaryBanner v-if="isHasAnniversary && !isDisableAnniversaryBanner" />
    </div>
</template>

<script setup>
import { useThemeStore } from "@/stores/system-config";
import AnniversaryBanner from "@/components/AnniversaryBanner.vue";
const route = useRoute();
const { locale } = useI18n({ inheritLocale: true, useScope: "global" });

const bannerRef = ref(null);
let resizeObserver = null;
const subscribeStore = useSubscribeStore();
const { isHasAnniversary } = storeToRefs(subscribeStore);

onMounted(() => {
    nextTick(() => {
        if ("ResizeObserver" in window) {
            resizeObserver = new ResizeObserver(handleResize);
            if (bannerRef.value) {
                resizeObserver.observe(bannerRef.value);
            }
        }
    });
});

const routerFilterPaths = computed(() => {
    return ["/user/subscribe"];
});

// 优化的路径匹配函数
const matchPath = (targetPath, patternPath) => {
    let cleanedTarget = targetPath.split("?")[0].split("#")[0]; // 移除查询参数和哈希
    const prefix = locale.value === "en" ? "" : `/${locale.value}`; // 添加语言前缀
    // 转换模式为正则表达式
    // 匹配动态参数
    // 匹配通配符
    let regexPattern = patternPath.replace(/\//g, "\\/").replace(/:\w+/g, "\\w+").replace(/\*/g, ".*");
    regexPattern = `${prefix}${regexPattern}`;
    const regex = new RegExp(`^${regexPattern}$`, "i");
    return regex.test(cleanedTarget);
};

const isDisableAnniversaryBanner = computed(() => {
    const currentPath = route.fullPath;

    return routerFilterPaths.value.some((path) => {
        return matchPath(currentPath, path);
    });
});

onBeforeUnmount(() => {
    if (resizeObserver && bannerRef.value) {
        resizeObserver.unobserve(bannerRef.value);
        resizeObserver.disconnect();
    }
});

const handleResize = (entries) => {
    for (let entry of entries) {
        const height = entry.contentRect.bottom + entry.contentRect.top;
        useThemeStore().setBannerAreaHeight(height);
    }
};
</script>
<style lang="scss" scoped></style>
