<!--
 * @Author: HuangQS
 * @Description: 每日第一次进入时，领取lumen弹窗
 * @Date: 2025-06-25 20:04:25
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-08-13 15:48:14
-->
<template>
    <n-modal :show="taskModalVisible" draggable :style="isHasAnniversary && `box-shadow:none`">
        <!-- 周年庆特殊样式 -->
        <div v-if="isHasAnniversary" class="relative w-[327px] h-[355px] md:w-[500px] md:h-[474px]">
            <div class="absolute top-[22px] size-8 text-text-t-5 right-[22px]" @click="handleDialogCloseClick()">
                <n-icon size="20" class="cursor-pointer">
                    <IconsClose />
                </n-icon>
            </div>

            <img
                src="@/assets/images/userPromotion/img_birthday_modal_bg_mo.webp"
                class="block md:hidden absolute top-0 lef-0 right-0 bottom-0 size-full object-cover object-center pointer-events-none"
                alt="anniversary banner mo"
            />
            <img
                src="@/assets/images/userPromotion/img_birthday_modal_bg.webp"
                class="hidden md:block absolute top-0 lef-0 right-0 bottom-0 size-full object-cover object-center pointer-events-none"
                alt="anniversary banner"
            />

            <!-- -内容- -->
            <div class="absolute left-0 right-0 bottom-0 flex flex-col items-center">
                <div
                    class="md:mb-5 w-full h-[86px] md:h-[96px] px-6 flex flex-col items-center justify-center gap-1.5 md:gap-2 text-center text-white text-base md:text-2xl font-semibold md:font-semibold"
                >
                    <div v-if="isIn48Hour" class="flex shrink-0 items-center gap-1.5 bg-[#FFDE59] rounded-[18px] w-[108px] px-3 py-1 text-[#F54032]">
                        <n-icon :size="16" class="text-[#F54032] rotate-icon"> <IconsAlarmClock /> </n-icon>
                        <div class="flex-1 min-w-[64px] text-sm font-semibold">{{ anniversaryHoursStr }}:{{ anniversaryMinutesStr }}:{{ anniversarySecondsStr }}</div>
                    </div>

                    <!-- 标题 -->
                    <span class="w-full">{{ $t("ANNIVERSARY_MODAL.TITLE") }}</span>
                </div>
                <!-- 描述区域 -->
                <div class="w-full h-[100px] md:h-[96px] px-4 flex items-center text-center text-xs font-medium md:text-sm text-white">
                    <!-- <span class="w-full">{{ $t("ANNIVERSARY_MODAL.CONTENT") }} </span> -->
                    <span class="w-full">
                        <i18n-t keypath="ANNIVERSARY_MODAL.CONTENT">
                            <template #count>
                                <span class="text-[#FFDE59]">{{ t("ANNIVERSARY_MODAL.CONTENT_COUNT") }}</span>
                            </template>
                        </i18n-t>
                    </span>
                </div>

                <!-- 按钮 -->
                <div class="flex items-center justify-center gap-4 pb-4 px-6 md:pb-6 md:pt-6">
                    <Button
                        type="primary"
                        class="min-w-[120px] *:"
                        style="background: rgba(255, 255, 255, 0.8) !important; color: rgba(0, 0, 0, 0.6) !important; font: weight 500px !important; font-size: 14px !important"
                        @click="handleClaim()"
                        :loading="loading"
                    >
                        <span>{{ $t("TASKS_CENTER_CLAIM") }}</span>
                        <IconsLumenFill class="ml-4" />
                        <span class="ml-0.5">10</span>
                    </Button>
                    <Button
                        type="primary"
                        class="min-w-[120px] h-10 font-medium text-sm"
                        style="background: #ffde59 !important  ; color: #f54032 !important; font: weight 500px !important; font-size: 14px !important"
                        @click="handleSubscribe()"
                    >
                        <span>{{ $t("ANNIVERSARY_MODAL.SUBSCRIBE") }}</span>
                    </Button>
                </div>
            </div>
        </div>

        <!-- 普通每日领取lumen样式 -->
        <div v-else class="relative w-[500px] border border-solid border-border-1 flex flex-col rounded-2xl overflow-hidden bg-bg-3">
            <div class="">
                <img src="@/assets/images/task/task_model_bg.webp" class="w-full block dark:hidden" />
                <img src="@/assets/images/task/task_model_bg_dark.webp" class="w-full hidden dark:block" />
            </div>
            <div class="absolute top-0 right-0 p-6 text-text-1" @click="handleDialogCloseClick()">
                <n-icon size="24" class="cursor-pointer">
                    <IconsClose />
                </n-icon>
            </div>
            <p class="text-text-1 text-xl font-semibold text-center">{{ $t("TASKS_CENTER_DAILY_REWARD") }}</p>
            <p class="px-6 py-4 text-sm text-text-2 font-medium text-center">{{ $t("TASKS_CENTER_MODAL_CENTER") }}</p>
            <div class="flex justify-center py-6 gap-x-3">
                <Button type="secondary" class="min-w-[120px]" @click="handleEarnMore()">
                    {{ $t("TASKS_CENTER_EARN_MORE") }}
                </Button>
                <Button type="primary" class="min-w-[120px]" @click="handleClaim()" :loading="loading">
                    <span>{{ $t("TASKS_CENTER_CLAIM") }}</span>
                    <span class="ml-3">10</span>
                    <IconsLumenFill class="ml-0.5" />
                </Button>
            </div>
        </div>
    </n-modal>
</template>
<script setup>
import { useTaskStore } from "@/stores/task";
import { useUserProfile } from "@/stores/index";
import { CLIENT_FINISH_IDS } from "@/constants/taskCenter";
import { storeToRefs } from "pinia";

const userProfile = useUserProfile();
const subscribeStore = useSubscribeStore();
const { isNeedLumenToDay } = storeToRefs(userProfile);
const { isLoadedUserPromotion, isHasAnniversary } = storeToRefs(subscribeStore);
const { user } = storeToRefs(useUserProfile());
import { I18nT, useI18n } from "vue-i18n";

const route = useRoute();
const { t } = useI18n({ useScope: "global" });
const loading = ref(false);
const taskStore = useTaskStore();
const { toSubAnniversaryCountDownTimer, toUnSubAnniversaryCountDownTimer } = useSubscribeStore();
const { isIn48Hour, anniversaryHoursStr, anniversaryMinutesStr, anniversarySecondsStr } = storeToRefs(useSubscribeStore());

// 任务中心弹窗是否显示
const taskModalVisible = computed(() => {
    if (!isLoadedUserPromotion.value) return false; // 折扣套餐数据未加载，不知道当前是否处于周年庆期间
    return isNeedLumenToDay.value;
});

onMounted(() => {
    taskStore.trackEvent("daily_popup_show");
    toSubAnniversaryCountDownTimer("task_modal");
});

const isTaskCenter = computed(() => {
    return route.path === localePath("/tasks");
});
const localePath = useLocalePath();
const handleEarnMore = async () => {
    handleClose();
    taskStore.trackEvent("daily_popup_earn_more");
    // 如果时任务中心页面，需要刷新任务列表
    if (isTaskCenter.value) {
        taskStore.getTaskList();
    }
    handleUserGetLumen();
    await navigateTo({ path: localePath("/tasks") });
};
const handleSubscribe = () => {
    window.trackEvent("Commercialization", { el: "anniversary_lumen_popup_subscribe" }); // 点击Subscribe按钮埋点
    navigateTo({ path: localePath("/user/subscribe") });
    handleClose();
    handleUserGetLumen();
};

onBeforeUnmount(() => {
    toUnSubAnniversaryCountDownTimer("task_modal");
});

/**
 * 领取每日登录奖励
 * @description 领取奖励
 */
const handleClaim = async () => {
    loading.value = true;
    const { status, message } = await taskStore.getTaskReward([CLIENT_FINISH_IDS.DAILY_LOGIN]).finally(() => {
        loading.value = false;
    });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    taskStore.trackEvent("daily_popup_claim");
    // 如果时任务中心页面， 领取每日登录奖励后，需要刷新任务列表
    if (isTaskCenter.value) {
        taskStore.getTaskList();
    }
    handleClose();
};

const handleDialogCloseClick = () => {
    handleClose();
    handleUserGetLumen();
};

const handleClose = () => {
    useUserProfile().updateUser({ dailyLumenDialog: false });
    useUserProfile().updateNeedLumenToday(false); //关闭弹窗
};

//上报关闭弹窗事件
const handleUserGetLumen = () => {
    useUserProfile().reportUserLumenDialogFinish();
};
</script>

<style lang="scss" scoped>
@keyframes rotateSequence {
    0% {
        transform: rotate(0);
    }
    3.125% {
        transform: rotate(-12deg);
    }
    6.25% {
        transform: rotate(0);
    }

    9.375% {
        transform: rotate(12deg);
    }
    12.5% {
        transform: rotate(0);
    }

    15.625% {
        transform: rotate(-12deg);
    }
    18.75% {
        transform: rotate(0);
    }

    21.875% {
        transform: rotate(12deg);
    }
    25% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(0);
    }
}

.rotate-icon {
    /* 动画总周期4秒（1秒动效 + 3秒间隔），无限循环 */
    animation: rotateSequence 4s infinite ease-in-out;
    transform-origin: center; /* 确保围绕中心旋转 */
}
</style>
