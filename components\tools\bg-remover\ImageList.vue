<template>
    <Upload
        v-if="!noProcessImages.length"
        eventName="Remove_BG"
        :limit="limit"
        @before-file-change="shouldOpenModal = true"
        @change="handleFileChange"
        @check-failed="handleUploadError"
        multiple
        :one-time-max="oneTimeMax"
        class="!rounded-2xl"
    >
        <div class="w-full h-full flex flex-col items-center justify-center no-data cursor-pointer !rounded-2xl">
            <img src="@/assets/images/image_empty.webp" class="w-32 aspect-square hidden dark:block pointer-events-none" />
            <img src="@/assets/images/image_empty_light.webp" class="w-36 aspect-square dark:hidden pointer-events-none" />
            <div class="flex items-center mt-8 gap-1 font-medium text-center text-text-2">
                {{ $t("UPLOAD.DRAG_TITLE_BEFORE") }}
                <span class="font-semibold text-text-1"> {{ oneTimeMax }}</span>
                {{ $t("UPLOAD.DRAG_TITLE_AFTER") }}
            </div>
            <span class="text-sm text-text-4 mt-1 text-center">{{ $t("UPLOAD.FORMAT_DES", { limit }) }}</span>
        </div>
    </Upload>
    <div class="w-full h-full flex gap-3 xl:gap-4 flex-wrap overflow-auto no-scroll xl:px-6" v-else-if="noProcessImages.length">
        <div
            class="bg-fill-wd-1 px-4 rounded-lg w-[148px] h-[148px] border border-dashed border-border-2 flex flex-col items-center justify-center overflow-hidden"
            :class="{ 'cursor-pointer hover:bg-fill-wd-2': !disabled }"
            @click="openUpload"
        >
            <n-icon size="20"><IconsUpload class="text-text-2" :class="{ 'text-text-6': disabled }" /></n-icon>
            <div class="text-text-3 text-xs font-medium mt-3 text-center" :class="{ 'text-text-6': disabled }">{{ $t("FEATURE_SELF_UPLOAD_TITLE") }}</div>
            <div class="mt-1 text-[10px] font-medium text-text-4 select-none text-center" :class="{ 'text-text-6': disabled }">PNG, JPG, JPEG, WEBP up to 20MB</div>
        </div>
        <div class="rounded-lg w-[148px] h-[148px] relative group overflow-hidden" v-for="i in noProcessImages" :key="i.id" @double-click="handleDoubleClick(i.id)">
            <img :src="i.imageUrlLocal" alt="" class="w-full h-full object-cover pointer-events-none" />
            <div
                class="delete items-center justify-center p-1 rounded-[4px] bg-fill-t-2 hidden group-hover:flex absolute top-2 right-2 text-white cursor-pointer backdrop-blur-[2px]"
                @click="handleDelete(i.id)"
            >
                <IconsDele class="w-4 h-4" />
            </div>
        </div>
    </div>
</template>
<script setup>
const { t } = useI18n({ useScope: "global" });
const emit = defineEmits(["fileAdded", "fileChange", "select", "delete"]);
const oneTimeMax = 30;
const props = defineProps({
    noProcessImages: {
        type: Array,
        default: () => [],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});
const limit = 20;

const openUpload = () => {
    if (props.disabled) return;
    emit("fileAdded");
};
const handleFileChange = (files) => {
    emit("fileChange", files);
};
const handleDelete = (id) => {
    emit("delete", id);
};
const handleDoubleClick = (id) => {
    emit("select", id);
};

const handleUploadError = (data) => {
    const { type, limit, maxPixels, oneTimeMax } = data;
    switch (type) {
        case "size":
        case "ratio":
            openToast.error(t("UPLOAD_ERROR.SIZE_PIXEL_EXCEED", { limit, maxPixels }));
            break;
        case "count":
            openToast.error(t("UPLOAD_ERROR.COUNT_EXCEED", { oneTimeMax })); //TODO 多语言
            break;
        default:
            break;
    }
};
</script>
