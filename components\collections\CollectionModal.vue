<template>
    <div class="p-2 font-medium text-text-3">
        <div class="text-base font-semibold pb-4 flex items-center justify-between text-text-1">
            <span>{{ param.id ? t("COLLECTION_MENU_EDIT") : t("COLLECTION_NEW") }}</span>
            <n-icon size="24" class="cursor-pointer" @click="handleCancel">
                <IconsClose />
            </n-icon>
        </div>
        <div class="pb-6 relative mt-4">
            <div>{{ t("COLLECTION_NAME") }}</div>
            <div class="bg-fill-t-w-1 rounded-lg mt-2 text-sm px-1.5 bg-fill-ipt-1">
                <n-input class="input-box" :bordered="false" v-model:value="param.collectName" maxlength="30"  :placeholder="t('COLLECTION_MODAL.INPUT_NAME_PLACEHOLDER')"@keydown.enter="handleSubmit" />
            </div>
            <span class="absolute bottom-1.5 text-xs left-2 text-danger-4">{{ errorTips }}</span>
        </div>
        <div class="pb-6">
            <div>{{ t("COLLECTION_DES") }}</div>
            <div class="bg-fill-t-w-1 rounded-lg mt-2 p-1.5 text-sm bg-fill-ipt-1">
                <n-input
                    class="input-box"
                    :bordered="false"
                    type="textarea"
                    maxlength="100"
                    :autosize="{ minRows: 3, maxRows: 3 }"
                    v-model:value="param.description"
                    :placeholder="t('COLLECTION_MODAL.INPUT_DESC_PLACEHOLDER')"
                />
            </div>
        </div>

        <div class="flex gap-3 justify-end">
            <n-button :bordered="false" class="px-5 h-10 rounded-full min-w-[108px] bg-fill-btn-1 hover:!bg-fill-btn-3" @click="handleCancel">
                {{ t("COMMON_BTN_CANCEL") }}
            </n-button>
            <n-button
                :bordered="false"
                :loading="hasLoading"
                :disabled="!param.collectName"
                class="px-5 h-10 rounded-full min-w-[108px] bg-primary-6 hover:disabled:!bg-primary-3 hover:!bg-primary-7 focus:!bg-primary-7 disabled:bg-primary-3 disabled:!text-text-t-5 !text-text-white !opacity-100"
                type="primary"
                @click="handleSubmit"
            >
                <span>{{ t("CONFIG_BASE_SUBMIT_BTN") }}</span>
            </n-button>
        </div>
    </div>
</template>

<script setup>
import { appendCollect, updateCollect } from "@/api";
import { t } from "@/utils/i18n-util";
const hasLoading = ref(false);
const param = ref({});
const errorTips = ref("");
const props = defineProps(["info"]);
const coverConf = ref({});

watch(
    () => props.info,
    () => {
        const { collectName, description, id, cover } = props.info;
        param.value = { collectName, description, id };
        coverConf.value = {
            tempLink: cover,
            originLink: cover,
            file: null,
        };
    },
    {
        immediate: true,
    }
);
const emits = defineEmits(["update:action", "confirm", "cancel"]);
//提交保存
const handleSubmit = async () => {
    if (hasLoading.value) {
        return;
    }

    let { collectName = "", description, id } = param.value;
    collectName = collectName.trim();
    if (collectName.length < 1) {
        errorTips.value = t("COLLECTION_EDIT_TIPS");
        return;
    }
    errorTips.value = "";
    // if (coverConf.value.tempLink && coverConf.value.file) {
    //     const { fullPath } = await uploadToCos({ file: coverConf.value.file, originalFileName: Date.now() + "_.webp", type: "collectionCover" });
    //     coverConf.value.originLink = fullPath;
    // }

    let submitRequest = appendCollect;
    const info = { collectName, description };
    if (id) {
        info.id = id;
        submitRequest = updateCollect;
    }
    hasLoading.value = true;
    const { status, data, message } = await submitRequest(info);
    hasLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    emits("confirm", { ...info });
};
const handleCancel = () => {
    emits("cancel", false);
};
//选择文件
const chooseFile = async ({ file }) => {
    return new Promise((resolve, reject) => {
        //安全值
        const safePix = 260;
        // const limitErr = { size: maxSizeStr, resolution: maxShapeStr, type: "PNG, JPG, JPEG, WEBP" };
        const res = file.file;
        const img = new Image();
        img.onload = async () => {
            let width = img.width;
            let height = img.height;
            const canvas = document.createElement("canvas");
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext("2d");
            if (width > height) {
                if (width > safePix) {
                    height *= safePix / width;
                    width = safePix;
                }
            } else {
                if (height > safePix) {
                    width *= safePix / height;
                    height = safePix;
                }
            }
            handleRemoveTempLink();

            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);
            canvas.toBlob(
                (blob) => {
                    coverConf.value.tempLink = URL.createObjectURL(blob);
                    coverConf.value.file = blob;
                    resolve(file);
                },
                "image/webp",
                0.7
            );
        };
        img.onerror = function () {
            // openToast.error(t("FEATURE_UPLOAD_TYPE_ERROR", limitErr), 5e3);
            // return reject(t("FEATURE_UPLOAD_TYPE_ERROR", limitErr));
        };
        img.src = URL.createObjectURL(res);
    });
};

//删除选中的文件
const handleRemoveTempLink = () => {
    URL.revokeObjectURL(coverConf.value.tempLink);
    coverConf.value.tempLink = "";
    coverConf.value.file = null;
    coverConf.value.originLink = null;
};
</script>

<style lang="scss" scoped>
.dark .input-box {
    --n-text-color: #c2c6cf !important;
}

::v-deep(.n-input__textarea-el),
::v-deep(.n-input__input-el),
::v-deep(.n-input__placeholder) {
    padding: 0 !important;
}
::v-deep(.n-input .n-input-wrapper) {
    padding: 0 10px;
}

::v-deep(.n-input.n-input--textarea .n-input__textarea-mirror) {
    padding: 0;
}
</style>
