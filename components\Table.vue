<!--
 * @Author: HuangQS
 * @Description: 适用于Piclumen的Table基础组件
 * @Date: 2025-06-10 15:49:27
 * @LastEditors: <PERSON><PERSON><PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-07-31 16:01:06
-->
<template>
    <div id="table-view" class="flex-1 overflow-hidden">
        <n-data-table ref="dataTableRef" :bordered="true"  :single-line="false"  class="w-full" :columns="columns" :data="data" :loading="loading" :style="tableStyle" :max-height="tableViewHeight" :on-scroll="handleTableScroll">
            <template #empty>
                <Empty text="NO_DATA" />
            </template>
        </n-data-table>
    </div>
</template>
<script setup>
import { debounce } from "@/utils/tools.js";

const tableViewHeight = ref(0);

onMounted(() => {
    nextTick(() => {
        // 获取headerView的DOM元素
        const tableElement = document.getElementById("table-view");
        if (tableElement) {
            tableViewHeight.value = tableElement.clientHeight - 50; // 容器高度 - table顶部标题高度
        }
    });
});

const isLoading = ref(false);

const emit = defineEmits([
    "onScrollToBottom", //当数据滚动到底部
]);

const props = defineProps({
    columns: { type: Array, default: () => [] },
    data: { type: Array, default: () => [] },
    loading: { type: Boolean, default: false }, // 是否加载中
});

const tableStyle = computed(() => {
    return {
        "--n-border-radius": "16px", //表格圆角
        "--n-merged-th-color": "var(--p-bg-2)", //标题栏背景色
        "--n-merged-td-color": "var(--p-bg-2)", //表格内容背景色
        "--n-merged-border-color": "var(--p-border-1)", //border颜色
        "--n-td-text-color": "var(--p-text-3)", // 表格文字颜色
    };
});

const handleTableScroll = debounce((e) => {
    if (isLoading.value) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollTop + clientHeight >= scrollHeight - 10) {
        isLoading.value = true;
        emit("onScrollToBottom", e);
        isLoading.value = false;
    }
});
</script>

<style lang="scss" scoped></style>
