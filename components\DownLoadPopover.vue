<template>
    <!-- <n-popover trigger="hover" raw :show-arrow="false" placement="bottom-start" @select="handleSelect">
        <template #trigger>
            <slot></slot>
        </template>
        <div class="p-4 rounded-lg flex flex-col gap-2 overflow-hidden dark:bg-dark-bg-2 bg-white">
            <div
                v-for="item in options"
                :key="item.value"
                @click="handleSelect(item.value)"
                class="flex items-center gap-2 h-8 cursor-pointer hover:text-black dark:hover:text-dark-active-text"
                :class="{ 'opacity-70 !cursor-not-allowed': isSensitive }"
            >
                <n-icon size="24">
                    <component :is="item.icon"></component>
                </n-icon>
                <div class="text-xs tracking-wider">
                    <span>{{ item.label }}</span>
                    <span class="mx-1">{{ item.quality }}</span>
                </div>
            </div>
        </div>
    </n-popover> -->
    <span class="w-6 h-6" @click="handleSelect()">
        <slot></slot>
    </span>
    <!-- <n-icon size="24" class="cursor-pointer" @click="handleSelect('png')"><IconsDownload /></n-icon> -->
</template>

<script setup>
import { useUserProfile } from "@/stores";
import { downloadImage } from "@/utils/tools";
import { IconJpg, IconPng } from "@/icons/index.js";
const userProfile = useUserProfile();
// const downloadOpts = computed(() => userProfile.userConfig.downloadOpt);
const downloadFileType = computed(() => userProfile.userConfig.downloadFileType);
const emit = defineEmits(["download-end"]);
const props = defineProps({
    link: {
        type: String,
        default: "",
    },
    thumbnail: {
        type: String,
        default: "",
    },
    isSensitive: {
        type: Boolean,
        default: false,
    },
});

const options = [
    {
        label: "JPG",
        quality: "",
        icon: IconJpg,
        value: "jpg",
    },
    {
        label: "PNG",
        quality: "",
        icon: IconPng,
        value: "png",
    },
];

const handleSelect = () => {
    const key = downloadFileType.value || "png";
    window.trackEvent("APP_DOWNLOAD_IMG", { el: `download_btn=${key}` });
    if (props.isSensitive) {
        return;
    }
    emit("download-end")
    if (key === "jpg") {
        downloadImage(props.thumbnail, key);
        return;
    }
    downloadImage(props.link, key);
};
</script>
