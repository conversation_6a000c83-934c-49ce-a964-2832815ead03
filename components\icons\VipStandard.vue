<template>
    <!-- <svg fill="none" height="1em" version="1.1" viewBox="0 0 48 48" width="1em" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <mask id="master_svg0_281_5759" maskUnits="objectBoundingBox" style="mask-type: alpha">
                <g>
                    <rect fill="#FFFFFF" fill-opacity="1" height="48" rx="0" width="48" x="0" y="0" />
                </g>
            </mask>
            <linearGradient id="master_svg1_281_4473" x1="0.5" x2="0.5" y1="0" y2="1">
                <stop offset="0%" stop-color="#BEF8D7" stop-opacity="1" />
                <stop offset="100%" stop-color="#8AE8B3" stop-opacity="1" />
            </linearGradient>
            <linearGradient id="master_svg2_281_4490" x1="0.5" x2="0.5" y1="0" y2="1">
                <stop offset="0%" stop-color="#83E6AE" stop-opacity="1" />
                <stop offset="100%" stop-color="#20B761" stop-opacity="1" />
            </linearGradient>
            <filter
                id="master_svg3_281_5763"
                color-interpolation-filters="sRGB"
                filterUnits="objectBoundingBox"
                height="1.2000024414360526"
                width="1.1666671435051892"
                x="-0.08333357175259455"
                y="-0.10000122071802635"
            >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
                <feOffset dx="0" dy="0" />
                <feGaussianBlur stdDeviation="0.5" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0" />
                <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow" />
                <feBlend in="SourceGraphic" in2="effect1_dropShadow" mode="normal" result="shape" />
            </filter>
        </defs>
        <g mask="url(#master_svg0_281_5759)">
            <g>
                <path
                    d="M24.0000390625,43.59015625C22.5240390625,43.59015625,21.1340390625,43.01415625,20.0900390625,41.96815625C18.480039062499998,40.35815625,16.3360390625,39.47015625,14.0580390625,39.47015625C11.010039062499999,39.47015625,8.5300390625,36.99015625,8.5300390625,33.94215625C8.5300390625,31.66215625,7.6420390625,29.52215625,6.0320390625,27.91015625C3.8760390625,25.75415625,3.8760390625,22.24615625,6.0320390625,20.09015625C7.6420390625,18.478156249999998,8.5300390625,16.33815625,8.5300390625,14.05815625C8.5300390625,11.01015625,11.010039062499999,8.530156250000001,14.0580390625,8.530156250000001C16.3360390625,8.530156250000001,18.480039062499998,7.64215625,20.0900390625,6.03215625C21.1340390625,4.98615625,22.5240390625,4.41015625,24.0000390625,4.41015625C25.4760390625,4.41015625,26.8660390625,4.98615625,27.9100390625,6.03215625C29.5200390625,7.64215625,31.6640390625,8.530156250000001,33.9420390625,8.530156250000001C36.9900390625,8.530156250000001,39.4700390625,11.01015625,39.4700390625,14.05815625C39.4700390625,16.33815625,40.3580390625,18.478156249999998,41.9680390625,20.09015625C44.1240390625,22.24615625,44.1240390625,25.75415625,41.9680390625,27.91015625C40.3580390625,29.52215625,39.4700390625,31.66215625,39.4700390625,33.94215625C39.4700390625,36.99015625,36.9900390625,39.47015625,33.9420390625,39.47015625C31.6640390625,39.47015625,29.5200390625,40.35815625,27.9100390625,41.96815625C26.8660390625,43.01415625,25.4760390625,43.59015625,24.0000390625,43.59015625C24.0000390625,43.59015625,24.0000390625,43.59015625,24.0000390625,43.59015625Z"
                    fill="url(#master_svg1_281_4473)"
                    fill-opacity="1"
                />
            </g>
            <g>
                <path
                    d="M24.000109375,5.912109375C25.076109375,5.912109375,26.088109375,6.330109375,26.850109375,7.092109375C28.744109375,8.986109375,31.262109375,10.030109375,33.942109375,10.030109375C36.162109375,10.030109375,37.970109375,11.838109375,37.970109375,14.058109375C37.970109375,16.738109375,39.014109375,19.256109375,40.908109375,21.150109375C42.480109375,22.722109375,42.480109375,25.278109375,40.908109375,26.850109375C39.014109375,28.744109375,37.970109375,31.262109375,37.970109375,33.942109375C37.970109375,36.162109375,36.162109375,37.970109375,33.942109375,37.970109375C31.262109375,37.970109375,28.744109375,39.014109375,26.850109375,40.908109375C26.088109375,41.670109375,25.076109375,42.088109375,24.000109375,42.088109375C22.924109375,42.088109375,21.912109375,41.670109375,21.150109375,40.908109375C19.256109375,39.014109375,16.738109375,37.970109375,14.058109375,37.970109375C11.838109375,37.970109375,10.030109375,36.162109375,10.030109375,33.942109375C10.030109375,31.262109375,8.986109375,28.744109375,7.092109375,26.850109375C5.5201093750000005,25.278109375,5.5201093750000005,22.722109375,7.092109375,21.150109375C8.986109375,19.256109375,10.030109375,16.738109375,10.030109375,14.058109375C10.030109375,11.838109375,11.838109375,10.030109375,14.058109375,10.030109375C16.738109375,10.030109375,19.256109375,8.986109375,21.150109375,7.092109375C21.912109375,6.330109375,22.924109375,5.912109375,24.000109375,5.912109375ZM24.000109375,2.912109375C22.202109375,2.912109375,20.402109375,3.598109375,19.030109375,4.970109375C19.030109375,4.970109375,19.030109375,4.970109375,19.030109375,4.970109375C17.712109375,6.2881093749999994,15.924109375,7.030109375,14.058109375,7.030109375C10.176109375,7.030109375,7.030109375,10.176109375,7.030109375,14.058109375C7.030109375,14.058109375,7.030109375,14.058109375,7.030109375,14.058109375C7.030109375,15.924109375,6.2881093749999994,17.712109375,4.970109375,19.030109375C4.970109375,19.030109375,4.970109375,19.030109375,4.970109375,19.030109375C2.226109375,21.774109375,2.226109375,26.226109375,4.970109375,28.970109375C4.970109375,28.970109375,4.970109375,28.970109375,4.970109375,28.970109375C6.2881093749999994,30.288109375,7.030109375,32.076109375,7.030109375,33.942109375C7.030109375,33.942109375,7.030109375,33.942109375,7.030109375,33.942109375C7.030109375,37.824109375,10.176109375,40.970109375,14.058109375,40.970109375C15.924109375,40.970109375,17.712109375,41.712109375,19.030109375,43.030109375C20.402109375,44.402109375,22.202109375,45.088109375,24.000109375,45.088109375C25.798109375,45.088109375,27.598109375,44.402109375,28.970109375,43.030109375C30.288109375,41.712109375,32.076109375,40.970109375,33.942109375,40.970109375C37.824109375,40.970109375,40.970109375,37.824109375,40.970109375,33.942109375C40.970109375,33.942109375,40.970109375,33.942109375,40.970109375,33.942109375C40.970109375,32.076109375,41.712109375,30.288109375,43.030109375,28.970109375C43.030109375,28.970109375,43.030109375,28.970109375,43.030109375,28.970109375C45.774109375,26.226109375,45.774109375,21.774109375,43.030109375,19.030109375C43.030109375,19.030109375,43.030109375,19.030109375,43.030109375,19.030109375C41.712109375,17.712109375,40.970109375,15.924109375,40.970109375,14.058109375C40.970109375,14.058109375,40.970109375,14.058109375,40.970109375,14.058109375C40.970109375,10.176109375,37.824109375,7.030109375,33.942109375,7.030109375C32.076109375,7.030109375,30.288109375,6.2881093749999994,28.970109375,4.970109375C28.970109375,4.970109375,28.970109375,4.970109375,28.970109375,4.970109375C27.598109375,3.598109375,25.798109375,2.912109375,24.000109375,2.912109375C24.000109375,2.912109375,24.000109375,2.912109375,24.000109375,2.912109375C24.000109375,2.912109375,24.000109375,2.912109375,24.000109375,2.912109375Z"
                    fill="url(#master_svg2_281_4490)"
                    fill-opacity="1"
                />
            </g>
            <g>
                <g filter="url(#master_svg3_281_5763)" style="mix-blend-mode: overlay">
                    <path
                        d="M23.9972,35.999326515603066C23.9972,35.999326515603066,12,22.000016515603065,12,22.000016515603065C12,22.000016515603065,35.9999,22.000016515603065,35.9999,22.000016515603065C35.9999,22.000016515603065,23.9972,35.999326515603066,23.9972,35.999326515603066C23.9972,35.999326515603066,23.9972,35.999326515603066,23.9972,35.999326515603066ZM32,15.999526515603065C32,15.999526515603065,16,15.999526515603065,16,15.999526515603065C16,15.999526515603065,12,21.999996515603065,12,21.999996515603065C12,21.999996515603065,35.9999,21.999996515603065,35.9999,21.999996515603065C35.9999,21.999996515603065,32,15.999526515603065,32,15.999526515603065C32,15.999526515603065,32,15.999526515603065,32,15.999526515603065Z"
                        fill="#FFAB03"
                        fill-opacity="1"
                        style="mix-blend-mode: overlay"
                    />
                </g>
                <g>
                    <path
                        d="M23.9972,35.9993147819519C23.9972,35.9993147819519,12,22.000014781951904,12,22.000014781951904C12,22.000014781951904,35.9999,22.000014781951904,35.9999,22.000014781951904C35.9999,22.000014781951904,23.9972,35.9993147819519,23.9972,35.9993147819519C23.9972,35.9993147819519,23.9972,35.9993147819519,23.9972,35.9993147819519Z"
                        fill="#13B558"
                        fill-opacity="1"
                    />
                </g>
                <g>
                    <path
                        d="M32,15.999507904052734C32,15.999507904052734,16,15.999507904052734,16,15.999507904052734C16,15.999507904052734,12,21.999977904052734,12,21.999977904052734C12,21.999977904052734,35.9999,21.999977904052734,35.9999,21.999977904052734C35.9999,21.999977904052734,32,15.999507904052734,32,15.999507904052734C32,15.999507904052734,32,15.999507904052734,32,15.999507904052734Z"
                        fill="#2AD071"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M18,22.000002044677736C18,22.000002044677736,24,15.999752044677734,24,15.999752044677734C24,15.999752044677734,30,22.000002044677736,30,22.000002044677736C30,22.000002044677736,18,22.000002044677736,18,22.000002044677736C18,22.000002044677736,18,22.000002044677736,18,22.000002044677736Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M18,21.99998426437378C18,21.99998426437378,23.99731,35.99928426437378,23.99731,35.99928426437378C23.99731,35.99928426437378,30,21.99998426437378,30,21.99998426437378C30,21.99998426437378,18,21.99998426437378,18,21.99998426437378C18,21.99998426437378,18,21.99998426437378,18,21.99998426437378Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M16,15.999752044677734C16,15.999752044677734,18,22.000002044677736,18,22.000002044677736C18,22.000002044677736,12,22.000002044677736,12,22.000002044677736C12,22.000002044677736,16,15.999752044677734,16,15.999752044677734C16,15.999752044677734,16,15.999752044677734,16,15.999752044677734Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
                <g style="opacity: 0.20000000298023224">
                    <path
                        d="M32,15.968746185302734C32,15.968746185302734,30,21.999996185302734,30,21.999996185302734C30,21.999996185302734,36,21.999996185302734,36,21.999996185302734C36,21.999996185302734,32,15.968746185302734,32,15.968746185302734C32,15.968746185302734,32,15.968746185302734,32,15.968746185302734Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    />
                </g>
            </g>
        </g>
    </svg> -->

    <svg v-if="uid" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path
            d="M29.0207 11.9237C29.9829 13.5274 29.7054 15.5838 28.3526 16.8751L18.7616 26.0301C17.2159 27.5056 14.7835 27.5056 13.2378 26.0301L3.64677 16.8751C2.29394 15.5838 2.01648 13.5274 2.9787 11.9237L6.1678 6.60852C6.89069 5.4037 8.19272 4.6665 9.59777 4.6665H22.4016C23.8066 4.6665 25.1087 5.4037 25.8316 6.60852L29.0207 11.9237Z"
            :fill="`url(#paint0_linear_standard_${uid})`"
        />
        <path d="M20.6663 17.3335L15.9997 22.0002L11.333 17.3335" stroke="#BAE2ED" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <defs>
            <linearGradient :id="`paint0_linear_standard_${uid}`" x1="1.333" y1="5.33317" x2="15.2447" y2="36.2684" gradientUnits="userSpaceOnUse">
                <stop stop-color="#71C6D5" />
                <stop offset="0.4" stop-color="#5CACBA" />
                <stop offset="0.8" stop-color="#2D9AAD" />
            </linearGradient>
        </defs>
    </svg>
</template>
<script setup>
const uid = ref("");
onMounted(() => {
    uid.value = Math.random().toString(36).slice(2, 10);
});
</script>
<style scoped></style>
