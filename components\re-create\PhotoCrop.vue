<template>
    <div class="re-create__container">
        <div class="re-create__close" @click="handleClose()">
            <IconsClose class="size-4 lg:size-6" />
        </div>
        <section class="flex-1 dark:bg-[#292A2B] bg-white relative overflow-hidden flex items-center justify-center">
            <cropper-image v-if="conf.url" ref="cropperRef" :imageSrc="conf.url" />
            <n-icon v-else size="38" class="text-primary">
                <IconsSpinLoading />
            </n-icon>
        </section>
        <section class="h-16 shrink-0 flex items-center justify-around gap-4 px-4">
            <div class="flex gap-3">
                <div v-for="(item, index) in SHAPE_ALL" :key="index" @click="setCropArea(item.width / item.height, item.label)" :class="{ active: checkedRatio === item.label }" class="ratio-item">
                    <div class="ratio-label">
                        <span>{{ item.label }}</span>
                    </div>
                    <canvas
                        class="legend-item"
                        :style="{ aspectRatio: item.width / item.height }"
                        :class="{ 'w-full h-auto': item.width > item.height, 'h-full w-auto': item.width < item.height, 'w-full h-full': item.width === item.height }"
                    ></canvas>
                </div>
                <div @click="setCropArea(0, 'auto')" class="ratio-item" :class="{ active: checkedRatio === 'auto' }">
                    <div class="ratio-label">
                        <n-icon size="24">
                            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 -960 960 960" width="1em" fill="currentColor">
                                <path
                                    d="M200-120q-33 0-56.5-23.5T120-200v-160h80v160h160v80H200Zm400 0v-80h160v-160h80v160q0 33-23.5 56.5T760-120H600ZM120-600v-160q0-33 23.5-56.5T200-840h160v80H200v160h-80Zm640 0v-160H600v-80h160q33 0 56.5 23.5T840-760v160h-80Z"
                                />
                            </svg>
                        </n-icon>
                    </div>
                </div>
                <div @click="handleReset" class="ratio-item">
                    <div class="ratio-label">
                        <span>{{ $t("CONFIG_BASE_RESET_BTN") }}</span>
                    </div>
                </div>
            </div>
            <n-button class="w-32 h-9 rounded-md !bg-primary shrink-0 !text-dark-active-text" :bordered="false" :loading="loading" @click="handleSubmit">{{ $t("CONFIG_BASE_SUBMIT_BTN") }}</n-button>
        </section>
    </div>
</template>

<script setup>
import { debounce, uploadToCos } from "@/utils/tools";
import { SHAPE_ALL } from "@/utils/constant";
import { saveCustomUpload } from "@/api";
const emits = defineEmits(["confirm", "cancel", "close"]);
const props = defineProps({
    item: {
        type: Object,
        required: true,
        default: () => ({}),
    },
});

const cropperRef = ref(null);
const loading = ref(false);
const conf = ref({});

const initCropper = () => {
    const { imgUrl } = props.item;
    //优化cropper 实例初始化速度
    fetch(imgUrl)
        .then((response) => response.blob()) // 获取远程图片的 Blob 对象
        .then((blob) => {
            // 创建一个临时的本地 URL
            const localImageUrl = URL.createObjectURL(blob);
            conf.value = { url: localImageUrl };
        });
};

initCropper();
//设置裁剪区域
const checkedRatio = ref("auto");
const setCropArea = (ratio, key) => {
    checkedRatio.value = key;
    if (ratio === 0) {
        cropperRef.value.getCropper().setAspectRatio(NaN);
        return;
    }
    cropperRef.value.getCropper().setAspectRatio(ratio);
    // const cropper = cropperRef.value.getCropper();
    // const imageData = cropper.getImageData();
    // const imageWidth = imageData.naturalWidth;
    // const imageHeight = imageData.naturalHeight;

    // // 计算最大宽度和最大高度，确保裁剪框不会超出图片边界
    // let maxWidth = imageWidth;
    // let maxHeight = imageHeight;

    // // 根据比例计算裁剪框的宽度和高度
    // let cropWidth, cropHeight;

    // if ((imageWidth / imageHeight) > ratio) {
    //   // 如果宽高比大于目标比例，按高度来计算宽度
    //   cropHeight = maxHeight;
    //   cropWidth = cropHeight * ratio;
    // } else {
    //   // 如果宽高比小于目标比例，按宽度来计算高度
    //   cropWidth = maxWidth;
    //   cropHeight = cropWidth / ratio;
    // }

    // // 确保裁剪框的尺寸不会超出图片区域
    // cropWidth = Math.min(cropWidth, maxWidth);
    // cropHeight = Math.min(cropHeight, maxHeight);

    // // 计算裁剪框的左上角位置，确保裁剪框居中
    // const cropBoxLeft = (imageWidth - cropWidth) / 2;
    // const cropBoxTop = (imageHeight - cropHeight) / 2;
    // let res = {
    //   x: cropBoxLeft,   // 更新裁剪框的 left
    //   y: cropBoxTop,     // 更新裁剪框的 top
    //   width: cropWidth,    // 更新裁剪框的宽度
    //   height: cropHeight   // 更新裁剪框的高度
    // }
    // cropper.setData(res);
};
//重置
const handleReset = () => {
    cropperRef.value.getCropper().reset();
    setCropArea(0, "auto");
};
const handleSubmit = () => {
    cropperRef.value
        .getCropper()
        .getCroppedCanvas()
        .toBlob(
            async (blob) => {
                const { width, height } = cropperRef.value.getCropper().getData();
                blob.name = Date.now() + ".webp";
                loading.value = true;
                const res = await uploadToCos({ file: blob });
                await saveCustomUpload({
                    fileUrl: res.fullPath,
                    width: parseInt(width),
                    height: parseInt(height),
                    bePiclumen: true,
                    originCreate: "crop",
                });
                loading.value = false;
                emits("close", "reload");
            },
            "image/webp",
            1
        );
};
const handleClose = () => {
    emits("cancel");
    emits("close");
};
</script>

<style lang="scss" scoped>
.ratio-item {
    @apply w-12 h-12 rounded-lg border-2 border-dashed dark:border-dark-bg-2 border-white flex items-center justify-center p-1 relative cursor-pointer;
    .ratio-label {
        @apply absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center dark:text-dark-text text-xs;
    }
    .legend-item {
        @apply object-contain border-2 border-solid dark:border-white/30 border-white rounded-sm;
    }
    &:hover,
    &.active {
        @apply dark:border-white border-dark-bg-2;
        .legend-item {
            @apply dark:border-white border-dark-bg-2;
        }
    }
}
</style>
