<template>
    <div class="aspect-box p-0.5">
        <div class="aspect-item" :class="[width < height ? 'w-auto h-full max-w-full' : width >= height ? 'h-auto w-full  max-h-full' : '']" :style="{ aspectRatio: width / height }"><slot /></div>
    </div>
</template>

<script setup>
const props = defineProps({
    width: {
        type: [String, Number],
        default: 1,
    },
    height: {
        type: [String, Number],
        default: 1,
    },
});
</script>

<style lang="scss" scoped>
.aspect-box {
    @apply w-6 h-6 flex items-center justify-center;
}
.aspect-item {
    @apply rounded-sm border-2 border-solid border-text-tab-5;
    object-fit: contain;
}
</style>
