m
<template>
    <!-- 文本增强 -->
    <n-tooltip placement="bottom" :delay="100" :show-arrow="false" raw>
        <template #trigger>
            <div
                class="rounded-lg flex items-center justify-center shrink-0 size-8 transition-all duration-[250]"
                :class="[disabled ? '!bg-fill-wd-1 !text-text-6' : 'bg-fill-wd-1 hover:text-text-1 cursor-pointer hover:!bg-fill-wd-2']"
                @click="handleImprove"
            >
                <n-icon size="18">
                    <IconsSpinLoading v-if="loading" class="text-text-2" />
                    <IconsImprove v-else />
                </n-icon>
            </div>
        </template>

        <div class="tips-box hidden lg:block">
            <div class="font-medium text-text-1 flex items-center gap-2">
                <span>{{ t("SHORT_PROMPT_IMPROVE_TITLE") }}</span>
            </div>
            <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_IMPROVE_MESSAGE") }}</div>
        </div>
    </n-tooltip>
</template>
<script setup>
import { translateOrEnhanceText } from "@/hook/create.js";
const emit = defineEmits(["change", "error", "enhanced"]);
const props = defineProps({
    disabled: {
        type: Boolean,
        default: true,
    },
    value: {
        type: String,
        default: "",
    },
    showError: {
        type: Boolean,
        default: true,
    },
});
const loading = ref(false);
/**
 * 提示词翻译或提示词增强
 * @param mode 类型
 */
const handleImprove = async () => {
    if (!props.value || props.disabled) {
        if (props.showError) {
           openToast.error(t("SHORT_PROMPT_TOO_LONG"))
        }
        return;
    }
    loading.value = true;
    try {
        const { success, data } = await translateOrEnhanceText(props.value, "enhance");
        if (success) {
            emit("change", data);
            emit("enhanced", data);
        }
    } catch (error) {
        emit("error", error);
    }
    loading.value = false;
};
</script>
