<template>
    <div class="w-screen cursor-default">
        <swiper @swiper="onSwiper" :modules="[Virtual]" :slides-per-view="1" :space-between="10" :virtual="true" :initialSlide="currentIndex" @slideChange="onSlideChange">
            <swiper-slide v-for="(item, index) in list" :key="item.imgName" :virtualIndex="index" @click="handleViewConf(item)" class="relative">
                <img class="w-screen h-screen object-contain" :src="item.thumbnailUrl" />
            </swiper-slide>
        </swiper>

        <H5FeaturePanel :key="currentConf" :item="currentConf" v-model:showPrompt="showPrompt" v-if="showPrompt" />
    </div>
</template>
<script setup>
import { Virtual } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import "swiper/css/virtual";

const props = defineProps(["list", "currentIndex"]);
const showPrompt = ref(false);
const emits = defineEmits(["swiperIndex"]);
const onSlideChange = ({ activeIndex }) => {
    showPrompt.value = false;
    emits("swiperIndex", activeIndex);
};
const swiperRef2 = ref();
const onSwiper = (swiper) => {
    swiperRef2.value = swiper;
};
const currentConf = ref({});
const handleViewConf = (item) => {
    showPrompt.value = !showPrompt.value;
    currentConf.value = item;
};
watch(
    () => props.currentIndex,
    (newIndex, oldIndex) => {
        swiperRef2.value.slideTo(newIndex);
    },
    {
        deep: true,
    }
);
</script>
