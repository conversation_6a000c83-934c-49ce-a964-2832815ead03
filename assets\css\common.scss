.tips-box {
    @apply py-2 px-3 rounded-lg max-w-96 bg-bg-6 text-text-3;
}
/*规范后的 modal tooltip容器样式
*/
.tooltip-container {
    @apply p-4 bg-bg-6 border border-solid border-border-t-1 rounded-lg;
}
.no-drag {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
}
.new-tag {
    background: linear-gradient(270deg, #ff3700 0%, #ff9417 99.83%);
}
.selected-mask {
    @apply w-full h-full bg-fill-t-4 absolute z-[5] top-0 left-0 bottom-0 transition-opacity duration-75;
}
.selected-border {
    // @apply border-[2px] border-solid border-primary-6;
    &::before {
        content: "";
        @apply absolute w-full h-full top-0 left-0 z-[20] transition-opacity duration-75 border-primary-6 border-[2px] border-solid bg-transparent rounded-lg;
    }
    &::after {
        content: "";
        @apply absolute w-full h-full top-0 left-0 z-[10] transition-opacity duration-75 border-bg-2 border-[4px] border-solid bg-transparent rounded-lg;
    }
}

.re-create__container {
    @apply w-screen h-[100dvh] lg:h-screen bg-fill-t-3 backdrop-blur-[32px] p-6 flex flex-col justify-center gap-6 overflow-hidden relative;
}

.re-create__close {
    @apply absolute top-4 left-4 lg:top-6 lg:left-6 size-8 lg:size-10 flex items-center justify-center border border-solid border-border-t-1 rounded-full text-text-2 bg-fill-dw-4 hover:bg-fill-dw-5 cursor-pointer z-[100];
}
.scroll-container {
    &::-webkit-scrollbar {
        @apply w-1 lg:w-2;
    }
}
