<template>
    <VirtualWaterfall
        v-bind="$attrs"
        v-show="showImagesList.length > 0"
        style="box-sizing: content-box; min-height: 30vh"
        class="h-full"
        padding="0 0 0 0"
        rowKey="id"
        :gap="8"
        :virtual="true"
        :items="showImagesList"
        :calcItemHeight="calcItemHeight"
        :itemMinWidth="128"
        :preload-screen-count="[1, 2]"
        :columnCount="waterfallColCount"
        :maxColumnCount="waterfallColCount"
    >
        <template #default="{ item, index }">
            <CommunityImageItem :item="item" class="rounded-2xl" @click="setShareList" @updateCommunityItem="updateCommunityItem"> </CommunityImageItem>
        </template>
    </VirtualWaterfall>
    <div class="py-20 flex justify-center" v-if="pageLoading">
        <n-icon size="48" class="text-primary-6">
            <IconsSpinLoading />
        </n-icon>
    </div>
    <Empty class="py-20" v-if="!showImagesList.length && !pageLoading" text="EMPTY_DATA"></Empty>
</template>

<script setup>
import { getCommunityPage } from "@/api";
import { decryptResult, formatPonyV6Prompt } from "@/utils/tools";
import { useUserCommunity, useUserProfile, useShareDataStore } from "@/stores";
import VirtualWaterfall from "@/components/waterfall/Waterfall.vue";
import useWindowResize from "@/hook/windowResize";
import { useSyncAction } from "@/stores/syncAction";
const userCommunity = useUserCommunity();
const syncAction = useSyncAction();
const { user } = useUserProfile();

const showImagesList = ref([]);

const shareData = useShareDataStore();
const setShareList = () => {
    shareData.setList(showImagesList.value);
};
//高度计算
const calcItemHeight = (item, w) => {
    let { width, realWidth, height, realHeight } = item;
    realWidth = realWidth || width;
    realHeight = realHeight || height;
    return w / (realWidth / realHeight);
};
const windowResize = useWindowResize();
const waterfallColCount = computed(() => {
    let base = 5;
    if (windowResize.width.value <= 1660) {
        base = 4;
    }
    if (windowResize.width.value <= 1440) {
        base = 3;
    }
    if (windowResize.width.value <= 768) {
        base = 2;
    }
    return base;
});
/** 社区图片属性值发生变更 */
const updateCommunityItem = (communityItem) => {
    const findIndex = showImagesList.value.findIndex((item) => item.id === communityItem.id);
    if (findIndex > -1) {
        const current = showImagesList.value[findIndex];
        if (!communityItem.liked) {
            updateSelfLikesCount(current.accountInfo?.userId);
            showImagesList.value.splice(findIndex, 1);
        } else {
            showImagesList.value[findIndex] = {
                ...current,
                ...communityItem,
            };
        }
    }
};

//取消自己的点赞，自己的likes -1
const updateSelfLikesCount = (userId) => {
    if (user.userId !== userId) {
        return;
    }
    userCommunity.updateCommunityUser({
        likes: Math.max(0, userCommunity.communityUser.likes - 1),
    });
};

const pageLoading = ref(false);
const pages = ref({
    pageNum: 0,
    pageSize: 50,
    isDone: false,
});
const loadMoreData = async () => {
    console.log("loadMoreData");
    if (pageLoading.value) {
        return;
    }
    let { pageNum, pageSize, isDone, lastLikeId = "" } = pages.value;
    if (isDone) {
        return;
    }
    pageNum += 1;
    pageLoading.value = true;
    //游标分页  当前数据最后一条数据ID
    if (pageNum === 1) {
        lastLikeId = "";
    }
    const { status, data, message } = await getCommunityPage({ pageSize, lastLikeId, collationName: "Likes" });
    pageLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    pages.value.lastLikeId = data.lastId;
    const list = (decryptResult(data.encryptResult) || []).map((item) => {
        const genInfo = JSON.parse(item.genInfo) || {};
        const { resolution = {}, prompt, ...info } = formatPonyV6Prompt(genInfo, "output");

        return {
            ...info,
            resolution,
            ...item,
            prompt,
            width: resolution.width,
            height: resolution.height,
        };
    });
    if (pageNum === 1) {
        showImagesList.value = list;
    } else {
        showImagesList.value = [...showImagesList.value, ...list];
    }
    pages.value.isDone = list.length === 0;
    pages.value.pageNum = pageNum;
};
loadMoreData();

onMounted(() => {
    syncAction.subscribe("updateCommunityItem", updateCommunityItem);
});
onBeforeUnmount(() => {
    syncAction.unsubscribe("updateCommunityItem", updateCommunityItem);
});
defineExpose({ loadMoreData });
</script>

<style lang="scss" scoped></style>
