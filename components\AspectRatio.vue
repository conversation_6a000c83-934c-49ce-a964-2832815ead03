<template>
    <n-tooltip trigger="click" placement="bottom" :show-arrow="false" raw ref="aspectRef">
        <template #trigger>
            <div
                class="cursor-pointer h-11 bg-fill-btn-1 rounded-full flex items-center justify-between g-node-size"
                @click="
                    () => {
                        active = !active;
                    }
                "
            >
                <div class="w-24 h-full flex items-center justify-center gap-0.5 cursor-pointer">
                    <IconsAutoSize v-if="includeContentRefer" class="text-2xl" />
                    <Aspect v-else :width="renderSize.width" :height="renderSize.height" />

                    <n-icon size="20" class="shrink-0">
                        <IconsClose />
                    </n-icon>
                    <span class="shrink-0 text-lg">{{ batchSize }}</span>
                </div>
            </div>
        </template>

        <div v-if="!isMobile" class="panel-box" :class="isMobile ? '' : 'bg-white dark:bg-dark-bg-2'">
            <n-scrollbar style="max-height: 85vh">
                <div class="grid grid-cols-2 gap-2 p-4 dark:text-dark-active-text text-black">
                    <div class="col-span-2">
                        {{ $t("CONFIG_BASE_RESOLUTION") }}
                    </div>
                    <div class="col-span-2 grid grid-cols-2 gap-2">
                        <div
                            v-for="item in squareList"
                            :key="item.label"
                            class="aspect-item"
                            :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                            @click="chooseAspect(item)"
                        >
                            <Aspect :width="item.width" :height="item.height" />
                            <span class="tracking-widest">{{ item.label }}</span>
                        </div>
                        <div v-if="includeContentRefer" class="aspect-item" :class="{ 'active-aspect-item': includeContentRefer }">
                            <IconsAutoSize class="text-2xl" />
                            <span class="tracking-widest">Auto</span>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <div
                            v-for="item in verticalList"
                            :key="item.label"
                            class="aspect-item"
                            :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                            @click="chooseAspect(item)"
                        >
                            <Aspect :width="item.width" :height="item.height" />
                            <span class="tracking-widest">{{ item.label }}</span>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <div
                            v-for="item in horizontalList"
                            :key="item.label"
                            class="aspect-item"
                            :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                            @click="chooseAspect(item)"
                        >
                            <Aspect :width="item.width" :height="item.height" />
                            <span class="tracking-widest">{{ item.label }}</span>
                        </div>
                    </div>

                    <div class="mt-4 col-span-2" v-if="showCountChoose">
                        <div class="capitalize">{{ $t("SUBSCRIBE_FEATURES_TABLE_5_1") }}</div>
                        <div class="mt-2">
                            <SwitchTab :options="batchList" v-model:value="batchCount" :checkedValues="[3, 4]" @change="changeSize" @click="closeModel" />
                        </div>
                    </div>
                </div>
            </n-scrollbar>
        </div>
    </n-tooltip>

    <AdaptiveDraw v-if="isMobile" v-model:active="active" :rrTitle="$t('DONE')" @titleLLClick="handleDrawViewLLClick" @titleRRClick="handleDrawViewRRClick">
        <template #content>
            <div class="panel-box" :class="isMobile ? '' : 'bg-white dark:bg-dark-bg-2'">
                <n-scrollbar style="max-height: 85vh">
                    <div class="grid grid-cols-2 gap-2 text-base dark:text-dark-active-text text-black">
                        <div class="col-span-2">
                            {{ $t("CONFIG_BASE_RESOLUTION") }}
                        </div>
                        <div class="col-span-2 grid grid-cols-2 gap-2">
                            <div
                                v-for="item in squareList"
                                :key="item.label"
                                class="aspect-item"
                                :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                                @click="chooseAspect(item)"
                            >
                                <Aspect :width="item.width" :height="item.height" />
                                <span class="tracking-widest">{{ item.label }}</span>
                            </div>
                            <div v-if="includeContentRefer" class="aspect-item" :class="{ 'active-aspect-item': includeContentRefer }">
                                <IconsAutoSize class="text-2xl" />
                                <span class="tracking-widest">Auto</span>
                            </div>
                        </div>
                        <div class="flex flex-col gap-2">
                            <div
                                v-for="item in verticalList"
                                :key="item.label"
                                class="aspect-item"
                                :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                                @click="chooseAspect(item)"
                            >
                                <Aspect :width="item.width" :height="item.height" />
                                <span class="tracking-widest">{{ item.label }}</span>
                            </div>
                        </div>
                        <div class="flex flex-col gap-2">
                            <div
                                v-for="item in horizontalList"
                                :key="item.label"
                                class="aspect-item"
                                :class="{ 'active-aspect-item': `${item.width} x ${item.height}` === size, 'disabled-item': includeContentRefer }"
                                @click="chooseAspect(item)"
                            >
                                <Aspect :width="item.width" :height="item.height" />
                                <span class="tracking-widest">{{ item.label }}</span>
                            </div>
                        </div>

                        <div class="mt-4 col-span-2" v-if="showCountChoose">
                            <div class="capitalize">{{ $t("SUBSCRIBE_FEATURES_TABLE_5_1") }}</div>
                            <div class="mt-2">
                                <SwitchTabMobile :options="batchList" v-model:value="batchCount" :checkedValues="[3, 4]" @change="changeSize" />
                            </div>
                        </div>
                    </div>
                </n-scrollbar>
            </div>
        </template>
    </AdaptiveDraw>
</template>

<script setup>
import { isMjModel, isFluxKontextModel, isPicLumenArtV1 } from "@/utils/tools";
import { storeToRefs } from "pinia";
import { IconDiamond } from "@/icons/index.js";
import { withDirectives, resolveDirective, onMounted, onDeactivated, computed } from "vue";
const showPlan = resolveDirective("showPlan");
import AdaptiveDraw from "@/components/AdaptiveDraw.vue";
import SwitchTabMobile from "@/components/SwitchTabMobile.vue";

import { useThemeStore } from "@/stores/system-config";
const { isMobile } = storeToRefs(useThemeStore());

const active = ref(false);

const aspectRef = ref(null);
const props = defineProps({
    size: {
        type: String,
        default: "",
    },
    batchSize: {
        type: [String, Number],
        default: 1,
    },
    shapeList: {
        type: Array,
        default: () => [],
    },
    modelId: {
        type: String,
        default: "",
    },
    includeContentRefer: {
        type: Boolean,
        default: false,
    },
});
const batchList = [
    { label: "1", value: 1 },
    { label: "2", value: 2 },

    { label: "3", value: 3, icon: () => withDirectives(h(IconDiamond, { class: "text-lg text-info-6" }), [[showPlan]]) },
    { label: "4", value: 4, icon: () => withDirectives(h(IconDiamond, { class: "text-lg text-info-6" }), [[showPlan]]) },
];
const batchCount = ref(props.batchSize);
watch(
    () => props.batchSize,
    (newVal) => {
        batchCount.value = newVal;
    }
);
const emits = defineEmits(["update:size", "update:batchSize"]);
// 方向
const squareList = computed(() => {
    return props.shapeList.filter((item) => item.width === item.height);
});
// 垂直方向
const verticalList = computed(() => {
    return props.shapeList.filter((item) => item.width < item.height);
});
// 水平方向
const horizontalList = computed(() => {
    return props.shapeList.filter((item) => item.width > item.height);
});

const renderSize = computed(() => {
    let size = props.size || "";
    if (!size) {
        size = "1024 x 1024";
    }
    const checked = props.shapeList.find((item) => item.value === size);
    if (!checked) {
        return {
            width: 1,
            height: 1,
        };
    }
    return {
        label: checked.label,
        width: checked.width,
        height: checked.height,
    };
});

const closeModel = () => {
    aspectRef.value.setShow(false);
    active.value = false;
};
//选择比例
const chooseAspect = ({ width, height }) => {
    if (!props.includeContentRefer) emits("update:size", `${width} x ${height}`);
    if (!isMobile.value) closeModel();
};
const changeSize = () => {
    emits("update:batchSize", batchCount.value);
    if (!isMobile.value) closeModel();
};

const handleDrawViewRRClick = () => {
    closeModel();
};

const handleDrawViewLLClick = () => {
    closeModel();
};

onDeactivated(() => {
    active.value = false;
});

const showCountChoose = computed(() => {
    return !isMjModel({ model_id: props.modelId }) && !isFluxKontextModel({ model_id: props.modelId });
});
</script>

<style lang="scss" scoped>
.panel-box {
    @apply rounded-lg overflow-hidden relative dark:text-dark-active-text text-sm min-w-80;
}
.aspect-item {
    @apply flex items-center gap-2 p-2 cursor-pointer  rounded-lg;
    &:not(.disabled-item) {
        @apply hover:dark:bg-dark-tag-bg hover:bg-neutral-200;
    }
}
.active-aspect-item:not(.disabled-item) {
    @apply dark:bg-dark-tag-bg  bg-neutral-200;
}
.disabled-item {
    @apply opacity-30 cursor-not-allowed;
}
</style>
