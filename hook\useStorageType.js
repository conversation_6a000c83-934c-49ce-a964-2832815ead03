import { ref } from "vue";

export const useStorageType = () => {
    // 客户端初始化
    const storageType = ref("localStorage");
    const setStorageType = (type) => {
        if (type === "localStorage" || type === "sessionStorage") {
            storageType.value = type;
            if (import.meta.client) {
                localStorage.setItem("storageTypeText", type);
            }
        } else {
            console.error('Invalid storage type. Use "localStorage" or "sessionStorage".');
        }
    };

    return {
        storageType,
        setStorageType,
    };
};
