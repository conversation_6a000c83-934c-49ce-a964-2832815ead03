<template>
    <div class="h-full overflow-hidden p-6 w-[352px] shrink-0 relative">
        <div class="relative h-full flex flex-col px-4 bg-bg-2 rounded-2xl border border-solid border-border-t-1 text-text-2 overflow-y-auto no-scroll font-medium">
            <div class="bg-bg-2 sticky top-0 z-20 pt-4 pb-3">
                <div
                    class="px-3 h-12 flex items-center gap-2 rounded-lg cursor-pointer text-text-opt-1 bg-fill-opt-1 hover:text-text-opt-2 hover:bg-fill-opt-2"
                    :class="{ '!text-text-opt-3 !bg-fill-opt-3': checkedGroup === unorganizedId }"
                    @click="checkCollection(unorganizedId)"
                >
                    <n-icon size="20"><IconsUnorganized /></n-icon>
                    <span>{{ t("COLLECTION_UNORGANIZED") }}</span>
                </div>
                <div class="h-8 flex items-center justify-between text-text-3 mt-4">
                    <span>{{ t("COLLECTION_ORGANIZED") }}</span>

                    <div class="flex items-center gap-1.5 text-xs text-text-2 bg-fill-btn-1 hover:bg-fill-btn-2 h-full px-3 rounded-full cursor-pointer" @click="updateCollectDefs()">
                        <n-icon size="16"><IconsAdd /></n-icon>
                        <span>{{ t("ADD_BUTTON") }}</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-3 text-text-3 text-xs pb-28">
                <div
                    class="rounded-lg group/item h-[182px] cursor-pointer relative overflow-hidden bg-fill-opt-1 text-text-opt-1 flex flex-col hover:bg-fill-opt-2 hover:text-text-opt-2"
                    v-for="item in shareCollect.collectList"
                    :key="item.id"
                    :class="{ 'text-text-opt-3 bg-fill-opt-3': checkedGroup === item.id || dragItem === item.id, 'pointer-events-none': checkedGroup === item.id && dragItem === item.id }"
                    @dragenter.prevent="handleDragover(item.id)"
                    @drop.stop="handleDrop(item.id)"
                    @dragleave.prevent="dragItem = null"
                    @dragover.prevent
                    @dragstart.stop.prevent
                    @click="checkCollection(item.id)"
                >
                    <div v-if="dragItem === item.id" class="flex items-center justify-center flex-col bg-fill-opt-2 text-center w-full h-full gap-2 text-text-opt-2 px-2 pointer-events-none">
                        <IconsFavorite class="text-2xl" />
                        <div v-if="checkedGroup !== unorganizedId">{{ t("COLLECTION_MOVE_COVER") }}</div>
                        <div v-else>{{ t("COLLECTION_ADD_COVER") }}</div>
                    </div>
                    <template v-if="dragItem !== item.id">
                        <div class="w-full aspect-square shrink-0 flex items-center justify-center">
                            <img v-if="item.cover" :src="item.cover" class="aspect-square object-cover" />
                            <div v-else>
                                <img src="@/assets/images/file_cover.webp" class="w-16 h-16 dark:hidden" />
                                <img src="@/assets/images/file_cover_dark.webp" class="hidden w-16 h-16 dark:block" />
                            </div>
                            <!-- <img src="@/assets/images/file_cover.webp" class="w-16 h-16" /> -->
                        </div>
                        <div class="flex flex-col items-center justify-center gap-0.5 flex-1 px-2">
                            <div class="w-full overflow-hidden text-ellipsis whitespace-nowrap text-center">{{ item.collectName }}</div>
                            <div class="text-xs scale-90">{{ numberFormat(item.collectNums || 0) }} P</div>
                        </div>
                        <n-popover trigger="hover" placement="bottom" class="rounded-lg overflow-hidden" raw :show-arrow="false" :zIndex="10">
                            <template #trigger>
                                <div
                                    class="invisible group-hover/item:visible w-7 h-7 flex items-center justify-center bg-fill-t-2 backdrop-blur-sm text-text-white absolute top-1.5 right-1.5 rounded-full"
                                    @click.stop
                                >
                                    <IconsMore class="text-base rotate-90" />
                                </div>
                            </template>
                            <div class="p-2 rounded-lg border border-solid border-border-t-1 bg-bg-6 flex flex-col gap-1 min-w-32">
                                <div class="px-3 h-8 flex items-center gap-2 rounded-[4px] hover:bg-fill-opt-2 text-text-2 cursor-pointer" @click="checkMoreAction('Edit', item)">
                                    <n-icon size="16"><IconsPencil /></n-icon>
                                    <span>{{ t("COMMUNITY_EDIT_BTN") }}</span>
                                </div>
                                <div class="px-3 h-8 flex items-center gap-2 rounded-[4px] hover:bg-fill-opt-2 text-danger-6 cursor-pointer" @click="checkMoreAction('Delete', item)">
                                    <n-icon size="16"><IconsDele /></n-icon>
                                    <span>{{ t("TOOLBAR_DELETE") }}</span>
                                </div>
                            </div>
                        </n-popover>
                    </template>
                </div>
            </div>
        </div>
        <div class="absolute left-10 right-10 bottom-6 bg-bg-2 z-10 pt-4">
            <div class="border border-solid border-border-2 text-text-3 p-3 pb-4 mb-4 rounded-lg">
                <div class="flex items-center gap-2 text-text-2 font-medium">
                    <n-icon size="20"><IconsCloud /></n-icon>
                    <span class="flex-1">{{ t("COLLECTION_STORAGE") }}</span>
                    <span class="text-text-4 text-xs">{{ userProfile.user.usedCollectNum }} / {{ userProfile.user.totalCollectNum }} P</span>
                </div>
                <div class="mt-4 h-1 rounded-full bg-fill-pgs-2">
                    <div class="h-full rounded-full bg-fill-pgs-1" :style="{ width: `${currentUsedStorage || 0}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getCollect, delCollectById } from "@/api";
import { useUserProfile, useShareCollect } from "@/stores/index";
import { useUpdateCloudStorage, useInitCollection } from "@/hook/updateAccount";
import { numberFormat } from "@/utils/tools";
import CollectionModal from "@/components/collections/CollectionModal.vue";
import { NIcon } from "naive-ui";
import { Alert } from "@/icons/index.js";
onMounted(() => {
    useInitCollection();
});
const { t } = useI18n({ useScope: "global" });
const userProfile = useUserProfile();
const { showMessage } = useModal();
const emits = defineEmits(["update:collectId", "handleMoveFiles"]);

const shareCollect = useShareCollect();
const unorganizedId = -1;
const props = defineProps({
    collectId: {
        type: [String, Number],
        default: unorganizedId,
    },
});
const checkedGroup = ref(props.collectId);
//获取当前使用量
const currentUsedStorage = computed(() => Math.min(userProfile.user.usedCollectNum / userProfile.user.totalCollectNum, 1) * 100);
// 收藏夹 列表
// const collectionList = ref([]);
const dragItem = ref(null);
const handleDragover = (id) => {
    if (checkedGroup.value === id) {
        return;
    }
    dragItem.value = id;
};
//选择收藏夹
const checkCollection = (id) => {
    if (props.collectId === id) {
        return;
    }
    checkedGroup.value = id;
    emits("update:collectId", id);
};
const handleDrop = (id) => {
    dragItem.value = null;
    if (checkedGroup.value === id) {
        return;
    }
    // 批量添加到收藏夹
    const params = { command: "Add", targetId: id };
    // 批量移动到收藏夹
    if (checkedGroup.value !== unorganizedId) {
        params.command = "Move";
    }
    emits("handleMoveFiles", params);
};

//新增/修改 收藏夹
const updateCollectDefs = (info = {}) => {
    let elAction = info.id ? "edit_collection" : "new_folder";
    window.trackEvent("Gallery", { el: `${elAction}` });
    showMessage({
        style: { width: "400px" },
        showCancel: false,
        showConfirm: false,
        content: h(CollectionModal, { info }),
    })
        .then((extInfo) => {
            //修改
            if (extInfo.id) {
                const collectionList = shareCollect.collectList;
                const index = collectionList.findIndex((item) => item.id === extInfo.id);
                if (index !== unorganizedId) {
                    collectionList[index].collectName = extInfo.collectName;
                    collectionList[index].description = extInfo.description;
                    collectionList[index].cover = extInfo.cover;
                    shareCollect.setCollectList(collectionList);
                }
                // return;
            }
            // 新增
            useInitCollection();
        })
        .catch((_) => {});
};
//选择的操作
const checkMoreAction = (command, row) => {
    if (command === "Delete") {
        window.trackEvent("Gallery", { el: `delete_collection` });
        if (row.collectNums > 0) {
            try {
                showMessage({
                    style: { width: "480px" },
                    showCancel: false,
                    confirmBtn: t("COMMON_BTN_OK"),
                    content: h("div", null, [h("p", null, t("COLLECTION_DEL_NOT_EMPTY"))]),
                    icon: h(NIcon, { size: 32, class: "text-danger-6" }, { default: () => h(Alert) }),
                    title: t("TOAST_TITLE_WARNING"),
                });
            } catch (error) {}
            return;
        }
        handleDelCollection(row.id);
        return;
    }
    updateCollectDefs(row);
};
//删除指定的收藏夹
const handleDelCollection = async (id) => {
    const hasNext = await showMessage({
        style: { width: "480px" },
        confirmBtn: t("COMMON_BTN_CONTINUE"),
        content: h("div", null, [h("p", null, t("COLLECTION_DEL_TIPS"))]),
        icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_ATTEN"),
    })
        .then(() => Promise.resolve(true))
        .catch((_) => Promise.resolve(false));
    if (!hasNext) {
        return;
    }
    const { status, data = {}, message } = await delCollectById({ id });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    const list = shareCollect.collectList.filter((item) => item.id !== id);
    shareCollect.setCollectList(list);
    useUpdateCloudStorage(data);
    useInitCollection();

    if (list.length === 0) {
        checkedGroup.value = unorganizedId;
        emits("update:collectId", checkedGroup.value);
        return;
    }
    if (checkedGroup.value === id) {
        checkedGroup.value = unorganizedId;
        emits("update:collectId", checkedGroup.value);
    }
};
const updateCount = ({ fromId, toId, changeCount }) => {
    const collectionList = shareCollect.collectList;
    if (fromId) {
        const fromIndex = collectionList.findIndex((item) => item.id === fromId);
        if (fromIndex !== unorganizedId) {
            collectionList[fromIndex].collectNums = Math.max(collectionList[fromIndex].collectNums - changeCount, 0);
        }
    }
    if (toId) {
        const fromIndex = collectionList.findIndex((item) => item.id === toId);
        const oldCount = collectionList[fromIndex].collectNums;
        collectionList[fromIndex].collectNums += changeCount;
        oldCount === 0 && useInitCollection();
    }
    shareCollect.setCollectList(collectionList);
};
const updateItemCover = (id, cover) => {
    const collectionList = shareCollect.collectList;
    const index = collectionList.findIndex((item) => item.id === id);
    collectionList[index].cover = cover;
    shareCollect.setCollectList(collectionList);
};

defineExpose({ updateCount, updateItemCover });
</script>
