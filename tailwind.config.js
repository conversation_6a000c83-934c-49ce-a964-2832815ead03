/** @type {import('tailwindcss').Config} */
export default {
    content: ["./components/**/*.{vue,js,ts}", "./layouts/**/*.vue", "./pages/**/*.vue", "./composables/**/*.{js,ts}", "./plugins/**/*.{js,ts}", "./App.vue", "./app.vue", "./nuxt.config.{js,ts}"],
    darkMode: "class",
    theme: {
        extend: {
            screens: {
                xs: "750px",
                lg: "1025px",
                xl: "1280px",
                temp: "1079px", //临时适配Outpaint等图像编辑功能
                "2xl": "1440px",
                "3xl": "1660px",
                "4xl": "1920px",
            },
            fontSize: {
                "2.5xl": ["1.75rem", "2.25rem"],
                "3.5xl": ["2rem", "2.5rem"],
                "4.5xl": ["2.5rem", "3rem"],
            },
            colors: {
                "dark-bg": "#121216",
                "color-c8": "#C8C8C8",
                "dark-bg-2": "#26262A",
                "dark-bg-3": "#1B1B1F",
                primary: "#7B63FE",
                error: "#F06161",
                "dark-text": "#C2C6CF",
                "dark-active-text": "#F4F4F4", // 主題-功能-作用域
                "dark-desc-text": "#79787E",
                "dark-tag-bg": "#45454A",
                "com-status-waring": "#FF9626",
                "com-status-success": "#27C346",
                dark: {
                    "com-status-waring": "#FFCE47",
                },
                "primary-1": "var(--p-primary-1)",
                "primary-2": "var(--p-primary-2)",
                "primary-3": "var(--p-primary-3)",
                "primary-4": "var(--p-primary-4)",
                "primary-5": "var(--p-primary-5)",
                "primary-6": "var(--p-primary-6)",
                "primary-7": "var(--p-primary-7)",
                "text-1": "var(--p-text-1)",
                "text-2": "var(--p-text-2)",
                "text-3": "var(--p-text-3)",
                "text-4": "var(--p-text-4)",
                "text-5": "var(--p-text-5)",
                "text-6": "var(--p-text-6)",
                "text-white": "var(--p-text-white)",
                "text-dark": "var(--p-text-dark)",
                "text-t-2": "var(--p-text-t-2)",
                "text-t-5": "var(--p-text-t-5)",
                "text-opt-1": "var(--p-text-opt-1)",
                "text-opt-2": "var(--p-text-opt-2)",
                "text-opt-3": "var(--p-text-opt-3)",
                "text-opt-4": "var(--p-text-opt-4)",
                "text-tab-5": "var(--p-text-tab-5)",
                "text-tab-6": "var(--p-text-tab-6)",
                "text-tab-7": "var(--p-text-tab-7)",
                "text-tab-8": "var(--p-text-tab-8)",
                "text-drop-1": "var(--p-text-drop-1)",
                "text-drop-2": "var(--p-text-drop-2)",
                "text-drop-3": "var(--p-text-drop-3)",
                "text-drop-4": "var(--p-text-drop-4)",
                "text-drop-5": "var(--p-text-drop-5)",
                "text-drop-6": "var(--p-text-drop-6)",
                "text-ad-1": "var(--p-text-ad-1)",
                "text-ad-2": "var(--p-text-ad-2)",
                "fill-ww-1": "var(--p-fill-ww-1)",
                "fill-ww-2": "var(--p-fill-ww-2)",
                "fill-ww-3": "var(--p-fill-ww-3)",
                "fill-ww-4": "var(--p-fill-ww-4)",
                "fill-ww-10": "var(--p-fill-ww-10)",
                "fill-ww-11": "var(--p-fill-ww-11)",
                "fill-dd-1": "var(--p-fill-dd-1)",
                "fill-dd-2": "var(--p-fill-dd-2)",
                "fill-dd-3": "var(--p-fill-dd-3)",
                "fill-dd-4": "var(--p-fill-dd-4)",
                "fill-dd-10": "var(--p-fill-dd-10)",
                "fill-wd-0": "var(--p-fill-wd-0)",
                "fill-wd-1": "var(--p-fill-wd-1)",
                "fill-wd-2": "var(--p-fill-wd-2)",
                "fill-wd-3": "var(--p-fill-wd-3)",
                "fill-wd-4": "var(--p-fill-wd-4)",
                "fill-dw-1": "var(--p-fill-dw-1)",
                "fill-dw-2": "var(--p-fill-dw-2)",
                "fill-dw-3": "var(--p-fill-dw-3)",
                "fill-dw-4": "var(--p-fill-dw-4)",
                "fill-dw-5": "var(--p-fill-dw-5)",
                "fill-dw-10": "var(--p-fill-dw-10)",
                "border-t-1": "var(--p-border-t-1)",
                "border-t-2": "var(--p-border-t-2)",
                "border-t-5": "var(--p-border-t-5)",
                "border-1": "var(--p-border-1)",
                "border-2": "var(--p-border-2)",
                "border-3": "var(--p-border-3)",
                "border-4": "var(--p-border-4)",
                "border-ipt-1": "var(--p-border-ipt-1)",
                "border-ipt-2": "var(--p-border-ipt-2)",
                "border-ipt-3": "var(--p-border-ipt-3)",
                "border-ipt-4": "var(--p-border-ipt-4)",
                "border-upload-1": "var(--p-border-upload-1)",
                "border-upload-2": "var(--p-border-upload-2)",
                "border-gradient-0": "var(--p-border-gradient-0)",
                "border-gradient-4": "var(--p-border-gradient-4)",
                "fill-t-1": "var(--p-fill-t-1)",
                "fill-t-2": "var(--p-fill-t-2)",
                "fill-t-3": "var(--p-fill-t-3)",
                "fill-t-4": "var(--p-fill-t-4)",
                "fill-t-5": "var(--p-fill-t-5)",
                "fill-btn-1": "var(--p-fill-btn-1)",
                "fill-btn-2": "var(--p-fill-btn-2)",
                "fill-btn-3": "var(--p-fill-btn-3)",
                "fill-btn-4": "var(--p-fill-btn-4)",
                "fill-btn-5": "var(--p-fill-btn-5)",
                "fill-btn-6": "var(--p-fill-btn-6)",
                "fill-btn-7": "var(--p-fill-btn-7)",
                "fill-btn-8": "var(--p-fill-btn-8)",
                "fill-btn-9": "var(--p-fill-btn-9)",
                "fill-btn-10": "var(--p-fill-btn-10)",
                "fill-btn-11": "var(--p-fill-btn-11)",
                "fill-btn-12": "var(--p-fill-btn-12)",
                "fill-ipt-1": "var(--p-fill-ipt-1)",
                "fill-ipt-2": "var(--p-fill-ipt-2)",
                "fill-ipt-3": "var(--p-fill-ipt-3)",
                "fill-ipt-4": "var(--p-fill-ipt-4)",
                "fill-ipt-s-1": "var(--p-fill-ipt-s-1)",
                "fill-ipt-s-2": "var(--p-fill-ipt-s-2)",
                "fill-ipt-s-3": "var(--p-fill-ipt-s-3)",
                "fill-ipt-s-4": "var(--p-fill-ipt-s-4)",
                "fill-opt-1": "var(--p-fill-opt-1)",
                "fill-opt-2": "var(--p-fill-opt-2)",
                "fill-opt-3": "var(--p-fill-opt-3)",
                "fill-opt-4": "var(--p-fill-opt-4)",
                "fill-drop-1": "var(--p-fill-drop-1)",
                "fill-drop-2": "var(--p-fill-drop-2)",
                "fill-drop-3": "var(--p-fill-drop-3)",
                "fill-drop-4": "var(--p-fill-drop-4)",
                "fill-drop-5": "var(--p-fill-drop-5)",
                "fill-drop-6": "var(--p-fill-drop-6)",
                "fill-tab-1": "var(--p-fill-tab-1)",
                "fill-tab-2": "var(--p-fill-tab-2)",
                "fill-tab-3": "var(--p-fill-tab-3)",
                "fill-tab-4": "var(--p-fill-tab-4)",
                "fill-tab-5": "var(--p-fill-tab-5)",
                "fill-tab-6": "var(--p-fill-tab-6)",
                "fill-tab-7": "var(--p-fill-tab-7)",
                "fill-pgs-1": "var(--p-fill-pgs-1)",
                "fill-pgs-2": "var(--p-fill-pgs-2)",
                "fill-slider-1": "var(--p-fill-slider-1)",
                "fill-slider-2": "var(--p-fill-slider-2)",
                "fill-slider-3": "var(--p-fill-slider-3)",
                "fill-slider-4": "var(--p-fill-slider-4)",
                "fill-slider-5": "var(--p-fill-slider-5)",
                "fill-slider-6": "var(--p-fill-slider-6)",
                "fill-switch-1": "var(--p-fill-switch-1)",
                "fill-switch-3": "var(--p-fill-switch-3)",
                "fill-switch-2": "var(--p-fill-switch-2)",
                "fill-switch-4": "var(--p-fill-switch-4)",
                "fill-switch-5": "var(--p-fill-switch-5)",
                "fill-switch-6": "var(--p-fill-switch-6)",
                "fill-tag-brand-t-3": "var(--p-fill-tag-brand-t-3)",
                "fill-tag-red-t-3": "var(--p-fill-tag-red-t-3)",
                "fill-tag-gray-t-2": "var(--p-fill-tag-gray-t-2)",
                "fill-tag-green-t-3": "var(--p-fill-tag-green-t-3)",
                "fill-tag-orange-t-3": "var(--p-fill-tag-orange-t-3)",
                "bg-1": "var(--p-bg-1)",
                "bg-2": "var(--p-bg-2)",
                "bg-3": "var(--p-bg-3)",
                "bg-4": "var(--p-bg-4)",
                "bg-5": "var(--p-bg-5)",
                "bg-6": "var(--p-bg-6)",
                "bg-7": "var(--p-bg-7)",
                "success-1": "var(--p-success-1)",
                "success-2": "var(--p-success-2)",
                "success-3": "var(--p-success-3)",
                "success-4": "var(--p-success-4)",
                "success-5": "var(--p-success-5)",
                "success-6": "var(--p-success-6)",
                "success-7": "var(--p-success-7)",
                "info-1": "var(--p-info-1)",
                "info-2": "var(--p-info-2)",
                "info-3": "var(--p-info-3)",
                "info-4": "var(--p-info-4)",
                "info-5": "var(--p-info-5)",
                "info-6": "var(--p-info-6)",
                "info-7": "var(--p-info-7)",
                "warning-1": "var(--p-warning-1)",
                "warning-2": "var(--p-warning-2)",
                "warning-3": "var(--p-warning-3)",
                "warning-4": "var(--p-warning-4)",
                "warning-5": "var(--p-warning-5)",
                "warning-6": "var(--p-warning-6)",
                "warning-7": "var(--p-warning-7)",
                "danger-1": "var(--p-danger-1)",
                "danger-2": "var(--p-danger-2)",
                "danger-3": "var(--p-danger-3)",
                "danger-4": "var(--p-danger-4)",
                "danger-5": "var(--p-danger-5)",
                "danger-6": "var(--p-danger-6)",
                "danger-7": "var(--p-danger-7)",
                "link-1": "var(--p-link-1)",
                "link-2": "var(--p-link-2)",
                "link-3": "var(--p-link-3)",
                "link-4": "var(--p-link-4)",
                "link-5": "var(--p-link-5)",
                "link-6": "var(--p-link-6)",
                "link-7": "var(--p-link-7)",
                "fill-upload-1": "var(--p-fill-upload-1)",
                "fill-upload-2": "var(--p-fill-upload-2)",
                "fill-gradient-8": "var(--p-fill-gradient-8)",
                "fill-gradient-9": "var(--p-fill-gradient-9)",
                "fill-gradient-10": "var(--p-fill-gradient-10)",
            },

            borderRadius: {
                "4xl": "1.75rem",
                "5xl": "2rem",
            },
            lineHeight: {
                5.5: "1.375rem",
            },
            fontFamily: {
                bebas: ['"Bebas Neue"', "sans-serif"],
            },
        },
    },
    variants: {
        extend: {
            backgroundColor: ["dark"],
            textColor: ["dark"],
        },
    },
    plugins: [],
    corePlugins: {
        // preflight: false, // 如果与 Naive UI 样式冲突时启用
    },
};
