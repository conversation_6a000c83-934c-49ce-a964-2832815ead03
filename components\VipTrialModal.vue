<!--
 * @Author: HuangQS
 * @Description: VIP试用组件弹出框
 * @Date: 2025-06-04 10:47:06
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-06-09 14:07:50
-->

<template>
    <div class="flex w-full h-full rounded-2xl bg-bg-2 overflow-hidden" :class="isMobile ? 'flex-col' : '!w-[720px] !h-[460px]'">
        <!-- 左(网页端) -->
        <template v-if="!isMobile">
            <div class="left-bg flex flex-col w-[320px] h-full pt-10 pl-8 pr-8 relative">
                <div v-if="false" class="flex flex-row w-full gap-4 z-10">
                    <!-- 左侧 -->
                    <div class="flex flex-col items-center">
                        <template v-for="(item, index) in lDatas" :key="item.id">
                            <div class="bg-white rounded-full size-6 shrink-0 text-black text-base font-semibold flex items-center justify-center">{{ item.id + 1 }}</div>
                            <img v-if="index != lDatas.length - 1" class="w-2 h-[96px] bg-red-200 my-4" src="@/assets/images/viptrial/image_vip_trial_mobile_arrow.webp" />
                        </template>
                    </div>

                    <!-- 右侧 -->
                    <div class="flex flex-col items-center">
                        <template v-for="(item, index) in lDatas" :key="item.id">
                            <div>
                                <div class="text-sm font-medium text-white">{{ item.title }}</div>
                                <div class="text-neutral-300 text-xs font-medium mt-1">{{ item.desc }}</div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 内容区域 -->

                <div class="flex flex-col w-full z-10">
                    <div v-for="(item, index) in lDatas" :key="item.id" class="flex text-white gap-4">
                        <!-- 左侧球球数字 -->
                        <div class="flex flex-col items-center gap-2">
                            <div class="bg-white rounded-full size-6 shrink-0 text-black text-base font-semibold flex items-center justify-center">
                                {{ item.id + 1 }}
                            </div>

                            <img v-if="index != lDatas.length - 1" class="w-6 h-[96px] my-4" src="@/assets/images/viptrial/image_vip_trial_mobile_arrow.webp" />
                        </div>

                        <!-- 右侧文字 -->
                        <div>
                            <div class="text-sm font-medium text-white">{{ item.title }}</div>
                            <div class="text-neutral-300 text-xs font-medium mt-1">{{ item.desc }}</div>
                        </div>
                    </div>
                </div>

                <!-- 背景 -->
                <img class="absolute dark:block bottom-0 left-0 size-full z-0" src="@/assets/images/viptrial/image_vip_trial_left_bg.webp" />
                <img class="absolute dark:hidden bottom-0 left-0 size-full z-0" src="@/assets/images/viptrial/image_vip_trial_left_bg_light.webp" />
            </div>
        </template>
        <!-- 上(移动端) -->
        <template v-else>
            <div class="left-bg flex flex-col w-full h-[210px] relative -mb-5">
                <img class="absolute bottom-0 left-0 size-full z-0" src="@/assets/images/viptrial/image_vip_trial_mobile_bg.webp" />
                <img class="absolute bottom-0 left-0 w-full h-[158px] z-0" src="@/assets/images/viptrial/image_vip_trial_mobile_gift.webp" />
                <div class="absolute top-0 right-0 bottom-0 left-0 size-full z-0 flex items-center justify-center">
                    <n-icon size="64"> <IconsVipStandard /> </n-icon>
                </div>

                <div class="absolute size-8 top-2 right-2 flex items-center justify-center" @click="hanleCloseModal">
                    <n-icon size="20" class="text-text-t-5"> <IconsClose /> </n-icon>
                </div>
            </div>
        </template>

        <!-- 右 -->
        <div class="flex flex-col items-center flex-1 h-full pt-8 px-6 pb-6 gap-8 relative" :class="isMobile ? ' rounded-2xl bg-bg-2' : ''">
            <!-- 头部区域 -->
            <div class="flex flex-col items-center">
                <n-icon v-if="!isMobile" size="40"> <IconsVipStandard /> </n-icon>
                <span class="right-title">{{ $t(`VIP_TRIAL.TITLE`) }}</span>
            </div>

            <!-- 内容介绍 -->
            <div class="flex w-full flex-col gap-2">
                <template v-if="!isMobile">
                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <img class="size-5 text-5.5" src="@/assets/images/viptrial/image_vip_trial_finger.webp" />
                        <div class="bg-clip-text text-transparent bg-gradient-to-r from-[rgba(0,193,154,1)] to-[rgba(32,183,97,1)]">
                            {{ t("VIP_TRIAL.RIGHT.INCLUDE_1") }}
                        </div>
                    </div>

                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <n-icon size="16" class="size-5 text-success-6"><IconsSuccess /> </n-icon>
                        <i18n-t keypath="VIP_TRIAL.RIGHT.INCLUDE_2" tag="div">
                            <template v-slot:content_str>
                                <span class="text-primary-6"> {{ t("VIP_TRIAL.RIGHT.INCLUDE_2", 2) }} </span>
                            </template>
                        </i18n-t>
                    </div>

                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <n-icon size="16" class="size-5 text-success-6"><IconsSuccess /> </n-icon>
                        <i18n-t keypath="VIP_TRIAL.RIGHT.INCLUDE_3" tag="div">
                            <template v-slot:count> <span class="text-primary-6 px-1"> 2 </span> </template>
                        </i18n-t>
                    </div>
                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <n-icon size="16" class="size-5 text-success-6"><IconsSuccess /> </n-icon>
                        <i18n-t keypath="VIP_TRIAL.RIGHT.INCLUDE_4" tag="div">
                            <template v-slot:count> <span class="text-primary-6 px-1"> 5 </span> </template>
                        </i18n-t>
                    </div>
                </template>
                <template v-else>
                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <img class="size-5 text-5.5" src="@/assets/images/viptrial/image_vip_trial_finger.webp" />
                        <div class="bg-clip-text text-transparent bg-gradient-to-r from-[rgba(0,193,154,1)] to-[rgba(32,183,97,1)]">
                            {{ t("VIP_TRIAL.RIGHT.INCLUDE_MO_1") }}
                        </div>
                    </div>

                    <!-- $0 for a 3-day trial -->
                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <n-icon size="16" class="size-5 text-success-6"><IconsSuccess /> </n-icon>

                        <i18n-t keypath="VIP_TRIAL.RIGHT.INCLUDE_MO_2" tag="div">
                            <template #num1>
                                <span class="text-primary-6"> {{ t("VIP_TRIAL.RIGHT.INCLUDE_MO_2_NUM1") }} </span>
                            </template>
                            <template #key1>
                                <span class="text-primary-6"> {{ t("VIP_TRIAL.RIGHT.INCLUDE_MO_2_KEY1") }} </span>
                            </template>
                        </i18n-t>
                    </div>

                    <!-- Unlock all features -->
                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <n-icon size="16" class="size-5 text-success-6"><IconsSuccess /> </n-icon>
                        <i18n-t keypath="VIP_TRIAL.RIGHT.INCLUDE_MO_3" tag="div">
                            <template v-slot:content>
                                <span class="text-primary-6"> {{ t("VIP_TRIAL.RIGHT.INCLUDE_MO_3", 2) }} </span>
                            </template>
                        </i18n-t>
                    </div>
                    <div class="flex items-start w-full gap-2 text-sm font-medium">
                        <n-icon size="16" class="size-5 text-success-6"><IconsSuccess /> </n-icon>
                        <i18n-t keypath="VIP_TRIAL.RIGHT.INCLUDE_MO_4" tag="div"> </i18n-t>
                    </div>
                </template>

                <div class="flex items-center gap-2 text-text-4 cursor-pointer hover:text-text-2 text-sm w-max" @click="handleMoreClick">
                    <span class="size-5 text-5.5"> </span>
                    <div class="flex gap-1 items-center">
                        <span class="hover:underline">{{ $t("VIP_TRIAL.RIGHT.LEARN_MORE") }}</span>
                        <n-icon size="20"> <IconsArrowRight /> </n-icon>
                    </div>
                </div>
            </div>

            <div class="flex flex-col items-center mt-auto text-text-4 gap-4">
                <div class="text-xs text-center">
                    <i18n-t keypath="VIP_TRIAL.RIGHT.MONEY_DESC" tag="div">
                        <template #num1>
                            <span class="text-text-3 font-semibold"> 75.52 </span>
                        </template>
                        <template #num2>
                            <span class="text-text-3 font-semibold"> 6.30 </span>
                        </template>
                    </i18n-t>
                </div>

                <Button block :loading="isLoading" :disabled="isLoading" @click.once="handleSubscribe">{{ $t("VIP_TRIAL.RIGHT.BUTTON") }}</Button>
            </div>

            <template v-if="!isMobile">
                <div class="absolute size-8 top-4 right-4 cursor-pointer hover:bg-fill-wd-1 hover:rounded-full flex items-center justify-center" @click="hanleCloseModal">
                    <n-icon size="20"> <IconsClose /> </n-icon>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup>
import { useCallSubscribePlan, useSubscribeErrorModal } from "@/hook/subscribe.js";
import { BILLING_TYPE, SUBSCRIBE_TYPE, PAYMENT_METHOD } from "@/utils/constant.js";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const { t } = useI18n({ useScope: "global" });
const { isMobile } = storeToRefs(useThemeStore());
const { showErrorModal } = useSubscribeErrorModal();
const router = useRouter();
const isLoading = ref(false);

onMounted(() => {
    customTrackEvent("new_free_trial_popup_show");
});

const customTrackEvent = (el) => {
    window.trackEvent("Commercialization", { el });
};

// js获取3天后的时间 并转换格式为：3 June, 2025
const getFutureDate = (days) => {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" });
};

const lDatas = ref([
    { id: 0, title: t("VIP_TRIAL.LEFT.TITLE_1"), desc: t("VIP_TRIAL.LEFT.CONTENT_1") },
    { id: 1, title: `${getFutureDate(3)} - ${t("VIP_TRIAL.LEFT.TITLE_2")}`, desc: t("VIP_TRIAL.LEFT.CONTENT_2") },
]);

//点击立即订阅
const handleSubscribe = async () => {
    isLoading.value = true;
    customTrackEvent("new_free_trial_popup_subscribe");
    window.trackEvent("Subscribe", { el: "subscribe_from=new_free_trial_popup" });

    try {
        // 发起订阅计划
        const response = await useCallSubscribePlan({
            product: SUBSCRIBE_TYPE.STANDARD,
            priceInterval: BILLING_TYPE.YEARLY,
            paymentMethod: PAYMENT_METHOD.STRIPE,
        });
        if (!response) return;

        const { code } = response;

        if (code != 0) {
            showErrorModal("SUBSCRIBE_EX_TEXT", { code: code });
        }

        emits("cancel");
    } catch (error) {
    } finally {
        isLoading.value = false;
    }
};

const emits = defineEmits(["cancel"]);

const hanleCloseModal = () => {
    emits("cancel");
};
const localePath = useLocalePath();
const handleMoreClick = async () => {
    customTrackEvent("new_free_trial_popup_learn_more");
    emits("cancel");
    await navigateTo({ path: localePath("/user/subscribe") });
};
</script>

<style lang="scss">
.left-bg {
    background: linear-gradient(154deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 33%, rgba(255, 255, 255, 0) 66%, rgba(255, 255, 255, 0.16) 100%);
}

.right-title {
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    background: linear-gradient(90deg, #00c19a 0%, #20b761 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 8px;
}
</style>
