// https://nuxt.com/docs/api/configuration/nuxt-config
import { writeFileSync } from "node:fs";
import { join } from "node:path";
import Components from "unplugin-vue-components/vite";
import { NaiveUiResolver } from "unplugin-vue-components/resolvers";
import AutoImport from "unplugin-auto-import/vite";

const timestamp = Date.now().toString();
const baseCdnUrl = process.env.CDN_URL || "";
const cdnUrlWithTimestamp = baseCdnUrl ? `${baseCdnUrl}/app-${timestamp}/` : "";

export default defineNuxtConfig({
    compatibilityDate: "2025-06-15",
    devtools: { enabled: true },
    runtimeConfig: {
        public: {
            //  GA4 追踪 ID
            env: process.env.NUXT_PUBLIC_ENV || "development",
            gaMeasureId: "G-8GVHYJ2FGV",
            apiBase: process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3000",
            version: "*******",
        },
    },
    content: ["./components/**/*.{js,vue,ts}", "./layouts/**/*.vue", "./pages/**/*.vue", "./customComp/**/*.vue", "./plugins/**/*.{js,ts}", "./nuxt.config.{js,ts}", "./app.vue"],
    modules: ["@nuxtjs/tailwindcss", "@pinia/nuxt", "@nuxtjs/i18n", "pinia-plugin-persistedstate/nuxt", "nuxtjs-naive-ui", "@nuxtjs/critters"],
    piniaPersistedstate: { storage: "localStorage" },
    hooks: {
        "nitro:build:public-assets": async (nitro) => {
            writeFileSync(join(nitro.options.output.dir, "version.txt"), timestamp);
            console.log(`🕒 构建时间戳生成: ${timestamp}----------${cdnUrlWithTimestamp}`);
        },
    },
    routeRules: {
        "/account/login": { ssr: false }, // 禁用登录页面的 SSR
    },
    app: {
        baseURL: "/app/",
        cdnURL: cdnUrlWithTimestamp || "", //
        head: {
            meta: [
                { name: "format-detection", content: "telephone=no" },
                { name: "apple-mobile-web-app-capable", content: "yes" },
                { name: "mobile-web-app-capable", content: "yes" },
                { name: "apple-mobile-web-app-status-bar-style", content: "black" },
            ],
            script: [
                // 预加载中提前声明window事件
                { innerHTML: `window.trackEvent=()=>{}` },
            ],
            // title: "Free AI Image Generator for AI Art Creation - PicLumen",
            link: [{ rel: "icon", type: "image/svg+xml", href: "https://www.piclumen.com/wp-content/themes/piclumen/assets/build/images/favicon.svg" }],
            htmlAttrs: {
                lang: "en",
            },
            charset: "utf-8",
            viewport: "initial-scale=1.0, user-scalable=no, maximum-scale=1, width=device-width",
        },
    },
    vite: {
        ssr: { noExternal: ["vueuc"] }, // 或 external: [], noExternal: ['vueuc']
        build: {
            minify: "terser",
            // 压缩选项
            terserOptions: {
                compress: {
                    // 去除console语句
                    drop_console: true,
                    drop_debugger: true,
                },
                format: {
                    comments: false, // 移除注释
                },
            },
        },

        plugins: [
            AutoImport({
                imports: [
                    {
                        "naive-ui": ["useDialog", "useMessage", "useNotification", "useLoadingBar"],
                    },
                ],
            }),
            Components({
                imports: [
                    "vue",
                    "@vueuse/core", // 明确声明自动导入
                    "pinia",
                ],
                resolvers: [NaiveUiResolver()],
            }),
        ],
        css: {
            devSourcemap: true, // 可选：生成 sourcemap 但不提取文件
            preprocessorMaxWorkers: true,
        },
    },
    i18n: {
        locales: [
            { code: "en", name: "英语", file: "en_us.yaml" },
            { code: "de", name: "德语", file: "de_de.yaml" },
            { code: "es", name: "西班牙", file: "es_es.yaml" },
            { code: "fr", name: "法语", file: "fr_fr.yaml" },
            { code: "it", name: "意大利", file: "it_it.yaml" },
            { code: "ja", name: "日语", file: "ja_jp.yaml" },
            { code: "ko", name: "韩语", file: "ko_kr.yaml" },
            { code: "pt", name: "葡萄牙", file: "pt_pt.yaml" },
            { code: "zhTW", name: "繁体中文", file: "zh_tw.yaml" },
        ],
        defaultLocale: "en",
        lazy: true, // 开启按需加载
        langDir: "locales",
        strategy: "prefix_except_default", // 推荐用 prefix！
        detectBrowserLanguage: {
            useCookie: true,
            cookieKey: "i18n_redirected",
            alwaysRedirect: true, // 推荐
            fallbackLocale: "en",
        },
    },
    css: ["@/assets/css/tailwind.css", "@/assets/css/global.css", "@/assets/css/common.scss"],

    components: true,

    build: {
        transpile: ["naive-ui", "@easeus/editor-core"],
        analyze: true,
    },
    nitro: {
        compressPublicAssets: true,
        minify: true,
        headers: {
            "Cache-Control": "public, max-age=31536000, immutable", // 缓存一年，不可变
        },
        output: {
            dir: "nuxt_web",
        },
    },
    serverMiddleware: ["~/server/middleware/theme.server.js"],
    devServer: {
        host: "localhost",
        port: 3000,
    },
    experimental: {
        lazyHydration: true,
        treeshakeClientOnly: true,
        purgeCachedData: true,
        sharedPrerenderData: true,
    },
});
