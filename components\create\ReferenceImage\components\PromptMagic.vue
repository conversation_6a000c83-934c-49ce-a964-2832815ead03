<template>
    <div class="flex flex-col w-full h-full scroll-container max-h-full font-medium pb-4 lg:pb-0 gap-2 no-scroll">
        <template v-for="group in magicItemsGroup" :key="group.type">
            <p class="text-text-4 h-10 leading-10">
                {{ t(group.label) }}
            </p>
            <div class="w-full flex flex-wrap gap-2">
                <div
                    v-for="item in group.items"
                    :key="item.name"
                    class="text-center md:text-left rounded-lg bg-fill-wd-1 cursor-pointer w-[calc((100%-16px)/3)] lg:w-24 lg:h-[130px] hover:!bg-fill-wd-2 transition-all duration-[250] relative shrink-0"
                    :class="{ 'selected-border': isItemSelected(item) }"
                    @click="handleChoose(item)"
                >
                    <img class="w-full aspect-square object-cover rounded-t-lg" :src="renderStaticImage(item.cover)" alt="" />
                    <div class="h-8 flex items-center justify-center text-xs">
                        <span>{{ item.name }}</span>
                    </div>
                    <div class="selected-mask rounded-[6px]" :class="[isItemSelected(item) ? 'opacity-100' : 'opacity-0']"></div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup>
import { renderStaticImage } from "@/utils/tools";
import { magicTypes, magicItems } from "@/utils/magic";
import { REFER_TYPES } from "@/utils/constant";
const props = defineProps({
    selectedMagic: {
        type: Object,
        default: () => ({}),
    },
});
const emits = defineEmits(["change"]);

const magicItemsGroup = ref([]);
const handleChoose = (item) => {
    emits("change", {
        ...item,
        displayType: REFER_TYPES.PROMPT_MAGIC,
    });
    window.trackEvent("Create", { el: "prompt_magic=" + item.name });
};

const isItemSelected = (item) => {
    const { type, name } = item;
    const { mainCategory, subCategory } = props.selectedMagic;
    return type === mainCategory && name === subCategory;
};

onMounted(() => {
    //对magicItems 进行分组 根据type字段 创建一个二维数组
    magicItems.forEach((item) => {
        const index = magicItemsGroup.value.findIndex((group) => group.type === item.type);
        if (index > -1) {
            magicItemsGroup.value[index].items.push(item);
        } else {
            const { label = "" } = magicTypes.find((cat) => cat.name === item.type);
            magicItemsGroup.value.push({
                type: item.type,
                label,
                items: [item],
            });
        }
    });
});
</script>

<style lang="scss" scoped></style>
