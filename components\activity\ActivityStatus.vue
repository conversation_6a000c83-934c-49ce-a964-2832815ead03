<template>
    <div class="absolute top-2 left-2 h-6 flex items-center text-white px-[6px] gap-x-1 rounded-full" :class="[classStr]">
        <n-icon size="16">
            <component :is="statusMap[status]?.icon" />
        </n-icon>
        <label class="font-medium">{{ text }}</label>
    </div>
</template>

<script setup>
import { convertBeijingToLocal, numberFormat, timeDifference, dateIsAfter } from "@/utils/tools";
import IconTime from "@/components/icons/Time.vue";
import IconCloseRound from "@/components/icons/CloseRound.vue";
import IconActivity from "@/components/icons/Activity.vue";
import { computed } from "vue";

const props = defineProps({
    activity: {
        type: Object,
        default: () => ({}),
    },
});
const { t } = useI18n({ useScope: "global" });

const statusMap = {
    0: {
        icon: IconCloseRound,
        text: t("ACTIVITY.ENDED"),
        class: "bg-text-4",
    },
    1: {
        icon: IconTime,
        text: t("ACTIVITY.ONGOING"),
        class: "bg-success-6",
    },
    2: {
        icon: IconActivity,
        text: t("ACTIVITY.RATING"),
        class: "bg-warning-6",
    },
};
const status = computed(() => {
    const { beginTime, postEndTime, endTime } = props.activity;
    if (!postEndTime || !endTime) {
        return 0;
    }
    // const beginTimeLocal = convertBeijingToLocal(beginTime)
    const postEndTimeLocal = convertBeijingToLocal(postEndTime);
    const endTimeLocal = convertBeijingToLocal(endTime);
    const curDate = new Date();
    // 当前时间大于 结束时间 = 已结束
    if (dateIsAfter(curDate, endTimeLocal)) {
        return 0;
    }
    // 当前时间 大于投稿时间 = 评奖中
    else if (dateIsAfter(curDate, postEndTimeLocal)) {
        return 2;
    } else {
        return 1;
    }
});
const text = computed(() => {
    if (status.value === 1) {
        const { postEndTime } = props.activity;
        return `${t("ACTIVITY.EXPIRES_IN")}: ${timeDifference(new Date(), convertBeijingToLocal(postEndTime))}`;
    }
    return statusMap[status.value].text;
});
const classStr = computed(() => {
    return statusMap[status.value].class;
});
</script>
