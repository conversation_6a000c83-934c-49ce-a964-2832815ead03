<script setup>
import IconVipBasic from "@/components/icons/VipBasic.vue";
import IconVipStandard from "@/components/icons/VipStandard.vue";
import IconVipPro from "@/components/icons/VipPro.vue";

import { SUBSCRIBE_TYPE } from "@/utils/constant";

const props = defineProps({
    vipType: { type: String, default: SUBSCRIBE_TYPE.BASIC },
});

// Vip类型映射
const vipMapping = {
    [SUBSCRIBE_TYPE.BASIC]: {
        icon: IconVipBasic,
        className: "basic-plan",
        label: "SUBSCRIBE_PLAN_BASIC",
    },
    [SUBSCRIBE_TYPE.STANDARD]: {
        icon: IconVipStandard,
        className: "standard-plan",
        label: "SUBSCRIBE_PLAN_STANDARD",
    },
    [SUBSCRIBE_TYPE.PRO]: {
        icon: IconVipPro,
        className: "pro-plan",
        label: "SUBSCRIBE_PLAN_PRO",
    },
};

const currentVip = computed(() => vipMapping[props.vipType] || vipMapping[SUBSCRIBE_TYPE.BASIC]);
</script>

<template>
    <div :class="[currentVip.className]" class="basic-plan pl-5 pr-2 border-2 border-solid border-transparent rounded-full relative flex items-center">
        <component :is="currentVip.icon" class="absolute -left-4 text-3.5xl" />
        <span class="text-sm text-white font-medium">{{ $t(currentVip.label) }}</span>
    </div>
</template>

<style lang="scss" scoped>
.basic-plan {
    @apply border-[#BCBCBC];
    background: linear-gradient(90deg, #7c7c7c 0%, #bcbcbc 100%);
}

.standard-plan {
    @apply border-[#3CEA85];
    background: linear-gradient(90deg, #2c9e5d 0%, #3cd97e 100%);
}

.pro-plan {
    @apply border-[#FFDD34];
    background: linear-gradient(90deg, #fb6842 0%, #ffd500 100%);
}
</style>
