<template>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_7748_5108" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="1" y="3" width="22" height="18">
            <path
                d="M21.7657 8.94289C22.4874 10.1457 22.2793 11.6879 21.2647 12.6564L14.0714 19.5227C12.9122 20.6293 11.0878 20.6293 9.92857 19.5227L2.73532 12.6564C1.7207 11.6879 1.5126 10.1457 2.23427 8.94289L4.62609 4.95651C5.16826 4.0529 6.14478 3.5 7.19857 3.5H16.8014C17.8552 3.5 18.8317 4.0529 19.3739 4.95651L21.7657 8.94289Z"
                fill="black"
            />
        </mask>
        <g mask="url(#mask0_7748_5108)">
            <g filter="url(#filter0_f_7748_5108)">
                <g clip-path="url(#paint0_angular_7748_5108_clip_path)" data-figma-skip-parse="true">
                    <g transform="matrix(-0.01 0.007 -0.007 -0.01 12 11)">
                        <foreignObject x="-1483.22" y="-1483.22" width="2966.44" height="2966.44"
                            ><div
                                xmlns="http://www.w3.org/1999/xhtml"
                                style="
                                    background: conic-gradient(
                                        from 90deg,
                                        rgba(0, 164, 193, 1) 0deg,
                                        rgba(255, 140, 0, 1) 61.2deg,
                                        rgba(255, 149, 0, 1) 118.8deg,
                                        rgba(242, 195, 63, 1) 180deg,
                                        rgba(113, 198, 213, 1) 241.2deg,
                                        rgba(50, 155, 173, 1) 298.8deg,
                                        rgba(0, 164, 193, 1) 360deg
                                    );
                                    height: 100%;
                                    width: 100%;
                                    opacity: 1;
                                "
                            ></div
                        ></foreignObject>
                    </g>
                </g>
                <rect
                    y="-1"
                    width="24"
                    height="24"
                    data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.55000001192092896,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.17000001668930054},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.58431375026702881,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.33000004291534424},{&#34;color&#34;:{&#34;r&#34;:0.94999998807907104,&#34;g&#34;:0.76673227548599243,&#34;b&#34;:0.25059053301811218,&#34;a&#34;:1.0},&#34;position&#34;:0.50},{&#34;color&#34;:{&#34;r&#34;:0.44313725829124451,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.83529412746429443,&#34;a&#34;:1.0},&#34;position&#34;:0.66999995708465576},{&#34;color&#34;:{&#34;r&#34;:0.19719998538494110,&#34;g&#34;:0.60809361934661865,&#34;b&#34;:0.68000000715255737,&#34;a&#34;:1.0},&#34;position&#34;:0.82999998331069946},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.64419054985046387,&#34;b&#34;:0.75999999046325684,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.55000001192092896,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.17000001668930054},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.58431375026702881,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.33000004291534424},{&#34;color&#34;:{&#34;r&#34;:0.94999998807907104,&#34;g&#34;:0.76673227548599243,&#34;b&#34;:0.25059053301811218,&#34;a&#34;:1.0},&#34;position&#34;:0.50},{&#34;color&#34;:{&#34;r&#34;:0.44313725829124451,&#34;g&#34;:0.77647060155868530,&#34;b&#34;:0.83529412746429443,&#34;a&#34;:1.0},&#34;position&#34;:0.66999995708465576},{&#34;color&#34;:{&#34;r&#34;:0.19719998538494110,&#34;g&#34;:0.60809361934661865,&#34;b&#34;:0.68000000715255737,&#34;a&#34;:1.0},&#34;position&#34;:0.82999998331069946},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.64419054985046387,&#34;b&#34;:0.75999999046325684,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-20.000007629394531,&#34;m01&#34;:-14.000003814697266,&#34;m02&#34;:29.000007629394531,&#34;m10&#34;:14.000004768371582,&#34;m11&#34;:-20.000007629394531,&#34;m12&#34;:13.999998092651367},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
                />
            </g>
        </g>
        <path d="M15.5 13L12 16.5L8.5 13" stroke="white" stroke-opacity="0.8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        <defs>
            <filter id="filter0_f_7748_5108" x="-4" y="-5" width="32" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_7748_5108" />
            </filter>
            <clipPath id="paint0_angular_7748_5108_clip_path"><rect y="-1" width="24" height="24" /></clipPath>
        </defs>
    </svg>
</template>
