import { computed } from "vue";
import { renderStaticImage, isFluxModel, isPonyV6, isLineArtModel, isAnimeModel, isPicLumenArtV1, isFluxDevModel } from "@/utils/tools";
const maxPixel = 200 * 10000;
// 是否允许超分
export const allowUpscale = computed(() => {
    return ({ realHeight, realWidth, originCreate, sensitive, model_id }) => !sensitive && (maxPixel > realHeight * realWidth) && !isFluxKreaModel({ model_id });
});
// 是否允许扩图
export const allowExpand = computed(() => {
    return ({ realHeight, realWidth, sensitive, model_id }) => !sensitive && maxPixel > realHeight * realWidth && !isFluxDevModel({ model_id });
});
// 是否允许局部重绘
export const allowInpaint = computed(() => {
    return ({ realHeight, realWidth, sensitive, model_id }) => !sensitive && maxPixel > realHeight * realWidth && !isFluxDevModel({ model_id });
});
// 是否允许上色
export const allowReColorize = computed(() => {
    return ({ realHeight, realWidth, sensitive, model_id }) => !sensitive && maxPixel > realHeight * realWidth && !isFluxDevModel({ model_id });
});
import { realisticModelId, animeModelId, ponyV6ModelId } from "@/utils/constant";
// 是否允许Vary
export const allowVary = computed(() => {
    const modelList = [
        realisticModelId, // 真实
        animeModelId, // 动漫
        ponyV6ModelId, // pony v6
    ];
    return ({ realHeight, realWidth, sensitive, originCreate, model_id }) => !sensitive && maxPixel > realHeight * realWidth && modelList.includes(model_id);
});

//模型选择组件渲染函数
const realistic = renderStaticImage("modIcon/m-realistic-v2.webp");
const anime = renderStaticImage("modIcon/m-anime-v2.webp");
const piclumenArtV1 = renderStaticImage("modIcon/m-piclumen-art-v1.webp");
const lineart = renderStaticImage("modIcon/m-lineart-v1.webp");
const schnell = renderStaticImage("modIcon/m-flux.1-schnell.webp");
const ponyV6 = renderStaticImage("modIcon/m-pony-v6.webp");
const fluxDev = renderStaticImage("modIcon/m-flux.1-dev.webp");

export const renderModelIcon = computed(() => {
    if (import.meta.server) {
        return "";
    }
    return (model_id) => {
        let icon = realistic;
        if (isAnimeModel({ model_id })) {
            icon = anime;
        }
        if (isLineArtModel({ model_id })) {
            icon = lineart;
        }
        if (isFluxModel({ model_id })) {
            icon = schnell;
        }
        if (isPicLumenArtV1({ model_id })) {
            icon = piclumenArtV1;
        }
        if (isFluxDevModel({ model_id })) {
            icon = fluxDev;
        }
        if (isPonyV6({ model_id })) {
            icon = ponyV6;
        }
        return icon;
    };
});
