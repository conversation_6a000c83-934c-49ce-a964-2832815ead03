<template>
    <div class="flex flex-col gap-2">
        <div v-for="item in formateOptions" :key="item[field.value]" class="flex items-center gap-1 text-text-2 cursor-pointer item-radio" @click="$emit('update:value', item[field.value])">
            <div class="radio-icon w-8 h-8 rounded-full flex items-center justify-center border-[6px] border-none border-fill-wd-1 shrink-0">
                <div class="border-2 border-solid border-border-3 rounded-full w-5 h-5 flex items-center justify-center" :class="{ '!border-primary-6': value === item[field.value] }">
                    <div class="w-2.5 h-2.5 rounded-full bg-primary-6" :class="{ hidden: value !== item[field.value] }"></div>
                </div>
            </div>
            <span>{{ item[field.label] }}</span>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    value: {
        type: [Number, String, null, undefined],
    },
    options: {
        type: Array,
        required: true,
        default: () => [],
    },
    field: {
        type: Object,
        default: () => ({
            label: "label",
            value: "value",
        }),
    },
});
const formateOptions = computed(() => {
    const item = props.options[0];
    if (item && typeof item === "object" && !Array.isArray(item) && item !== null) {
        return props.options;
    }
    if (item && typeof item === "string") {
        return props.options.map((i) => ({ [props.field.label]: i, [props.field.value]: i }));
    }
    return [];
});
</script>

<style lang="scss" scoped>
.item-radio:hover .radio-icon {
    border-style: solid;
}
</style>
