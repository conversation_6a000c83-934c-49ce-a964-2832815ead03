const findSubstringsIndexes = (str, start, end) => {
    let stack = []; // 栈，用于跟踪括号
    let results = []; // 存储结果的数组

    // 遍历字符串的每个字符
    for (let i = 0; i < str.length; i++) {
        let char = str[i];

        // 检测括号的开启
        if (char === "(" || char === "<") {
            stack.push({ char, index: i });
        } else if ((char === ")" || char === ">") && stack.length > 0) {
            // 检测括号的闭合
            let last = stack.pop();
            // 检查是否匹配的括号和格式
            if ((last.char === "(" && char === ")") || (last.char === "<" && char === ">")) {
                let substring = str.substring(last.index, i + 1);
                if (isValidFormat(substring)) {
                    results.push({ substring, start: last.index, end: i });
                }
            }
        }
    }
    for (let item of results) {
        if (item.start <= start && item.end >= end) {
            return {
                checked: true,
                startIndex: item.start,
                endIndex: item.end,
                substring: item.substring,
            };
        }
    }
    return {
        checked: false,
    };
};
//n-input type=textarea 键盘钩子(提示词权重)
const isValidFormat = (substring) => {
    // 检查子字符串是否符合 "<任意字符:整数或小数>" 或 "(任意字符:整数或小数)" 格式
    let colonIndex = substring.lastIndexOf(":");
    if (colonIndex === -1) return false; // 没有冒号

    let numberPart = substring.slice(colonIndex + 1, -1);
    // 检查数字部分是否是整数或小数
    return !isNaN(parseFloat(numberPart)) && isFinite(numberPart) && numberPart.trim() === numberPart;
};
// 自动匹配光标 //n-input type=textarea 键盘钩子(提示词权重)
const autoMatchCursor = (str, index) => {
    //光标前后匹配截断的标点
    const punctuation = [",", "，", ".", ":", ";"];
    let start = -1;
    let end = -1;
    if (punctuation.includes(str[index])) {
        index -= 1;
    }

    // 从指定下标向前搜索截断标点
    for (let i = index; i >= 0; i--) {
        if (punctuation.includes(str[i])) {
            start = i;
            break;
        }
    }
    ++start;
    if (index === str.length) {
        end = index;
    } else {
        // 从指定下标向后搜索截断标点
        for (let i = index; i < str.length; i++) {
            if (punctuation.includes(str[i])) {
                end = i;
                break;
            }
        }
    }
    if (end == -1) {
        end = str.length;
    }
    return { start, end };
};

//n-input type=textarea 键盘钩子(提示词权重) 自定义指令
export const Keyboard = {
    mounted: (el, binding) => {
        el.handler = function (e) {
            if (!e.ctrlKey && !e.metaKey) {
                return false;
            }
            if (e.code != "ArrowDown" && e.code != "ArrowUp") {
                return false;
            }
            e.preventDefault();
            // 如果元素不存在，删除该元素监听事件，防止按键冲突
            const isHasEl = document.contains(el);
            if (!isHasEl) {
                el.removeEventListener("keydown", el.handler);
                return;
            }
            const textareaElRef = binding.arg.textareaRef.textareaElRef || binding.arg.textareaRef.inputElRef;
            let context = binding.arg.textareaRef.value;
            let code = e.code;
            if (context === "") {
                return;
            }

            let start = textareaElRef.selectionStart;
            let end = textareaElRef.selectionEnd;
            const { checked, startIndex, endIndex, substring } = findSubstringsIndexes(context, start, end);
            // console.log(checked, startIndex, endIndex)
            let prefix = "";
            //设置光标选中
            if (checked) {
                start = startIndex + 1;
                end = endIndex;
                let len = substring.lastIndexOf(":") + 1;
                let lastNum = substring.slice(len, substring.length - 1);
                const step = 0.05 * 1000;
                let newNum = Math.floor(Number(lastNum) * 1000);
                code === "ArrowDown" ? (newNum -= step) : (newNum += step);
                prefix = substring.substring(1, len - 1);
                newNum = Math.max(0, newNum);
                newNum = Math.min(newNum, 10 * 1000);

                let str = prefix + ":" + newNum / 1000;
                if (newNum === 1000) {
                    --start;
                    ++end;
                    str = prefix;
                }
                const char = context.substring(0, start) + str + context.substring(end);
                binding.arg.textareaRef.$emit("update:value", char);
            } else {
                if (start == end) {
                    //获取光标最近符合分割的起止索引
                    let indexObj = autoMatchCursor(context, start);
                    start = indexObj.start;
                    end = indexObj.end;
                }

                prefix = context.slice(start, end);
                let num = code === "ArrowDown" ? 0.95 : 1.05;
                let char = context.substring(0, start) + `(${prefix}:${num})` + context.substring(end);
                binding.arg.textareaRef.$emit("update:value", char);
                ++start;
            }
            nextTick(() => {
                textareaElRef.setSelectionRange(start, start + prefix.length);
            });
        };
        // 添加监听事件
        el.addEventListener("keydown", el.handler);
    },
    // 绑定元素的父组件卸载后调用
    beforeUnmount: (el) => {
        // 删除监听事件
        el.removeEventListener("keydown", el.handler);
    },
    getSSRProps: () => {
        // 返回空对象即可，防止报错
        return {};
    },
};

// import { useSubscribeStore } from "@/stores/subscribe";
// import { SUBSCRIBE_TYPE } from "@/utils/constant";
export const showSubscribeIcon = {
    mounted: (el) => {
        // if (import.meta.server) return;
        // const { vipInfo = {} } = useSubscribeStore();
        // if (vipInfo.plan === SUBSCRIBE_TYPE.STANDARD || vipInfo.plan === SUBSCRIBE_TYPE.PRO || vipInfo.getInfoLoading) {
        //     // el.style.display = "none";
        //     el.style.display = "";
        // } else if (vipInfo.plan === SUBSCRIBE_TYPE.BASIC && !vipInfo.isShowSubscribeIcon) {
        //     el.style.display = "";
        // }
    },
    updated: (el) => {
        // if (import.meta.server) return;
        // const { vipInfo } = useSubscribeStore();
        // if (vipInfo.plan === SUBSCRIBE_TYPE.STANDARD || vipInfo.plan === SUBSCRIBE_TYPE.PRO || vipInfo.getInfoLoading) {
        //     // el.style.display = "none";
        //     el.style.display = "";
        // } else if (vipInfo.plan === SUBSCRIBE_TYPE.BASIC && !vipInfo.isShowSubscribeIcon) {
        //     el.style.display = "";
        // }
    },
    getSSRProps: () => {
        // 返回空对象即可，防止报错
        return {};
    },
};
