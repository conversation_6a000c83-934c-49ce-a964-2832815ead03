import { defineStore } from "pinia";
import { reqLumenNum, reqSubscriptionSchedule, reqUserVipList, reqVipPermissionList, requestUserPromotionStatus, freeTry3Day, getPayChannels, getRemainLumens } from "@/api/index.js";
import { formatDateShort, getPlatformVipToWeight, numberFormat, getCountDownTime, countDonwTimeFormat } from "@/utils/tools.js";
import { PLATFORM_TYPE, SUBSCRIBE_TYPE } from "@/utils/constant.js";
import { useAsyncUserCount } from "@/hook/updateAccount.js";
import { useStorageType } from "@/hook/useStorageType.js";

const { storageType } = useStorageType();

export const useSubscribeStore = defineStore(
    "subscribe",
    () => {
        // 会员默认数据
        const DEFAULT_VIP_INFO = {
            billingPeriod: "",
            plan: SUBSCRIBE_TYPE.BASIC,
            platform: "", // 会员来源平台
            planLumenLeft: 0, // 订阅剩余数
            planLumenTotal: 0, // 订阅总数
            freeLumenLeft: 0, // 每日免费使用数
            freeLumenTotal: 0, // 每日免费总数
            awardedLumenLeft: 0, // 单独购买剩余数
            giftLumenLeft: 0, // 赠送剩余数
            expirationDate: "", // 到期时间
            subExpirationDate: "", // 付费用户到期时间
            refreshDate: "", // 下次刷新时间
            refreshPlan: "", // 下次计划
            getInfoLoading: true, // 获取会员信息loading
            getLumenLoading: false, // 获取Lumen数量loading
            getRefreshLoading: false, // 获取订阅周期刷新loading
            renewPrice: "", // 当前生效会员 购买的会员单价
            hasInTrial: false, // 生效的会员是否在免费试用期间内
        };

        const remainTotalLumens = ref(0);

        const vipInfo = reactive({ ...DEFAULT_VIP_INFO }); // 会员信息
        // 设置会员信息
        const setVipInfo = (info) => {
            // info.billingPeriod = BILLING_TYPE.YEARLY;
            // info.plan = SUBSCRIBE_TYPE.PRO;
            Object.assign(vipInfo, info);
        };
        //重置会员信息
        const resetVipInfo = () => {
            setVipInfo({ ...DEFAULT_VIP_INFO });
            subList.value = []; // 重置订阅列表
            vipPermissionList.value = []; // 重置会员权限列表
            allowFreeTry.value = false; // 重置免费试用状态
            subGaEvent.value = ""; // 重置订阅GA事件来源
            buyLumenGaEvent.value = ""; // 重置购买Lumen GA事件来源
        };

        const isFreeVip = computed(() => vipInfo.plan === SUBSCRIBE_TYPE.BASIC); // 是否是免费会员
        const isGiftVip = computed(() => PLATFORM_TYPE.GIFT.includes(vipInfo.platform)); // 是否是赠送会员

        // 格式化到期时间
        const formatExpireDate = computed(() => {
            if (!vipInfo.expirationDate) return { shortStr: "", shortStrKey: "" };
            const { shortStr, shortStrKey } = formatDateShort(vipInfo.expirationDate, true);
            return { shortStr, shortStrKey };
        });

        // 格式化付费用户到期时间
        const formatSubExpireDate = computed(() => {
            if (!vipInfo.subExpirationDate) return { shortStr: "", shortStrKey: "" };
            const { shortStr, shortStrKey } = formatDateShort(vipInfo.subExpirationDate, true);
            return { shortStr, shortStrKey };
        });

        // 获取Lumen数量
        const getLumenNum = () => {
            vipInfo.getLumenLoading = true;
            return reqLumenNum()
                .then((res) => {
                    if (res.status === 0) {
                        const { dailyLumens, leftDailyLumens, vipLumens, leftVipLumens, leftRechargeLumens, leftGiftLumens } = res.data || {};
                        setVipInfo({
                            freeLumenTotal: dailyLumens,
                            freeLumenLeft: leftDailyLumens,
                            planLumenTotal: vipLumens,
                            planLumenLeft: leftVipLumens,
                            awardedLumenLeft: leftRechargeLumens,
                            giftLumenLeft: leftGiftLumens,
                        });
                        vipInfo.getLumenLoading = false;
                    }
                    return res;
                })
                .catch((err) => err);
        };
        // 查询可用lumen数量（预扣后的）
        const updateRemainLumens = async () => {
            try {
                const { status, data } = await getRemainLumens();
                if (status === 0) {
                    remainTotalLumens.value = numberFormat(data?.remainingAvailableLumen || 0);
                    // console.log(remainTotalLumens.value, "当前用户剩余可用lumen")
                }
            } catch (error) {
                console.error(error, "lumen查询失败");
            }
        };

        // 获取订阅刷新时间
        const getRefreshOn = () => {
            vipInfo.getRefreshLoading = true;
            return reqSubscriptionSchedule()
                .then((res) => {
                    if (res.status === 0) {
                        const { planLevel, startDate } = res.data || {};
                        setVipInfo({
                            refreshDate: startDate ? startDate * 1000 : "",
                            refreshPlan: planLevel ? planLevel : "",
                        });
                        vipInfo.getRefreshLoading = false;
                    }
                    return res;
                })
                .catch((err) => err);
        };
        const formatRefreshDate = computed(() => {
            if (!vipInfo.refreshDate) return "";
            return formatDateShort(vipInfo.refreshDate, true).shortStr || "";
        });

        // watch(() => vipInfo.plan, getSubList);

        const subList = ref([]); // 订阅列表（包含Web、IOS、Android）

        // 是付费用户(赠送会员不是订阅会员)
        const isPaymentVip = computed(() => subList.value.some((item) => !PLATFORM_TYPE.GIFT.includes(item.vipPlatform)));
        // 获取web的支付平台(如果存在多个，则取最高等级的，等级相同先取支持能力更广泛的stripe)
        const paymentPlatform = computed(() => {
            const webSubList = getPlatformVipToWeight(subList.value, PLATFORM_TYPE.WEB);
            return webSubList[0]?.vipPlatform || "";
        });

        /**
         * 获取用户订阅列表
         * @param {Boolean} isRecursive 是否来自递归调用
         * @returns {Promise<axios.AxiosResponse<any>>}
         */
        const getSubList = (isRecursive = false) => {
            if (!useCookie("authToken").value) {
                console.warn("请先登录获取订阅信息");
                return Promise.resolve({ status: 0, data: [] });
            }
            return reqUserVipList()
                .then((res) => {
                    if (res.status === 0) {
                        subList.value = res.data || [];

                        if (vipInfo.plan === SUBSCRIBE_TYPE.BASIC) return;
                        // WEB平台付费订阅（按有效期排序）
                        const webSubList = getPlatformVipToWeight(subList.value, PLATFORM_TYPE.WEB);
                        if (webSubList.length) {
                            const billingPeriod = webSubList[0].priceInterval;
                            const subExpirationDate = Number(webSubList[0].vipEndTime) * 1000;
                            setVipInfo({
                                billingPeriod,
                                subExpirationDate,
                                renewPrice: webSubList[0].renewPrice || "",
                                hasInTrial: !!webSubList[0].trial,
                            }); // 设置真正的有效账单
                        }
                        // 设置当前VIP信息
                        const validityVip = subList.value.filter((item) => item.planLevel === vipInfo.plan);
                        if (validityVip.length) {
                            // 计算同等级订阅中时间最长的会员
                            const maxVipEndTimeItem = validityVip.reduce((maxItem, currentItem) => (currentItem.vipEndTime > maxItem.vipEndTime ? currentItem : maxItem), validityVip[0]);
                            const expirationDate = Number(maxVipEndTimeItem.vipEndTime) * 1000;
                            setVipInfo({
                                expirationDate,
                                platform: maxVipEndTimeItem.vipPlatform,
                            });
                        } else {
                            /**
                             * isRecursive避免递归调用产生死循环
                             *
                             * 此处当进入订阅页面会根据当前用户会员等级反推用户信息，预防会员过期用户未刷新页面
                             */
                            if (isRecursive) return res;
                            useAsyncUserCount().then(() => getSubList(true));
                        }
                    }
                    return res;
                })
                .catch((err) => err);
        };
        watch(() => vipInfo.plan, getSubList);

        const vipPermissionList = ref([]); // 会员权限列表
        //  获取会员权限列表
        const getVipPermissionList = async () => {
            try {
                const res = await reqVipPermissionList();
                if (res?.status !== 0 || !res?.data) return;
                vipPermissionList.value = res.data || [];
                return res.data;
            } catch (error) {
                console.error("getVipPermissionList 错误：", error);
                return false;
            }
        };
        // 当前会员权限
        const currVipPermission = computed(() => vipPermissionList.value.find((item) => item.vipType === vipInfo.plan));

        // asyncFreeTryStatus();

        // 订阅GA事件来源
        const subGaEvent = ref("");
        const setSubGaEvent = (eve) => {
            subGaEvent.value = eve;
        };

        // 购买 Lumen GA 事件来源
        const buyLumenGaEvent = ref("");
        const setBuyLumenGaEvent = (eve) => {
            buyLumenGaEvent.value = eve;
        };


        // 当前用户是否具备3天试用权益
        const allowFreeTry = ref(false);
        const updateAllowFreeTry = (isTry = false) => {
            allowFreeTry.value = isTry;
        };

        // const asyncFreeTryStatus = () => {
        //   return freeTry3Day().then((res) => {
        //     if (res.status === 0) {
        //       allowFreeTry.value = res.data;
        //     } else {
        //       allowFreeTry.value = false;
        //     }
        //     return res;
        //   });
        // };
        const isLoadedUserPromotion = ref(false); // 是否加载过数据
        const userPromotionList = ref([]); // 用户允许的促销列表
        const planPromotionBox = ref([]); // 套餐列表
        const lumenPromotionBox = ref({ isFirst: false, lumensList: [] }); // lumen列表 购买lumen弹窗展示 lumen金额 送lumen数量
        const isHasAnniversary = ref(false); // 是否存在周年庆活动
        const isHaveOldVipBack = ref(false); // 是否存在老会员回馈活动
        const isHaveFirstBuySub = ref(false); // 是否存在首次订阅优惠活动
        const isNewUser = ref(false); // 是否是新游戏
        const anniversaryEndTime = ref(0); // 周年庆结束时间
        // 获取促销状态，当前可用什么活动，可展示什么组件
        const initUserPromotionStatus = async () => {
            try {
                const res = await requestUserPromotionStatus();
                let { status, data } = res;
                // ============================== mock ==============================
                if (false) {
                    console.error("mock  注意 subscribe中的initUserPromotionStatus方法 是模拟数据");
                    console.error("mock  注意 subscribe中的initUserPromotionStatus方法 是模拟数据");
                    console.error("mock  注意 subscribe中的initUserPromotionStatus方法 是模拟数据");
                    data = {
                        planPromotion: [
                            {
                                type: "anniversary",
                                off: 50,
                                redeemBy: 1755170848,
                            },
                        ],
                        lumenPromotion: {
                            type: "anniversary",
                            off: 50,
                        },
                        newUser: true,
                        firstBuyLumen: true,
                        discountedPrices: [
                            {
                                planLevel: "standard",
                                productType: "plan",
                                priceInterval: "year",
                                originalPrice: "107.88",
                                originalPriceMonth: "11.99",
                                discountedPrice: "6.00",
                                discountedPriceNotMonth: "53.94",
                                off: 50,
                                frontOff: 55,
                                savedAmount: "53.94",
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 2000,
                                initialLumen: 200,
                                trialDay: 3,
                                mark: "v2",
                            },
                            {
                                planLevel: "pro",
                                productType: "plan",
                                priceInterval: "year",
                                originalPrice: "263.88",
                                originalPriceMonth: "28.99",
                                discountedPrice: "14.50",
                                discountedPriceNotMonth: "211.10",
                                off: 20,
                                frontOff: 62,
                                savedAmount: "52.78",
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 5000,
                                initialLumen: 500,
                                trialDay: 3,
                                mark: "v2",
                            },
                            {
                                planLevel: null,
                                productType: "one",
                                priceInterval: null,
                                originalPrice: "0.99",
                                originalPriceMonth: null,
                                discountedPrice: "0.99",
                                discountedPriceNotMonth: "0.49",
                                off: 50,
                                frontOff: 50,
                                savedAmount: null,
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 100,
                                initialLumen: 50,
                                trialDay: 0,
                                mark: "v2",
                            },
                            {
                                planLevel: null,
                                productType: "one",
                                priceInterval: null,
                                originalPrice: "8.99",
                                originalPriceMonth: null,
                                discountedPrice: "8.99",
                                discountedPriceNotMonth: "4.49",
                                off: 50,
                                frontOff: 50,
                                savedAmount: null,
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 1000,
                                initialLumen: 500,
                                trialDay: 0,
                                mark: "v2",
                            },
                            {
                                planLevel: null,
                                productType: "one",
                                priceInterval: null,
                                originalPrice: "79.99",
                                originalPriceMonth: null,
                                discountedPrice: "79.99",
                                discountedPriceNotMonth: "39.99",
                                off: 50,
                                frontOff: 50,
                                savedAmount: null,
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 10000,
                                initialLumen: 5000,
                                trialDay: 0,
                                mark: "v2",
                            },
                            {
                                planLevel: "standard",
                                productType: "plan",
                                priceInterval: "month",
                                originalPrice: "11.99",
                                originalPriceMonth: "11.99",
                                discountedPrice: "5.39",
                                discountedPriceNotMonth: "5.99",
                                off: 50,
                                frontOff: 50,
                                savedAmount: "6.00",
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 2000,
                                initialLumen: 200,
                                trialDay: 3,
                                mark: "v3",
                            },
                            {
                                planLevel: "pro",
                                productType: "plan",
                                priceInterval: "month",
                                originalPrice: "28.99",
                                originalPriceMonth: "28.99",
                                discountedPrice: "11.00",
                                discountedPriceNotMonth: "14.49",
                                off: 50,
                                frontOff: 50,
                                savedAmount: "14.50",
                                appliedPromotionType: "anniversary",
                                lumenDiscountType: null,
                                lumen: 5000,
                                initialLumen: 500,
                                trialDay: 3,
                                mark: "v3",
                            },
                        ],
                        planLevel: "standard",
                        priceInterval: "year",
                    };
                }
                // ============================== ==== ==============================

                if (status !== 0 || !data) return;

                // 活动类型
                // 1.first_buy_sub, 2.old_vip_back， 3.anniversary
                userPromotionList.value = data?.planPromotion ?? [];
                const firstBuyLumen = data.firstBuyLumen ?? false; //是否送lumen,true:有giftUnit

                // 将折扣价格数据清洗为符合前端套餐数据的结构
                let priceList = data?.discountedPrices ?? [];

                const planPriceList = priceList.filter((item) => item.productType === "plan"); // 订阅套餐价格列表
                const lumenList = priceList.filter((item) => item.productType === "one"); //一次性套餐列表， 买lumen的数据列表

                if (planPriceList.length) {
                    // 套餐中是否存在首次订阅优惠
                    updateAllowFreeTry(planPriceList[0]?.appliedPromotionType === "first_buy_sub");
                }

                // =================== 套餐促销数据清洗 ===================
                const validPlanBox = planPriceList
                    .map((item) => {
                        const { planLevel, priceInterval, frontOff, discountedPrice, originalPriceMonth } = item;

                        // 跳过无效数据 originalPriceNum为0时，使用本地的套餐价格（兜底）
                        if (!frontOff || isNaN(frontOff)) {
                            return null;
                        }

                        // 转换并验证价格数据
                        const currentPrice = parseFloat(discountedPrice);
                        const originalPriceNum = parseFloat(originalPriceMonth);
                        return {
                            key: `${planLevel}_${priceInterval}`,
                            value: {
                                CURRENT: currentPrice.toFixed(2),
                                ORIGINAL: originalPriceNum.toFixed(2),
                                SALE: `${frontOff}%`,
                            },
                        };
                    })
                    .filter((item) => item !== null) // 过滤掉无效数据
                    .reduce((acc, item) => {
                        acc[item.key] = item.value;
                        return acc;
                    }, {});

                planPromotionBox.value = validPlanBox;
                // console.log('planBoxplanBox>>' , validPlanBox)

                // =================== lumen促销数据清洗 ===================
                // { count: 0, giftUnit: 5000, lumen: 10000, price: 79.99 }, // 需要的格式
                const _lumenBox = [];
                const hasInvalidDiscount = lumenList.some((item) => !item.frontOff || isNaN(item.frontOff)); //判断是否存在无效折扣
                if (!hasInvalidDiscount) {
                    // 映射有效数据
                    const newLumItems = lumenList.map((item) => ({
                        promotionType: item.appliedPromotionType ?? "", //活动名称 可能是周年庆 anniversary
                        giftUnit: item.initialLumen,
                        lumen: item.lumen,
                        price: parseFloat(item.originalPrice),
                        discount: item.frontOff || 0, // 确保折扣为有效数字
                    }));
                    _lumenBox.push(...newLumItems);
                }

                //====================== 当前存在活动的数据清洗
                let _hasAnniversary = !!userPromotionList.value.some((item) => item.type === "anniversary"); // 是否存在周年庆活动
                isHaveOldVipBack.value = !!userPromotionList.value.some((item) => item.type === "old_vip_back"); // 是否存在老会员回馈活动
                isHaveFirstBuySub.value = !!userPromotionList.value.some((item) => item.type === "first_buy_sub"); // 是否存在首次订阅优惠活动
                isNewUser.value = data?.newUser ?? false; // 是否是新用户
                //============
                lumenPromotionBox.value = { isFirst: firstBuyLumen, lumenList: _lumenBox };
                isLoadedUserPromotion.value = true;

                // 存在周年庆，解析活动时间
                if (_hasAnniversary) {
                    const activity = userPromotionList.value.find((item) => item.type === "anniversary");
                    anniversaryEndTime.value = activity?.redeemBy * 1000 ?? 0;

                    // 校验倒计时是否为0
                    const { days, hours, minutes, seconds } = getCountDownTime(anniversaryEndTime.value);
                    _hasAnniversary = days + hours + minutes + seconds > 0; // 根据倒计时判断周年庆活动是否存在，1.周年庆活动存在，但倒计时已结束
                }
                isHasAnniversary.value = _hasAnniversary;
            } catch (error) {
                console.error("initUserPromotionStatus 错误：", error);
            }
        };

        /**
         * 根据类型查询对应的折扣金额
         * @param {*} planLevel standard、pro
         * @param {*} unit yearly、monthly
         */
        const getUserPromotionByUnit = (planLevel, unit) => {
            return planPromotionBox.value?.[`${planLevel}_${unit}`] ?? {};
        };

        const anniversaryCountdownTimer = ref(null);
        const anniversarySubMapping = ref({}); // 周年庆订阅信息
        // 倒计时
        const isIn48Hour = ref(false); //是否在48小时内
        const anniversaryHoursStr = ref("00");
        const anniversaryMinutesStr = ref("00");
        const anniversarySecondsStr = ref("00");

        // 前往订阅倒计时（共享一个计时器）
        const toSubAnniversaryCountDownTimer = (whereSub) => {
            const hasSub = anniversarySubMapping.value[whereSub];
            if (hasSub) return;
            anniversarySubMapping.value[whereSub] = true;

            const currentKeys = Object.keys(anniversarySubMapping.value);
            if (currentKeys.length === 1) {
                if (anniversaryCountdownTimer.value) {
                    clearInterval(anniversaryCountdownTimer.value);
                }
                anniversaryCountdownTimer.value = setInterval(anniversaryRefreshCountdown, 1000);
            }
        };

        // 取消订阅倒计时（最后一个订阅取消时才清除计时器）
        const toUnSubAnniversaryCountDownTimer = (whereSub) => {
            if (!anniversarySubMapping.value[whereSub]) return;

            // 删除当前订阅的字段
            delete anniversarySubMapping.value[whereSub];

            const currentKeys = Object.keys(anniversarySubMapping.value);
            if (currentKeys.length === 0) {
                if (anniversaryCountdownTimer.value) {
                    clearInterval(anniversaryCountdownTimer.value);
                    anniversaryCountdownTimer.value = null; // 重置计时器状态
                }
                anniversarySubMapping.value = {};
            }
        };

        const anniversaryRefreshCountdown = () => {
            if (anniversaryEndTime.value > 0) {
                const { days, hours, minutes, seconds } = getCountDownTime(anniversaryEndTime.value);

                const totalHours = days * 24 + hours; //  总小时数
                isIn48Hour.value = totalHours > 47 ? false : true;
                anniversaryHoursStr.value = countDonwTimeFormat(days * 24 + hours);
                anniversaryMinutesStr.value = countDonwTimeFormat(minutes);
                anniversarySecondsStr.value = countDonwTimeFormat(seconds);

                // 倒计时结束剩余0 清空计时器
                if (totalHours + minutes + seconds <= 0) {
                    if (anniversaryCountdownTimer.value) {
                        clearInterval(anniversaryCountdownTimer.value);
                    }
                    isHasAnniversary.value = false; // 周年庆结束
                    setTimeout(() => {
                        initUserPromotionStatus(); //刷新数据
                    }, 5000);
                }
            } else {
                isIn48Hour.value = false;
            }
        };

        // 周年庆活动倒计时展示逻辑

        /**
         * 获取当前真实的付费订阅最高权限(web 平台)
         * 权重计算规则：会员等级>计费周期
         * 等级Count pro:1000   standard : 100  basic : 1
         * 周期Count year : 10 month : 1
         * 最终权重： 等级Count + 周期Count
         * @return {Object} 返回最高权限的订阅信息
         */
        const weightMap = {
            pro: 1000,
            standard: 100,
            basic: 1,
            year: 10,
            month: 1,
        };
        const getHighestVipPermission = () => {
            const vipList = subList.value
                .filter((item) => PLATFORM_TYPE.WEB.includes(item.vipPlatform))
                .map((item) => {
                    const totalWeight = weightMap[item.planLevel] + weightMap[item.priceInterval];
                    return { ...item, totalWeight };
                })
                .sort((a, b) => b.totalWeight - a.totalWeight); // 按权重降序排序
            if (vipList.length === 0) return null;
            return vipList[0]; // 返回最高权限的订阅信息
        };

        const supportPaymentChannels = ref([]); // 支持的支付渠道列表
        const getPaymentChannels = async () => {
            try {
                const { status, data, message } = await getPayChannels();
                if (status === 0 && Array.isArray(data)) {
                    supportPaymentChannels.value = data || [];
                    return supportPaymentChannels.value;
                }
                console.error("获取支付通道失败:", message);
                return [];
            } catch (error) {
                console.error("获取支付通道失败:", error);
                return [];
            }
        };

        return {
            vipInfo,
            setVipInfo,
            isFreeVip,
            isGiftVip,
            isPaymentVip,
            paymentPlatform,
            formatExpireDate,
            formatSubExpireDate,
            vipPermissionList,
            remainTotalLumens,
            getVipPermissionList,
            currVipPermission,
            getLumenNum,
            updateRemainLumens,
            getRefreshOn,
            formatRefreshDate,
            getSubList,
            subList,
            resetVipInfo,
            subGaEvent,
            setSubGaEvent,
            buyLumenGaEvent,
            setBuyLumenGaEvent,
            allowFreeTry,
            isNewUser,
            isHasAnniversary,
            isHaveOldVipBack,
            isHaveFirstBuySub,
            isLoadedUserPromotion,
            anniversaryEndTime,
            userPromotionList,
            initUserPromotionStatus,
            getUserPromotionByUnit,
            lumenPromotionBox,
            getHighestVipPermission,
            supportPaymentChannels,
            getPaymentChannels,
            // 周年庆倒计时相关
            isIn48Hour,
            anniversaryHoursStr,
            anniversaryMinutesStr,
            anniversarySecondsStr,
            toSubAnniversaryCountDownTimer,
            toUnSubAnniversaryCountDownTimer,
        };
    }
    // {
    //   persist: {
    //     paths: ["userVipNotice"],
    //     storage: {
    //       getItem: (key) => {
    //         return window[storageType.value].getItem(key);
    //       },
    //       setItem: (key, value) => {
    //         window[storageType.value].setItem(key, value);
    //       },
    //       removeItem: (key) => {
    //         window[storageType.value].removeItem(key);
    //       },
    //     },
    //   },
    // }
);
