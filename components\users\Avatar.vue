<!--
 * @Author: HuangQS
 * @Description: 用户头像组件, 我的头像、用户的头像
 * @Date: 2025-06-25 20:04:25
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-31 17:03:33

  <template #rightbotom>  头像的右下角位置插槽

  当传入planLevel时，会显示右下角钻石图标
  当传入userId时，会根据userId获取最新的planLevel
-->

<template>
    <div class="relative shrink-0 group/item" :style="{ width: `${iconSize}px`, height: `${iconSize}px` }">
        <!-- <n-icon :size="20">
            <component :is="currPlan.icon" />
        </n-icon> -->

        <!-- 头像 -->
        <img v-if="src" :src="src" class="rounded-full pointer-events-none shrink-0" :style="{ width: `${iconSize}px`, height: `${iconSize}px` }" />
        <n-icon v-else :size="iconSize">
            <IconPerson class="text-text-4" />
        </n-icon>

        <div v-if="$slots.rightbotom" class="absolute right-0 bottom-0">
            <slot name="rightbotom"></slot>
        </div>

        <!--右下角图标  -->
        <template v-if="isShowFilterPlanLevel(localPlanLevel)">
            <div class="absolute -right-[1px] -bottom-[1px]" :class="isDetails ? '-right-[3px] -bottom-[3px]' : ''">
                <!-- 有边框的特殊情况 -->
                <template v-if="isDetails">
                    <div class="relative flex items-center justify-center">
                        <n-icon :size="16">
                            <IconsVipIconBG class="text-white dark:text-black" />
                        </n-icon>

                        <n-icon :size="14" class="absolute flex items-center justify-center">
                            <component :is="currPlan.icon" />
                        </n-icon>
                    </div>
                </template>
                <!-- 无边框 图标自带边框-->
                <template v-else>
                    <div class="relative flex items-center justify-center">
                        <n-icon :size="10">
                            <component :is="currPlan.icon" />
                        </n-icon>
                    </div>
                </template>
            </div>
        </template>

        <div
            v-if="$slots.upload"
            @click="handleUploadClick"
            class="cursor-pointer invisible group-hover/item:visible absolute top-0 left-0 right-0 bottom-0 rounded-full bg-fill-t-3 text-text-white flex items-center justify-center"
        >
            <slot name="upload"></slot>
        </div>
    </div>
</template>

<script setup>
import { markRaw } from "vue";

import { SUBSCRIBE_TYPE } from "@/utils/constant";
import IconPerson from "@/components/icons/Person.vue";

import IconsVipBasic from "@/components/icons/VipBasic.vue";
import IconsVipStandard from "@/components/icons/VipStandard.vue";
import IconsVipPro from "@/components/icons/VipPro.vue";
import IconsVipBorderBasic from "@/components/icons/VipBorderBasic.vue";
import IconsVipBorderStandard from "@/components/icons/VipBorderStandard.vue";
import IconsVipBorderPro from "@/components/icons/VipBorderPro.vue";

import { getCommunityPersonalById } from "@/api";

const levelDict = ref({
    [SUBSCRIBE_TYPE.BASIC]: { icon: markRaw(IconsVipBasic) },
    [SUBSCRIBE_TYPE.STANDARD]: { icon: markRaw(IconsVipStandard) },
    [SUBSCRIBE_TYPE.PRO]: { icon: markRaw(IconsVipPro) },
});

const levelBorderDict = ref({
    [SUBSCRIBE_TYPE.BASIC]: { icon: markRaw(IconsVipBorderBasic) },
    [SUBSCRIBE_TYPE.STANDARD]: { icon: markRaw(IconsVipBorderStandard) },
    [SUBSCRIBE_TYPE.PRO]: { icon: markRaw(IconsVipBorderPro) },
});

const props = defineProps({
    src: { type: String, default: "" },
    iconSize: { type: Number, default: 40 },
    planLevel: { type: String, default: "" },
    userId: { type: String, default: "" },
    isDetails: { type: Boolean, default: false }, // 是否显示边框
    // hidePlanLevelBorder: { type: Boolean, default: false }, // 是否隐藏VIP等级的黑色边框
});

const { userId, isDetails } = toRefs(props);
const planLevel = computed(() => {
    return props.planLevel || "";
});
const localPlanLevel = ref(props.planLevel || "");

watchEffect(async () => {
    // 监听 planLevel 的变化
    if (userId.value) {
        if (!localPlanLevel.value) {
            const { status, data } = await getCommunityPersonalById({ userId: userId.value });
            const newestAccountInfo = status === 0 ? data?.accountInfo : { planLevel: "" };
            localPlanLevel.value = newestAccountInfo.planLevel;
        }
    } else {
        localPlanLevel.value = planLevel.value;
    }
});

// 低级VIP不显示等级标签
const isShowFilterPlanLevel = (planLevel) => {
    if (!planLevel || planLevel === SUBSCRIBE_TYPE.BASIC) return "";
    return planLevel;
};

const currPlan = computed(() => {
    const dists = isDetails.value ? levelDict.value : levelBorderDict.value;

    return dists[localPlanLevel.value];
});

const emits = defineEmits(["uploadClick"]);

const handleUploadClick = () => {
    emits("uploadClick");
};
</script>

<style lang="scss" scoped></style>
