<template>
    <!-- 遮罩层单独控制 -->
    <transition name="fade">
        <div v-if="show" class="popup fixed inset-0 z-[100] h-fit bottom-0 max-h-[dvh] flex lg:hidden">
            <div v-if="showMask" class="popup-mask w-full h-screen bg-fill-t-3" :class="maskClass" @click.self="close" />
            <!-- 弹窗内容单独控制 -->
            <div
                v-if="show"
                ref="popupRef"
                class="popup__content fixed left-0 right-0 bottom-0 z-[101] rounded-t-xl bg-bg-2 px-4"
                :class="[wrapperClass, { 'popup-blur': blur }]"
                :style="{ transform: `translateY(${translateY}px)` }"
            >
                <div v-if="slidable" class="w-full flex justify-center mt-2 cursor-pointer" @touchstart="onTouchStart" @touchmove.prevent="onTouchMove" @touchend="onTouchEnd">
                    <div class="slide-line w-9 h-1.5 rounded-full bg-text-4" />
                </div>

                <slot name="header">
                    <title v-if="title" :title="title" class="!text-center mt-2 justify-center" />
                </slot>
                <div class="overflow-x-hidden" :class="[contentClass, { 'pt-4': showClose }]">
                    <slot />
                </div>

                <div v-if="$slots.bottom" class="m-4 mx-0">
                    <slot name="bottom" />
                </div>
                <div v-if="closeable && showClose" class="p-1.5 bg-light_5 text-light_2 rounded-full flex justify-center items-center absolute left-2 top-2" :class="closeIconClass">
                    <IconsClose class="size-5" @click="close" />
                </div>
            </div>
        </div>
    </transition>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useThemeStore } from "@/stores/system-config";
const { isPc } = storeToRefs(useThemeStore());

const props = defineProps({
    show: { type: Boolean, default: false },
    showMask: { type: Boolean, default: true }, // 单独控制mask显示
    blur: { type: Boolean, default: false }, // 背景模糊
    slidable: { type: Boolean, default: false },
    closeable: { type: Boolean, default: true },
    showClose: { type: Boolean, default: false },
    title: { type: String, default: "" },
    contentClass: { type: String, default: "" },
    wrapperClass: { type: String, default: "" },
    maskClass: { type: String, default: "" },
    closeIconClass: { type: String, default: "" },
});
const emit = defineEmits(["update:show"]);

const popupRef = ref(null);
const translateY = ref(0);

let startY = 0;
let lastY = 0;
let isDragging = false;

function close() {
    if (!props.closeable) return;
    emit("update:show", false);
    resetPosition();
}

function resetPosition() {
    translateY.value = 0;
}

function onTouchStart(e) {
    if (!props.slidable) return;
    startY = e.touches[0].clientY;
    lastY = startY;
    isDragging = true;
}

function onTouchMove(e) {
    if (!isDragging) return;

    const currentY = e.touches[0].clientY;
    const deltaY = currentY - startY;
    lastY = currentY;

    if (deltaY <= 0) return; // 不允许往上滑

    translateY.value = deltaY;

    const height = popupRef.value?.offsetHeight || 0;
    const percent = deltaY / height;

    if (percent >= 0.95) {
        isDragging = false;
        close(); // 拖过 95% 的距离，直接关闭
    }
}

function onTouchEnd() {
    if (!isDragging) return;
    isDragging = false;

    const finalDelta = lastY - startY;

    if (finalDelta > 0) {
        close(); // 最后是向下滑动释放 -> 关闭
    } else {
        resetPosition(); // 最后是向上或未移动 -> 不关闭
    }
}

defineExpose({ close });

watch(
    () => isPc,
    (newVal) => {
        console.log(newIsIpad, newIsMobile, "newVal");
        if (newVal && props.show) {
            close();
        }
    }
);
</script>

<style scoped lang="scss">
.popup {
    min-height: 120px;
}
.popup-blur {
    backdrop-filter: blur(16px);
}

.slide-up-enter-active,
.slide-up-leave-active {
    transition: transform 0.25s ease;
}
.slide-up-enter-from,
.slide-up-leave-to {
    transform: translateY(100%);
}
</style>
