<template>
    <n-modal v-model:show="visible" draggable>
        <div class="w-[880px] h md:max-h-[600px] h-[600px] flex flex-col rounded-2xl bg-bg-2 px-6 pb-6">
            <div class="relative py-6 flex justify-between items-center">
                <h2 class="text-xl font-semibold text-text-1">{{ $t("ACTIVITY.SUBMISSION") }}</h2>
                <n-icon size="24" class="cursor-pointer hover:text-primary-5" @click="visible = false">
                    <IconsClose />
                </n-icon>
            </div>
            <div class="flex flex-1 flex-col gap-y-6 overflow-hidden" v-show="stepNum === 1">
                <SubmissionGalleryCollect @change="handleGalleryCollect" :defaultCollect="defaultCollect" />
                <ScrollView class="flex-1 relative" @scrolltolower="loadMore" :distance="100">
                    <SubmissionGalleryImages :list="list" :selectedList="selectedImages" :loading="loading" @select="handleSelectImg" />
                    <div v-if="loading" class="sticky left-0 top-1/2 right-0 -translate-y-1/2 flex items-center justify-center">
                        <n-icon size="32" class="text-primary">
                            <IconsSpinLoading />
                        </n-icon>
                    </div>
                    <Empty class="h-full" v-show="!loading && isEmpty" text="ACTIVITY.CHALLENGE_EMPTY" imageClass="w-24"></Empty>
                </ScrollView>
                <div class="flex justify-between items-center">
                    <div>
                        <span class="mr-2">{{ $t("ACTIVITY.JOIN_WITH_CREATION") }}: </span>{{ submissionImages.length }} / {{ maxSubmissions }}
                    </div>
                    <div class="flex gap-4">
                        <Button @click="handleCancel" type="secondary" size="medium" class="min-w-[108px]">{{ $t("ACTIVITY.CANCEL") }}</Button>
                        <Button @click="handleNext" size="medium" class="min-w-[108px]" :disabled="!submissionImages.length">{{ $t("ACTIVITY.NEXT") }}</Button>
                    </div>
                </div>
            </div>
            <div class="flex flex-1 flex-col gap-y-6 pt-4 overflow-hidden" v-show="stepNum === 2">
                <div class="text-sm text-text-3">
                    {{ $t("ACTIVITY.SUBMISSION_DETAIL") }}
                </div>
                <div class="scroll-container flex-1 overflow-y-auto">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex rounded-md bg-bg-3 h-[132px] overflow-hidden" v-for="(item, index) in submissionImages" :key="item.fileId || item.imgUrl">
                            <div class="w-[132px] h-full relative rounded-md flex items-center justify-center">
                                <img class="w-[132px] h-[132px] object-cover" :src="item.highMiniUrl || item.thumbnailUrl" />
                                <div class="absolute w-6 h-6 top-2 right-2 z-10 rounded backdrop-blur-sm bg-fill-t-2 flex items-center justify-center text-text-white" @click="deleteItem(item)">
                                    <n-icon size="14" class="cursor-pointer" :class="{ 'animate-pulse': deleteLoading }">
                                        <IconsDele />
                                    </n-icon>
                                </div>
                            </div>
                            <div class="p-4 flex-1 flex flex-col gap-y-2">
                                <p class="text-text-3 mb-2 text-sm">{{ $t("ACTIVITY.SHOW_PROMPTS") }}</p>
                                <n-select
                                    class="w-full mb-3 text-text-2 bg-fill-ipt-1 rounded-lg"
                                    :disabled="!item.isNew"
                                    :on-update:value="(value) => handlePromptsChange(value, item, index)"
                                    v-model:value="item.publicType"
                                    :options="promptsOptions"
                                />

                                <p v-if="item.isNew" class="text-primary-6 text-xs font-medium">{{ $t("ACTIVITY.UNREVIEWED") }}</p>
                                <p v-else-if="item.isPublic === 1" class="text-text-4 text-xs font-medium">{{ $t("ACTIVITY.POSTED_TO_COMMUNITY") }}</p>
                                <p v-else class="text-info-6 text-xs font-medium">{{ $t("ACTIVITY.PENDING_PROCESS") }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <div></div>
                    <div class="flex gap-4">
                        <Button v-if="submittedImages.length !== maxSubmissions" @click="handlePrevious" type="secondary" size="medium" class="min-w-[108px]">{{ $t("ACTIVITY.BACK") }}</Button>
                        <Button @click="handleSubmit" :disabled="!selectedImages.length" size="medium" class="min-w-[108px]">{{ $t("ACTIVITY.SUBMIT") }}</Button>
                    </div>
                </div>
            </div>
            <!-- <div v-if="loading" class="sticky left-0 bottom-0 right-0 py-10 flex items-center justify-center">
                <n-icon size="32" class="text-primary">
                    <IconsSpinLoading />
                </n-icon>
            </div> -->
        </div>
    </n-modal>
</template>
<script setup>
import { updateCommunityPromptDisplay } from "@/api";
import { getActivityUserImagesApi, deleteActivityImgApi, submitActivityImgApi, getActivityNotPublishApi } from "@/api/activity";
import { debounce } from "@/utils/tools";

import useListFetch from "@/hook/useListFetch";

import SubmissionGalleryCollect from "@/components/activity/SubmissionGalleryCollect.vue";
import SubmissionGalleryImages from "@/components/activity/SubmissionGalleryImages.vue";
import { computed, nextTick, ref } from "vue";

const { t } = useI18n({ useScope: "global" });
const props = defineProps({
    activity: {
        type: Object,
        default: () => ({}),
    },
});
const visible = defineModel("visible");
const emits = defineEmits(["success"]);

/** 默认查询的图集 */
const defaultCollect = computed(() => ({
    id: 0,
    collectName: t("COLLECTION_UNORGANIZED"),
}));

const promptsOptions = computed(() => {
    return [
        {
            label: t("COMMUNITY_PUBLISH_PROMPT_SELF"),
            value: "myself",
        },
        {
            label: t("COMMUNITY_PUBLISH_PROMPT_EVERYONE"),
            value: "everyone",
        },
    ];
});
// 最大投稿数量
const maxSubmissions = computed(() => {
    return props.activity.maxSubmissions;
});
// 图库查询条件
const queryData = ref({
    classifyId: defaultCollect.value.id,
});

const { loading, reload, list, isEmpty, loadMore } = useListFetch({
    api: getActivityNotPublishApi,
    queryData,
});
/**
 * 图库分组切换
 */
const handleGalleryCollect = debounce(async (collect) => {
    queryData.value.classifyId = collect.id;
    console.log(collect);
    // console.log(queryData.value)
    reload();
});

const stepNum = ref(1);
let delAction = false;
//back 上一步 需要重刷一下数据，防止删除后数据未恢复
const handlePrevious = () => {
    stepNum.value = 1;
    if (delAction) {
        reload();
    }
    delAction = false;
};
const modalInit = () => {
    selectedImages.value = [];
    stepNum.value = 1;
    queryData.value = {
        classifyId: defaultCollect.value.id,
    };
    getActivityUserImages().then(() => {
        if (submittedImages.value.length === maxSubmissions.value) {
            nextTick(() => {
                stepNum.value = 2;
            });
        }
    });
    reload();
};

watch(
    () => visible.value,
    (newVal) => {
        if (newVal) {
            // todo 是否所有初始化？
            modalInit();
        }
    }
);

// 选中的图片
const selectedImages = ref([]);
// 已投稿图片
const submittedImages = ref([]);
// 选中+已投稿作品
const submissionImages = computed(() => {
    return [...selectedImages.value, ...submittedImages.value];
});

const getActivityUserImages = async () => {
    const res = await getActivityUserImagesApi({ activityId: props.activity.id });
    if (res.status === 0) {
        submittedImages.value = res.data;
    }
};
const deleteLoading = ref(false);
/**
 * 删除已发布或者待发布作品
 *  */
const deleteItem = async (imgData) => {
    // 删除待发布作品
    if (imgData.isNew) {
        const index = selectedImages.value.findIndex((item) => item.imgUrl === imgData.imgUrl);
        selectedImages.value.splice(index, 1);
        return;
    }
    if (deleteLoading.value) {
        return;
    }
    deleteLoading.value = true;
    const res = await deleteActivityImgApi({
        fileId: imgData.fileId,
        type: imgData.isPublic,
    }).finally(() => {
        deleteLoading.value = false;
    });
    if (res.status !== 0) {
        openToast.error(res.message);
        return;
    }
    delAction = true;
    getActivityUserImages();
};
/**
 * 选作品进入待发布
 *  */
const handleSelectImg = (img) => {
    const index = selectedImages.value.findIndex((item) => item.imgUrl === img.imgUrl);
    if (index > -1) {
        selectedImages.value.splice(index, 1);
        return;
    } else if (submissionImages.value.length < maxSubmissions.value) {
        selectedImages.value.push({
            ...img,
            publicType: promptsOptions.value[0].value,
            imgName: img.imgName,
            promptId: img.promptId,
            isNew: true,
        });
    } else {
        openToast.error(t(`ACTIVITY.LIMIT_HINT`));
    }
    // console.log('selectedImages', selectedImages)
};

const handleCancel = () => {
    visible.value = false;
};
const handleNext = () => {
    stepNum.value = 2;
};
const submitLoading = ref(false);

/**
 * 提示词修改
 */
const handlePromptsChange = async (publicType, item, index) => {
    // 已提交发布的 调整社区里面的可见范围
    if (!item.isNew) {
        const { status, message } = await updateCommunityPromptDisplay({ commFileId: item.id, publicType });
        if (status !== 0) {
            submissionImages.value[index].publicType = item.publicType;
            openToast.error(message);
            return;
        }
        submissionImages.value[index].publicType = publicType;
    }
};
/**
 * 提交作品
 */
const handleSubmit = async () => {
    if (!selectedImages.value.length || submitLoading.value) {
        return;
    }
    submitLoading.value = true;
    const activityPublishList = selectedImages.value.map((item) => ({
        imgName: item.imgName,
        promptId: item.promptId,
        publicType: item.publicType,
    }));
    const res = await submitActivityImgApi({
        activityId: props.activity.id,
        activityPublishList,
    }).finally(() => {
        submitLoading.value = false;
    });
    if (res.status !== 0) {
        openToast.error(res.message);
        return;
    }
    emits("success");
    // modalInit()
};
</script>

<style lang="scss" scoped></style>
