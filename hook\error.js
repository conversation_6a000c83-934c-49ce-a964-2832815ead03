import { ERROR_CODE_ENUM, ERROR_MESSAGE_ENUM, SUB_EL } from "@/utils/constant.js";
import { usePromptRefusedDialog } from "@/hook/create";
import { useCreateStore } from "@/stores/create";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useBuyLumen } from "@/hook/subscribe";
import { h } from "vue";
import { Alert, Close } from "@/icons/index";


export const useGlobalError = () => {
    const createStore = useCreateStore();
    const { showMessage, clearMessageBox } = useModal();
    const subscribeStore = useSubscribeStore();
    const localePath = useLocalePath();
    const { toBuyLumen } = useBuyLumen();
    const { t } = useI18n({ useScope: "global" });

    const showError = (code, options = {}) => {
        const exists = !!ERROR_MESSAGE_ENUM[code]
        // RELAX 生图上限
        if (code === ERROR_CODE_ENUM.RELAX_TASK_LIMIT_ERROR) {
            clearMessageBox();
            window.trackEvent("Create", { el: `relax_uesed_up_show` });
            showMessage({
                style: { width: "500px", borderRadius: "16px", padding: "24px" },
                cancelBtn: t("BTN_GOT_IT"),
                confirmBtn: t("BTN_UPGRADE_NOW"),
                content: h("div", { class: "text-sm font-medium " }, [
                    h("div", { class: "flex justify-between items-center mb-10 text-text-1" }, [
                        h("div", { class: "text-2xl font-semibold " }, t("RELAX_LIMIT_ERR_TITLE")),
                        h(Close, {
                            class: "text-2xl cursor-pointer",
                            onClick: clearMessageBox,
                        }),
                    ]),
                    h("div", null, t("RELAX_LIMIT_ERR_SUB")),
                    h("div", { class: "py-4" }, t("RELAX_LIMIT_ERR_DESC")),
                ]),
            }).then((_) => {
                subscribeStore.setSubGaEvent(SUB_EL.RELAX_LIMIT_MODEL);
                navigateTo({ path: localePath("/user/subscribe") });
                window.trackEvent("Create", { el: `relax_uesed_up_upgrade` });
            });

            return;
        }
        // 提示词检测错误
        if (code === ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR) {
            usePromptRefusedDialog();
            return;
        }
        //lumen 不足
        if (code === ERROR_CODE_ENUM.LUMENS_LACK_ERROR || code === ERROR_CODE_ENUM.EXCEED_AUTH_FEATURE_ERROR) {
            subscribeStore.setBuyLumenGaEvent(options?.triggerEl || "other");
            clearMessageBox();
            toBuyLumen(options);
            return;
        }
        //生图达到上限
        if (code === ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR || code === ERROR_CODE_ENUM.EXCEED_PRELOAD_LIMIT_ERROR) {
            clearMessageBox();
            showMessage({
                style: { width: "520px" },
                showCancel: false,
                confirmBtn: t("COMMON_BTN_OK"),
                content: t("TASK_PROCESS_LIMIT"),
                icon: h(Alert, { class: "text-error text-3xl" }),
                title: t("DIALOG_TITLE_NOTICE"),
            });
            return;
        }
        //无模型id
        if (code === ERROR_CODE_ENUM.GEN_NOT_MODEL_ID_ERROR) {
            clearMessageBox();
            showMessage({
                style: { width: "520px" },
                showCancel: false,
                confirmBtn: t("COMMON_BTN_OK"),
                content: t("NOT_MODEL_ERROR"),
                icon: h(Alert, { class: "text-error text-3xl" }),
                title: t("DIALOG_TITLE_OOPS"),
            });
            return;
        }
        //所有其他仅展示（无特定逻辑）的错误
        if (exists && ERROR_MESSAGE_ENUM[code]) {
            clearMessageBox();
            showMessage({
                style: { width: "520px" },
                showCancel: false,
                confirmBtn: t("COMMON_BTN_OK"),
                content: t(ERROR_MESSAGE_ENUM[code]),
                icon: h(Alert, { class: "text-error text-3xl" }),
                title: t("DIALOG_TITLE_OOPS"),
            });
            return;
        }

        showMessage({
            style: { width: "520px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: t("TOAST_GEN_ERROR_CONTENT"),
            icon: h(Alert, { class: "text-error text-3xl" }),
            title: t("DIALOG_TITLE_OOPS"),
        });
    };

    return {
        showError,
    };
};
