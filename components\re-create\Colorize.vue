<template>
    <div class="re-create__container">
        <div class="re-create__close" @click="handleClose()">
            <IconsClose class="size-4 lg:size-6" />
        </div>
        <section class="flex-1 overflow-hidden flex items-center justify-center relative">
            <img class="max-w-full max-h-full" :src="reColorizeConf.previewUrl || reColorizeConf.thumbnailUrl" alt="" />
        </section>
        <section class="hidden max-md:block h-52 mt-4 relative z-[99999] rounded-t-xl">
            <div class="flex items-center justify-between text-center py-4 text-base">
                <div class="w-1/3"></div>
                <div class="w-1/3">Colorize</div>
                <div class="w-1/3 flex items-center justify-end">
                    <Button class="!rounded-lg" @click="handleSubmitRecolor">Submit</Button>
                </div>
            </div>
            <div class="p-4 bg-bg-2 rounded-lg">
                <div class="dark:text-dark-desc-text">Prompt</div>
                <div class="mt-2 dark:!bg-dark-bg-3 !bg-neutral-200 rounded-md overflow-hidden">
                    <n-input type="text" :placeholder="t('COMMON_PROMPT')" v-model:value="prompt" :maxlength="2500" class="w-full min-w-0 text-sm" @keydown.stop="shortcutKey" />
                </div>
            </div>
        </section>
        <section class="h-[72px] bg-bg-2 px-6 flex items-center gap-4 max-md:hidden rounded-lg">
            <div class="flex-1 bg-fill-ipt-1 rounded-lg h-11 flex items-center gap-4 pr-4 text-text-4">
                <n-input
                    type="text"
                    :placeholder="t('COMMON_PROMPT')"
                    v-model:value="prompt"
                    ref="textareaRef"
                    v-keyboard:[textareaRefNode]
                    :maxlength="2500"
                    class="w-full min-w-0 h-9 text-sm"
                    @keydown.stop="shortcutKey"
                />

                <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <n-icon size="24" class="shrink-0" :class="{ 'text-text-2 cursor-pointer hover:text-text-1': allowImprove }" @click="handleImprove('translation')">
                            <IconsSpinLoading v-if="promptTranslating.isLoading && promptTranslating.type === 'translation'" class="text-primary" />
                            <IconsTranslate v-else />
                        </n-icon>
                    </template>

                    <div class="tips-box">
                        <div class="font-medium text-text-1 flex items-center gap-2">
                            <span>{{ t("SHORT_PROMPT_TRANS_TITLE") }}</span>
                            <!-- <n-icon size="18" class="text-info-6" v-show-plan><IconsDiamond /></n-icon> -->
                        </div>
                        <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_TRANS_MESSAGE") }}</div>
                    </div>
                </n-tooltip>

                <n-tooltip placement="top" trigger="hover" :delay="100" :show-arrow="false" raw>
                    <template #trigger>
                        <n-icon size="24" class="shrink-0" :class="{ 'text-text-2 cursor-pointer hover:text-text-1': allowImprove }" @click="handleImprove('enhance')">
                            <IconsSpinLoading v-if="promptTranslating.isLoading && promptTranslating.type === 'enhance'" class="text-primary" />
                            <IconsImprove v-else />
                        </n-icon>
                    </template>

                    <div class="tips-box">
                        <div class="font-medium text-text-1 flex items-center gap-2">
                            <span>{{ t("SHORT_PROMPT_IMPROVE_TITLE") }}</span>
                            <!-- <n-icon size="18" class="text-info-6" v-show-plan><IconsDiamond /></n-icon> -->
                        </div>
                        <div class="mt-2.5 text-text-3">{{ t("SHORT_PROMPT_IMPROVE_MESSAGE") }}</div>
                    </div>
                </n-tooltip>
            </div>
            <n-tooltip placement="bottom" trigger="hover" :delay="100" :show-arrow="false" raw>
                <template #trigger>
                    <n-button
                        class="w-32 h-9 rounded-md !bg-primary shrink-0 !text-dark-active-text cursor-pointer"
                        :bordered="false"
                        :disabled="isExceed"
                        :loading="isExceed"
                        @click="handleSubmitRecolor"
                    >
                        <span> {{ t("CONFIG_BASE_SUBMIT_BTN") }}</span>
                        <ExpendConst v-if="lumenCostKey" :feature-type="lumenCostKey" :num="1" class="ml-1" />
                    </n-button>
                </template>
                <div v-if="!isMobile" class="bg-white text-dark-bg-2 dark:text-dark-text p-3 rounded dark:bg-dark-bg-2 max-w-96">
                    <div>{{ t("ESTIMATE_COST_LUMENS") }}</div>
                </div>
            </n-tooltip>
        </section>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { useUserProfile } from "@/stores/index";
import { useCurrentTaskQueue } from "@/stores/create";
import { formatPonyV6Prompt, mergeArraysById, getCurrentISO8601Time, debounce, getFuncNameByModelId } from "@/utils/tools";
import { ERROR_CODE_ENUM, PRELOAD_TASK_TYPE, SUBSCRIBE_PERMISSION } from "@/utils/constant";
import { lineArtColorize } from "@/api";
import { useCheckMaxTask, useAppendPreloadTask, longTaskDialog, translateOrEnhanceText, useGoToCreate } from "@/hook/create";
import { useSyncAction } from "@/stores/syncAction";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";
import { useGlobalError } from "@/hook/error";
import ExpendConst from "@/components/subscribe/ExpendConst.vue";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";
const { toCreate } = useGoToCreate();

const emits = defineEmits(["confirm", "cancel", "close"]);

const { showError } = useGlobalError();
const { isMobile } = storeToRefs(useThemeStore());
const syncAction = useSyncAction();
const userProfile = useUserProfile();
const { checkPermission } = useSubPermission();
const { checkShowVipNotice } = useVipNotice();

const props = defineProps({
    item: {
        type: Object,
        required: true,
        default: () => ({}),
    },
});
const reColorizeConf = ref(props.item);
const prompt = ref("");
const allowImprove = computed(() => !!prompt.value && prompt.value.length <= 600);

const lumenCostKey = computed(() => getFuncNameByModelId({ model_id: reColorizeConf?.value?.model_id }));

const currentTaskQueue = useCurrentTaskQueue();
const isExceed = computed(() => {
    return currentTaskQueue.taskQueue.length >= currentTaskQueue.maxTask || currentTaskQueue.isLoading;
});
const textareaRef = ref(null);
const textareaRefNode = ref({
    textareaRef,
});

const lazyLoadImg = debounce(() => {
    if (import.meta.client) {
        const img = new Image();
        const { highThumbnailUrl, imgUrl } = reColorizeConf.value;
        img.src = highThumbnailUrl || imgUrl;
        img.onload = () => {
            reColorizeConf.value.previewUrl = img.src;
        };
    }
}, 600);
lazyLoadImg();

const handleBack = () => {
    emits("close");
};

// 翻译或增强提示词
const promptTranslating = ref({
    type: "",
    isLoading: false,
});
//提示词增强或翻译
const handleImprove = async (mode) => {
    let permission = SUBSCRIBE_PERMISSION.ENHANCE;
    if (mode === "translation") {
        permission = SUBSCRIBE_PERMISSION.TRANSLATION;
        window.trackEvent("APP_AUTO_TRANSLATE", { el: `translate_btn` });
    } else {
        window.trackEvent("APP_AUTO_ENHANCE", { el: `enhance_btn` });
    }
    if (!allowImprove.value) {
        prompt.value.length > 600 && openToast.info(t("SHORT_PROMPT_TOO_LONG"));
        return;
    }
    if (promptTranslating.isLoading) {
        return;
    }

    const hasPermission = await checkPermission(permission);
    if (!hasPermission) {
        return;
    }
    // checkShowVipNotice(`colorize_${mode}`);

    promptTranslating.value = { type: mode, isLoading: true };
    const { data } = await translateOrEnhanceText(prompt.value, mode);
    promptTranslating.value = { type: "", isLoading: false };
    prompt.value = data;
    promptTranslating.value = false;
};
const handleSubmitRecolor = () => {
    if (isExceed.value) {
        return;
    }
    const { model_id, promptId, realWidth, width, realHeight, height, imgUrl } = reColorizeConf.value;
    const genParameters = {
        model_id,
        promptId,
        prompt: prompt.value,
        resolution: {
            width: realWidth || width,
            height: realHeight || height,
            batch_size: 1,
        },
        lineRecolorPara: {
            img_url: imgUrl,
        },
    };

    colorizeTask(genParameters);
};

//重新上色任务开启
const colorizeTask = async (conf, longTask = false) => {
    if (currentTaskQueue.isLoading) {
        openToast.error(t("TOAST_TASK_LIMIT"), 5e3);
        return;
    }
    // 检查当期是否允许生图
    let checked = await useCheckMaxTask();
    if (!checked.concurrencyStatus && !checked.preloadStatus) {
        showError(ERROR_CODE_ENUM.EXCEED_TASK_LIMIT_ERROR);
        return;
    }
    if (!longTask) {
        const { prompt } = formatPonyV6Prompt(conf, "input");
        conf.prompt = prompt;
    }
    // 并发已满 提交预载
    if (!checked.concurrencyStatus) {
        useAppendPreloadTask({ genParameters: conf }, PRELOAD_TASK_TYPE.LINE_RECOLOR);
        handleBack();
        return;
    }
    currentTaskQueue.updateReqStatus(true);
    try {
        conf.continueCreate = longTask;
        const { data, status, message } = await lineArtColorize(conf);
        currentTaskQueue.updateReqStatus(false);
        //关闭 操作窗口，显示 原页面
        if (status === ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR) {
            showError(ERROR_CODE_ENUM.PROMPT_DETECTED_ERROR);
            return;
        }
        // 耗时任务 二次确认 重新提交
        if (status === ERROR_CODE_ENUM.LONG_TASK_ERROR) {
            const hasReSubmit = await longTaskDialog();
            hasReSubmit && colorizeTask(conf, true);
            return;
        }

        if (status !== 0) {
            showError(status);
            return;
        }

        let originCreate = "lineRecolor";
        const list = mergeArraysById(currentTaskQueue.taskQueue, [
            {
                ...conf,
                originCreate,
                markId: data.markId,
                status: "pending",
                fastHour: data.fastHour,
                index: data.index,
                createTimestamp: getCurrentISO8601Time(),
            },
        ]);
        currentTaskQueue.updateTaskQueue(list);
        toCreate();
        handleBack();
    } catch (error) {
        currentTaskQueue.updateReqStatus(false);
        openToast.error(error.message);
    }
};

// const shortcutKey = (e) => {
//     e.stopPropagation();
//     if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
//         handleSubmitRecolor();
//     }
// };

const shortcut = computed(() => {
    return userProfile.userConfig.shortcutKey === "enter";
});
const shortcutKey = (event) => {
    event.stopPropagation();
    // 只按enter提交任务
    if (shortcut.value && event.key === "Enter" && !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey) {
        event.preventDefault();
        handleSubmitRecolor();
        return;
    }
    //组合键提交任务
    if (!shortcut.value && (event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        handleSubmitRecolor();
        return;
    }
};

// onMounted(() => {
//     document.addEventListener("keydown", shortcutKey);
// });
// onBeforeUnmount(() => {
//     document.removeEventListener("keydown", shortcutKey);
// });
const handleClose = () => {
    emits("cancel");
    emits("close");
};
</script>

<style lang="scss" scoped>
.icon-bar {
    @apply h-9 w-9 rounded-md dark:bg-dark-bg-2 bg-black/5 flex items-center justify-center dark:text-neutral-200 opacity-80 hover:opacity-100;
}
.icon-bar.is-checked {
    color: #6f47ff;
    opacity: 1;
}
</style>
