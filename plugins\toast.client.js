// plugins/toast.client.js

import { useToast } from "~/composables/useToast";
import { openToast } from "~/utils/toast";

export default defineNuxtPlugin(() => {
  // 在插件（setup 上下文）中安全地调用 useToast
  const { show } = useToast();

  // 将真正的 show 函数实现，赋给全局 API 对象
  openToast.success = (message, duration) => {
    show({ message, duration, type: "success" });
  };

  openToast.error = (message, duration) => {
    show({ message, duration, type: "error" });
  };

  openToast.warning = (message, duration) => {
    show({ message, duration, type: "warning" });
  };

  openToast.info = (message, duration) => {
    show({ message, duration, type: "info" });
  };

  // 可选：你也可以通过 provide 的方式提供，这样在模板里也能用 $openToast
  // return {
  //   provide: {
  //     openToast
  //   }
  // }
});
