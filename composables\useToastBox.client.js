import { createVNode, render } from "vue";
import Toast from "@/components/toast/index.vue";
const activeInstances = new Map();
const currIndex = 0; // 设定的序号

export const useToastBox = () => {
  const showToast = (planType, message, duration = 3000) => {
    return new Promise(async (resolve, reject) => {
      const container = document.createElement("div");
      // 设置高度
      let top = 0;
      const viewSize = activeInstances.size ?? 0;
      if (viewSize) {
        const lastView = Array.from(activeInstances.values())[viewSize - 1];
        top = viewSize * (lastView.vnode.component.proxy.$el.offsetHeight + 20);
      }

      const vnode = createVNode(Toast, { planType, message, duration, top });

      render(vnode, container);
      await nextTick();
      const parent = document.querySelector("#app_content") || document.body;
      parent.appendChild(container);
      const vm = vnode.component.proxy;
      activeInstances.set(vm, { container, vnode }); // 存储

      const onVanish = () => {
        render(null, container);
        container.remove();
        activeInstances.delete(vm);
      };

      vm.onVanish = onVanish;
      setTimeout(onVanish, duration); // 到期删除
    });
  };

  return {
    showToast,
  };
};

export default useToastBox;
