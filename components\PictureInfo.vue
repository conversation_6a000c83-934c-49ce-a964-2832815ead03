<template>
    <div class="px-4 py-3 rounded-lg bg-fill-wd-1 flex flex-col gap-2">
        <div v-if="!isCustomUpload" class="flex items-center justify-between gap-2 text-text-4 h-6">
            <span>Model</span>
            <div class="flex items-center gap-2 text-text-3">
                <img v-if="isShowVipModelIcon" class="size-4" src="@/assets/images/subscribe/icon_fo_all_member.webp" />
                <span>{{ modelInfo(info).label }}</span>
                <img :src="modelInfo(info).icon" class="w-5 h-5 rounded" />
            </div>
        </div>
        <div class="flex items-center justify-between gap-2 text-text-4 h-6">
            <span>Date created</span>
            <div class="text-text-3">{{ timeFormat(info) }}</div>
        </div>
        <div v-if="!isCustomUpload && info.seed" class="flex items-center justify-between gap-2 text-text-4 h-6">
            <span>Seed</span>
            <div class="text-text-3">{{ info.seed }}</div>
        </div>
        <div v-if="!isCustomUpload && info.cfg" class="flex items-center justify-between gap-2 text-text-4 h-6">
            <span>Guidance Scale</span>
            <div class="text-text-3">{{ info.cfg }}</div>
        </div>
        <div class="flex items-center justify-between gap-2 text-text-4 h-6">
            <span>Resolution</span>
            <div class="text-text-3">{{ info?.realWidth }} x {{ info?.realHeight }} {{ ratioLabel ? "(" + ratioLabel + ")" : "" }}</div>
        </div>
    </div>
</template>

<script setup>
import { useGetModelInfo } from "@/hook/create";
import { formatDate, renderModelIcon, isVipModel } from "@/utils/tools";
import { SHAPE_ALL } from "@/utils/constant";
const props = defineProps({
    info: {
        type: Object,
        required: true,
        default: () => ({}),
    },
});

const isShowVipModelIcon = computed(() => {
    if (!props.info.model_id) return false;
    return isVipModel({ model_id: props.info.model_id });
});

//模型选择组件渲染函数
const modelInfo = computed(() => {
    return () => {
        const option = useGetModelInfo(props.info.model_id);
        if (!option.value) {
            option.value = props.info.model_id;
            option.label = props.info.modelDisplay || "";
        }
        const model_id = option.value;
        let icon = renderModelIcon(model_id);
        return { ...option, icon };
    };
});
// 判断是否是上传还是裁剪的图片
const isCustomUpload = computed(() => props.info.originCreate === "customUpload" || props.info.originCreate === "crop");
//time
const timeFormat = computed(
    () =>
        ({ createTimestamp, createTime }) =>
            formatDate(createTimestamp || createTime)
);

// 比例计算
const ratioLabel = computed(() => SHAPE_ALL.find((item) => item.width === props.info.realWidth && item.height === props.info.realHeight)?.label);
</script>

<style lang="scss" scoped>
.info-item {
    @apply dark:bg-dark-bg-2 flex text-xs items-center rounded-lg h-10 p-2 dark:text-neutral-200;
}
</style>
