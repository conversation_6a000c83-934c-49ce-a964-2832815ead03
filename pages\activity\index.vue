<template>
    <MobileHeader :title="t('ACTIVITY.TITLE')" />

    <ScrollView class="h-full relative bg-bg-1 dark:text-dark-text overflow-hidden font-medium py-14 px-4 overflow-y-auto" @scrolltolower="scrollLoadActivity" :distance="100">
        <h1 class="text-center text-text-1 text-2.5xl mb-14">{{ $t("ACTIVITY.TITLE") }}</h1>
        <div>
            <div class="grid grid-cols-[repeat(auto-fit,_minmax(380px,380px))] justify-center gap-4">
                <template v-for="item in activityList" :key="item.id">
                    <ActivityItem :activity="item" />
                </template>
                <div v-if="activityList.length && activityList.length < 2" class="rounded-xl text-text-4 overflow-hidden border-2 border-dashed border-border-2 flex items-center justify-center">
                    <span>{{ $t("ACTIVITY.MORE_TEXT") }}</span>
                </div>
            </div>
            <div v-if="activityLoading" class="sticky left-0 bottom-0 right-0 py-10 flex items-center justify-center">
                <n-icon size="32" class="text-primary">
                    <IconsSpinLoading />
                </n-icon>
            </div>
        </div>
    </ScrollView>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });

useSeoMeta({
    title: () => t("SEO_META.SEO_ACTIVITY_TITLE"),
    ogTitle: () => t("SEO_META.SEO_ACTIVITY_TITLE"),
    description: () => t("SEO_META.SEO_ACTIVITY_DESC"),
    ogDescription: () => t("SEO_META.SEO_ACTIVITY_DESC"),
});
import { getActivityListApi } from "@/api/activity";
import { useUnreadMessage } from "@/stores";

import useListFetch from "@/hook/useListFetch";
import ScrollView from "@/components/ScrollView.vue";
import ActivityItem from "@/components/activity/ActivityItem.vue";
const unreadMessages = useUnreadMessage();

const {
    list: activityList,
    reload: reloadActivityList,
    loadMore,
    loading: activityLoading,
} = useListFetch({
    api: getActivityListApi,
    queryData: {},
    cursorKey: "",
});
reloadActivityList();

const scrollLoadActivity = () => {
    if (activityLoading.value) return;
    loadMore();
};
onMounted(() => {
    unreadMessages.updateNewActivityCount(0);
});
</script>

<style lang="scss" scoped></style>
