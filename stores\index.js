import { defineStore } from "pinia";
import { useStorageType } from "@/hook/useStorageType";
const { storageType } = useStorageType();

import { updateUserProfile, getPreloadTasks, delPreloadTask, updateReadStatus, updateUserDailyLumenDailogControl } from "@/api";
export * from "./subscribe.js";
export * from "./shareList.js";
import { useShareDataStore } from "./shareList.js";
import { PRELOAD_TASK_TYPE, NEW_ROUTERS_DOT, DEFAULT_LANG_LIST as langList } from "@/utils/constant.js";

//用户信息
export const useUserProfile = defineStore(
    "userProfile",
    () => {
        const user = ref({});
        const userConfig = ref({});
        const isNeedLumenToDay = ref(false); // 每日获取lumen开关 true: 需要领取，false: 不需要领取
        const funcVisibledDict = ref({}); // 用户第一次使用功能的记录
        const setUser = (info = {}) => {
            info.token && (useCookie("authToken").value = info.token);
            user.value = info;
            return Promise.resolve();
        };

        /**
         * 更新每日领取lumen状态，
         * 1.现阶段服务端每次只展示1次dailyUpdate字段，下次请求不再返回
         *
         * @param {*} target true: 需要领取，false: 不需要领取
         */
        const updateNeedLumenToday = (target) => {
            isNeedLumenToDay.value = target;
        };

        //上报用户已签到
        const reportUserLumenDialogFinish = async () => {
            const { status } = await updateUserDailyLumenDailogControl({ showDialog: false });
        };

        const clearUserInfo = () => {
            const shareData = useShareDataStore();
            user.value = {};
            userConfig.value = {};
            shareData.setList([]);
        };
        const updateUser = (info) => {
            user.value = { ...user.value, ...info };
            updateNeedLumenToday(user.value.dailyLumenDialog); //true:需要展示 false: 不需要展示
        };
        const updateUserLocalConf = (conf) => {
            if (conf.delConfirm !== false) {
                conf.delConfirm = true;
            }
            conf.clearPrompt = !!conf.clearPrompt; //默认不清空提示词
            if (conf.detailShowParameters !== false) {
                conf.detailShowParameters = true;
            }
            if (conf.displayPrompt !== false) {
                conf.displayPrompt = true;
            }
            if (!conf.changeTabStatus) {
                conf.changeTabStatus = false;
            }
            if (!conf.downloadFileType) {
                conf.downloadFileType = "png";
            }
            if (conf.showLumenCost !== false) {
                conf.showLumenCost = true;
            }
            if (!conf.newRouters) {
                conf.newRouters = NEW_ROUTERS_DOT;
            }
            if (!conf.hiddenPromptWhenUploadImg) {
                // 社区中上传图片 是否显示prompt
                conf.hiddenPromptWhenUploadImg = false;
            }

            if (!conf.usedFuncList) {
                // 曾使用过的功能列表
                conf.usedFuncList = [];
            }
            userConfig.value = conf || {};
        };
        const syncUpdateUserConfig = (info = {}) => {
            return new Promise(async (resolve) => {
                const conf = Object.assign({}, userConfig.value, info);
                const { status } = await updateUserProfile({ userConfig: conf });
                if (status === 0) {
                    updateUserLocalConf(conf);
                }
                resolve(status === 0);
            });
        };
        // 校验标签 Standed 和 Pro用户校验
        const userVipNotice = ref([]);

        // 校验用户VIP提示是否已展示过
        const checkUserVipNoticeKey = (key) => {
            if (!key) return false;
            return userVipNotice.value.some((item) => item === key);
        };

        // 添加用户VIP提示标签，用于校验下次是否展示
        const appendUserVipNoticeKey = (key) => {
            if (!key) return;
            userVipNotice.value.push(key);
        };

        // 清空用户Vip提示数据
        const clearUserVipNoticeKeys = () => {
            userVipNotice.value = [];
        };

        /**
         * 校验用户是否使用过xxx功能 规则：用户UserId + 功能key (如果userId存在的话)
         * @param {*} key
         * @returns true: 已经展示过了  false: 从未展示过(执行后续逻辑)
         * 使用样例:
         * const userProfile = useUserProfile();
         *
         * if (!userProfile.checkUsedFuncByKey("create_edit")) {
         *   - 在这里实现对应功能判断与分叉逻辑 -
         *   --- 保存新状态
         *   userProfile.refreshUsedFuncByKey("create_edit");
         * }
         */
        const checkUsedFuncByKey = (key) => {
            const list = userConfig.value.usedFuncList ?? [];
            return list.some((item) => item === key); // 查询是否存在key值 true:存在、已使用过此功能
        };
        const refreshUsedFuncByKey = (key) => {
            const list = userConfig.value.usedFuncList ?? [];
            list.push(key);
            syncUpdateUserConfig({ usedFuncList: list });
        };

        return {
            user,
            userConfig,
            isNeedLumenToDay,
            userVipNotice,
            checkUserVipNoticeKey,
            appendUserVipNoticeKey,
            clearUserVipNoticeKeys,
            funcVisibledDict,
            setUser,
            clearUserInfo,
            updateUser,
            syncUpdateUserConfig,
            updateUserLocalConf,
            updateNeedLumenToday,
            reportUserLumenDialogFinish,
            checkUsedFuncByKey,
            refreshUsedFuncByKey,
        };
    },
    {
        persist: import.meta.client
            ? {
                  paths: ["user", "userConfig", "funcVisibledDict"], // 仅持久化选中的变量
                  storage: {
                      getItem: (key) => {
                          return window[storageType.value].getItem(key);
                      },
                      setItem: (key, value) => {
                          window[storageType.value].setItem(key, value);
                      },
                      removeItem: (key) => {
                          window[storageType.value].removeItem(key);
                      },
                  },
              }
            : false,
    }
);
export const useRelaxTaskState = defineStore("relaxTaskState", () => {
    const relaxTaskState = ref(false);
    const updateRelaxTaskState = (state = false) => {
        relaxTaskState.value = state;
    };

    return { relaxTaskState, updateRelaxTaskState };
});
// 删除时 二次確認窗口是否需要弹出
export const useDefDelConfirm = defineStore("defDelConfirm", () => {
    const isConfirm = ref(false);
    const setDefConfirm = (val) => {
        isConfirm.value = val;
    };
    return { isConfirm, setDefConfirm };
});
// 取消收藏的时候 二次确认
export const useDefUnCollectionsConfirm = defineStore("defUnCollectionsConfirm", () => {
    const isConfirm = ref(false);
    const setDefConfirm = (val) => {
        isConfirm.value = val;
    };
    return { isConfirm, setDefConfirm };
});
// 收藏夹共享
export const useShareCollect = defineStore("shareCollect", () => {
    const collectList = ref([]);
    const currentCollectItem = ref({});
    const setCollectItem = (item = {}) => {
        currentCollectItem.value = item;
    };
    const setCollectList = (list = []) => {
        collectList.value = list;
    };
    return { collectList, currentCollectItem, setCollectList, setCollectItem };
});
// 强制更新状态保持的3个[create、history、collection]页面显示状态(详情、后处理、滚动)
export const useForceUpdatePageState = defineStore("forceUpdatePageState", () => {
    const key = ref({});
    const updatePageKey = (path, val) => {
        key.value[path] = val;
    };
    return { key, updatePageKey };
});
//每日公开图片限制
export const useDailyPostPicLimit = defineStore("dailyPostPicLimit", () => {
    const limit = ref(0);
    const setLimit = (val = 0) => {
        limit.value = val;
    };
    return { limit, setLimit };
});
// 用户未读消息
export const useUnreadMessage = defineStore("unreadMessage", () => {
    const typeKeyMap = Object.freeze({
        nlikeNums: "like",
        ncommentNums: "comment",
        nsysUpdateNums: "sysUpdate",
        nplatformMessageNums: "platformMessage",
        nplatformActivityNums: "platformActivity",
    });

    const typeMap = Object.freeze({
        nlikeNums: 0,
        ncommentNums: 1,
        nsysUpdateNums: 2,
        nplatformActivityNums: 3,
        nplatformMessageNums: 4,
    });

    const transformObjectToArray = (obj) => {
        return Object.keys(obj)
            .map((key) => ({
                label: key,
                value: obj[key],
                order: typeMap[key],
            }))
            .sort((a, b) => a.order - b.order);
    };

    const messageGroup = ref([]);
    const newActivityCount = ref(0);
    const updateUnreadList = (messages = {}) => {
        const messageTypeKey = Object.keys(typeKeyMap);
        const list = transformObjectToArray(messages);
        messageGroup.value = list.filter((item) => messageTypeKey.includes(item.label));
    };

    const readAllMessage = () => {
        messageGroup.value = messageGroup.value.map((item) => ({
            ...item,
            value: 0,
        }));
    };

    const updateNoticeReadStatus = async (messageId, type) => {
        const { status, message } = await updateReadStatus({
            messageId,
            messageType: typeKeyMap[type],
        });
        if (status !== 0) {
            openToast.error(message);
            return Promise.resolve(false);
        }
        const index = messageGroup.value.findIndex((item) => item.label === type);
        const count = messageGroup.value[index].value - 1;
        messageGroup.value[index].value = Math.max(0, count);
        return Promise.resolve(true);
    };
    const updateNewActivityCount = (count) => {
        if (!count) {
            count = 0;
        }
        newActivityCount.value = count;
    };

    const unReadCount = computed(() => {
        return messageGroup.value.reduce((acc, item) => acc + item.value, 0) || 0;
    });

    return {
        newActivityCount,
        messageGroup,
        unReadCount,
        updateUnreadList,
        readAllMessage,
        updateNoticeReadStatus,
        updateNewActivityCount,
    };
});

export const useUserCommunity = defineStore("userCommunity", () => {
    const communityUser = ref({
        posted: 0,
        likes: 0,
        following: 0,
        followers: 0,
        introduction: "",
        userAvatarUrl: "",
    });
    const userProfileStore = useUserProfile();
    const updateCommunityUser = (info = {}) => {
        Object.assign(communityUser.value, info);
        const { introduction, userAvatarUrl } = info;
        const params = {};
        if (introduction) {
            params.introduction = introduction;
        }
        if (userAvatarUrl) {
            params.avatarUrl = userAvatarUrl;
        }
        if (introduction || userAvatarUrl) {
            userProfileStore.updateUser(params);
        }
    };
    return { communityUser, updateCommunityUser };
});
