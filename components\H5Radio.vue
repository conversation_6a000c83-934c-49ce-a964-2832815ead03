<template>
    <div>
        <div @click="changeVal(item)" class="flex gap-2 items-center py-4" :class="{ 'border-t border-solid dark:border-dark-bg-2': index > 0 }" v-for="(item, index) in options" :key="item.label">
            <component :is="item.icon"></component>
            <div class="flex-1">{{ item.label }}</div>
            <div v-if="item.value !== value" class="w-5 h-5 border-2 rounded-full dark:border-dark-tag-bg border-dark-text"></div>
            <div v-else class="w-5 h-5 rounded-full bg-primary flex items-center justify-center">
                <n-icon class="shrink-0 text-white" size="18"><IconsSuccess /></n-icon>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    value: {
        type: [String, Boolean, Number],
        default: false,
    },
    hidden: {
        type: Boolean,
        default: false,
    },
    options: {
        type: Array,
        default: () => [],
    },
});
const emits = defineEmits(["update:value", "update:hidden"]);
const changeVal = (item) => {
    emits("update:value", item.value);
    emits("update:hidden", false);
};
</script>

<style lang="scss" scoped></style>
