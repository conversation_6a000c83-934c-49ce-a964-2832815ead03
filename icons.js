export default defineNuxtPlugin((nuxtApp) => {
  // 自动导入根目录 icons 文件夹下的所有 .vue 文件，注意路径根据插件文件位置进行配置
  const icons = import.meta.glob("../icons/*.vue", { eager: true });

  for (const path in icons) {
    const component = icons[path].default;
    // 使用组件的 name 属性注册，如果没有 name 可采用文件名作为组件名
    nuxtApp.vueApp.component(
      component.name ||
        path
          .split("/")
          .pop()
          ?.replace(/\.\w+$/, ""),
      component
    );
  }
});
