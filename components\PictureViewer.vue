<template>
    <Teleport to="body">
        <Transition name="fade">
            <div
                ref="stage"
                class="w-screen h-screen inset-0 bg-fill-t-3 backdrop-blur fixed z-[9999] top-0 left-0 overflow-hidden cursor-grab"
                :class="{ 'cursor-grabbing': isDragging }"
                @wheel.prevent="handleWheel"
                @mousedown="handleMouseDown"
                @mousemove="handleMouseMove"
                @mouseup="handleMouseUp"
                @mouseleave="handleMouseUp"
            >
                <img ref="img" @load="adaptiveDisplay" :src="imageUrl" alt="" draggable="false" class="transition-opacity" :style="imageStyle" />

                <div
                    class="size-10 rounded-full border border-solid border-border-t-1 backdrop-blur-lg text-text-t-2 flex items-center justify-center bg-fill-dw-4 hover:bg-fill-dw-5 absolute z-10 top-6 left-6 cursor-pointer transition-colors duration-200"
                    @click.stop.prevent="closeImageViewer"
                >
                    <n-icon size="24">
                        <IconsClose />
                    </n-icon>
                </div>

                <div class="absolute bottom-10 left-1/2 -translate-x-1/2 flex items-center gap-3" @mousedown.stop>
                    <div class="rounded-xl backdrop-blur-lg border border-solid border-border-t-1 bg-fill-ipt-s-1 p-1 flex items-center gap-2 text-text-2 cursor-default">
                        <div class="size-10 flex items-center justify-center rounded-lg cursor-pointer hover:bg-fill-ww-11 hover:text-text-1 transition-colors duration-200" @click="fitToScreen">
                            <n-icon size="24">
                                <IconsOriginScale />
                            </n-icon>
                        </div>
                        <div class="size-10 flex items-center justify-center rounded-lg cursor-pointer hover:bg-fill-ww-11 hover:text-text-1 transition-colors duration-200" @click="adaptiveDisplay">
                            <n-icon size="24">
                                <IconsFullScreenScale />
                            </n-icon>
                        </div>
                    </div>
                    <div class="rounded-xl backdrop-blur-lg border border-solid border-border-t-1 bg-fill-ipt-s-1 p-1 flex items-center gap-2 text-text-2 cursor-default">
                        <div
                            class="size-10 flex items-center justify-center rounded-lg cursor-pointer hover:bg-fill-ww-11 hover:text-text-1 transition-colors duration-200"
                            @click="handleUpdateScale(-1)"
                        >
                            <n-icon size="20">
                                <IconsMinus />
                            </n-icon>
                        </div>

                        <n-slider :tooltip="false" class="w-36 text-primary-6" v-model:value="displayScale" :on-update:value="changeScale" :step="1" :max="800" :min="5" />

                        <div
                            class="size-10 flex items-center justify-center rounded-lg cursor-pointer hover:bg-fill-ww-11 hover:text-text-1 transition-colors duration-200"
                            @click="handleUpdateScale(1)"
                        >
                            <n-icon size="20">
                                <IconsAdd />
                            </n-icon>
                        </div>

                        <div class="w-0.5 h-6 rounded bg-border-2"></div>

                        <PopoverDropdown class="w-40" :options="scaleList" trigger="click" :to="false" :noTrans="true" @select="changeScale">
                            <div class="px-3 flex items-center gap-1.5 cursor-pointer h-10 rounded-lg hover:bg-fill-ww-11 hover:text-text-1 transition-colors duration-200">
                                <div class="w-11 text-center">{{ Math.round(scale * 100) }}%</div>
                                <n-icon size="20">
                                    <IconsCaretDown />
                                </n-icon>
                            </div>
                        </PopoverDropdown>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup>
const props = defineProps({
    imageUrl: {
        type: String,
        default: "https://uploads.piclumen.com/community/20250430/13/13c97440-20ca-44f7-8e35-ea0c802ee888.webp",
    },
    show: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(["update:show"]);
const stage = ref(null); // 容器 DOM
const img = ref(null); // 图片 DOM

const initScreenPad = 24; //初始化时屏幕边距，防止图片被遮挡

const displayScale = ref(100); // 显示缩放比例，初始为 100%（图像原始大小）
const scale = ref(1); // 当前真实缩放
const pos = reactive({ x: 0, y: 0, opacity: 0 }); // 图片左上角坐标
const isDragging = ref(false);
const dragStartPos = reactive({ x: 0, y: 0 }); // 拖拽起始鼠标位置
const dragStartImg = reactive({ x: 0, y: 0 }); // 拖拽起始图片位置

//默认的可选缩放列表
const scaleList = [
    { label: "5%", key: "5", value: 5 },
    { label: "10%", key: "10", value: 10 },
    { label: "25%", key: "25", value: 25 },
    { label: "100%", key: "100", value: 100 },
    { label: "125%", key: "125", value: 125 },
    { label: "150%", key: "150", value: 150 },
    { label: "200%", key: "200", value: 200 },
    { label: "300%", key: "300", value: 300 },
    { label: "400%", key: "400", value: 400 },
    { label: "800%", key: "800", value: 800 },
];

//计算属性：最终样式
const imageStyle = computed(() => ({
    position: "absolute",
    left: 0,
    top: 0,
    transform: `translate(${pos.x}px, ${pos.y}px) scale(${scale.value})`,
    transformOrigin: "0 0",
    willChange: "transform",
    opacity: pos.opacity,
}));

//工具函数
// 获取容器宽高
const getViewportSize = () => ({
    width: document.body.clientWidth,
    height: document.body.clientHeight,
});
// 获取图片原始宽高
const getImageSize = () => ({
    width: img.value.naturalWidth,
    height: img.value.naturalHeight,
});

//初始化时图片按照原始大小展示
const fitToScreen = () => {
    const { width: cw, height: ch } = getViewportSize();
    const { width: iw, height: ih } = getImageSize();
    scale.value = 1;
    displayScale.value = 100;
    // 居中
    pos.x = (cw - iw) / 2;
    pos.y = (ch - ih) / 2;
    pos.opacity = 1; // 确保图片可见
};

//自适应屏幕大小展示完整图片
const adaptiveDisplay = (val) => {
    let { width: cw, height: ch } = getViewportSize();
    const { width: iw, height: ih } = getImageSize();
    // 透明度为0时，说明当前为初始化调用
    let recoupOffset = 0; // onload 初始化时 4边预留24px padding
    if (pos.opacity === 0) {
        pos.opacity = 1;
        recoupOffset = initScreenPad;
        cw -= initScreenPad * 2;
        ch -= initScreenPad * 2;
    }
    // 计算缩放比例，使图片完整可见
    let s = Math.min(cw / iw, ch / ih);
    // 如果传入了缩放比例，则使用该值 需要防止val为Click Event
    if (val && Number.isFinite(val)) {
        s = val; // 如果传入了缩放比例，则使用该值
    }
    scale.value = s;
    displayScale.value = s * 100;
    // 居中
    pos.x = (cw - iw * s) / 2 + recoupOffset;
    pos.y = (ch - ih * s) / 2 + recoupOffset;
};
//手动设置缩放比例,强制回归视口中心(防止图片偏移量过大 从视图消失)
const changeScale = (v) => {
    adaptiveDisplay(v / 100);
};
const handleUpdateScale = (v) => {
    const oldScale = displayScale.value;
    const newScale = Math.max(5, Math.min(oldScale + v * 8, 800)); // 限制在 5% 到 800% 之间
    changeScale(newScale);
};

//滚轮缩放
const handleWheel = (e) => {
    const { deltaY } = e;
    const delta = scale.value * (1 - Math.sign(deltaY) * 0.08); // 每次 基于当前scale * ±8% 缩放
    const newScale = Math.max(0.05, Math.min(delta, 8)); // 5%  到 800% 之间
    // 计算鼠标在图片上的坐标
    const rect = stage.value.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    // 计算缩放前后鼠标在图片内的相对坐标不变
    pos.x = mouseX - (mouseX - pos.x) * (newScale / scale.value);
    pos.y = mouseY - (mouseY - pos.y) * (newScale / scale.value);
    scale.value = newScale;
    displayScale.value = Math.round(newScale * 100);
};

//拖拽平移
const handleMouseDown = (e) => {
    e.preventDefault();
    isDragging.value = true;

    dragStartPos.x = e.clientX;
    dragStartPos.y = e.clientY;
    dragStartImg.x = pos.x;
    dragStartImg.y = pos.y;
};
const handleMouseMove = (e) => {
    if (!isDragging.value) return;
    pos.x = dragStartImg.x + (e.clientX - dragStartPos.x);
    pos.y = dragStartImg.y + (e.clientY - dragStartPos.y);
};
const handleMouseUp = () => {
    isDragging.value = false;
};

const closeImageViewer = () => {
    emits("update:show", false);
};
</script>

<style scoped>
.cursor-grab {
    cursor: grab;
}
.cursor-grabbing {
    cursor: grabbing;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
