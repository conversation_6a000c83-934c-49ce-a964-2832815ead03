<template>
    <div class="h-7 px-3 py-1 absolute top-0 right-0 flex items-center justify-center text-white font-semibold text-sm" :class="isTranslateY ? '-translate-y-1/2' : ''">
        <slot></slot>
    </div>
</template>

<script setup>
const props = defineProps({
    isTranslateY: { type: Boolean, default: true }, //当设置为false的时候 组件不会向上移，为ture时组件会高于父容器半个身子 Subscribe页、SubscribeModal组件为false
});

const { isTranslateY } = toRefs(props);
</script>
