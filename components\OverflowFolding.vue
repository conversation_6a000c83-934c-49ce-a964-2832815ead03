<template>
    <div class="relative">
        <div ref="container" class="overflow-hidden transition-all duration-300" :style="{ maxHeight: expanded ? '1000px' : `${props.maxHeight}px` }">
            <slot></slot>
        </div>

        <div
            v-if="shouldShowToggle && !expanded"
            class="absolute bottom-[40px] left-0 w-full h-[80px] bg-gradient-to-t from-white to-transparent dark:from-dark-bg-3 z-10 flex items-end justify-center"
        ></div>
        <div class="w-full flex justify-center items-center relative z-[10]" v-if="shouldShowToggle">
            <n-button @click="expanded = !expanded" :bordered="false" class="rounded-full min-w-8 h-10 px-4 text-sm !text-text-2 !bg-fill-btn-1 backdrop-blur-[16px]">
                {{ expanded ? t('OVERFLOW_FOLDING.SHOW_LESS') : t('OVERFLOW_FOLDING.SHOW_MORE')}}
            </n-button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from "vue";

const props = defineProps({
    maxHeight: {
        type: Number,
        default: 120,
    },
});

const container = ref(null);
const expanded = ref(false);
const shouldShowToggle = ref(false);

onMounted(async () => {
    await nextTick();
    const el = container.value;
    if (el && el.scrollHeight > props.maxHeight + 10) {
        // 加10是为了误差余量
        shouldShowToggle.value = true;
    }
});
</script>
