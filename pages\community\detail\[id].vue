<template>
    <div class="w-screen h-screen bg-bg-1 md:p-6 flex justify-center fixed top-0 left-0 z-[50] font-medium">
        <template v-if="!dataException">
            <div
                class="md:hidden absolute top-4 left-4 z-50 gap-4 w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl hover:bg-fill-wd-2 cursor-pointer"
                @click.stop="handleClose"
            >
                <IconsArrowLeft />
            </div>
            <section class="h-full flex w-full justify-center" @click="handleClose">
                <div class="max-md:hidden w-16 shrink-0 flex flex-col justify-center relative gap-6">
                    <div
                        class="absolute top-0 left-0 gap-4 w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl hover:bg-fill-wd-2 cursor-pointer"
                        @click.stop="handleClose"
                    >
                        <!-- 左上返回按钮 -->
                        <IconsArrowLeft />
                    </div>
                    <div
                        class="w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl -rotate-90 hover:bg-fill-wd-2 cursor-pointer"
                        :class="{ '!text-text-drop-3 !bg-fill-wd-1 !cursor-default': disableSwitch(-1) }"
                        @click.stop="handlePrevious"
                    >
                        <!-- 左侧 上一张 -->
                        <IconsArrowRight />
                    </div>
                    <div
                        class="w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl rotate-90 hover:bg-fill-wd-2 cursor-pointer"
                        :class="{ '!text-text-drop-3 !bg-fill-wd-1 !cursor-default': disableSwitch(1) }"
                        @click.stop="handleNext"
                    >
                        <!-- 左侧 下一张 -->
                        <IconsArrowRight />
                    </div>
                </div>
                <div class="h-full flex-1 w-[1560px] max-w-[1560px] bg-bg-2 rounded-2xl flex font-medium overflow-hidden" @click.stop>
                    <div class="p-4 w-full flex-1 min-w-96 max-w-[1200px] flex flex-col gap-4 relative">
                        <div class="h-10 flex gap-3 justify-end absolute top-4 right-4">
                            <div
                                class="p-2.5 min-w-16 rounded-full bg-fill-dw-4 backdrop-blur-lg border border-solid border-border-1 flex items-center justify-center text-text-t-2 text-[20px] cursor-pointer gap-1"
                                @click="commandDispense('like')"
                            >
                                <HeartLike :checked="currentPreview.liked" />
                                <span class="text-sm">{{ formatLike(currentPreview.fileLikeNums) }}</span>
                            </div>
                            <div
                                class="p-2.5 min-w-16 rounded-full bg-fill-dw-4 backdrop-blur-lg border border-solid border-border-1 flex items-center justify-center text-text-t-2 text-[20px] cursor-pointer gap-1"
                                @click="commandDispense('replay')"
                            >
                                <IconsNoticeComments />
                                <span class="text-sm">{{ formatLike(currentPreview.fileCommentNums) }}</span>
                            </div>

                            <ShareBar :url="currentPreview.thumbnailUrl" :id="currentPreview.id" :fileId="currentPreview.fileId" :community="true">
                                <div
                                    class="p-2.5 rounded-full bg-fill-dw-4 backdrop-blur-lg border border-solid border-border-1 flex items-center justify-center text-text-t-2 text-[20px] cursor-pointer gap-1"
                                    @click="gtagController('share')"
                                >
                                    <IconsShare />
                                </div>
                            </ShareBar>

                            <n-dropdown :options="moreActionList" placement="top" class="explore-more-dropdown" trigger="click" @select="checkMoreAction">
                                <div
                                    class="p-2.5 rounded-full bg-fill-dw-4 backdrop-blur-lg border border-solid border-border-1 flex items-center justify-center text-text-t-2 text-[20px] cursor-pointer gap-1"
                                >
                                    <IconsHorMore />
                                </div>
                            </n-dropdown>
                        </div>
                        <div
                            class="rounded-lg overflow-hidden flex-1 max-w-full w-[1200px] bg-contain bg-no-repeat bg-center mx-auto my-auto cursor-zoom-in"
                            :style="{ 'aspect-ratio': currentPreview.realWidth / currentPreview.realHeight, backgroundImage: `url('${currentPreview.bgUrl}')` }"
                            @click="handlePreviewImage"
                        >
                            <img v-show="currentPreview.preUrl" :src="currentPreview.preUrl" class="object-contain w-full h-full" alt="" />
                        </div>
                    </div>
                    <!-- 图片右侧详情信息展示 -->
                    <div class="max-md:hidden border-l border-solid border-border-1 w-[360px] flex flex-col shrink-0">
                        <div class="p-4 border-b border-solid border-border-1 flex items-center gap-2 w-full text-text-2 overflow-hidden shrink-0">
                            <div @click="useToCommunityHome(currentPreview.accountInfo?.userId)" class="cursor-pointer h-8 w-8">
                                <UsersAvatar
                                    :icon-size="32"
                                    :src="currentPreview?.accountInfo?.userAvatarUrl"
                                    :user-id="currentPreview?.accountInfo?.userId"
                                    :plan-level="currentPreview?.accountInfo?.planLevel"
                                    :isDetails="true"
                                />
                            </div>
                            <span @click="useToCommunityHome(currentPreview.accountInfo?.userId)" class="cursor-pointer flex-1 text-ellipsis text-nowrap overflow-hidden">{{
                                currentPreview.accountInfo?.userName
                            }}</span>
                            <template v-if="!isOwn">
                                <Button v-if="!currentPreview.followed" type="tertiary" size="small" @click="handleUpdateFollow">{{ t("COMMUNITY_FOLLOW_BTN") }}</Button>
                                <Button v-else type="secondary" size="small" @click="handleUpdateFollow">{{ t("COMMUNITY_FOLLOWING") }}</Button>
                            </template>
                        </div>

                        <div class="p-4 flex flex-col gap-4 flex-1 relative overflow-hidden">
                            <SwitchTab class="mx-8" :options="tabsOptions" v-model:value="tab" @change="switchTabs" />

                            <div class="no-scrollbar flex flex-col gap-4 overscroll-y-none overflow-y-auto" v-if="tab === 'details'">
                                <div v-if="!!showTags" class="text-primary-6 text-sm">{{ showTags }}</div>

                                <NuxtLinkLocale v-if="actrivityInfo" :to="`/activity/${actrivityInfo.activityId}`" class="flex gap-2 cursor-pointer group">
                                    <n-icon :size="20">
                                        <IconsRankTrophy />
                                    </n-icon>
                                    <div class="text-warning-6 flex-1 line-clamp-1 group-hover:underline">
                                        {{ actrivityInfo.title }}
                                    </div>
                                    <n-icon :size="16">
                                        <IconsArrowRight class="text-text-5" />
                                    </n-icon>
                                </NuxtLinkLocale>

                                <div v-if="!!currentPreview.prompt" class="p-3 rounded-lg bg-fill-wd-1">
                                    <template v-if="currentPreview.showPrompt">
                                        <div class="flex items-center justify-between gap-2 text-text-4">
                                            <span>{{ t("COMMON_PROMPT") }}</span>
                                            <IconsCopyText
                                                v-if="currentPreview.showPrompt && !!currentPreview.prompt"
                                                class="text-xl hover:text-text-2 cursor-pointer"
                                                @click="copyPromptText(currentPreview.prompt)"
                                            />
                                        </div>
                                        <div class="mt-2 text-text-3 break-words max-h-32 overflow-y-auto no-scroll">{{ currentPreview.prompt }}</div>
                                    </template>

                                    <div v-else class="mt-2 text-text-4 flex flex-col gap-2 items-center justify-center">
                                        <n-icon size="20" class="text-info-6" :class="{ 'cursor-pointer': currentPreview.showPrompt }">
                                            <IconsLock />
                                        </n-icon>
                                        <span>{{ t("COMMUNITY_AUTHOR_HIDE_PROMPT") }}</span>
                                    </div>
                                </div>
                                <div v-if="!!currentPreview.negative_prompt && currentPreview.showPrompt" class="p-3 rounded-lg bg-fill-wd-1">
                                    <!-- <template v-if="currentPreview.showPrompt"></template> -->
                                    <div class="flex items-center justify-between gap-2 text-text-4">
                                        <span>{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>
                                        <IconsCopyText class="text-xl hover:text-text-2 cursor-pointer" @click="copyPromptText(currentPreview.negative_prompt)" />
                                    </div>
                                    <div class="mt-2 text-text-3 break-words max-h-32 overflow-y-auto no-scroll">{{ currentPreview.negative_prompt }}</div>

                                    <!-- <div v-else class="mt-2 text-text-4 flex flex-col gap-2 items-center justify-center">
                                        <n-icon size="20" class="text-info-6" :class="{ 'cursor-pointer': currentPreview.showPrompt }">
                                            <IconsLock />
                                        </n-icon>
                                        <span>{{ t("COMMUNITY_AUTHOR_HIDE_PROMPT") }}</span>
                                    </div> -->
                                </div>
                                <PictureInfo :info="currentPreview" />
                            </div>
                            <div class="no-scrollbar flex flex-col gap-4 overscroll-y-none overflow-y-auto" ref="commentScrollRef" v-if="tab === 'comments'">
                                <div v-if="comments.maxPage > comments.pageNum">
                                    <n-divider>
                                        <span v-show="!comments.loading" class="text-xs cursor-pointer" @click="getCommentDetails({})">
                                            {{ t("COMMUNITY_VIEW_REPLIES", { num: comments.total - (comments.list?.length || 0) }) }}
                                        </span>
                                        <n-icon v-show="comments.loading" size="24" class="text-primary">
                                            <IconsSpinLoading />
                                        </n-icon>
                                    </n-divider>
                                </div>
                                <div v-for="item in comments.list" :key="item.id">
                                    <div class="mb-5">
                                        <CommunityComment @reply="handleReply" @hiddenSelf="(type) => handleHiddenCurrent(item, type)" :comment="item" :selfUserId="user.userId" />
                                    </div>

                                    <div v-if="item.replyNums > 0 && item.replyNums > (item.list?.length || 0)">
                                        <n-divider>
                                            <span v-show="!item.loading" class="text-xs cursor-pointer" @click="getCommentDetails(item)">
                                                {{ t("COMMUNITY_VIEW_REPLIES", { num: item.replyNums - (item.list?.length || 0) }) }}
                                            </span>
                                            <n-icon v-show="item.loading" size="24" class="text-primary">
                                                <IconsSpinLoading />
                                            </n-icon>
                                        </n-divider>
                                    </div>
                                    <div class="mb-5 ml-8" v-for="child in item.list" :key="child.id">
                                        <CommunityComment @reply="handleReply" @hiddenSelf="(type) => handleHiddenCurrent(child, type)" :comment="child" :selfUserId="user.userId" />
                                    </div>
                                </div>
                                <div v-if="comments.total === 0">
                                    <div class="text-center text-text-6 text-sm mt-20">
                                        <span>{{ t("NO_COMMENTS") }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="tab === 'details'" class="p-4 border-t border-solid border-border-1 flex gap-2">
                            <Button type="primary" size="medium" rounded="lg" :disabled="!currentPreview.showPrompt" class="w-1/2 shrink" @click="commandDispense('remix')">
                                <div class="flex items-center justify-center gap-1.5">
                                    <IconsCreate class="text-xl" />
                                    <span>{{ t("TOOLBAR_REMIX") }}</span>
                                </div>
                            </Button>
                            <Button type="secondary" size="medium" rounded="lg" :disabled="!currentPreview.showPrompt" class="w-1/2 shrink" @click="commandDispense('copy_info')">
                                <div class="flex items-center justify-center gap-1.5">
                                    <IconsCopyText class="text-xl" />
                                    <span>{{ t("TOOLBAR_COPY_INFO") }}</span>
                                </div>
                            </Button>
                        </div>
                        <div v-if="tab === 'comments'" class="p-4 border-t border-solid border-border-1">
                            <div class="rounded-lg border border-solid border-border-ipt-1 bg-fill-ipt-1" :class="{ 'border-border-ipt-3': defMinHeight !== 'auto' }">
                                <n-input
                                    @keydown.stop="keyDownHandlePost"
                                    :on-focus="commentFocus"
                                    class="comment-box text-text-2"
                                    style="--n-text-color: inherit"
                                    :maxlength="100"
                                    ref="commentBoxRef"
                                    type="textarea"
                                    :style="{ 'min-height': defMinHeight }"
                                    :placeholder="currentReplyTarget.ownerAcc?.userName ? `Reply @ ${currentReplyTarget.ownerAcc.userName}` : t('COMMUNITY_COMMENT')"
                                    v-model:value="newComment"
                                    round
                                    :rows="1"
                                    :autosize="{
                                        minRows: 1,
                                        maxRows: 4,
                                    }"
                                />
                            </div>

                            <div class="mt-4 flex items-center justify-between gap-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-xl hover:scale-105 cursor-pointer" @click="chooseEmoji(item)" :key="index" v-for="(item, index) in communityHotEmojis.slice(0, 4)">{{
                                        item
                                    }}</span>

                                    <n-popover trigger="hover" placement="top-end" raw :show-arrow="false" ref="emojiPopoverRef">
                                        <template #trigger>
                                            <n-icon size="16" class="cursor-pointer text-text-4 hover:text-text-2">
                                                <IconsHorMore />
                                            </n-icon>
                                        </template>
                                        <div class="p-4 rounded-lg grid grid-cols-8 text-2xl bg-bg-2 gap-2">
                                            <span @click="chooseEmoji(item)" class="hover:scale-105 cursor-pointer" :key="index" v-for="(item, index) in communityHotEmojis">{{ item }}</span>
                                        </div>
                                    </n-popover>
                                </div>
                                <div class="ml-auto flex gap-2">
                                    <Button type="primary" size="medium" :loading="commentLoading" class="min-w-[88px]" rounded="lg" @click="submitComment">
                                        {{ t("COMMUNITY_POST") }}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="max-md:hidden w-16 shrink-0"></div>
            </section>
        </template>

        <div v-else class="h-screen flex flex-col justify-center items-center">
            <img src="@/assets/images/notice_empty.webp" class="w-36 aspect-square hidden dark:block" />
            <img src="@/assets/images/notice_empty_light.webp" class="w-36 aspect-square dark:hidden" />
            <div class="mt-2 dark:text-dark-text">{{ t("AUTHOR_DEL_RES") }}</div>

            <Button class="mt-6 w-32" size="large" type="primary" @click="handleClose"> {{ t("ACC_SIGN_IN_BACK") }}</Button>
        </div>
    </div>

    <PictureViewer v-if="isPreviewMode" v-model:show="isPreviewMode" :imageUrl="previewImgUrl" />
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });

import { useGetModelInfo, useRemix } from "@/hook/create";
import { useReportContent, useToCommunityHome } from "@/hook/updateAccount";
import { useRecordCommunityScore } from "@/hook/useCommon";
import { useThemeStore } from "@/stores/system-config";

import { copyToClipboard, handleResetParams, formatNumber, communityHotEmojis, decryptResult, removeNulls, formatPonyV6Prompt, debounce } from "@/utils/tools";
import { useUserProfile, useShareDataStore } from "@/stores";
import { NIcon } from "naive-ui";
import { ShareLink, Dele, Alert } from "@/icons/index.js";
import LangText from "@/components/LangText.vue";
import { getActivityDetailApi } from "@/api/activity";

import {
    addLikeByFileId,
    reduceLikeByFileId,
    followUserById,
    unfollowUserById,
    queryCommentList,
    postComment,
    replyComment,
    delCommunityImgById,
    getCommunityDetail,
    getCommunityPersonalById,
} from "@/api";
import { useSyncAction } from "@/stores/syncAction";
import { storeToRefs } from "pinia";
import UsersAvatar from "@/components/users/Avatar.vue";
import { SUBSCRIBE_TYPE } from "@/utils/constant";

const currentPreview = ref({});
const { isMobile } = storeToRefs(useThemeStore());

const { showMessage } = useModal();
const syncAction = useSyncAction();
const { recordCommunityScore } = useRecordCommunityScore();
const route = useRoute();
const dataException = ref(false);
const { user } = useUserProfile();
const tab = ref("details");
const formatLike = computed(() => {
    return (num) => (isNaN(num) ? "-" : formatNumber(num));
});
const actrivityInfo = ref(undefined); //活动详情，当存在活动时，此变量成立

const filterPlanLevel = (planLevel) => {
    if (!planLevel || planLevel === SUBSCRIBE_TYPE.BASIC) return "";
    return planLevel;
};
const currPlanInfo = [];

const tabsOptions = computed(() => {
    return [
        { value: "details", label: t("DETAILS") },
        { value: "comments", label: t("MESSAGE_CENTER_COMMENTS") },
    ];
});
const renderIcon = (icon, props = {}) => {
    return () => {
        return h(
            NIcon,
            { size: 20, ...props },
            {
                default: () => h(icon),
            }
        );
    };
};
//更多操作
const moreActionList = computed(() => {
    const { reported, dataUserId } = currentPreview.value;
    const options = [
        {
            label: () => h(LangText, { labelKey: "TOOLBAR_COPY_LINK" }),
            key: "COPY_LINK",
            icon: renderIcon(ShareLink),
        },
    ];
    if (dataUserId == user.userId) {
        options.push({
            label: () => h(LangText, { labelKey: "TOOLBAR_DELETE" }),
            key: "DELETE",
            icon: renderIcon(Dele, { class: "text-text-drop-5" }),
            props: { class: "danger-bg !text-text-drop-5" },
        });
    }
    if (dataUserId != user.userId) {
        options.push({
            label: () => h(LangText, { labelKey: "REPORT" }),
            key: "REPORT",
            disabled: reported,
            icon: renderIcon(Alert, { class: "text-text-drop-5" }),
            props: { class: "danger-bg !text-text-drop-5" },
        });
    }
    return options;
});

//预览大图的URL 链接
const previewImgUrl = computed(() => {
    const { highThumbnailUrl, imgUrl, fileUrl } = currentPreview.value;
    return highThumbnailUrl || imgUrl || fileUrl;
});
//是否开启预览模式
const isPreviewMode = ref(false);
const handlePreviewImage = () => {
    if (!isMobile.value) {
        isPreviewMode.value = true;
    }
};

const showTags = computed(() => {
    const { tags = [] } = currentPreview.value;
    let tagStr = "";
    if (!tags) {
        return tagStr;
    }
    tags.map((item) => {
        tagStr += `#${item} `;
    });
    return tagStr;
});

//这张图片数据是否是自己的
const isOwn = computed(() => {
    return currentPreview.value.accountInfo?.userId === user.userId;
});
const detailLoading = ref(false);
const getDetail = async (commFileId) => {
    console.log("route.params.idroute.params.id>", commFileId);

    try {
        dataException.value = false;
        detailLoading.value = true;
        const { data } = await getCommunityDetail({ commFileId });
        detailLoading.value = false;
        const imgData = decryptResult(data);
        if (currentPreview.value.id && currentPreview.value.id !== imgData.id) {
            return;
        }
        updateCurrent(imgData);
        getCommentDetails();
    } catch (error) {
        dataException.value = true;
        detailLoading.value = false;
    }
    gtagController("show");
};

//初始化当前状态
const followLoading = ref(false);
const commentLoading = ref(false);
const queryCommentLoading = ref(false);
const newComment = ref("");
const currentReplyTarget = ref({});
const defMinHeight = ref("auto");

const def_comments = {
    list: [],
    pageNum: 0,
    pageSize: 50,
    maxPage: 1,
    total: 1,
};
const comments = ref({ ...def_comments });

const initCurrentStatus = () => {
    followLoading.value = false;
    commentLoading.value = false;
    queryCommentLoading.value = false;
    newComment.value = "";
    currentReplyTarget.value = {};
    defMinHeight.value = "auto";

    const total = currentPreview.value.fileFirstCommentNums || 0;
    const maxPage = Math.ceil(total / comments.value.pageSize);
    comments.value = {
        ...def_comments,
        total,
        maxPage,
    };
    if (!currentPreview.value.showPrompt) {
        tab.value = "comments";
    }
    lazyLoadImg();
};
//lazyLoad  img
const lazyLoadImg = debounce(() => {
    if (import.meta.client) {
        const img = new Image();
        const { highThumbnailUrl, imgUrl, fileUrl, promptId } = currentPreview.value;
        const preUrl = highThumbnailUrl || imgUrl || fileUrl;
        img.src = preUrl;
        img.onload = () => {
            if (currentPreview.value.fileUrl === img.src || currentPreview.value.highThumbnailUrl === img.src || currentPreview.value.imgUrl === img.src) {
                currentPreview.value.preUrl = preUrl;
            }
        };
    }
}, 400);
const getCommentDetails = async (parentComment = {}) => {
    if (queryCommentLoading.value) {
        return;
    }

    let params = { commFileId: currentPreview.value.id };
    let { refresh, id, firstCommentId } = parentComment;
    // 游标分页 最后一条数据ID
    //一级评论 入参
    if (!firstCommentId && !id) {
        let { pageNum = 0, pageSize, maxPage = 1, lastCommentId = "" } = comments.value;
        pageNum = refresh ? 1 : pageNum + 1;

        if (pageNum > maxPage) {
            return;
        }
        if (pageNum == 1) {
            lastCommentId = "";
        }
        comments.value.loading = true;
        params = {
            ...params,
            pageNum,
            pageSize,
            lastCommentId,
        };
    } else {
        //后代 评论入参
        let { pageNum = 0, pageSize = 50, maxPage = 1, lastCommentId = "" } = parentComment;
        pageNum = refresh ? 1 : pageNum + 1;
        if (pageNum > maxPage) {
            return;
        }
        if (pageNum == 1) {
            // 因为数据倒置  所以 LAST游标ID LIST[0]
            lastCommentId = "";
        }
        params = {
            ...params,
            firstCommentId: firstCommentId || id,
            pageNum,
            pageSize,
            lastCommentId,
        };
        parentComment.loading = true;
    }
    queryCommentLoading.value = true;
    const { status, data, message } = await queryCommentList(params);
    queryCommentLoading.value = false;
    comments.value.loading = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    // 查询一级评论
    if (!params.firstCommentId) {
        const { resultList = [], lastId } = data;
        resultList.reverse();
        comments.value.lastCommentId = lastId;
        comments.value.pageNum = params.pageNum;
        if (params.pageNum === 1) {
            comments.value.list = resultList;
        } else {
            comments.value.list.unshift(...resultList);
        }
        if (refresh) {
            nextTick(() => {
                commentScrollRef.value.scrollTop = 99999;
            });
        }
    } else {
        const belongIndex = comments.value.list.findIndex((item) => item.id === params.firstCommentId);
        if (belongIndex < 0) {
            return;
        }
        parentComment = { ...comments.value.list[belongIndex] };
        parentComment.loading = false;
        const { resultList = [], lastId } = data;
        resultList.reverse();
        const total = parentComment.replyNums || 0;
        parentComment.pageNum = params.pageNum;
        parentComment.maxPage = Math.max(Math.ceil(total / params.pageSize), 1);
        parentComment.lastCommentId = lastId;
        if (params.pageNum === 1) {
            parentComment.list = resultList;
        } else {
            parentComment.list.unshift(...resultList);
        }
        comments.value.list.splice(belongIndex, 1, parentComment);
    }
};
const handleReply = (info = {}) => {
    currentReplyTarget.value = info;
    if (!!info.id) {
        newComment.value = "";
    }
    commentBoxRef.value.focus();
};
//删除或举报当前评论 , 更新当前数据 type: 'report' | 'delete'
const handleHiddenCurrent = (item, type) => {
    if (type === "delete") {
        item.deleted = true;
    } else {
        item.reported = true;
    }
};

//评论滚动
const commentScrollRef = ref(null);
const commentBoxRef = ref(null);
const submitComment = async () => {
    gtagController("post");
    if (commentLoading.value) {
        return false;
    }
    if (newComment.value === "" || newComment.value.trim().length == 0) {
        return false;
    }

    let { firstCommentId = "", id } = currentReplyTarget.value;
    const params = {
        commFileId: currentPreview.value.id,
        content: newComment.value,
    };
    let updateComment = postComment;
    if (id) {
        //回复评论
        updateComment = replyComment;
        params.commentId = id;
        params.firstCommentId = firstCommentId || id;
    }
    commentLoading.value = true;
    const { status, message } = await updateComment(params);
    commentLoading.value = false;
    const parentComment = { ...currentReplyTarget.value, refresh: true };
    cancelComment();
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    //如果是回复的1级评论，手动维护1级评论总数，防止无法分页
    if (!id) {
        let { total, pageSize } = comments.value;
        comments.value.total = total + 1;
        comments.value.maxPage = Math.ceil((total + 1) / pageSize);
    } else {
        const index = comments.value.list.findIndex((item) => item.id === params.firstCommentId);
        const { replyNums, pageSize } = comments.value.list[index];
        comments.value.list[index].replyNums = replyNums + 1;
        comments.value.list[index].maxPage = Math.ceil((replyNums + 1) / pageSize);
    }
    //更新外部列表评论数量
    const isFirst = !id;
    currentPreview.value.fileCommentNums++;
    if (isFirst) {
        currentPreview.value.fileFirstCommentNums++;
    }
    //刷新评论
    getCommentDetails(parentComment);
    syncCommunityData();
};
//选择emoji
const emojiPopoverRef = ref(null);
const chooseEmoji = (emoji) => {
    newComment.value += emoji;
    emojiPopoverRef.value.setShow(false);
    commentBoxRef.value.focus();
};

//聚焦评论，输入框弹高
const commentFocus = () => {
    defMinHeight.value = "88px";
};
const commentCancel = () => {
    if (newComment.value === "") {
        defMinHeight.value = "auto";
        currentReplyTarget.value = {};
        commentBoxRef.value.blur();
    }
};
const cancelComment = () => {
    newComment.value = "";
    defMinHeight.value = "auto";
    currentReplyTarget.value = {};
    commentBoxRef.value.blur();
};
// ctrl + enter 组合键 发布评论
const keyDownHandlePost = (e) => {
    console.log(e.keyCode);
    if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
        submitComment();
        return;
    }
    if (e.keyCode === 8 || e.keyCode === 27) {
        commentCancel();
    }
};
//关闭页面逻辑
const router = useRouter();
const localePath = useLocalePath();
const handleClose = async () => {
    if (!history.state.back) {
        await navigateTo({ path: localePath("/community/explore") });
    } else {
        router.back();
    }
};

// 同步更新社区数据
const syncCommunityData = () => {
    const { id, fileLikeNums, liked, reported, fileCommentNums, fileFirstCommentNums, isDel } = currentPreview.value;
    const params = { id, fileLikeNums, liked, reported, fileCommentNums, fileFirstCommentNums, isDel };
    syncAction.publish("updateCommunityItem", params);
    if (isDel || reported) {
        shareData.deleteHideItem(id);
    }
};

//删除/举报 当前后 自动切换上下图片
const autoSwitchItem = () => {
    if (!disableSwitch.value(1)) {
        handleNext();
        return;
    }
    if (!disableSwitch.value(-1)) {
        handlePrevious();
        return;
    }
    //已经没有了 返回
    handleClose();
};

const checkMoreAction = async (e) => {
    if (e === "DELETE") {
        deleteCommunityImg();
        return;
    }
    if (e === "REPORT") {
        gtagController("report");
        const action = await useReportContent({ type: "GEN_CONTENT", commFileId: currentPreview.value.id });
        if (action !== "cancel") {
            currentPreview.value.reported = true;
            syncCommunityData();
            autoSwitchItem();
        }
        return;
    }
    if (e === "COPY_LINK") {
        commandDispense("copy_link");
        return;
    }
};

//事件分发，统一拦截
const commandDispense = (actionName, disabledAction = false) => {
    gtagController(actionName);
    if (disabledAction) {
        return false;
    }
    switch (actionName) {
        case "remix":
            recordCommunityScore(currentPreview.value.fileId, "remix");
            useRemix(currentPreview.value);
            break;
        case "copy_info":
            setCopyToClipboard();
            break;
        case "like":
            handleLike();
            break;
        case "copy_link":
            setCopyLink();
            break;
        case "replay":
            focusComment();
            break;
        default:
            break;
    }
};
//聚焦到评论
const focusComment = async () => {
    if (tab.value !== "comments") {
        tab.value = "comments";
        await nextTick();
    }
    commentBoxRef.value.focus();
};

// gtag 派发代理
const elNames = {
    show: "image_details_show",
    remix: "image_details_remix",
    share: "image_details_share",
    copy_info: "image_details_copy_info",
    like: "image_details_like",
    copy_link: "image_details_copy_link",
    replay: "image_details_comment",
    report: "image_details_report",
    follow: "image_details_follow",
    unfollow: "image_details_unfollow",
    post: "image_details_post",
    tab_details: "image_details_tab=details",
    tab_comments: "image_details_tab=comments",
};
//
const switchTabs = (val) => {
    gtagController(`tab_${val}`);
};
const gtagController = (key) => {
    if (import.meta.client) {
        window.trackEvent("Community", { el: elNames[key] || "未定义事件" });
    }
};
//change -like
const likeLoading = ref(false);
const handleLike = async () => {
    const { id, fileLikeNums, liked } = currentPreview.value;
    if (likeLoading.value) {
        return;
    }
    likeLoading.value = true;
    let callback = addLikeByFileId;
    let step = 1;
    if (liked) {
        callback = reduceLikeByFileId;
        step = -1;
    }
    const newLikeCount = Math.max(0, fileLikeNums + step);
    try {
        currentPreview.value.fileLikeNums = newLikeCount;
        currentPreview.value.liked = !liked;
        const { status, message } = await callback({ commFileId: id });
        likeLoading.value = false;
        if (status !== 0) {
            currentPreview.value.fileLikeNums = fileLikeNums;
            currentPreview.value.liked = liked;
            openToast.error(message);
            return;
        }

        syncCommunityData();
    } catch (error) {
        likeLoading.value = false;
    }
};
//全参数复制
const setCopyToClipboard = () => {
    const task = currentPreview.value;
    const modelName = useGetModelInfo(task.model_id)?.label;
    let str = `${task.prompt || ""} --piclumen -rs ${task.realWidth}:${task.realHeight} -np "${task.negative_prompt || ""}"`;
    if (task.cfg) {
        str += ` -g ${task.cfg}`;
    }
    if (task.steps) {
        str += ` -s ${task.steps}`;
    }
    if (task.resolution?.batch_size) {
        str += ` -bs ${task.resolution.batch_size}`;
    }
    if (task.seed) {
        str += ` -e ${task.seed}`;
    }
    if (task.originCreate) {
        str += ` -st "${task.originCreate}"`;
    }
    if (modelName) {
        str += ` -mn "${modelName}"`;
    }
    copyToClipboard(str);
    openToast.success(t("TOAST_COPY_SUCCESS"));
};
//复制分享链接
const setCopyLink = () => {
    const { id } = currentPreview.value;
    let href = window.location.host + `/app${localePath("/community/share/" + id)}`;
    copyToClipboard(href);
    openToast.success(t("TOAST_COPY_SUCCESS"));
};
//复制提示词语
const copyPromptText = (text) => {
    copyToClipboard(text);
    openToast.success(t("TOAST_COPY_SUCCESS"));
};

//关注/取关 指定用户
const handleUpdateFollow = async () => {
    const isFollowed = currentPreview.value.followed;
    gtagController(isFollowed ? "unfollow" : "follow");
    if (followLoading.value) {
        return;
    }
    followLoading.value = true;
    let updateFollowStatus = followUserById;
    if (isFollowed) {
        updateFollowStatus = unfollowUserById;
    }
    const { status, message } = await updateFollowStatus({ userId: currentPreview.value.dataUserId });
    followLoading.value = false;
    if (status === 0) {
        currentPreview.value.followed = !isFollowed;
        return;
    }
    openToast.error(message);
};
//删除自己公开的图片
const deleteCommunityImg = () => {
    showMessage({
        style: { width: "480px" },
        confirmBtn: t("COMMON_BTN_CONTINUE"),
        content: h("div", null, [h("p", null, t("SHORT_DEL_MESSAGE"))]),
        icon: h(NIcon, { size: 48, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_ATTEN"),
    })
        .then(async () => {
            const commFileId = currentPreview.value.id;
            const { status, message } = await delCommunityImgById({ commFileId });
            if (status !== 0) {
                openToast.error(message);
                return;
            }
            currentPreview.value.isDel = true;
            syncCommunityData();
            autoSwitchItem();
        })
        .catch(() => {});
};

const shareData = useShareDataStore();

//是否允许切换上一张/下一张
const disableSwitch = computed(() => {
    return (step = 1) => {
        const currentIndex = shareData.targetItemIndex(currentPreview.value.id);
        const maxIndex = shareData.maxIndex;
        const targetIndex = currentIndex + step;
        return targetIndex === -1 || targetIndex >= maxIndex;
    };
});
//上下图片切换
const handlePrevious = () => {
    const nextItem = shareData.getPreviousOrNextId(currentPreview.value.id, "id", false);
    actrivityInfo.value = undefined; // 清空活动信息
    nextItem && switchShareDataItem(nextItem);
};
const handleNext = () => {
    const nextItem = shareData.getPreviousOrNextId(currentPreview.value.id, "id", true);
    actrivityInfo.value = undefined; // 清空活动信息
    nextItem && switchShareDataItem(nextItem);
};

const switchShareDataItem = async (nextItem) => {
    //预先使用本地数据提高性能
    updateCurrent(nextItem);
    const current = localePath(`/community/detail/${nextItem.id}`);
    await navigateTo(current, { replace: true });
};

const updateCurrent = (detail) => {
    try {
        if (!detail.id) {
            dataException.value = true;
            return; // 没有 ID 时提前返回
        }

        // 修复 JSON 解析错误
        const info = removeNulls(JSON.parse(detail.genInfo));
        const { resolution = {} } = info;
        const { prompt } = formatPonyV6Prompt(info, "output");

        currentPreview.value = {
            ...info,
            ...detail,
            prompt,
            width: resolution.width,
            height: resolution.height,
            dataUserId: detail.accountInfo.userId,
            showPrompt: user.userId == detail.accountInfo.userId || detail.publicType !== "myself",
            bgUrl: detail.highMiniUrl || detail.thumbnailUrl,
        };

        useSeoMeta({
            title: () => t("SEO_META.SEO_COMMUNITY_DETAIL_TITLE", { Username: currentPreview.value?.accountInfo?.userName }),
            ogTitle: () => t("SEO_META.SEO_COMMUNITY_DETAIL_TITLE", { Username: currentPreview.value?.accountInfo?.userName }),
            description: () => t("SEO_META.SEO_COMMUNITY_DETAIL_DESC"),
            ogDescription: () => t("SEO_META.SEO_COMMUNITY_DETAIL_DESC"),
        });

        initCurrentStatus();
    } catch (error) {
        console.error("updateCurrent 处理出错:", error);
        dataException.value = true; // 发生错误时标记数据异常
    }
};

const planLevel = ref("");
const userId = computed(() => {
    return currentPreview.value?.accountInfo?.userId;
});
const userIdWatch = watch(userId, async (newestUserId) => {
    // 在此处单独取一次用户信息 由于后端没有额外给接口，在此处采集到UserId变化，更新最新会员信息
    if (newestUserId) {
        const { status, data, message } = await getCommunityPersonalById({ userId: newestUserId });
        const newestAccountInfo = status === 0 ? data?.accountInfo : { planLevel: "" };
        planLevel.value = newestAccountInfo.planLevel;
    }
});

const activityId = computed(() => {
    return currentPreview.value?.activityId;
});

// 获取活动信息
const activityWatch = watch(activityId, async (newestActivityId) => {
    console.log("获取活动信息", newestActivityId);
    if (newestActivityId) {
        const res = await getActivityDetailApi({ activityId: newestActivityId });
        if (res.status === 0) {
            actrivityInfo.value = res.data;
        }
    }
});

onUnmounted(() => {
    userIdWatch();
    activityWatch();
});

onMounted(() => {
    if (route.query.comment) {
        tab.value = "comments";
    }
    const currentItem = shareData.getCurrentDetailById(route.params.id);
    if (currentItem) {
        updateCurrent(currentItem);
    }
    getDetail(route.params.id);
});

//注册键盘事件，处理esc/↑↓键 兼容预览大图时弹窗中的事件
useShortcutKey({
    onEsc: () => {
        if (isPreviewMode.value) {
            isPreviewMode.value = false;
            return;
        }
        handleClose();
    },
    onUp: () => {
        if (isPreviewMode.value) {
            return;
        }
        handlePrevious();
    },
    onDown: () => {
        if (isPreviewMode.value) {
            return;
        }
        handleNext();
    },
});
</script>

<style lang="scss" scoped>
:global(.explore-more-dropdown) {
    @apply min-w-40 !important;
    padding: 8px !important;
}

:global(.explore-more-dropdown .n-dropdown-option-body) {
    @apply px-0;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body::before) {
    left: 0 !important;
    right: 0 !important;
}
:global(.n-dropdown-menu .n-dropdown-option .n-dropdown-option-body:not(.n-dropdown-option-body--disabled).n-dropdown-option-body--pending.danger-bg::before) {
    background-color: var(--p-fill-drop-5);
}

// ========= 活动动效 =========

/* 翻转容器 - 提供3D空间 */
.flip-container {
    /* 确保动画在元素出现时触发 */
    animation: triggerFlip 0.1s forwards;
    perspective: 1000px; /* 创建3D空间感 */
    opacity: 0; /* 初始不可见 */
}

/* 翻转内容 - 实际执行翻转的元素 */
.flip-content {
    display: flex;
    gap: 2px;
    transform-style: preserve-3d; /* 保持3D变换 */
    transform-origin: bottom center; /* 从底部中心开始翻转 */
    transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform: rotateX(90deg); /* 初始状态：底部朝上 */
}

/* 触发翻转动画的关键帧 */
@keyframes triggerFlip {
    to {
        opacity: 1; /* 变得可见 */
    }
}

/* 当容器变得可见时，触发内容翻转 */
.flip-container:not(.opacity-0) .flip-content {
    transform: rotateX(0deg); /* 最终状态：正面朝前 */
}
</style>
