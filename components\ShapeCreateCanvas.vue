<template>
    <div
        class="absolute top-0 left-0 right-0 bottom-0 z-50 cursor-none"
        ref="shapeCanvasRef"
        @mousedown.stop="($event) => startPoint($event)"
        @mousemove="($event) => updateMousePosition($event)"
        @mouseleave="setMousePosition()"
        @contextmenu.stop.prevent="close()"
        @touchstart.stop.prevent="($event) => startPoint($event)"
        @touchmove.stop.prevent="($event) => updateMousePosition($event)"
        @touchend.stop.prevent="close()"
        @touchcancel.stop.prevent="close()"
    >
        <svg overflow="visible" class="overflow-visible w-full h-full custom-cursor opacity-65" :class="{ '!cursor-none': !isLasso }">
            <path :d="path" :stroke="isEraser ? 'rgba(247, 105, 101, 1)' : 'rgba(123, 87, 229, 1)'" :fill="fillStyle" :stroke-width="pencilWidth" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>

        <div
            v-if="type === 'pencil'"
            class="absolute rounded-full border-2 border-solid border-white bg-primary-6 opacity-65 z-50 -translate-x-1/2 -translate-y-1/2"
            :class="{ '!bg-danger-4': isEraser }"
            :style="{ width: `${pencilWidth}px`, height: `${pencilWidth}px`, top: mousePoint.y, left: mousePoint.x }"
        ></div>
    </div>
</template>

<script setup>
const props = defineProps({
    type: {
        type: String,
        default: "lasso",
    },
    isLasso: {
        type: Boolean,
        default: true,
    },
    isEraser: {
        type: Boolean,
        default: true,
    },
    pencilWidth: {
        type: Number,
        default: 30,
    },
});
const shapeCanvasRef = ref();
const isMouseDown = ref(false);
const offset = ref({ x: 0, y: 0 });
const mousePoint = ref({
    x: "50%",
    y: "50%",
});
watch(
    () => props.pencilWidth,
    () => {
        setMousePosition(true);
    }
);
let canvas = null;
const fillStyle = computed(() => {
    if (!props.isLasso) {
        return "none";
    }
    if (props.isEraser) {
        return "rgba(247, 105, 101, 1)";
    }
    return "rgba(123, 87, 229, 1)";
});
const setCanvas = (easeCanvas) => {
    canvas = easeCanvas;
};
const emits = defineEmits(["createShape"]);
onMounted(() => {
    document.addEventListener("mouseup", close);
    document.addEventListener("touchend", close);
    document.addEventListener("touchcancel", close);
    if (!shapeCanvasRef.value) return;
    const { x, y } = shapeCanvasRef.value.getBoundingClientRect();
    offset.value = { x, y };
});
onBeforeUnmount(() => {
    document.removeEventListener("mouseup", close);
    document.removeEventListener("touchend", close);
    document.removeEventListener("touchcancel", close);
    canvas = null;
});
const mousePosition = ref(null);
const points = ref([]);
const absolutePoint = [];
const close = () => {
    let d = "";
    for (let i = 0; i < absolutePoint.length; i++) {
        const point = absolutePoint[i];
        if (i === 0) d += `M ${point.x} ${point.y} `;
        else d += `L ${point.x} ${point.y} `;
    }
    if (d && props.isLasso) {
        d += "Z";
    }
    if (d) {
        emits("createShape", d);
    }
    isMouseDown.value = false;
    points.value.length = 0;
    absolutePoint.length = 0;
};
//记录相对位置
const updateAbsolutePoint = (e) => {
    if (!canvas) return;
    const { x, y } = canvas.getScenePoint(e);
    absolutePoint.push({ x, y });
};

/**
 * 支持鼠标和触摸事件
 */
const getPoint = (e, custom = false) => {
    let pageX, pageY;
    if (e.touches && e.touches.length) {
        pageX = e.touches[0].pageX - offset.value.x;
        pageY = e.touches[0].pageY - offset.value.y;
    } else if (e.changedTouches && e.changedTouches.length) {
        pageX = e.changedTouches[0].pageX - offset.value.x;
        pageY = e.changedTouches[0].pageY - offset.value.y;
    } else {
        pageX = e.pageX - offset.value.x;
        pageY = e.pageY - offset.value.y;
    }
    mousePoint.value = {
        x: `${pageX}px`,
        y: `${pageY}px`,
    };
    if (custom) {
        updateAbsolutePoint(e);
        return { pageX, pageY };
    }
    if (points.value.length) {
        const [lastPointX, lastPointY] = points.value[points.value.length - 1];
        if (Math.abs(lastPointX - pageX) - Math.abs(lastPointY - pageY) > 0) {
            pageY = lastPointY;
        } else pageX = lastPointX;
    }
    return { pageX, pageY };
};

const startPoint = (e) => {
    isMouseDown.value = true;
    absolutePoint.length = 0;
    const { pageX, pageY } = getPoint(e, true);
    points.value.push([pageX, pageY]);
};
const updateMousePosition = (e) => {
    const { pageX, pageY } = getPoint(e, isMouseDown.value);
    if (isMouseDown.value) {
        points.value.push([pageX, pageY]);
        mousePosition.value = null;
        return;
    }
};
const setMousePosition = (isReset = false) => {
    mousePoint.value = {
        x: isReset ? "50%" : `-100%`,
        y: `50%`,
    };
};
const path = computed(() => {
    let d = "";
    for (let i = 0; i < points.value.length; i++) {
        const point = points.value[i];
        if (i === 0) d += `M ${point[0]} ${point[1]} `;
        else d += `L ${point[0]} ${point[1]} `;
    }
    if (points.value.length && mousePosition.value) {
        d += `L ${mousePosition.value[0]} ${mousePosition.value[1]} `;
    }
    if (d && props.isLasso) {
        return (d += "Z");
    }
    return d;
});
onBeforeUnmount(() => {
    canvas = null;
});
defineExpose({ setCanvas });
</script>

<style lang="scss" scoped>
.custom-cursor {
    cursor: url("@/assets/images/custom_cursor.svg"), auto;
}
</style>
