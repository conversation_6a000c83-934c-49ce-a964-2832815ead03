<template>
    <h2 class="text-base font-medium text-text-2" v-if="[1, 2].includes(item.messType)">{{ item.title }}</h2>
    <pre class="preview-area" v-if="item.details && item.messType < 3">{{ item.details }}</pre>
    <div class="flex flex-col" v-else>
        <!-- 会员等级变化 -->
        <template v-if="item.messType === 3">
            <div class="preview-area">
                {{
                    `You’re All Set, [${
                        item.accountInfo.userName
                    }]! 🎉\nThanks for subscribing to the [${getUpperCaseLevel()} ${getUpperCaseInternal()}]. \n\nYour membership has been activated successfully.\n👉 Plan: ${getUpperCaseLevel()} \n👉 Billing Cycle: ${getUpperCaseInternal()}ly\n👉 Next Charge:  ${getNextChargeTime()}`
                }}
                <p class="mt-4">
                    Ready to start creating? Enjoy all your benefits — and if you have any questions, feel free to
                    <a href="mailto:<EMAIL>" class="text-primary-6 underline">contact</a> our support team.
                </p>
            </div>
        </template>
        <!-- 活动获奖 -->
        <template v-if="item.messType === 4">
            <p class="preview-area">
                Congratulations! You've won [{{ item.commActivityLevelName || "" }}] in [{{ item.commActivityTitle || "" }}]. Your prize will be automatically added to your account.
            </p>
            <img :src="item.miniThumbnailUrl" alt="" class="w-[120px] xl:w-[240px] mt-4" />
        </template>
        <!-- 会员即将到期 -->
        <template v-if="item.messType === 5">
            <div class="preview-area">
                {{
                    `Hey [${
                        item.accountInfo.userName
                    }],\n\nJust a quick reminder — your [${getUpperCaseLevel()} ${getUpperCaseInternal()}] membership ends on [${getNextChargeTime()}].\n\nWith it, you've been enjoying:\n${
                        item.details
                    }\n\nDon't lose access to your favorite features. \n\n`
                }}
            </div>
            <NuxtLinkLocale to="/user/subscribe" @click="handleClose()" class="text-primary-6 underline cursor-pointer block">Renew my membership.</NuxtLinkLocale>
        </template>
        <!-- 会员已经到期 -->
        <template v-if="item.messType === 6">
            <div class="preview-area">
                {{
                    `Hi [${item.accountInfo.userName}],\n\n Your PicLumen membership has ended — but you can renew anytime and get back to full access.\nDon't miss out on your favorite tools and benefits.`
                }}
            </div>
            <NuxtLinkLocale to="/user/subscribe" @click="handleClose()" class="text-primary-6 underline cursor-pointer mt-6 block">Renew my membership.</NuxtLinkLocale>
        </template>
        <!-- 试订阅结束 -->
        <template v-if="item.messType === 7">
            <NuxtLinkLocale to="/user/subscribe" class="preview-area block" @click="handleClose()">
                {{ `Continue with PicLumen [${getUpperCaseLevel()} ${getUpperCaseInternal()}] automatically, or make changes anytime before then.` }}
                <p>Tap to <span class="text-primary-6 underline cursor-pointer">review </span>your plan or update your preferences.</p>
            </NuxtLinkLocale>
        </template>
        <!-- 扣款失败 -->
        <template v-if="item.messType === 8">
            <div class="preview-area">
                {{
                    `We weren’t able to complete the payment for your [${getUpperCaseLevel()} ${getUpperCaseInternal()}], so your membership is temporarily on hold.\n\nNo worries — here’s how to reactivate:`
                }}
                <NuxtLinkLocale to="/user/subscribe" class="block" @click="handleClose()"
                    >👉 You can <span class="text-primary-6 cursor-pointer underline">repurchase</span> your plan with another payment method</NuxtLinkLocale
                >
                <p>👉 Or check with your bank/card issue</p>
                <p class="mt-5">As soon as the payment goes through, full access will be restored instantly.</p>
                <p><a href="mailto:<EMAIL>" class="text-primary-6 underline">Need help?</a> Our Support Team is here for you.</p>
            </div>
        </template>
    </div>
</template>
<script setup>
import { formatDateIntl, getLocalDateFromEast8Time } from "@/utils/timeFormat";
const router = useRouter();
const emit = defineEmits(["close"]);
const props = defineProps({
    item: Object,
});

const handleClose = () => {
    emit("close");
};

const getNextChargeTime = () => {
    const { vipEndTime } = props.item;
    if (!vipEndTime) return "";
    const date = new Date(vipEndTime * 1000); // JS 里时间戳是毫秒
    const dateStr = date.toISOString().split(".")[0]; // 去掉毫秒和Z
    const newStr = getLocalDateFromEast8Time(dateStr);
    return formatDateIntl(newStr);
};
const getUpperCaseLevel = () => {
    const { planLevel } = props.item;
    if (!planLevel) return "";
    return planLevel === "pro" ? "Pro" : "Standard";
};
const getUpperCaseInternal = () => {
    const { priceInterval } = props.item;
    if (!priceInterval) return "";
    return priceInterval === "month" ? "Month" : "Year";
};
</script>
<style scoped lang="scss">
.preview-area {
    @apply text-text-3 whitespace-pre-wrap leading-[22px];
}
</style>
