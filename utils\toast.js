// utils/toast.js

/**
 * 这是暴露给整个应用的全局 Toast API 对象。
 * 初始状态下，这些方法是空壳。
 * Nuxt 插件会在应用启动时，将真正的实现注入进来。
 */
export const openToast = {
  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {number} [duration=3000] - 显示时长 (ms)
   */
  success(message, duration) {
    console.warn("[Toast] Success method called before initialization.");
  },

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {number} [duration=3000] - 显示时长 (ms)
   */
  error(message, duration) {
    console.warn("[Toast] Error method called before initialization.");
  },

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {number} [duration=3000] - 显示时长 (ms)
   */
  warning(message, duration) {
    console.warn("[Toast] Warning method called before initialization.");
  },

  /**
   * 显示普通消息
   * @param {string} message - 消息内容
   * @param {number} [duration=3000] - 显示时长 (ms)
   */
  info(message, duration) {
    console.warn("[Toast] Info method called before initialization.");
  },
};
