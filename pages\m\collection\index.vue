<!--
 * @Author: HuangQS
 * @Description: 收藏夹 临时恢复到以前版本，等待新版本Gallery迭代完成后替换
 * @Date: 2025-06-20 17:08:19
 * @LastEditors: <PERSON><PERSON><PERSON> huang<PERSON>ush<PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-07-31 14:48:17
-->
<template>
    <div class="flex flex-col w-full h-full">
        <!-- <H5CollcetionList :currentCollect="currentCollect" v-model:showList="showList" v-if="showList" /> -->
        <div class="dark:text-dark-text p-2 relative flex-1 dark:bg-black overflow-auto pb-20">
            <div class="h-11 p-3 flex items-center justify-between sticky top-0">
                <div class="dark:text-dark-active-text w-9 h-9 rounded-full dark:bg-black/70 bg-white/70 flex items-center justify-center" @click="showEditCollection = true">
                    <n-icon size="18">
                        <IconsAdd />
                    </n-icon>
                </div>
                <div class="dark:text-dark-active-text w-9 h-9 rounded-full dark:bg-black/70 bg-white/70 flex items-center justify-center" @click="handleViewStorage">
                    <n-icon size="18">
                        <IconsCloud />
                    </n-icon>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-2 mt-2">
                <div class="dark:bg-dark-bg bg-white rounded-lg overflow-hidden" v-for="item in shareCollect.collectList" :key="item.id">
                    <NuxtLinkLocale :to="`/m/collection/${item.id}`" class="w-full aspect-square dark:bg-dark-bg-3 bg-neutral-100 rounded-lg overflow-hidden flex items-center justify-center">
                        <img v-if="item.total > 0 && item.cover" :src="item.cover" class="w-full h-full object-cover" alt="" />
                        <IconsVectorLogo class="w-1/3 h-1/3 opacity-30" v-else-if="item.total > 0 && !item.cover" />
                        <img
                            v-else
                            class="w-1/3 h-1/3"
                            src="data:image/svg+xml;base64,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"
                            alt=""
                        />
                    </NuxtLinkLocale>
                    <div class="p-3 flex items-center justify-between gap-4">
                        <div class="font-medium text-ellipsis overflow-hidden whitespace-nowrap">{{ item.collectName }}</div>
                        <div class="text-xs h-6 flex items-center justify-center px-2 shrink-0 dark:bg-dark-bg-3 bg-neutral-100 rounded-3xl">
                            <span class="opacity-30">{{ item.total }} P</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <EditCollectionDrawer v-model:show="showEditCollection" @change="queryCollectionList" />

        <n-drawer v-model:show="showUseStorage" height="70vh" class="!rounded-t-2xl dark:!bg-dark-bg dark:text-dark-text" placement="bottom">
            <n-drawer-content>
                <template #header>
                    <div class="flex items-center justify-between font-medium text-base">
                        <n-icon size="24" @click="showUseStorage = false">
                            <IconsClose />
                        </n-icon>
                        <span>{{ $t("COLLECTION_STORAGE") }}</span>
                        <span></span>
                    </div>
                </template>

                <div class="rounded-lg dark:bg-dark-bg-3 bg-neutral-100 p-4">
                    <div class="flex items-center justify-between gap-2">
                        <div class="flex items-center fiex-1 gap-2">
                            <n-icon size="20">
                                <IconsCloud />
                            </n-icon>
                            <div>{{ $t("MENU_COLLECTION") }}</div>
                        </div>

                        <VipLevel :plan="vipPlan" />
                    </div>
                    <div class="mt-4 h-1 rounded-full bg-neutral-200 dark:bg-dark-bg-2">
                        <div class="h-full rounded-full bg-primary" :style="{ width: Math.min(100, (computedTotalCount / maxTotal) * 100) + '%' }"></div>
                    </div>

                    <div class="mt-4 text-xs">{{ computedTotalCount }} P / {{ maxTotal }} P</div>
                </div>
            </n-drawer-content>
        </n-drawer>
    </div>
</template>

<script setup>
import { useShareCollect } from "@/stores/index";
import { useSubscribeStore } from "@/stores/subscribe";

import { getCollect } from "@/api";
import EditCollectionDrawer from "@/components/collections/EditCollectionDrawer.vue";

const subscribeStore = useSubscribeStore();
const shareCollect = useShareCollect();
const router = useRouter();

const computedTotalCount = computed(() => {
    const totalSum = shareCollect.collectList.reduce((accumulator, currentValue) => {
        return accumulator + Number(currentValue.total || 0);
    }, 0);
    return totalSum;
});
const maxTotal = computed(() => {
    return subscribeStore.currVipPermission?.collectNum || 500;
});

const vipPlan = computed(() => {
    return subscribeStore?.vipInfo?.plan ?? "";
});

const showEditCollection = ref(false);
const showUseStorage = ref(false);
// const collectionList = ref([]);
// const dataMap = new Map();
//查询收藏夹列表
const queryCollectionList = async () => {
    let { status, data = [], message } = await getCollect();
    if (status !== 0) {
        openToast.error(message);
        return false;
    }
    const list = data.map((item) => {
        return {
            ...item,
            imgList: [],
            pageNum: 0,
            pageSize: 100,
            maxPage: Math.ceil(item.collectNums / 100) || 0,
            total: item.collectNums,
        };
    });
    const oldDatas = new Map();
    shareCollect.collectList.forEach((item) => {
        oldDatas.set(item.id, item);
    });
    list.forEach((item) => {
        if (oldDatas.has(item.id)) {
            Object.assign(item, oldDatas.get(item.id));
        }
    });
    shareCollect.setCollectList(list);
};
queryCollectionList();

//查看储存用量
const handleViewStorage = () => {
    showEditCollection.value = false;
    showUseStorage.value = true;
};
</script>
