<template>
    <div class="w-full p-6 pt-4 text-sm font-medium" :class="{ 'opacity-0 pointer-events-none': onlyOneChannel }">
        <div class="flex items-center justify-between text-base font-semibold text-text-1">
            <span>{{ t("SUBSCRIBE_PAYMENT_METHOD") }}</span>
            <div class="translate-x-2 w-8 h-8 rounded-full text-text-4 hover:text-text-2 flex items-center justify-center hover:bg-fill-wd-1 cursor-pointer" @click="handleClose">
                <n-icon size="20">
                    <IconsClose />
                </n-icon>
            </div>
        </div>

        <div class="mt-8 grid md:grid-cols-2 gap-4">
            <div
                v-if="showPaymentChannels(PAYMENT_METHOD.STRIPE)"
                class="relative px-4 py-3 bg-fill-wd-1 hover:bg-fill-wd-2 transition-colors rounded-xl flex items-center gap-2 cursor-pointer text-text-2 hover:text-text-1"
                @click="paymentMethod = PAYMENT_METHOD.STRIPE"
            >
                <SaleBadge v-if="allowFreeTry" class="sale-bar-style text-xs !h-5 !font-medium">
                    <span>{{ t("SUB_PLAN_3_DAY") }}</span>
                </SaleBadge>
                <IconsStripe class="shrink-0 w-8 h-8 text-[#6772E5]" />

                <div class="flex-1">Stripe</div>

                <div class="w-8 h-8 rounded-full flex items-center justify-center border-[6px] border-none border-fill-wd-1 shrink-0 hover:border-solid">
                    <div class="border-2 border-solid border-border-3 rounded-full w-5 h-5 flex items-center justify-center" :class="{ '!border-primary-6': paymentMethod === PAYMENT_METHOD.STRIPE }">
                        <div class="w-2.5 h-2.5 rounded-full bg-primary-6" :class="{ hidden: paymentMethod !== PAYMENT_METHOD.STRIPE }"></div>
                    </div>
                </div>
            </div>
            <div
                v-if="showPaymentChannels(PAYMENT_METHOD.PAYPAL)"
                class="px-4 py-3 bg-fill-wd-1 hover:bg-fill-wd-2 transition-colors rounded-xl flex items-center gap-2 cursor-pointer text-text-2 hover:text-text-1"
                @click="paymentMethod = PAYMENT_METHOD.PAYPAL"
            >
                <IconsPaypal class="shrink-0 w-8 h-8" />
                <div class="flex-1">Paypal</div>

                <div class="w-8 h-8 rounded-full flex items-center justify-center border-[6px] border-none border-fill-wd-1 shrink-0 hover:border-solid">
                    <div class="border-2 border-solid border-border-3 rounded-full w-5 h-5 flex items-center justify-center" :class="{ '!border-primary-6': paymentMethod === PAYMENT_METHOD.PAYPAL }">
                        <div class="w-2.5 h-2.5 rounded-full bg-primary-6" :class="{ hidden: paymentMethod !== PAYMENT_METHOD.PAYPAL }"></div>
                    </div>
                </div>
            </div>
        </div>

        <Button class="mt-10 ml-auto min-w-32 max-md:w-full" @click="createdPaymentLink" :loading="subscribeLoading">
            <span>{{ t("SHORT_CONFIRM_BTN") }}</span>
        </Button>
    </div>
</template>

<script setup>
import { reqBiSavePayLog } from "@/api";
import { useCallSubscribePlan } from "@/hook/subscribe.js";
import { PAYMENT_METHOD } from "@/utils/constant.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { t } from "@/utils/i18n-util";
const subscribeLoading = ref(false);
const paymentMethod = ref(PAYMENT_METHOD.STRIPE);
const emits = defineEmits(["confirm", "cancel"]);
const props = defineProps({
    source: {
        type: String,
        default: "",
    },
    priceType: {
        type: String,
        default: "",
    },
    params: {
        type: Object,
        default: () => ({}),
    },
    price: {
        // 产品单价
        type: [String, Number],
        default: "",
    },
    allowFreeTry: {
        //是否允许3天试用
        type: Boolean,
        default: false,
    },
});
const { supportPaymentChannels } = useSubscribeStore();

const showPaymentChannels = computed(() => {
    return (payment) => supportPaymentChannels.includes(payment);
});
const onlyOneChannel = computed(() => supportPaymentChannels.length === 1);
//关闭弹窗
const handleClose = () => {
    emits("cancel", "cancel");
};
//生成支付链接
const createdPaymentLink = async () => {
    if (subscribeLoading.value) {
        return;
    }
    subscribeLoading.value = true;
    const { source, priceType } = props;
    reqBiSavePayLog({ source, priceType, paymentChannel: paymentMethod.value }).catch((err) => {
        console.error(err.message);
    });
    const params = {
        ...props.params,
        paymentMethod: paymentMethod.value,
    };
    try {
        const res = await useCallSubscribePlan(params);
        emits("confirm", res);
    } catch (error) {
        emits("confirm", error);
    } finally {
        subscribeLoading.value = false;
    }
};
// createdPaymentLink();
onMounted(() => {
    paymentMethod.value = supportPaymentChannels[0];
    // 如果有默认的支付方式，直接选择
    if (onlyOneChannel.value) {
        createdPaymentLink();
    }
});
</script>
<style lang="scss" scoped>
.sale-bar-style {
    background: linear-gradient(270deg, #ff3700 0%, #ff9417 99.83%);
    border-radius: 2px 16px;
}
</style>
