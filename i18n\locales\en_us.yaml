NULL_CONTENT: ""
TOAST_NOT_SIGN_IN: Please sign in and try again.
TOAST_TOKEN_EXPIRED: "Identity authentication has expired, please log in again."
TOAST_ERR_NETWORK: "Network error, please try again later."
TOAST_GEN_COMPLETE: "Generation Completed!"
TOAST_COPY_SUCCESS: "Copied successfully!"
TOAST_TASK_LIMIT: Processing your image. Please wait to submit new tasks.
TOAST_REMIX_FAILED: "Remix failed! The corresponding model has been removed."
TOAST_MODEL_DEL: The corresponding model has been removed.
TOAST_DOWNLOAD: Please wait as the download is being processed by your browser.
TOAST_RESET_PASSWORD: "Password reset succeeded, please sign in again."
TOAST_RESET_PASSWORD_SUCCESS: "Password reset succeeded!"
TOAST_DEL_CONFIRM_CONTENT: The deleted image will be impossible to recover.
TOAST_TITLE_WARNING: Alert
TOAST_TITLE_ERROR: Error
NO_FACE_ERROR_CONTENT: "No faces detected in the reference image, please use another image and try again."
TOAST_MJ_PROMPT_EMPTY_ERROR: Please enter the prompt first.
TOAST_MJ_PROMPT_CHECK_ERROR: "Please check your prompt (e.g., special characters, length, or NSFW content) and try again."
TOAST_MODEL_HIGH_TRAFFIC_ERROR: The model is currently experiencing high traffic. Please try again later.
TOAST_KONTEXT_TASK_API_FAILED_ERROR: This image processing is failed. Please try again later.
TOAST_KONTEXT_TASK_PROMPT_EMPTY_ERROR: prompt is required.
TOAST_KONTEXT_NSFW_ERROR: Inappropriate content detected. Please try other prompts or images.
TOAST_KONTEXT_COPYRIGHT_ERROR: Copyright content detected. Please try other prompts or images.
TOAST_GEN_ERROR_CONTENT: There was an issue during the image generating. Please try again later.
MENU_EXPLORE: Explore
MENU_CREATE: Create
MENU_TUTORIAL: Tutorial
MENU_COMMUNITY: Community
MENU_SUBSCRIBE: Subscribe
MENU_PROFILE: Profile
MENU_LOGOUT: "Logout?"
MENU_JOB_STATUS_QUEUEING: Queued
MENU_JOB_STATUS_GENERATING: Generating
MENU_JOB_STATUS_BATCH_SIZE: 1 image
MENU_JOB_STATUS_BATCH_SIZES: "{batch_size} images"
COMMON_PROMPT: Prompt
COMMON_BTN_GEN: Generate
COMMON_BTN_GENERATING: Generating...
COMMON_SEARCH_PLACEHOLDER: Search...
COMMON_SORT_EXPLORE_TITLE: Get inspired by the community
COMMON_SORT_HOT: Hot
COMMON_SORT_FEATURED: Featured
COMMUNITY_POST: Post
COMMUNITY_UN_FOLLOW_BTN: Unfollow
COMMUNITY_UN_FOLLOW_TIPS: "Do you want to unfollow {target}?"
COMMUNITY_FOLLOW_BTN: Follow
COMMUNITY_AUTHOR_HIDE_PROMPT: The author has hidden the prompts.
COMMUNITY_PUBLISHED: Posted
COMMUNITY_PUBLISH: Post to Community
COMMUNITY_PUBLISH_EMPTY: No current activities available
COMMUNITY_PUBLISH_PLACEHOLDER: Set a caption for your image...
SELECT_PLACEHOLDER: Please select...
COMMUNITY_PUBLISH_TIPS: "Once you post your images to the community, they will be permanently public."
COMMUNITY_SHOW_PROMPT_TITLE: Show Prompts
COMMUNITY_PUBLISH_PROMPT_EVERYONE: Visible to everyone
COMMUNITY_PUBLISH_PROMPT_SELF: Visible only to yourself
COMMUNITY_VIEW_REPLIES: "View {num} replies"
COMMUNITY_DEL_COMMENT: "Do you want to delete this comment?"
NO_COMMENTS: No one has commented yet.
COMMUNITY_COMMENT: Type your comment...
COMMUNITY_FOLLOWING: Following
COMMUNITY_EDIT_BTN: Edit
COMMUNITY_MY_IMAGES: My images
COMMUNITY_MY_FOLLOWING: My following
COMMUNITY_OLD_PUBLISH_DISABLED_TIPS: The generated image is private by default and will only be made public when you post it to the community.
COMMUNITY_POPULAR: Popular
COMMON_BTN_CANCEL: Cancel
COMMON_BTN_SAVE: Save
COMMON_BTN_CONTINUE: Continue
COMMON_BTN_OK: OK
CONFIG_BASE_TITLE: Settings
CONFIG_BASE_MODEL: Model
CONFIG_BASE_MODEL_TIPS: Choose a specific model that has been fine-tuned for different styles or effects.
CONFIG_REFER_TIPS: "The higher the value, the more reference effect."
CONFIG_BASE_STRENGTH: Strength
CONFIG_BASE_SUBMIT_BTN: Submit
CONFIG_BASE_RESET_BTN: Reset
CONFIG_BASE_ANIME_EFFECT: Anime effect
CONFIG_BASE_ANIME_EFFECT_TIPS: Adjust the intensity of the anime-style effect on the generated image.
CONFIG_BASE_ANIME_EFFECT_MESSAGE: Higher the effect stronger
CONFIG_BASE_ASPECT_RATIO: Aspect Ratio
CONFIG_BASE_RESOLUTION: Resolution
H5_BTN_MORE: More
CONFIG_BASE_ASPECT_RATIO_2X: 2x Upscale
CONFIG_BASE_ASPECT_RATIO_2X_TIPS: "By default, the image will be upscale by 2x"
CONFIG_BASE_BATCH_SIZE: Images
CONFIG_BASE_BATCH_SIZE_TIPS: The number of images needed to be generated in one batch
CONFIG_BASE_GEN_SIZE_TIPS: The aspect ratio of the image you want to generate
CONFIG_BASE_NEGATIVE_PROMPT: Negative Prompt
ENTER_ENTER_CONFIG_BASE_NEGATIVE_PROMPT: Please Enter Negative Prompt
CONFIG_BASE_NEGATIVE_PROMPT_TIPS: "Specify what you don't want in the generated image"
CONFIG_ADVANCE_TITLE: Advanced settings
CONFIG_ADVANCE_TITLE_TIPS: "Allows fine control of image generation, but incorrect images may occur."
CONFIG_ADVANCE_CFG_TIPS: Control how strongly your prompt influences the generated image.
CONFIG_ADVANCE_CFG_MESSAGE: Higher the effect stronger
CONFIG_ADVANCE_STEPS_TIPS: Determine the number of steps used to create the image; more steps can lead to more detailed images.
CONFIG_ADVANCE_STEPS_MESSAGE: Between 20 and 40 is recommended
CONFIG_ADVANCE_SAMPLER_TIPS: Select the sampling algorithm used to generate the image.
CONFIG_ADVANCE_SCHEDULER_TIPS: Controls how the noise level should change in each step.
CONFIG_ADVANCE_SEED_LANDSCAPE: Please Enter Seed...
CONFIG_ADVANCE_SEED_TIPS: Set a specific starting point for the random number generator to create reproducible results.
CONFIG_ASPECT_TYPE_LANDSCAPE: Landscape
CONFIG_ASPECT_TYPE_PORTRAIT: Portrait
TOOLBAR_HD: "Upscale redraw may fix hand or face problems, but the image may also be worse. The higher the intensity, the greater the effect of repainting and repair. Default value is 0.3"
TOOLBAR_DOWNLOAD: Download
QUALITY_LOSSY: Lossy
QUALITY_LOSSLESS: Lossless
TOOLBAR_USE_TEXT: Use Text
TOOLBAR_REMIX: Remix
TOOLBAR_INPAINT: Inpaint
TOOLBAR_COPY_INFO: Copy info
TOOLBAR_COPY_LINK: Copy Link
TOOLBAR_DELETE: Delete
TOOLBAR_RERUN: Rerun
OUTPUT_HD_IMG_TIPS: "Enabling the 1.5x image generation will achieve a direct output of 2 million pixels. This feature will consume 10 Lumens per image, and the image generation speed will become significantly slower. Please use it only when necessary. Not all models support this feature."
TOOLBAR_REMOVE: Remove
TOOLBAR_REMOVE_BG: Remove BG
TOOLBAR_REMOVE_BG_FULL: Remove Background
TOOLBAR_COPY_FULL_INFO: Copy complete information
TOOLBAR_LIKE: Like
TOOLBAR_HD_FIX: Upscale
PROFILE_OVERVIEW: Overview
PROFILE_ACCOUNT_SETTINGS: Account settings
PROFILE_ACCOUNT_PROFILE_TIPS: "View and update your account details, personal profile, etc."
PROFILE_ACCOUNT_SETTINGS_TIPS: Choose the configuration that works best for you.
PROFILE_DEL_ACCOUNT: Delete Account
PROFILE_DEL_ACCOUNT_TITLE: "Do you really want to delete your PicLumen account?"
PROFILE_DEL_ACCOUNT_CONTENT: "You are about to delete your account. Once confirmed, your account will be immediately deactivated, and you will no longer be able to log in or use our services. The deletion request cannot be undone. An email will be sent to you once the deletion process is complete."
PROFILE_DEL_ACCOUNT_EMAIL: "Email for notifications:"
PROFILE_DEL_ACCOUNT_BTN: Delete My Account
PROFILE_CLEAR_PROMPTS_TITLE: Clear prompts after submit task
PROFILE_ACCOUNT_NICKNAME: Nickname
PROFILE_ACCOUNT_NICKNAME_INPUT: Please enter the nickname
PROFILE_ACCOUNT_NICKNAME_CHAT: Consisting of 4 to 16 characters
PROFILE_ACCOUNT_NICKNAME_INVALID: Nickname invalid
PROFILE_ACCOUNT_NICKNAME_REGISTER: This nickname has been registered
PROFILE_ACCOUNT_EMAIL: Email Address
PROFILE_ACCOUNT_INVALID_EMAIL: Please enter a valid email address
PROFILE_ACCOUNT_EMPTY_EMAIL: Account cannot be empty
PROFILE_ACCOUNT_PASSWORD: Password
PROFILE_ACCOUNT_CHANGE_PASSWORD: Change password
PROFILE_ACCOUNT_RESET_PASSWORD: Reset password
PROFILE_ACCOUNT_NEW_PASSWORD: New password
PROFILE_ACCOUNT_CONFIRM_PASSWORD: Confirm password
PROFILE_ACCOUNT_INVALID_PASSWORD: Must be at least 6 characters.
PROFILE_ACCOUNT_NO_PASSWORD: Passwords do not match.
PROFILE_ACCOUNT_GET_CODE: Get code
PROFILE_ACCOUNT_CONFIRM_EMAIL: Confirm account
PROFILE_ACCOUNT_CONTINUE_WITH: Continue with
PROFILE_ACCOUNT_CONTINUE_EMAIL: or Continue with Email
PROFILE_ACCOUNT_WELCOME: Welcome to PicLumen
PROFILE_ACCOUNT_VERIFY_CODE: Verification code
PROFILE_ACCOUNT_INVALID_VERIFY_CODE: Invalid verification code
PROFILE_ACCOUNT_GENDER: Gender
PROFILE_ACCOUNT_GENDER_MALE: Male
PROFILE_ACCOUNT_GENDER_FEMALE: Female
PROFILE_ACCOUNT_GENDER_NOT_SAY: Rather not say
PROFILE_ACCOUNT_BIO: Bio
PROFILE_ACCOUNT_CHARTS_LIMIT: Tell us about yourself in 250 characters.
PROFILE_ACCOUNT_CHOOSE_AVATAR: Choose a photo
PROFILE_ACCOUNT_ADJUSTING_AVATAR: Adjusting images
PROFILE_MESSAGE: Message
PROFILE_NO_MESSAGE: No messages yet ~
PROFILE_ACCOUNT_PLATFORM: Platforms
FORM_CONTACT_US_UPLOAD_TITLE: Upload feedback image
FORM_CONTACT_US_UPLOAD_TYPE_ERR: "Please select an image smaller than 5MB in JPG, PNG, or WEBP format."
MENU_FEEDBACK: Feedback
FORM_CONTACT_US_TITLE: Submit a Feedback
FORM_CONTACT_US_ISSUE_LABEL: Please choose your issue below
FORM_CONTACT_US_ISSUE_PLACEHOLDER: Choose your issue
FORM_CONTACT_US_ISSUE_TECHNICAL: Technical Issue
FORM_CONTACT_US_ISSUE_FEATURES: Features Issue
FORM_CONTACT_US_ISSUE_ACCOUNT: Account Problems
FORM_CONTACT_US_ISSUE_DEL_ACCOUNT: Delete Account
FORM_CONTACT_US_ISSUE_BUSINESS: Business Cooperation
FORM_CONTACT_US_ISSUE_LEGAL_PRIVACY: "Legal & Privacy"
FORM_CONTACT_US_ISSUE_EMPTY: Issue cannot be blank
FORM_CONTACT_US_FULL_NAME_LABEL: Full Name
FORM_CONTACT_US_FULL_NAME_PLACEHOLDER: Please enter your name
FORM_CONTACT_US_FULL_NAME_EMPTY: Name cannot be blank
FORM_CONTACT_US_EMAIL_LABEL: Contact Email
FORM_CONTACT_US_EMAIL_EMPTY: Email cannot be blank
FORM_CONTACT_US_EMAIL_ERROR: Email format error
FORM_CONTACT_US_MESSAGE_LABEL: Message
FORM_CONTACT_US_MESSAGE_PLACEHOLDER: Your message
FORM_CONTACT_US_MESSAGE_EMPTY: Message cannot be blank
FORM_CONTACT_US_SEND_SUCCESS: Email sent successfully
FORM_CONTACT_US_SENDING: Sending...
FORM_CONTACT_US_SEND_BTN: Send message
FEATURE_REMOVE_BG_TITLE: Remove BG
FEATURE_HIR_FIX_TITLE: Upscale
FEATURE_VARY_TITLE: Vary
FEATURE_VARY_SUB_TITLE: Vary degree
FEATURE_VARY_SUBTLE: Subtle
FEATURE_VARY_STRONG: Strong
FEATURE_SELF_UPLOAD_TITLE: Upload
FEATURE_CROP_TITLE: Crop
FEATURE_EDIT_TITLE: Edit
FEATURE_CROP_DESC: Crop allows you to remove unwanted areas from an image by selecting and keeping only the desired portion.
FEATURE_EXPAND_TITLE: Expand
FEATURE_OUTPAINT_TITLE: Outpaint
FEATURE_EXPAND_DESC: The expand feature allows you to expand the image to a larger size and automatically complete the supplemented image by AI.
FEATURE_OUTPAINT_DESC: "The outpaint feature allows you to expand the boundaries of images, with AI automatically completing the supplementary visual content."
OUTPAINT_ERROR: The selected area contains no image data.
FEATURE_INPAINT_TITLE: Inpaint
FEATURE_INPAINT_DESC: Paint a mask that the model will fill using the prompt text.
FEATURE_UPLOAD_TITLE: Choose a file or drop it here
FEATURE_UPLOAD_SIZE_ERROR: "{type} up to {size} Resolution less than {resolution}."
FEATURE_UPLOAD_TYPE_ERROR: "Only {type} file formats are supported."
FEATURE_SHARE_DESC: Share this creation on social media.
FEATURE_ENLARGEMENT_TITLE: Enlargement factor
FEATURE_REDRAW_STRENGTH: Redraw Strength
THEME_MODE_DARK: Dark Mode
THEME_MODE_TIPS: Click to switch themes
SHORT_SELECTED: selected
SHORT_NAV_HOME: Home
SHORT_THEME: Dark Mode
SHORT_FIXED: Fixed
SHORT_RANDOM: Random
SHORT_LIKES: "Likes | Liked"
SHORT_NAV: NAVIGATE
SHORT_DRAW_WHAT: Describe your image...
SHORT_FAST: Fast
SHORT_RELAX: Relax
SHORT_QUALITY: Quality
SHORT_SETTING: Settings
SHORT_COMMUNITY: Community
SHORT_DASHBOARD: Dashboard
SHORT_DASHBOARD_MESSAGE: View image generation statistics and fast hours.
SHORT_VISIBILITY_TITLE: Image visibility
SHORT_VISIBILITY_HIDE: Hide
SHORT_VISIBILITY_PUBLIC: Public
SHORT_VISIBILITY_PRIVATE: Private
SHORT_GEN_STATISTICS: My generation statistics
SHORT_IMG_NUM_TODAY: images today
SHORT_IMG_NUM_TOTAL: total generation
SHORT_FAST_HOURS: "Daily fast hours (0.5 hour per day, reset everyday by GMT)"
SHORT_FAST_HOURS_TITLE: "What are Fast Hours?"
SHORT_FAST_HOURS_TIPS: "Fast Hours tries to give your task highest priority for execute. If Fast Hours runs out, your task will require more time for queueing. Whether you run out of Fast Hours or not, there is no limit for image generation."
PROMPT_DETECTED_NOT_PASS_TITLE: "Sorry!"
PROMPT_DETECTED_NOT_PASS_CONTENT: This prompt might break out community rules. Please try a different one.
SHORT_PROMPT_IMPROVE_TITLE: Prompt enhance.
SHORT_PROMPT_IMPROVE_MESSAGE: Improve image quality by expanding your prompt words. Quality is improved at the expense of accuracy.
SHORT_PROMPT_TRANS_TITLE: Auto translate.
SHORT_PROMPT_TRANS_MESSAGE: PicLumen currently only support English prompts. Click this button will translate your prompt to english automatically.
SHORT_DEL_MESSAGE: The deleted image will be impossible to recover.
SHORT_DEL_TASK_MESSAGE: These images will be completely deleted and cannot be restored.
SHORT_30_DAY_DELETE_TIPS: Your creations are saved for 30 days. Download them or upgrade to keep them long-term.
SHORT_DELETE_TIPS: Delete current image
SHORT_DEL_DEF_CONFIRM: Do not confirm before delete
SHORT_CANCEL_TASK: "Do you want to cancel the task?"
SHORT_BTN_YES: Yes
SHORT_BTN_NO: No
SHORT_PROMPT_TOO_LONG: Please keep prompt between 1-600 characters for optimal results.
SHORT_TRANS_ERR: "The translation service is busy now, please try again later."
SHORT_ENHANCE_ERR: "Prompt enhancement is busy now, please check back later!"
SHORT_SERVER_ERROR: "Service is currently unavailable, please come back later and press F5 to retry."
STYLE_HELPER_NON_SELECT: No Option Selected
STYLE_HELPER_PLACEHOLDER: Select the required image reference or prompt assistant from below.
SHORT_SAVE_CONF: Use as Defaults
SHORT_MODEL_FEAT_TITLE: Model feature table
SHORT_MODEL_FEAT_TYPE: Type
SHORT_MODEL_FEAT_SPEED: Speed
SHORT_MODEL_FEAT_CFG: Guidance scale
SHORT_MODEL_FEAT_STEPS: Steps
SHORT_MODEL_FEAT_UNIVERSAL: Universal
SHORT_MODEL_FEAT_ANIME: Anime
SHORT_MODEL_FEAT_FAST: Fast
SHORT_MODEL_FEAT_MEDIUM: Medium
SHORT_MODEL_FEAT_SLOW: Slow
ACC_SIGN_IN_TITLE: Sign in
ACC_SIGN_IN_MSG: Sign in to enjoy free access to PicLumen
ACC_SIGN_UP_MSG: Sign up to enjoy free access to PicLumen
ACC_SIGN_IN_OTHER: or Sign in with Email
ACC_SIGN_UP_OTHER: or Sign up with Email
ACC_SIGN_IN_EMAIL: Email
ACC_SIGN_IN_EMAIL_ERROR: Account cannot be empty
ACC_SIGN_IN_PWD: Password
ACC_SIGN_IN_PWD_ERROR: Must be at least 6 characters.
ACC_SIGN_IN_REMEMBER: Remember me
ACC_SIGN_IN_FORGOT: "Forgot password ?"
ACC_SIGN_IN_NO_ACC: "Not registered yet?"
ACC_SIGN_IN_SIGN_UP: Sign up
ACC_SIGN_IN_POLICY: Privacy Policy
ACC_SIGN_IN_TERMS: Terms of Use
ACC_SIGN_IN_EMAIL_VALID: Please enter a valid email address
ACC_SIGN_IN_VERIF_VALID: Invalid verification code
ACC_SIGN_IN_PWD_NOT_MATCH: Passwords do not match.
ACC_SIGN_IN_BACK: Back
ACC_SIGN_IN_NICKNAME_INVALID: Consisting of 4 to 16 characters
ACC_AGREE_BTN: Agree
ACC_SIGN_IN_AGREE: I agree to
ACC_SIGN_IN_READY_ACC: "Already have an account? "
ACC_REG_TITLE: Agreement Required
ACC_REG_CONTENT_1: You must agree to our
ACC_REG_CONTENT_2: to register. Please check the box to proceed.
LINE_ART: Colorize
LINE_ART_MESSAGE: Automatically color the lineart image according to the prompt words
REPORT: Report
REPORT_TITLE: "What is the reason for this report?"
REPORT_OPTION_VIOLENCE: Violence
REPORT_OPTION_PORNOGRAPHY: Pornography
REPORT_OPTION_DISCRIMINATION: Racial discrimination
REPORT_OPTION_INFRINGEMENT: Copyright infringement
REPORT_OPTION_OTHER: Others
REPORT_OPTION_OTHER_PLACEHOLDER: Please describe the issue in detail.
HISTORY_TIPS: History can be saved for 14 days and will be deleted after expiration. Download the images you want soon.
SHORT_TODAY: Today
SHORT_YESTERDAY: Yesterday
SHORT_RELOAD_MESSAGE: New version is live. Refresh to use the App.
SHORT_RELOAD_BTN: Refresh
SHORT_CONFIRM_BTN: Confirm
SET_CONFIG_SUCCESS: Default settings saved successfully.
SHORT_NO_MORE: "There's no more"
GUIDE_CREATE: Click the Generate button to start creating
NO_SUCH_RES: No search results found
AUTHOR_DEL_RES: The work has been deleted by the author
COMING_SOON: "Coming soon on!"
NEW_FEAT_TITLE_FLUX: "FLUX.1-schnell already added to model list!"
NEW_FEAT_TITLE_LINE_ART: Lineart colorize feature has been added to your toolbox
NEW_FEAT_CONTENT_LINE_ART: "AI automatic line drawing coloring, or auto-coloring of line art, is a technology that applies colors to black and white or sketched images with precision and artistry. This AI-driven tool can transform line drawings into vivid, full-color illustrations, enhancing the visual appeal and bringing characters and scenes to life."
APPLY_PARAM: Load Params
CUSTOM_UPLOAD_TIPS: Upload custom image to use AI tools.
CUSTOM_UPLOAD_LIMIT: "The number of images uploaded today has reached the limit. We will reset this restriction after 00:00 Greenwich Mean Time (GMT)."
REFER_TYPE_TITLE: Image Reference
REFER_TYPE_TITLE_H5: Reference
IMAGE_GUIDANCE: Image Guidance
CONTENT_REFER_TIPS: Refer to the image content and edges to generate
STYLE_REFER_TIPS: Reference the aesthetics and styles from an image
CHARACTER_REFER_TIPS: Use the facial features of the picture to influence the image
MODE_DARK: Dark
MODE_LIGHT: Light
MODE_SYSTEM_DARK: System(Dark)
MODE_SYSTEM_LIGHT: System(Light)
ADD_BUTTON: Add
COLLECTION_EDIT_TIPS: Name must contain at least 1 character(s).
COLLECTION_UN_COLLECTION: Uncollect
COLLECTION_REMOVE: Remove from this collection
COLLECTION_REMOVE_CONTENT: "Do you want to remove the image from your collection?"
COLLECTION_REMOVE_NOT_CONFIRM: "Don't notify me this message again."
COLLECTION_ADD_SUCCESS: Added to Collection
MENU_COLLECTION_TIPS: Add to collection
MENU_COLLECTION: Collections
MY_COLLECTION: My Collections
MENU_GALLERY: Gallery
MENU_PERSONAL: Personal
MENU_HELP: Help

MODEL_TIPS_ART_V1: Dreamy and romantic with soft, painterly tones.
MODEL_TIPS_REAL: "Photo-true style with clean lighting and detail."
MODEL_TIPS_ANIME: Bright tones and crisp detail in a classic anime look.
MODEL_TIPS_LINEART: Clean and expressive sketches perfect for manga or coloring.
MODEL_TIPS_NAMIYA: Lush lighting and soft brushwork with emotional storytelling.

MODEL_TIPS_FLUX_KOREA: Powerful performance with distinctive, breathtaking realism.
MODEL_TIPS_FLUX_KONTEXT: Elegant visuals with vivid detail and strong edit control.
MODEL_TIPS_MJ_V1: Highly artistic renders with rich textures and cinematic depth.
MODEL_TIPS_PONY_V6: Soft, stylized characters with animal features and smooth shading.
MODEL_TIPS_FLUX_SCHNELL: Ultra-fast generation with decent quality and clean style.
MODEL_TIPS_FLUX_DEV: Experimental model with evolving capabilities and raw power.

MODEL_TIPS_DEV_FLUX: "FLUX.1-dev from Black Forest Lab, Provides a great deal of control."
MODEL_SELECT: Select Model
MODEL_SWITCH: "Ok, Switch model"
MODEL_SWITCH_1: Switch
MODEL_SWITCH_INFO: "Your current plan does not support this model. Please upgrade to unlock {target}."
MODEL_SWITCH_MSG: "Currently {name} does not support {feature} yet, please switch to {target} and try again?"

REFER_POSE_ONLY: "Pose Control and Image Reference can't work together. Please choose one."
CONTROL_TITLE: Image control
CONTROL_TITLE_H5: Control
POSE: Pose Control
POSE_CONTROL_TITLE: Pose Control
POSE_CONTROL_DESC: Keep character as the main subject in the prompt and avoid contradictory terms with the pose. AI will generate the final image based on the pose as much as possible.
SEE_MORE: See more.
POSE_CONTROL_CREATE: Create Pose
POSE_CONTROL_ERR_TITLE: Pose detection failed.
POSE_CONTROL_ERR_DESC: Pose not detected. Use photos with clear human poses. Anime images may not be recognized.
POSE_CONTROL_DETECT: Detect Pose
POSE_CONTROL_DETECTING: Detecting...
POSE_CONTROL_SAVE: Add to Pose Library
CREATE:
    VIP_ONLY_MODEL:
        TITLE: This Model is for Members Only
        DESC: "The model you're trying to use is a member-exclusive.You can upgrade to unlock it — or switch to a available model to continue creating."
        MODEL_FOR_PRO_TIP: "Primo is for Standard & Pro now. Pro only soon."
    REFERENCE_IMAGE: "Reference Image"
    NO_OPTIONS_SELECTED: "No Option Selected"
    NO_SELECT_TIP: "Please select a reference image or prompt assistant from the left panel."
    PORTRAIT: "Portrait"
    LANDSCAPE: "Landscape"
    GENERATE: "Generate"
    DISPLAY_TYPE_IMAGE: "Image"
GEN_SHORTCUTS: "{shortcuts} to submit the task"
NOTICE_BTN: Maintenance notice
NOTICE_GREET: "Dear User,"
NOTICE_HEAD: We hope this message finds you well. We are writing to tell you about some scheduled maintenance on our web service. This maintenance is essential to ensure we continue providing you with the best possible experience and service reliability.
NOTICE_LIST_1: "1. Maintenance Schedule: The maintenance will begin on [Date 2024-10-18 GMT time] at [01:00:00] and is expected to last approximately 5 hours."
NOTICE_LIST_2: "2. What to Expect: During the maintenance period, the service will be temporarily unavailable."
NOTICE_LIST_3: "3. What We're Doing: Our team will be performing important updates and improvements. These include strengthening security measures, upgrading our database, and adding new features that will benefit all our users."
NOTICE_FOOTER: We sincerely appreciate your understanding and patience during this time. Thank you for your continued support.
NOTICE_END: "Best regards, PicLumen Team"
INFO_TITLE: Info
ACCOUNT_CURRENT: "We have sent a code by email to {email}. Enter it below to confirm your account."
EMPTY_DATA: Empty
NO_DATA: No Data
SEED_ERROR_TITLE: Failed to submit the task
SEED_ERROR_CONTENT: "The value of the seed must be an integer between 1 and *********, inclusive."
INSTALL_APP: Get the App
COMING_SOON_MESSAGE: Coming soon
PONY_TAG_TITLE: Prompt Helper
PONY_TAG_PRESETS: Model Presets
DEL_TIPS: "Processing, please do not click frequently."
COMMUNITY_REPLY_SECOND: "{time} second ago"
COMMUNITY_REPLY_MINUTE: "{time} minute ago"
COMMUNITY_REPLY_HOUR: "{time} hour ago"
COMMUNITY_REPLY_DAY: "{time} day ago"
COMMUNITY_REPLY_SECOND_S: "{time} seconds ago"
COMMUNITY_REPLY_MINUTE_S: "{time} minutes ago"
COMMUNITY_REPLY_HOUR_S: "{time} hours ago"
COMMUNITY_REPLY_DAY_S: "{time} days ago"
COMMUNITY_JOIN_DAY: "Joined PicLumen {day} days ago"
COMMUNITY_JOIN_TODAY: Joined PicLumen today
COMMUNITY_BIO: Your Bio
COMMUNITY_EMPTY_DESC: "Oops, looks like someone forgot to introduce themselves."
COMMUNITY_FANS: Followers
COMMUNITY_FOLLOWED: Followed
COMMUNITY_DEL_COMMENT_TXT: The author deleted this comment
COMMUNITY_REPORT_COMMENT_TXT: You reported this comment
COMMUNITY_REPORT_IMG_TXT: You reported this image
COLLECTION_MENU_DEL: Delete collections
COLLECTION_MENU_EDIT: Edit collections
COLLECTION_NEW: New Collections
COLLECTION_NAME: Name
COLLECTION_DES: Description
COLLECTION_ORDER_DESC: Descending
COLLECTION_ORDER_ASC: Ascending
COLLECTION_LAYOUT_RIVER: Full
COLLECTION_LAYOUT_GRID: Square
COLLECTION_SIZE_SMALL: Small
COLLECTION_SIZE_MEDIUM: Medium
COLLECTION_SIZE_LARGE: Large
COLLECTION_DEL_TIPS: "This collection and its contents will be permanently deleted and cannot be recovered. Confirm delete?"
ACTION_SUCCESS_TIPS: Operation successful
COLLECTION_STORAGE_LIMIT_MESSAGE: "You've reached your storage limit. Free up space to save more."
COLLECTION_STORAGE: Storage
COMMUNITY_PUBLIC_REVIEW: Pending for process
COMMUNITY_PUBLIC_REJECT: Rejected
COMMUNITY_PUBLIC_LIMIT: "Community Beta: Daily limit is 50 images. Thank you for your participation!"
COMMUNITY_IMG_FEATURE: "Congratulations! This image was featured in the community!"
LUMENS_REMAINING: "Remaining Lumens:"
LUMENS_REMAINING_DAILY: "Daily ({count} per day, reset everyday by GMT)"
LUMENS_REMAINING_SUBSCRIBE: Included in the subscription
LUMENS_PURCHASED_AWARDED: "Purchased & Awarded"
LUMENS_CONSUMPTION_TITLE: "What is the consumption order of Lumen?"
LUMENS_CONSUMPTION: "The system use your daily free Lumens first, then your subscription ones, and finally any purchased or awarded Lumens. Even without Lumens, you can still generate images in relax mode."
LUMENS_DESCRIPTION_TITLE: "What are Lumens?"
LUMENS_DESCRIPTION_CONTENT: "Lumen is the token used in PicLumen, replacing fast hours. If there are Lumens, they are consumed first to perform fast tasks. Once Lumens are run out, tasks will be downgraded to relax tasks."
GET_MORE_FAST_TASK: Get more fast task
TASK_PROCESS_LIMIT: "Too many tasks at once! Please wait for current ones to finish."
SUBSCRIBE_TITLE: Subscribe Plans
SUBSCRIBE_ACTIVE_PLAN_TEXT: My active plan
SUBSCRIBE_PLAN_BASIC: Basic
SUBSCRIBE_PLAN_STANDARD: Standard
SUBSCRIBE_PLAN_PRO: Pro
SUBSCRIBE_DAILY: Daily
SUBSCRIBE_INCLUDED: Included
SUBSCRIBE_REFRESH: Refresh on
SUBSCRIBE_BILLING: "Billing & Payment:"
SUBSCRIBE_PRICE: Price
SUBSCRIBE_PERIOD: Billing period
SUBSCRIBE_EXPIRATION: Expiration date
SUBSCRIBE_LUMEN: "Remaining Lumens:"
SUBSCRIBE_BUY_MORE: Buy more Lumens
SUBSCRIBE_BENEFITS_1: 10 Daily Free Lumens
SUBSCRIBE_BENEFITS_2: 500 Images Cloud Storage
SUBSCRIBE_BENEFITS_3: General commercial terms
SUBSCRIBE_BENEFITS_4: 5 GB Cloud storage
SUBSCRIBE_BENEFITS_5_BASIC: |
    Limited free generations in Relax Mode
    (PicLumen series models)
SUBSCRIBE_BENEFITS_5: |
    Unlimited generation in relax mode
    (PicLumen series models)
SUBSCRIBE_BENEFITS_6: Queue up to 5 generations
SUBSCRIBE_BENEFITS_7: Generate 2 jobs concurrently
SUBSCRIBE_BENEFITS_8: Advanced features
SUBSCRIBE_BENEFITS_9: 50 GB Cloud storage
SUBSCRIBE_BENEFITS_10: Creation history never expire
SUBSCRIBE_BENEFITS_11: Queue up to 10 generations
SUBSCRIBE_BENEFITS_12: Generate 5 jobs concurrently
SUBSCRIBE_BENEFITS_13: Free for a limited time
SUBSCRIBE_BENEFITS_14: Save a 30 days creation flow history
SUBSCRIBE_BENEFITS_15: |
    1800 Monthly Lumen
    (~1800 PicLumen images)
SUBSCRIBE_BENEFITS_16: |
    Save a 180 days creation flow history
    (During the subscription period)
SUBSCRIBE_BENEFITS_17: |
    4800 Monthly Lumen
    (~4800 PicLumen images)
SUBSCRIBE_BENEFITS_18: |
    Never expire
    (During the subscription period)
SUBSCRIBE_BENEFITS_19: 1800 Monthly Lumens
SUBSCRIBE_BENEFITS_20: |
    History will not expire
    (Within the subscription period)
SUBSCRIBE_BENEFITS_21: 4800 Monthly Lumens
SUBSCRIBE_TAB_MONTH: Monthly
SUBSCRIBE_TAB_YEAR: Yearly
SUBSCRIBE_UNIT_MONTH: / Month
SUBSCRIBE_UNIT_YEAR: / Year
SUBSCRIBE_PRICE_OFF: "{sale} OFF"
SUBSCRIBE_COST_TABLE_TITLE: Lumen cost table
SUBSCRIBE_COST_TABLE_1_1: Model
SUBSCRIBE_COST_TABLE_2_1: PicLumen Series
SUBSCRIBE_COST_TABLE_3_1: Pony Diffusion V6
SUBSCRIBE_COST_TABLE_4_1: FLUX.1-schnell
SUBSCRIBE_COST_TABLE_5_1: FLUX.1-dev
SUBSCRIBE_COST_TABLE_5_2: Generation / Edit cost
SUBSCRIBE_COST_TABLE_5_3: "{count} / image"
SUBSCRIBE_COST_TABLE_5_5: 12 Lumen / image
SUBSCRIBE_FEATURES_TABLE_TITLE: Advanced feature table
SUBSCRIBE_FEATURES_TABLE_2_1: Upscale
SUBSCRIBE_FEATURES_TABLE_3_1: Inpaint
SUBSCRIBE_FEATURES_TABLE_4_1: Expand
SUBSCRIBE_FEATURES_TABLE_5_1: Images per batch
SUBSCRIBE_FEATURES_TABLE_6_1: Colorize
SUBSCRIBE_FEATURES_TABLE_7_1: Remove BG
SUBSCRIBE_FEATURES_TABLE_8_1: Auto translate
SUBSCRIBE_FEATURES_TABLE_9_1: Prompt enhance
SUBSCRIBE_FEATURES_TABLE_10_1: Batch download
SUBSCRIBE_FEATURES_TABLE_11_1: Collection images
SUBSCRIBE_BASIC_DESC: "Enjoy the essentials! Our free plan offers a robust set of basic tools to get you started."
SUBSCRIBE_STANDARD_DESC: Unlock additional features that streamline your workflow and boost your productivity.
SUBSCRIBE_PRO_DESC: "Access the full suite of features, including large storage and sufficient concurrent tasks."
SUBSCRIBE_FREE_TEXT: Free
SUBSCRIBE_BUTTON_TEXT: Subscribe
SUBSCRIBE_FAQ_TITLE: Frequently Asked Questions
SUBSCRIBE_BUY_LUMEN_MODAL_TITLE: Get more Lumens
SUBSCRIBE_BUY_LUMEN_MODAL_SUBTITLE: Select amount of Lumens
SUBSCRIBE_BUY_LUMEN_MODAL_NOTE: "Note: Purchased Lumens expire after 60 days."
SUBSCRIBE_BUY_LUMEN_MODAL_BUTTON: Continue
SUBSCRIBE_MODAL_TITLE: Unleash Your Creative Vision with PicLumen
SUBSCRIBE_MODAL_SUBTITLE: "Your AI image toolbox. Simple to use, yet powerful."
SUBSCRIBE_MODAL_NOTE: We offer flexible plans to fit your needs.
SUBSCRIBE_MODAL_CHOOSE_BUTTON: Choose My Plan
SUBSCRIBE_MODAL_LATER_BUTTON: Later
SUBSCRIBE_MODAL_LOADING: "Processing request, please wait for a moment..."
SUBSCRIBE_MODAL_PURCHASE: Purchase success
SUBSCRIBE_MODAL_PURCHASE_TEXT: "Thank you for your purchase! Your order has been successfully processed. You will receive a confirmation email shortly. If you have any questions, please feel free to contact our customer service by {email}"
SUBSCRIBE_UPGRADE_CHOOSE: Choose a plan.
SUBSCRIBE_UPGRADE_DESC: Upgrade today to receive more Lumens in your new subscription cycle.
SUBSCRIBE_UPGRADE: Upgrade
SUBSCRIBE_DOWNGRADE: Downgrade
SUBSCRIBE_UPGRADE_IMMEDIATELY: Upgrade immediately
SUBSCRIBE_UPGRADE_NEXT: Next
SUBSCRIBE_VIEW_INVOICES: View Invoice
SUBSCRIBE_UPGRADE_PLAN: Upgrade Plan
SUBSCRIBE_DOWNGRADE_PLAN: Downgrade Plan
SUBSCRIBE_ACTIVE: Active
SUBSCRIBE_TO_NOTICE: Please go to subscribe to buy more lumen.
SUBSCRIBE_GET_BILLING_ERROR: "Failed to obtain bill, please try again later!"
SUBSCRIBE_CANCEL_CHANGE: Cancel Change
SUBSCRIBE_CANCEL_PLAN: Cancel Plan
SUBSCRIBE_RENEWED_ON: Renewed on
SUBSCRIBE_CANCELLING_ON: Cancelling on
SUBSCRIBE_UNCANCEL_PLAN: Uncancel Plan
SUBSCRIBE_MODAL_CANCEL_TITLE: Subscription Cancellation
SUBSCRIBE_MODAL_CANCEL_LINE1: "We noticed that you're considering canceling your subscription plan. Just a friendly heads-up: if you proceed with the cancellation, you won't be charged for the next billing cycle. Rest assured, all the benefits and privileges you currently enjoy will remain active until the end of your current subscription period."
SUBSCRIBE_MODAL_CANCEL_LINE2: Thank you for subscribing to PicLumen. We hope to continue serving you in the future.
SUBSCRIBE_MODAL_CANCEL_LINE3: "Best regards,"
SUBSCRIBE_MODAL_CANCEL_LINE4: PicLumen Team
SUBSCRIBE_MODAL_CANCEL_PLAN: "Subscription Plan:"
SUBSCRIBE_MODAL_CANCEL_PLATFORM: "Payment:"
SUBSCRIBE_MODAL_CANCEL_CONFIRM: Stay Subscribed
SUBSCRIBE_MODAL_CANCEL_CANCEL: Cancel Subscription
SUBSCRIBE_MODAL_CHANGE_CONFIRM: Confirm
SUBSCRIBE_MODAL_CHANGE_CONTENT: "Are you sure you want to cancel the modified future subscription plan?"
SUBSCRIBE_MODAL_CHANGE_CANCEL: Cancel
SUBSCRIBE_EX_GET: Failed to get subscription schedules.
SUBSCRIBE_EX_ACTIVE: Failed to get active subscription.
SUBSCRIBE_EX_CR_CHECKOUT: Failed to create checkout session.
SUBSCRIBE_EX_NOT: Subscription not found.
SUBSCRIBE_EX_CR_SCHEDULE: Failed to create subscription schedule.
SUBSCRIBE_EX_CA: Failed to cancel subscription.
SUBSCRIBE_EX_CA_SCHEDULE: Failed to cancel subscription schedule.
SUBSCRIBE_EX_PARAMETERS: Invalid parameters.
SUBSCRIBE_EX_GET_BILLING: Failed to get billing.
SUBSCRIBE_EX_UPGRADE: Cannot upgrade subscription.
SUBSCRIBE_EX_BAD: Bad request.
SUBSCRIBE_EX_NOT_PAYMENT: "The customer has no payment method saved, so a subscription cannot be created."
SUBSCRIBE_EX_NOT_BUY_LUMEN: You must subscribe before purchasing Lumen.
SUBSCRIBE_EX_BEFORE: You must subscribe before.
SUBSCRIBE_EX_PAYMENT: A generic error occurred during payment processing.
SUBSCRIBE_EX_TEXT: "Payment encountered a problem, error code {code}, please try again later. Customer service email service{'@'}piclumen.com"
SUBSCRIBE_EX_OPERATION: "Operation failed, please try again later!"
HISTORY_SAVE_DATE: "History is kept for {day} days"
HISTORY_DEL_AUTO_TIPS: "Free users will retain only the last {day} days of images. Please move them to your collections promptly to keep them permanently."
IMAGE_PREVIEW_QUALITY_TITLE: Image quality when preview images
IMAGE_DOWNLOAD_QUALITY_TITLE: Download image file type
IMAGE_DELETE_TITLE: Ask before deleting images
IMAGE_DISPLAY_PROMPT: Display corresponding prompts on the right side of the generated images
IMAGE_PARAMETERS_SHOW_TITLE: Show all parameters on image preview panel
TASK_SUBMIT_USE_TITLE: Submit task use
IMAGE_SHOW_OPTIONS: Show options
IMAGE_GENERATE_PROCESS: Processing...
LOADING_TEXT: Loading...
BILLED_MONTHLY: billed monthly
BILLED_YEARLY: billed yearly
SEE_FEATURE_TABLE: see feature table
SUBSCRIBE_GET_MORE_LUMENS: Subscribe to get more Lumens.
PAYMENT_ISSUES_TITLE: Payment Issues
MAINTENANCE_TITLE: Scheduled Maintenance Notice
MAINTENANCE_CONTENT_1: "Dear Users,"
MAINTENANCE_CONTENT_2: "Please be advised that our online service will be temporarily unavailable for scheduled maintenance from {startTime} to {endTime} (local time). During this {maintenanceMinutes}-minutes period, website access will be interrupted."
MAINTENANCE_CONTENT_3: We apologize for any inconvenience this may cause and appreciate your understanding as we work to improve the performance and reliability of our service.
MAINTENANCE_CONTENT_4: "Sincerely,"
MAINTENANCE_CONTENT_5: PicLumen Team
PROMPT_HELPER_TITLE: Prompt Helper
PROMPT_HELPER_DESC: "Image references, prompt magic, and other tools help you write prompts more easily and enhance control."
MAGIC_TITLE: Prompt Magic
MAGIC_TITLE_H5: Style
MAGIC_HELPER_DESC: "Various preset prompt effectors will guide changes in image style. The fewer the prompts, the more pronounced the effect."
MAGIC_TYPE_STYLE: Style
MAGIC_TYPE_PAINT: Painting
MAGIC_TYPE_CAMERA: Photography
CHOOSE_MODEL_DESC: "Choose the appropriate base model. The model is the foundation of image generation, involving language expression capabilities and the final image style and effect. Please select a model suitable for the target image."
ASPECT_DESC: Aspect ratio of generated images.
BATCH_IMG_COUNT: Batch image count
BATCH_IMG_COUNT_DESC: Number of images generated each time.
UPGRADE_CONFIRM_CONTENT: Your creations are saved for 30 days. Download them or subscribe for long-term storage.
UPGRADE_CONFIRM_BTN: "Got it, turn off the prompt"
IMPORT_FILE_TITLE: Import file
IMPORT_FILE_DESC: "By importing files, you can apply AI processing to regular images."
IMPORT_FILE_LOCAL: Import by upload
IMPORT_FILE_LOCAL_DESC: Drag and drop image file to upload
IMPORT_FILE_ORIGIN: or import from URL
IMPORT_FILE_ORIGIN_DESC: Enter the image URL...
IMPORT_FILE_WARNING: Images exceeding 1 million pixels may be preprocessed to meet the input requirements for AI processing.
IMPORT_FILE_BTN: Import
IMPORT_FILE_LOADING: Importing
IMPORT_FILE_SUCCESSFUL: "Upload successfully. The image has been sent to the 'Create' menu."
IMPORT_FILE_SUCCESSFUL_BTN: Go to Create
IMPORT_FILE_INVALID_URL: The target image URL does not allow direct image downloads. Please download and upload manually.
IMPORT_URL_TYPE_ERROR: Invalid URL address
IMPORT_FILE_TYPE_ERROR: "Currently only supports {type} type images"
COLLECTIONS_MOVE_TITLE: Move to
COLLECTIONS_MOVE_DESC: Select the collection to move the image to
COLLECTIONS_MOVE_BTN: Move
NOT_MODEL_ERROR: "Exception error! No correct model set, please refresh the application and try again."
GEN_STATUS_TAB_BAR_TITLE: Display image generation status in the title bar
LUMEN_COST_SHOW: The generate button shows the Lumen cost
CONFIG_SETTING_HIDDEN_PROMPT_WHEN_UPLOAD_IMG: Prompt are hidden when post images to community
TEMP_EXPORT_TITLE: Export prompt history
TEMP_EXPORT_BTN: Export
TEMP_EXPORT_AFTER_DESC: |
    The export request has been submitted, pending for processing.
    Export file will be sent to the email address:
TEMP_EXPORT_DESC: "Some users missed the 30-day image retention notice. This feature allows exporting all image generation prompt histories before January 2025, helping users to regenerate some of the images they need."
TEMP_EXPORT_SUBTITLE: Email address to receive the prompt history file
TEMP_EXPORT_WARNING: "Due to the large volume of data, the historical records will be processed and sent gradually starting from Feb 2025."
FEATURE_DESCRIBE_FEATURE_TITLE: Describe Image
FEATURE_DESCRIBE_TITLE: Describe
MENU_TOOLS: Tools
TOOLS_CENTER_TITLE: Tools Center
TOOLS_CENTER_DES: Your one-stop AI toolkit for smarter image editing. It’s built to speed up your workflow and deliver amazing results instantly.
TOOLS_CENTER_MORE: More tools coming soon
DESCRIBE_DES: "Transforming image content into precise text prompts, this feature leverages advanced AI technology to automatically analyze the core elements, style, and details of an image."
FEATURE_BG_REMOVER_FEATURE_TITLE: Remove Background
FEATURE_BG_REMOVER_FEATURE_TITLE_SHORT: Remove Bg
BG_REMOVER_DES: "Instantly remove backgrounds from single or multiple images in just 3 seconds. Fast, clean, and effortless."
DESCRIBE_OPTS_DESC: Natural Language
DESCRIBE_OPTS_TAG: Tag
DESCRIBE_BTN_COPY: Copy
DESCRIBE_BTN_SEND: Send to Create
DESCRIBE_TRANSLATE: Translate
DESCRIBE_TRANSLATE_TO: Translate to
DESCRIBE_PLACEHOLDER: "After uploading the picture, click Describe to generate the prompt word."
DESCRIBE_TRANSLATE_PLACEHOLDER: Click Translate to to translate into the target language.
MESSAGE_CENTER_TITLE: Message Center
MESSAGE_CENTER_READ_ALL: All Marked As Read
MESSAGE_CENTER_LIKE: Likes
MESSAGE_CENTER_COMMENTS: Comments
MESSAGE_CENTER_UPDATE: System Update
MESSAGE_CENTER_PERSONAL: Personal Notification
MESSAGE_CENTER_PUBLIC: Public Notification
MESSAGE_CENTER_COMMENTS_TITLE: Commented on my work
MESSAGE_CENTER_COMMENTS_REPLY: Reply
MESSAGE_CENTER_COMMENTS_PUBLISH: Publish
MESSAGE_CENTER_EMPTY: No content available
MESSAGE_CENTER_LIKE_TITLE: Liked my work
MESSAGE_CENTER_PERSONAL_TITLE: System Notice
MESSAGE_CENTER_PUBLIC_TITLE: Public Notice
SEE_LUMEN_TABLE: See lumen cost table.
LUMEN_COST_TABLE: Lumen cost table
LUMEN_COST_TABLE_DESC: "The consumption of Lumen is determined by the computational consumption and the licensing fees of third-party services. Here is the detailed list of consumption, please plan and use it wisely. Errors and the generation of NSFW content will not consume Lumen."
LUMEN_COST_TABLE_TYPE_GEN_TITLE: Image generation
LUMEN_COST_TABLE_TYPE_GEN: Generation / Edit cost
LUMEN_COST_TABLE_FEATURE: "(Edit includes inpaint, outpaint, colorize)"
LUMEN_COST_TABLE_TYPE_COST: Cost
COPY_PROMPT: Copy prompt
LUMEN_LEFT: Lumens Left
UPGRADE: UPGRADE
SUBSCRIBE: SUBSCRIBE
FREE: Free
AWARD: Award
SUBSCRIPTION: Subscription
PURCHASED: Purchased
PURCHASE: Purchase
AWARDED: Awarded
USAGE_PRIORITY: Usage Priority
USAGE_DES: "Free Lumen > Award > Subscription > Purchased"
MORE: More
LOG_OUT: Log Out
ACCOUNT_SETTINGS: Account Settings
BASE_INFO: Basic Info
COLLECTION_UNORGANIZED: Uncollected Creations
COLLECTION_ORGANIZED: Collections
COLLECTION_MOVE_COVER: Move to this collection
COLLECTION_ADD_COVER: Add to this collection
COLLECTION_MOVE_TITLE: Move to collection
COLLECTION_SET_COVER: Set as Cover
GALLERY_EMPTY: "You have not created any images temporarily, go create some now!"
LANGUAGE: Language
SAVE_SUCCESS: "Save successfully!"
LONG_TASK_TIPS: Looks like image generation is busy now. Please try again later.
RE_SUBMIT_TASK: Submit Task
COLLECTION_DEL_NOT_EMPTY: Please empty the folder first and then delete it.
BATCH_DOWNLOAD_MAX_COUNT: "You've reached the batch download limits. Please download with fewer images."
QUESTION_PLACEHOLDER: Your answer...
QUESTION_EMPTY_ANSWER: Please enter your answer
SUBMIT_BTN_GET_LUMEN: "Submit & Get {count}"
QUESTION_EXPIRED_TOAST: "This survey has closed. Thank you for your interest, and we welcome your participation next time!"
QUESTION_SUCCESS_TOAST: "Thanks for your time! Enjoy your 5 Lumens reward."
RELAX_LIMIT_ERR_BAR: "Creating in Relax Mode. Need unlimited generations?"
RELAX_LIMIT_ERR_TITLE: Free Generations Used Up
RELAX_LIMIT_ERR_SUB: You’ve reached your free limits for today.
RELAX_LIMIT_ERR_DESC: "Need more images? Come back tomorrow or upgrade for unlimited image creations now."
BTN_SUBSCRIBE_NOW: Subscribe Now
BTN_GOT_IT: Got It
BTN_UPGRADE_NOW: Upgrade Now
DIALOG_TITLE_NOTICE: Notice
DIALOG_TITLE_OOPS: Oops
DIALOG_TITLE_COMMUNITY_BETA: Community Beta
DIALOG_TITLE_UPGRADE: Upgrade
DIALOG_TITLE_INV_VALUE: Invalid Value
DIALOG_TITLE_CONFIRM: Confirm
DIALOG_TITLE_ATTEN: Attention
DIALOG_TITLE_ACCOUNT: Account Deletion
DIALOG_TITLE_FEATURED: Featured
DIALOG_TITLE_UPDATE: Update
DIALOG_TITLE_UPGRADE_PLAN: Upgrade Plan
DIALOG_TITLE_PROCESSING: Processing
DIALOG_TITLE_GET_LUMEN: Get More Lumens
MODEL_GROUP_PIC_TITLE: PicLumen Series Models
MODEL_GROUP_PIC_DESC: Unlimited Relax Mode generation available for Standard/Pro Subscribers.
MODEL_GROUP_OTHER_TITLE: Other Models
MODEL_GROUP_OTHER_DESC: Cost different Lumens to generate based on model.
COMMUNITY_TAG_TRENDING: Trending
COMMUNITY_TAG_NEW: New
ACTIVITY:
    MENU: Challenge
    TITLE: Challenge Center
    EXPIRES_IN: Expires in
    CREATE: Create New
    SUBMISSION: Submit My Creation
    ACTIVITY_INTRO: Activity Introduction
    SEE_MORE: See More
    RULE_DETAILS: Rule Details
    CREATORS_SHOWCASE: Creators Showcase
    ALL_WORKS: All Works
    ENDED: Ended
    ONGOING: Ongoing
    RATING: Rating
    WINNING_WORK: Winning Work
    SORT_BY_POPULARITY: By Popularity
    SORT_BY_TIME: By Time
    AWARDS_AND_NOMINATIONS: Awards and Nominations
    SUBMIT_MY_CREATION: Submit My Creation
    UNCOLLECTED_CREATIONS: Uncollected Creations
    JOIN_WITH_CREATION: Join with My Creation
    SUBMISSION_DETAIL: "Submissions are shared in the community by default, and winning works will be featured on the event page."
    CANCEL: Cancel
    NEXT: Next
    SHOW_PROMPTS: Show Prompts
    UNREVIEWED: Unreviewed
    PENDING_PROCESS: Pending for Process
    POSTED_TO_COMMUNITY: Posted to the Community
    BACK: Back
    SUBMIT: Submit
    MORE_TEXT: More challenges coming soon
    IMAGE_EMPTY: "No creations yet. Be the first to enter this challenge! "
    JOIN_ACTIVITY: Join the Activity
    LIMIT_HINT: "You've reached the submission limit for this challenge."
    CHALLENGE_EMPTY: "No challenge-ready images found. Create new and enter the challenge!"
MENU_TASKS: Rewards
TASKS_CENTER_TITLE: Rewards Center
TASKS_CENTER_SEE_REWARD_RECORD: Rewards History
TASKS_CENTER_DAILY_REWARD: Daily Rewards
TASKS_CENTER_ACHIEVEMENT_REWARD: My Achievements
TASKS_CENTER_GO: GO
TASKS_CENTER_RECORD_TITLE: Awards and Nominations
TASKS_CENTER_TASK_NAME: Task Name
TASKS_CENTER_TASK_REWARD: Task Reward
TASKS_CENTER_CLAIM_TIME: Claim Time
TASKS_CENTER_MODAL_CENTER: Claim your free Lumens and earn more in the Reward Center.
TASKS_CENTER_EARN_MORE: Earn More
TASKS_CENTER_CLAIM: Claim
TASKS_DAILY_LOGIN: Free Lumens
TASKS_DAILY_LIKE: Like 20 creations
TASKS_DAILY_COMMENT: Leave a comment
TASKS_DAILY_SHARE: Share your creation to
TASKS_ACHIEVEMENT_FIRST_CREATE: First image creation
TASKS_ACHIEVEMENT_FOLLOW_IG: Follow us on IG
TASKS_ACHIEVEMENT_FOLLOW_X: Follow us on X
TASKS_ACHIEVEMENT_GET_LIKE_5: Get 5 likes
TASKS_ACHIEVEMENT_GET_LIKE_20: Get 20 likes
TASKS_ACHIEVEMENT_GET_LIKE_50: Get 50 likes
TASKS_ACHIEVEMENT_GET_LIKE_100: Get 100 likes
TASKS_ACHIEVEMENT_GET_FOLLOWER_5: Get 5 followers
TASKS_ACHIEVEMENT_GET_FOLLOWER_30: Get 30 followers
TASKS_ACHIEVEMENT_GET_FOLLOWER_100: Get 100 followers
TASKS_ACHIEVEMENT_SUBSCRIBE_YOUTUBE: Subscribe to our channel
TASKS_ACHIEVEMENT_JOIN_DISCORD: Join our Discord
TASKS_ACHIEVEMENT_VOTE: Vote us on
TASKS_ACHIEVEMENT_VOTE_HOVER_HINT: "After leaving a Trustpilot review, contact our support for manual Lumens approval."
TASKS_CENTER_RECORD_EMPTY: "No reward history yet. Complete tasks to earn Lumens now!"
TOAST_VIP_FUNC_NOTICE: You have subscribed and are using the subscription feature.
CONTRAST: Contrast
DETAILS: Details
BATCH_REMOVE_BG:
    FIRST_IMAGE_PROCESS: The first image will be processed.
    REMOVE_BG_ERROR: This image processing is failed. Lumen has been refunded.
    REMOVE_ONE: Remove One
    REMOVE_ALL: Remove All
    CLEAR_ALL: Clear All
    LIMITED_TRIAL: Time-Limited Access
    REMOVING: Removing
    EXIT_TITLE: Exit Reminder
    EXIT_CONTENT: "Your image is still processing or hasn’t been downloaded yet. If you exit now, the Lumens you used won’t be refunded, and the result may be lost. Want to continue?"
    EXIT_CANCEL: Stay
    EXIT_CONFIRM: Exit
    CLEAR_TITLE: Unsaved Downloads
    CLEAR_CONTENT: "You haven't downloaded your images. Are you sure to delete all pictures?"
    CLEAR_CANCEL: Cancel
    CLEAR_CONFIRM: Clear
    NOT_ENOUGH_TITLE: Insufficient Balance
    NOT_ENOUGH_CONTENT: Not enough Lumens. Try reducing image count or upgrade your plan.
    NOT_ENOUGH_CANCEL: Reduce Image
UPLOAD:
    DRAG_TITLE_BEFORE: Drag or upload up to
    DRAG_TITLE_AFTER: images to start
    FORMAT_DES: "PNG, JPG, JPEG, or WEBP supported. Max size: {limit}MB each."
UPLOAD_ERROR:
    SIZE_PIXEL_EXCEED: "Your image exceeds the limit. Please upload an image under {limit}MB and {maxPixels} million pixels."
    COUNT_EXCEED: "Upload limit reached. Up to {oneTimeMax} images can be successfully uploaded."
    REACHED_THE_MAXIMUM: "You have reached the maximum number of uploads."
    UNKNOWN_ERROR: "Operation failed. Please try again later."
DONE: Done
TOAST_VERSION_ON_WEB: Enhanced version available on web (mobile optimization in progress)
VIP_BENEFITS_10_LUMENS: 10 Lumens
VIP_BENEFITS_1: "{count} reset daily"
SUBSCRIBE_PURCHASE_LUMEN: Purchase Lumens
SUBSCRIBE_TAX: ex.tax
SUBSCRIBE_CURRENT_PLAN: "Current Plan:"
SUBSCRIBE_LUMEN_TOTAL: Total
SUBSCRIBE_LUMEN_BUY_NOW: Buy Now
COMMON_SERVICE_EMAIL: "service{'@'}piclumen.com"
SUBSCRIBE_TITLE_DESC_HIGH_LIGHT: manage subscription
SUBSCRIBE_TITLE_DESC: "Pick a plan that fits your needs, or {count}."
SUBSCRIBE_FREE_TRIAL: "Start {count}-Day Free Trial"
SUBSCRIBE_NEW_USER_EXCLUSIVE: New User Exclusive
VIP_BENEFITS_2: "{count} Lumens per month"
VIP_BENEFITS_UNLIMIT: Unlimited generation
VIP_BENEFITS_3: "{count} in Relax Mode (PicLumen models)"
VIP_BENEFITS_LIMIT: Relax Mode
VIP_BENEFITS_3_BASIC: "Limited image generation in {count} (PicLumen models)"
VIP_BENEFITS_10_COUNT: "{count} images"
VIP_BENEFITS_10: "Save up to {count} in your collection"
VIP_BENEFITS_4_COUNT: creation history for 30 days
VIP_BENEFITS_4: "Access to {count}"
VIP_BENEFITS_DISCOUNT_10: "10% off"
VIP_BENEFITS_DISCOUNT_20: "20% off"
VIP_BENEFITS_DISCOUNT_X: "{count}% off"
VIP_BENEFITS_DISCOUNT: "{count} Lumen purchases"
VIP_BENEFITS_EDIT_TOOLS: Editing tools included
VIP_BENEFITS_6: "Queue up to {count} generations"
VIP_BENEFITS_7: "Generate {count} tasks at once"
VIP_BENEFITS_FULL_HISTORY_COUNT: full creation history
VIP_BENEFITS_FULL_HISTORY: "Access to {count} during subscription"
VIP_BENEFITS_BATCH_COUNT: "{count} images per request"
VIP_BENEFITS_BATCH_GEN: "Generate up to {count}"
VIP_BENEFITS_8: Batch downloads
SUBSCRIBE_DISCOUNT_BASIC: "No discount available in current plan. {count} for discount."
SUBSCRIBE_DISCOUNT_STANDARD: "Get 10% off with current plan. {count} for more savings."
SUBSCRIBE_DISCOUNT_PRO: "Enjoy 20% off with current plan."
SUBSCRIBE_FRIST_GIFT: "First Purchase Bonus {count}"
SUBSCRIBE_LUMEN_INCLIDES: Include Bonus Lumens
SUBSCRIBE_TRIAL_BOUNS: Trial Bonus
SUBSCRIBE_IN_TRIAL: In Trial
SUBSCRIBE_FAQ_1_TITLE: "What can I do with PicLumen?"
SUBSCRIBE_FAQ_1_DESC: "PicLumen offers AI-powered image generation services, allowing user to input prompts, upload guide images, transform prompts/image to desired images. It also enables editing of existing images, including upscale, inpaint, extend and so on. The generated images can be categorized with the embed image manage tool, with the ability to remix and adjust historical images at any time. Thank you for using PicLumen."
SUBSCRIBE_FAQ_2_TITLE: "What are Lumen?"
SUBSCRIBE_FAQ_2_DESC: Lumens are the credits used on PicLumen. Each image generation or editing action will consume a certain number of Lumens.
SUBSCRIBE_FAQ_3_TITLE: "What is unlimited relax generations?"
SUBSCRIBE_FAQ_3_DESC: "It will allow users to continue performing image generation and editing operations even when their Lumen runs out. However, the priority of these tasks will be lowered, resulting in slower image generation speeds. This feature requires a subscription and is only available on PicLumen series models."
SUBSCRIBE_FAQ_4_TITLE: "Can I use the generated-images for commercial purposes?"
SUBSCRIBE_FAQ_4_DESC: "Yes, you can."
SUBSCRIBE_FAQ_5_TITLE: "Does the pricing include tax?"
SUBSCRIBE_FAQ_5_DESC: "No, pricing in this table excludes tax."
SUBSCRIBE_FAQ_6_TITLE: "Can I cancel my purchased Subscription Plan?"
SUBSCRIBE_FAQ_6_DESC: "Yes. We have free Lumen every day for experience features. Please fully experience the product before subscribing. Subscriptions are not refundable. Canceling a subscription means that you will not be subscribing from the next period, but it does not amount to a refund of your current subscription. Upon cancellation of the subscription, all benefits are retained until the end of the subscription period. If an incorrect order occurs, please contact our customer service for processing. E-mail: {count}"
SUBSCRIBE_FAQ_7_TITLE: "Do I get a free trial?"
SUBSCRIBE_FAQ_7_DESC: "Yes! All new paid subscribers receive a 3-day free trial with full access to subscription features."
SUBSCRIBE_FAQ_8_TITLE: "How can I delete my account?"
SUBSCRIBE_FAQ_8_DESC: "In the Settings, there is the function to delete the account. You can delete an account by clicking the Delete Account button. Once the request to delete an account is submitted, the account will be frozen, all features will be unavailable, and the subscription fee already paid will not be refundable. The account will be frozen for 15 days, after which it will be permanently deleted and irrevocably deleted after we meet the data requirements of the relevant authorities."
SUBSCRIBE_FAQ_9_TITLE: "Can I still use PicLumen for free without a subscription?"
SUBSCRIBE_FAQ_9_DESC: "Yes, you can. PicLumen's Basic Plan offers limited free image generation every day."
SUBSCRIBE_FAQ_10_TITLE: "What payment methods can I use?"
SUBSCRIBE_FAQ_10_DESC: "PicLumen uses Stripe to receive payments, which supports a variety of payment methods including credit cards and debit cards."
SUBSCRIBE_FAQ_11_TITLE: "How do I secure my payment?"
SUBSCRIBE_FAQ_11_DESC: "Your payment information is processed through Stripe's secure system, which complies with PCI DSS standards to ensure the safety of your payment details."
SUBSCRIBE_FAQ_12_TITLE: "How do I pay in a currency different from USD?"
SUBSCRIBE_FAQ_12_DESC: "PicLumen uses Stripe to receive payments. Stripe typically handles currency conversion automatically. When you make a payment in a currency other than USD, Stripe will convert the amount to your local currency based on the exchange rate of the day. The exact rate may vary."
SUBSCRIBE_FAQ_13_TITLE: "Why didn’t I receive the full amount of Lumens during the trial?"
SUBSCRIBE_FAQ_13_DESC: "To prevent misuse, trial accounts receive only a partial amount of Lumens corresponding to the 3-day duration. You'll get the full monthly Lumen allocation once your subscription starts officially after the trial."
SUBSCRIBE_FAQ_14_TITLE: "How many bonus Lumens will I get?"
SUBSCRIBE_FAQ_14_DESC: |
    PicLumen offers bonus Lumens on your {count} (e.g. 100, 1,000, 10,000 Lumens). If you buy multiple units of the same Lumen package at once, only the first unit of each size will receive the bonus. For example, if you purchase 5 × 100 Lumens, 2 × 1,000 Lumens, and 1 × 10,000 Lumens in a single transaction, you will receive:  
    • 50 bonus Lumens for the first 100 Lumen package
    • 500 bonus Lumens for the first 1,000 Lumen package
    • 5,000 bonus Lumens for the 10,000 Lumen package
    That’s a total of 5,550 bonus Lumens.
SUBSCRIBE_FAQ_15_TITLE: "When will I be charged Automatically?"
SUBSCRIBE_FAQ_15_DESC: "Subscription fees are charged immediately upon subscription, and then for monthly plans, on the same day each month for renewal; for annual plans, on the anniversary of the subscription start date."
SUBSCRIBE_FAQ_14_DESC_LIGHT: first purchase of each Lumen package size
SUBSCRIBE_FAQ_16_TITLE: "Why was I charged more than the amount shown on the website?"
SUBSCRIBE_FAQ_16_DESC: |
    The price shown on our website is rounded to two decimal places for display. However, the bank calculates the final charge with the exact formula before rounding, which can result in a difference.
    For example, if a Pro user purchases 15 × 10,000 Lumens with a 20% discount:
    $79.99 × 80% × 15 = $63.992 × 15 = $959.88
    (You may see the discounted price $63.99 on our website.)
SUBSCRIBE_FAQ_17_TITLE: "Can I use my generated images for commercial purposes?"
SUBSCRIBE_FAQ_17_DESC: |
    Yes. All images you generated with PicLumen can be used for commercial purposes — as long as they comply with our Terms of Service. However, it is important to note that public images created by others can only be utilized as a point of reference and cannot be directly used for commercial purposes.
    You are responsible for ensuring that their use of generated images does not infringe upon the rights of others. PicLumen does not claim ownership of the generated images and does not provide any warranty or guarantee regarding the originality or legality of the images for commercial use. Users are encouraged to review the relevant local laws and regulations applicable to their intended use of the images.
SUBSCRIBE_FAQ_18_TITLE: "Who owns the images I create?"
SUBSCRIBE_FAQ_18_DESC: "You own the rights to the images you generate using your own prompts. However, you may not claim exclusive ownership of public or commonly shared content generated by others. For details, please refer to our Terms of Service."
SUBSCRIBE_FAQ_19_TITLE: "Are there any restrictions on commercial use?"
SUBSCRIBE_FAQ_19_DESC: "You may not use generated images in ways that infringe on third-party rights, promote harm, or violate our Community Guidelines. Additionally, selling AI-generated images as NFTs may be restricted on some platforms — please check local regulations."
ESTIMATE_COST_LUMENS: "Estimate cost Lumens (Relax mode free)|Estimate cost Lumens|Estimate cost {num} Lumen"

SUBSCRIBE_FAQ_20_TITLE: "Will my subscription price change in the future?"
SUBSCRIBE_FAQ_20_DESC: |
    As long as you keep your subscription active, your price stays the same—even if public pricing increases.
    If you cancel or turn off auto-renew, your plan will end when the current billing cycle finishes. If you resubscribe later, you'll need to pay the current price, and any previous discounts won't apply.
SUB_PLAN_POPUP_TITLE: "Upgrade for Full Access"
SUB_PLAN_POPUP_SEE_ALL: "See All Benefits"
SUB_PLAN_3_DAY: "3-Days Trial"
UPGRADE_PLAN_TITLE: "Upgrade Your Plan"
UPGRADE_PLAN_CONTENT: "Choose a new plan. Any unused Lumens will carry over to your next billing cycle."
DOWNGRADE_PLAN_CONTENT: "Want to downgrade the subscription? Your new plan will take effect on {time} ."
SUBSCRIBE_PAYMENT_METHOD: "Payment Method"

CANCEL_TRIAL_TITLE: "End trial Now?"
CANCEL_TRIAL_CONTENT: "Canceling means losing your new subscriber discount. If you return later, standard pricing will apply."
CANCEL_TRIAL_CONFIRM: Cancel Anyway
CANCEL_TRIAL_CLOSE: Keep Trial
LUMENS_RECORDS:
    TITLE: Lumens Records
    TAB_LUMENS: Lumens Records
    TAB_PURCHASE: Purchase Records
    SOURCE_NAME: Source Name
    DETAILS: Details
    COST_OR_GET_LUMENS: Cost/Get Lumens
    TIME: Time
    COST: Cost

VIP_TRIAL:
    VIEW:
        TITLE: Get Free Trial
        NOTICE_1: $0 for a 3-day trial
        NOTICE_2: Unlimited generations
    TITLE: Free Trial Includes
    LEFT:
        TITLE_1: Today - Your free trial begins
        CONTENT_1: Pay $0 to enjoy 3-day unlimited image generation. Cancel anytime during the trial.
        TITLE_2: Free trial ends
        CONTENT_2: Your subscription will renew the next day.
    RIGHT:
        INCLUDE_1: Limited-time discount for new subscribers
        INCLUDE_2: "{content_str} to start fast creating|Free Lumens"
        INCLUDE_3: "Generate {count} tasks at once"
        INCLUDE_4: "Queue up to {count} generations"
        INCLUDE_MO_1: Limited-time discount for new subscribers
        INCLUDE_MO_2: "{num1} for a {key1} trial"
        INCLUDE_MO_2_NUM1: $0
        INCLUDE_MO_2_KEY1: 3-day
        INCLUDE_MO_3: "Unlock {content}|all features"
        INCLUDE_MO_4: Cancel anytime
        LEARN_MORE: Learn More
        MONEY_DESC: "Free trial includes Standard Plan only. After it ends, your subscription will auto-renew at ${num1}-/year (${num2}-/month)."
        BUTTON: Free Trial
COLLECTION_MODAL:
    INPUT_NAME_PLACEHOLDER: "e.g: Anime Characters"
    INPUT_DESC_PLACEHOLDER: "e.g: My favorite anime characters"
OVERFLOW_FOLDING:
    SHOW_LESS: Show Less
    SHOW_MORE: Show More
COLLECTION:
    SELECT_IMAGE: Select Image
    X_PHOTO_SELECTED: "{count} photo selected"
    NO_SELECT_IMAGE: No image selected
    NO_COLLECTION_SELECTED: No collection selected
EDIT_IMAGE:
    GENERATE: Generate
    EDIT_IMAGE: Edit
    PROMPT: Turn the image into a drawing or painting... | Change the image style to... | Add or remove things in the image...
USER_RETURN_BORNS:
    OLD_USER_RETURN_BORNS_TITLE: Return Bonus
    OLD_USER_RETURN_BORNS_DEST_1: Up to 70% off
    OLD_USER_RETURN_BORNS_DEST_2: Unlock elite-tier features
ANNIVERSARY_BANNER:
    TITLE: 62% OFF Deal for Our Birthday | 62% OFF | Deal for Our Birthday — Claim Yours Before It Ends!
    SUBSCRIBE: Subscribe
    CARD_TITLE: Birthday Sale
    CARD_DESC: 62% OFF—Once-in-a-lifetime discount!
ANNIVERSARY_MODAL:
    SUBSCRIBE: Subscribe
    TITLE: IT'S OUR BIRTHDAY
    CONTENT: PicLumen's 1st birthday sale is finally here—grab {count}! This once-in-a-lifetime discount won't come around again!
    CONTENT_COUNT: 62% OFF
SEO_META:
    SEO_EXPLORE_TITLE: "Create & Share Stunning AI-Generated Images  {'|'} PicLumen Community"
    SEO_EXPLORE_DESC: "Join a vibrant community of AI creators on PicLumen. Share your stunning images, discover others' artworks, and take part in AI-powered creativity."

    SEO_CREATE_TITLE: "Generate Images in PicLumen"
    SEO_CREATE_DESC: "Use PicLumen's AI image creator to bring your ideas to life in the highest quality."

    SEO_GALLERY_TITLE: "AI Image Gallery by {Username} {'|'} PicLumen"
    SEO_GALLERY_DESC: "View all your generated images in PicLumen."

    SEO_TOOLS_TITLE: "AI Toolkit for Image Editing {'|'} PicLumen"
    SEO_TOOLS_DESC: "Use PicLumen's toolsets to edit your images or enhance your image generation in seconds."

    SEO_RM_BG_TITLE: "Remove Background from Photo with PicLumen"
    SEO_RM_BG_DESC: "Upload images to remove the background from a photo in seconds—support batch background removal."

    SEO_DESCRIBE_TITLE: "Image to Text: Describe Image with PicLumen"
    SEO_DESCRIBE_DESC: "Upload images to access the image content for use as image generation prompts in seconds with PicLumen."

    SEO_TASK_TITLE: "Rewards and Task Center {'|'} PicLumen"
    SEO_TASK_DESC: "Get free Lumens for image generation by finishing the reward tasks in PicLumen."

    SEO_ACTIVITY_TITLE: "Join AI Art Challenges & Creative Contests {'|'} PicLumen"
    SEO_ACTIVITY_DESC: "Explore ongoing and past AI art challenges on PicLumen. Share your AI-generated creations, join themed contests, and get featured in the creative community."

    SEO_ACTIVITY_DETAIL_TITLE: "AI Art Challenge on PicLumen"
    SEO_ACTIVITY_DETAIL_DESC: "Join our AI art challenge on PicLumen. Submit your AI-generated images to showcase your creativity and win rewards."

    SEO_COMMUNITY_DETAIL_TITLE: "AI Image Generated by {Username} {'|'} PicLumen"
    SEO_COMMUNITY_DETAIL_DESC: "Explore this unique AI-generated image by PicLumen fan. Like, comment, and get inspired by thousands of creators."

    SEO_SELF_PROFILE_TITLE: "My Profile Page {'|'} PicLumen"
    SEO_SELF_PROFILE_DESC: "See your creations on your profile page in PicLumen."

    SEO_OTHERS_PROFILE_TITLE: "Explore {Username}'s AI Image Creations {'|'} PicLumen"
    SEO_OTHERS_PROFILE_DESC: "View the latest AI-generated artworks by {Username} on PicLumen. Follow their profile, explore their creative journey, and share your favorite pieces."

    SEO_COMMUNITY_SHARE_TITLE: "Discover an AI Image by {Username} {'|'} PicLumen"
    SEO_COMMUNITY_SHARE_DESC: "Explore this AI-generated masterpiece by {Username}. Ready to create your own? Join PicLumen's creative community and get started now."

    SEO_ACCOUNT_TITLE: "Account Setting {'|'} PicLumen"
    SEO_ACCOUNT_DESC: "Manage your PicLumen account settings."

    SEO_LOGIN_TITLE: "Login to PicLumen"
    SEO_LOGIN_DESC: "Access your PicLumen account to create, share, and manage AI-generated images."

    SEO_MESSAGE_TITLE: "Message Center - Likes, Comments & Announcements {'|'} PicLumen"
    SEO_MESSAGE_DESC: "Check your latest messages, including likes, comments, and platform announcements in PicLumen's message center."

DEL_ACC:
    GO_SUBSCRIPTION: "Go Subscription"
    BTN_CLOSE: "Close"

    DEL_ACCOUNT_1: "We noticed that your account still has an active subscription with auto-renewal enabled."
    DEL_ACCOUNT_2: "To proceed with account deletion and avoid any future charges, please cancel your subscription first."
    DEL_ACCOUNT_3: "If you subscribed via the Apple App Store, kindly cancel it directly through your App Store settings."

    DEL_ACCOUNT_CONTENT: "Once confirmed, your account will be permanently deactivated, and you’ll no longer be able to access our services. This action is irreversible. You’ll receive a confirmation email once the deletion process is complete."
    DEL_ACCOUNT_CANCEL: "Not Now"
    DEL_ACCOUNT_CONFIRM: "Yes, Delete Account"
    DEL_ACCOUNT_PLACEHOLDER: "Email address for receiving notifications"
SUB_CALLBACK_REFRESH: "Please refresh the page. If your payment was completed but benefits haven’t appeared, email us at {email} — we’ll help you out right away."
BTN_SEND_EMAIL: "Send Email"
SUB_CALLBACK_PENDING: "The confirmation should happen within a few seconds. If it takes longer than 3 minutes, try refreshing the page, or feel free to contact us at {email} for assistance."
REWAEDS_CENTER:
    UPGRADE_PLAN_BTN: Upgrade Plan
    UPGRADE_PLAN_DESC_PC: Upgrade now to unlock more privileges - 50% OFF for new users.
    UPGRADE_PLAN_DESC_MO: Upgrade now - 50% OFF for new users

PICNAV_USERINFO:
    VIP_BOX:
        BASIC: "Subscribe to enjoy more privileges."
        STANDARD: "Enabled all the main features."
        PRO: "All features are available. Enjoy"
        UPGRADE_BTN: Upgrade
        ACTIVE_PLAN: Active Plan

SUBSCRIBE_NO_PAY_CHANNELS: No payment methods are currently available. Please check back soon.
TASK_BANNER:
    TITLE_PC: Upgrade now to unlock more privileges - 50% OFF for new users.
    TITLE_MO: Upgrade now - 50% OFF for new users
    BTN: Upgrade Plan
KONTEXT_MODEL:
    TITLE: Quick|Editing Image
    DESC: Modify your image by simply describing what you want to change — no technical skills needed!
    GET_STARTED: Get Started
NEW_MODEL_PREVIEW:
    XXX_ALIVE: "{name} is alive!"
    CREATE_IMAGE_WITH: "Create images fast with {name}."
    CREATE_STUNNNING_ANIME_SCENES: Create stunning anime scenes.
    NEXT_MODEL: Next Model
    DESC_NAMIYA_1: ✨ Refined anime look with cinematic lighting.
    DESC_NAMIYA_2: ✨ Story-driven compositions.
    DESC_KONTEXT_1: 👉  Strong Context & Character Consistency.
    DESC_KONTEXT_2: 👉  Hybrid Generation + Editing.
    DESC_KREA_0:  Make AI images that don't look AI.
    DESC_KREA_1: ✨ Focus on aesthetic photography.
    DESC_KREA_2: ✨  Overcome the common "AI look" problem.
