<script setup>
import { SUBSCRIBE_TYPE, BILLING_TYPE, PAYMENT_METHOD, PRICE_MAPPING } from "@/utils/constant.js";

import { useSubscribeStore } from "@/stores/subscribe.js";
import { I18nT, useI18n } from "vue-i18n";

import { useThemeStore } from "@/stores/system-config";
import { useCallSubscribePlan, useSubscribeErrorModal } from "@/hook/subscribe.js";
import { storeToRefs } from "pinia";
import { useMessageBox } from "@/hook/subscribe";
const { t } = useI18n({ useScope: "global" });
const { isMobile } = storeToRefs(useThemeStore());
const subscribeStore = useSubscribeStore();
const { clearMessageBox } = useMessageBox();
const { isHaveFirstBuySub } = storeToRefs(subscribeStore);

const allowFreeTry = computed(() => {
    return isHaveFirstBuySub.value;
});

const props = defineProps({
    triggerEl: {
        type: String,
        default: "other",
    },
});

const emits = defineEmits(["close"]);
const { showErrorModal } = useSubscribeErrorModal();

const vipBenefits = [
    {
        iconName: "IconVipStandard",
        plan: SUBSCRIBE_TYPE.STANDARD,
        planAlias: "SUBSCRIBE_PLAN_STANDARD",
        vipLevelStyle: "standard-style",
        list: [
            { name: "VIP_BENEFITS_2", i18nCount: "2,000" },
            { name: "VIP_BENEFITS_3", i18nKey: "VIP_BENEFITS_UNLIMIT" },
            { name: "VIP_BENEFITS_DISCOUNT", i18nKey: "VIP_BENEFITS_DISCOUNT_10" },
            { name: "VIP_BENEFITS_6", i18nCount: 5 },
            { name: "VIP_BENEFITS_7", i18nCount: 2 },
        ],
    },
    {
        iconName: "IconVipPro",
        plan: SUBSCRIBE_TYPE.PRO,
        planAlias: "SUBSCRIBE_PLAN_PRO",
        vipLevelStyle: "pro-style",
        list: [
            { name: "VIP_BENEFITS_2", i18nCount: "5,000" },
            { name: "VIP_BENEFITS_3", i18nKey: "VIP_BENEFITS_UNLIMIT" },
            { name: "VIP_BENEFITS_DISCOUNT", i18nKey: "VIP_BENEFITS_DISCOUNT_20" },
            { name: "VIP_BENEFITS_6", i18nCount: 10 },
            { name: "VIP_BENEFITS_7", i18nCount: 5 },
        ],
    },
];
const i18nValue = computed(() => {
    return ({ i18nCount, i18nKey }) => {
        if (i18nKey) {
            return t(i18nKey, { count: i18nCount });
        }
        if (i18nCount) {
            return i18nCount;
        }

        return "";
    };
});
const currBilling = ref(0);
const subscribePlanList = [
    {
        billing: "SUBSCRIBE_TAB_YEAR",
        unit: BILLING_TYPE.YEARLY,
    },
    {
        billing: "SUBSCRIBE_TAB_MONTH",
        unit: BILLING_TYPE.MONTHLY,
    },
];
const tabSlider = ref();
const handleTabChange = (tabIndex) => {
    currBilling.value = tabIndex;
    tabSlider.value.style.transform = `translateX(${tabIndex}00%)`;
};

const checkedPlan = ref(SUBSCRIBE_TYPE.PRO); // 默认选择的 VIP 计划
//选择 VIP 计划
const chooseVipPlan = (plan) => {
    checkedPlan.value = plan;
};

const customTrackEvent = (el) => {
    window.trackEvent("Commercialization", { el });
};

//点击购买
const loading = ref(false);
const payment = ref("");
const handleBuy = async (paymentMethod) => {
    if (loading.value) return;
    loading.value = true;
    payment.value = paymentMethod;
    const priceInterval = subscribePlanList[currBilling.value].unit;

    if (props.triggerEl === "old_vip_back") {
        customTrackEvent("expired_member_popup_subscribe_stripe");
    }

    try {
        const res = await useCallSubscribePlan({
            product: checkedPlan.value,
            priceInterval,
            paymentMethod,
        });

        if (res.code !== 0) {
            showErrorModal("SUBSCRIBE_EX_TEXT", res);
        }
    } catch (error) {
        showErrorModal("SUBSCRIBE_EX_TEXT", { code: 500 });
    } finally {
        loading.value = false;
        payment.value = "";
    }
};

// 根据产品类型和周期计算价格和折扣
const productList = computed(() => {
    const { unit } = subscribePlanList[currBilling.value];

    const billing = { ...PRICE_MAPPING[unit] };

    return Object.keys(billing)
        .map((key) => {
            return {
                key,
                ...billing[key],
                ...subscribeStore.getUserPromotionByUnit(key, unit), //根据接口获取到套餐折扣的真价格，如果存在则覆盖前端的金额
            };
        })
        .filter((item) => item.key !== SUBSCRIBE_TYPE.BASIC)
        .sort((a, b) => b.ORIGINAL - a.ORIGINAL); // 排除 BASIC 计划，并按价格降序排序
});

const handleSeeAll = () => {
    clearMessageBox("create_ImageEdit");
    emits("close");
    if (props.triggerEl === "old_vip_back") {
        customTrackEvent("expired_member_popup_more");
    }
};

const channels = ref([]); // 支付方式列表
//获取本地支付方式
const getSupportPayChannels = async () => {
    channels.value = await subscribeStore.getPaymentChannels();
    if (channels.value.length === 0) {
        showErrorModal("SUBSCRIBE_NO_PAY_CHANNELS");
    }
};

const showPaymentChannels = computed(() => {
    return (payment) => channels.value.includes(payment);
});

getSupportPayChannels();
</script>

<template>
    <div class="flex max-md:flex-col md:grid md:grid-cols-2">
        <div v-if="!isMobile" class="hidden md:grid grid-cols-2 text-sm font-medium relative">
            <div v-for="item in vipBenefits" :key="item.plan" class="pt-14 pb-[86px] px-4" :class="[item.vipLevelStyle]">
                <div class="flex flex-col items-center justify-center gap-2">
                    <n-icon size="40">
                        <IconsVipStandard v-if="item.iconName === 'IconVipStandard'" />
                        <IconsVipPro v-if="item.iconName === 'IconVipPro'" />
                    </n-icon>
                    <span class="text-xl font-semibold text-text-2 bg-clip-text plan-name-style">{{ $t(item.planAlias) }}</span>
                </div>
                <div class="space-y-4 mt-10">
                    <div class="flex gap-2" v-for="(inner, index) in item.list" :key="index">
                        <IconsSuccess class="shrink-0 text-xl text-success-6" />
                        <div class="text-sm font-medium text-text-3">
                            <i18n-t :keypath="inner.name">
                                <template v-slot:count>
                                    <span class="text-primary-6 mx-0.5">{{ i18nValue(inner) }}</span>
                                </template>
                            </i18n-t>
                        </div>
                    </div>
                </div>
            </div>

            <NuxtLinkLocale @click="handleSeeAll" to="/user/subscribe" class="text-text-5 absolute bottom-8 left-1/2 -translate-x-1/2 cursor-pointer hover:text-text-3">
                {{ $t("SUB_PLAN_POPUP_SEE_ALL") }}
            </NuxtLinkLocale>
        </div>

        <div class="p-6 relative flex flex-col">
            <div
                class="size-8 rounded-full absolute top-4 right-4 flex items-center justify-center bg-fill-wd-1 md:bg-transparent hover:bg-fill-wd-1 text-text-4 hover:text-text-2 cursor-pointer transition-colors"
                @click="$emit('close')"
            >
                <IconsClose class="text-xl text-text-2" />
            </div>

            <div class="pt-8 font-semibold text-base text-text-3 text-center">{{ $t("SUB_PLAN_POPUP_TITLE") }}</div>
            <div class="my-8 h-11 bg-fill-tab-4 rounded-full p-0.5 w-full md:w-60 mx-auto">
                <div class="h-full flex justify-center relative gap-0.5 text-text-4">
                    <div ref="tabSlider" class="w-1/2 rounded-full transition-transform duration-500 bg-fill-tab-3 absolute left-0 top-0 bottom-0"></div>
                    <div
                        v-for="(tabItem, tabIndex) in subscribePlanList"
                        :key="tabIndex"
                        :class="{ 'text-text-1': currBilling === tabIndex }"
                        class="w-1/2 h-10 md:h-full flex justify-center items-center rounded-full text-sm font-medium cursor-pointer select-none transition-colors duration-500 relative"
                        @click="handleTabChange(tabIndex)"
                    >
                        <span>{{ $t(tabItem.billing) }}</span>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <div
                    v-for="item in productList"
                    :key="item.key"
                    class="rounded-xl relative py-5 flex items-center justify-between cursor-pointer px-6 border-2 border-solid border-border-2 transition-colors"
                    :class="[checkedPlan === item.key ? '!bg-fill-t-5 !border-border-t-5' : '', `${item.key}-level-style`]"
                    @click="chooseVipPlan(item.key)"
                >
                    <SaleBadge class="sale-bar-style text-xs !font-medium !h-5 !px-2 -translate-y-1/2">
                        <!-- <span>{{ $t("SUBSCRIBE_PRICE_OFF", { sale: allowFreeTry ? item.FIRST_SALE : item.SALE }) }}</span> -->
                        <span>{{ $t("SUBSCRIBE_PRICE_OFF", { sale: item.SALE }) }}</span>
                    </SaleBadge>
                    <span class="text-xl font-semibold text-text-2 bg-clip-text plan-name-style">{{ $t(item.PLAN_KEY) }}</span>
                    <div class="flex items-center gap-1.5 text-xs font-normal">
                        <span class="text-text-3 line-through">${{ item.ORIGINAL }}</span>
                        <span class="text-text-1 text-xl font-semibold">${{ item.CURRENT }}</span>
                        <span class="text-text-4">{{ $t("SUBSCRIBE_UNIT_MONTH") }}</span>
                    </div>
                </div>
            </div>

            <div class="w-full mt-5 mb-5 md:mt-auto md:mb-0 text-text-3 flex flex-col items-center">
                <NuxtLinkLocale v-if="isMobile" @click="handleSeeAll" to="/user/subscribe" class="md:hidden block text-text-3 pb-[18px]">
                    {{ $t("SUB_PLAN_POPUP_SEE_ALL") }}
                </NuxtLinkLocale>

                <div class="w-full grid gap-3" :class="{ 'md:grid-cols-2': channels.length > 1 }">
                    <Button
                        v-if="showPaymentChannels(PAYMENT_METHOD.STRIPE)"
                        :disabled="loading"
                        :loading="loading && payment === PAYMENT_METHOD.STRIPE"
                        type="primary"
                        class="relative pro-level-style"
                        :class="{ ' w-3/4 mx-auto': channels.length === 1 }"
                        @click="handleBuy(PAYMENT_METHOD.STRIPE)"
                    >
                        <SaleBadge v-if="allowFreeTry" class="sale-bar-style !font-medium !h-[18px] !px-2" :is-translate-y="false">
                            <span class="text-xs scale-90">{{ $t("SUB_PLAN_3_DAY") }}</span>
                        </SaleBadge>
                        <IconsStripe class="w-6 h-6 mr-1" />
                        <span>Stripe</span>
                    </Button>
                    <Button
                        v-if="showPaymentChannels(PAYMENT_METHOD.PAYPAL)"
                        :disabled="loading"
                        :loading="loading && payment === PAYMENT_METHOD.PAYPAL"
                        type="secondary"
                        :class="{ ' w-3/4 mx-auto': channels.length === 1 }"
                        @click="handleBuy(PAYMENT_METHOD.PAYPAL)"
                    >
                        <IconsPaypal class="w-6 h-6 mr-1" />
                        <span>Paypal</span>
                    </Button>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.standard-level-style,
.standard-style {
    background: linear-gradient(180deg, rgba(135, 255, 187, 0.08) 0%, rgba(135, 255, 187, 0) 100%), var(--p-bg-2, #121216);
    .plan-name-style {
        background-image: linear-gradient(90deg, #00c19a 0%, #20b761 100%);
    }
    .sale-bar-style {
        background: linear-gradient(90deg, #00c19a 0.17%, #20b761 100%);
    }
}
.pro-level-style,
.pro-style {
    background: linear-gradient(180deg, rgba(255, 185, 79, 0.08) 0%, rgba(255, 185, 79, 0) 100%), var(--p-bg-2, #121216);
    .plan-name-style {
        background-image: linear-gradient(90deg, #fa0 0%, #ff6c3f 100%);
    }
    .sale-bar-style {
        background: linear-gradient(270deg, #ff3700 0%, #ff9417 99.83%);
    }
}
.standard-level-style,
.pro-level-style,
.standard-style,
.pro-style {
    .plan-name-style {
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .sale-bar-style {
        border-radius: 2px 16px;
    }
}
.standard-level-style,
.pro-level-style {
    background: none;
}
</style>
