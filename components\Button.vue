<template>
    <!-- DEMO 
   <Button type="primary" size="medium">
        <span>{{ $t("FEATURE_DESCRIBE_FEATURE_TITLE") }}</span>
    </Button>
  -->
    <n-button type="primary" :bordered="false" class="flex items-center justify-center font-medium disabled:opacity-100 !outline-none" :class="[...buttonClass]" secondary>
        <slot name="icon"></slot>
        <slot></slot>
    </n-button>
</template>

<script setup>
const sizeClass = {
    small: "h-8 px-3 py-[7px] text-xs",
    medium: "min-h-10 px-4 py-[9px] text-sm",
    large: "min-h-11 px-4 py-[10px] text-base ",
};
const typeClass = {
    primary: "!bg-primary-6 !text-white hover:!bg-primary-5 active:!bg-primary-7 disabled:!bg-primary-3 disabled:!text-text-t-5",
    secondary: "!bg-fill-btn-1 !text-text-2 hover:!bg-fill-btn-2 active:!bg-fill-btn-3 disabled:!bg-fill-btn-4 disabled:!text-text-6",
    tertiary: "!bg-fill-btn-5 !text-primary-6 hover:!bg-fill-btn-6 active:!bg-fill-btn-7 disabled:!bg-fill-btn-8 disabled:!text-text-6",
    danger: "!bg-danger-6 !text-white hover:!bg-danger-5 active:!bg-danger-7 disabled:!bg-danger-3 disabled:!text-text-t-5",
    textPrimary: "!bg-transparent !text-primary-6 hover:!bg-primary-1 active:!bg-primary-2 disabled:!bg-transparent disabled:!text-text-6",
    textSecondary: "!bg-transparent !text-text-2 hover:!bg-fill-btn-2 active:!bg-fill-btn-3 disabled:!bg-transparent disabled:!text-text-6",
};

const roundedClass = {
    default: "rounded-lg",
    lg: "rounded-lg",
    md: "rounded-md",
    full: "rounded-full",
};

const props = defineProps({
    type: {
        type: String,
        default: "primary",
        validate: (value) => {
            return true;
        },
    },
    size: {
        type: String,
        default: "medium",
        validate: (value) => {
            return true;
        },
    },
    rounded: {
        type: String,
        default: "full",
        validate: (value) => {
            return true;
        },
    },
});
const buttonClass = computed(() => {
    return [sizeClass[props.size] || "", typeClass[props.type] || "", roundedClass[props.rounded] || ""];
});
</script>

<style lang="scss" scoped></style>
