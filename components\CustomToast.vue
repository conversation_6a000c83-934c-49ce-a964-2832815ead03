<template>
    <div class="p-message-wrapper">
        <TransitionGroup name="p-message" tag="div" :class="isMobile && 'w-full'">
            <div v-for="item in toasts" :key="item.id" class="p-message p-message--show gap-2 text-text-3 lg:max-w-4xl max-w-[80%]">
                <svg class="shrink-0 w-[22px] h-[22px]" v-if="item.type === 'error'" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path
                        d="M11.0007 20.1693C16.0633 20.1693 20.1673 16.0652 20.1673 11.0026C20.1673 5.93999 16.0633 1.83594 11.0007 1.83594C5.93804 1.83594 1.83398 5.93999 1.83398 11.0026C1.83398 16.0652 5.93804 20.1693 11.0007 20.1693Z"
                        fill="#F76965"
                    />
                    <path d="M13.75 8.25L8.25 13.75" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M8.25 8.25L13.75 13.75" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                </svg>

                <svg class="shrink-0 w-[22px] h-[22px]" v-else-if="item.type === 'success'" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path
                        d="M11.0007 20.1693C16.0633 20.1693 20.1673 16.0652 20.1673 11.0026C20.1673 5.93999 16.0633 1.83594 11.0007 1.83594C5.93804 1.83594 1.83398 5.93999 1.83398 11.0026C1.83398 16.0652 5.93804 20.1693 11.0007 20.1693Z"
                        fill="#40B745"
                    />
                    <path d="M7.33398 11.4609L9.62565 13.7526L14.6673 8.71094" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <svg class="shrink-0 w-[22px] h-[22px]" v-else-if="item.type === 'warning'" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path
                        d="M11.0007 20.1693C16.0633 20.1693 20.1673 16.0652 20.1673 11.0026C20.1673 5.93999 16.0633 1.83594 11.0007 1.83594C5.93804 1.83594 1.83398 5.93999 1.83398 11.0026C1.83398 16.0652 5.93804 20.1693 11.0007 20.1693Z"
                        fill="#FF9626"
                    />
                    <path d="M11 7.33594V11.4609" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M11 14.6641V15.1224" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <svg class="shrink-0 w-[22px] h-[22px]" v-else xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path
                        d="M11.0007 20.1693C16.0633 20.1693 20.1673 16.0652 20.1673 11.0026C20.1673 5.93999 16.0633 1.83594 11.0007 1.83594C5.93804 1.83594 1.83398 5.93999 1.83398 11.0026C1.83398 16.0652 5.93804 20.1693 11.0007 20.1693Z"
                        fill="#7B57E5"
                    />
                    <path d="M11 6.875V7.33333" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M11 10.5391V14.6641" stroke="#F9F9F9" stroke-width="1.83333" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span class="flex-1">{{ item.message }}</span>
                <span
                    v-if="item.duration === -1"
                    @click="hide(item.id)"
                    class="mb-auto text-base w-[22px] h-[22px] rounded-full cursor-pointer text-text-4 hover:bg-fill-wd-1 hover:text-text-2 flex items-center justify-center"
                >
                    <IconsClose />
                </span>
            </div>
        </TransitionGroup>
    </div>
</template>

<script setup>
//获取状态和方法
import { useToast } from "~/composables/useToast";
const { toasts, hide } = useToast();
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());
</script>

<style scoped>
.p-message-wrapper {
    pointer-events: none;
    position: fixed;
    top: 24px;
    left: 0;
    right: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.p-message {
    pointer-events: all;
    min-width: 180px;
    margin: 8px auto;
    padding: 12px 12px;
    border-radius: 8px;
    box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.12), 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
    background: var(--p-bg-6);
    display: flex;
    align-items: center;
    transition: none;
    user-select: none;
    will-change: opacity, transform;
    opacity: 1;
    border: 1px solid var(--p-border-t-1, rgba(255, 255, 255, 0.05));
}

/* 动画，完全还原 naive-ui useMessage */
.p-message-enter-active,
.p-message-leave-active {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), margin 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.p-message-enter-from,
.p-message-leave-to {
    opacity: 0;
    transform: translateY(16px) scale(0.98);
    max-height: 0;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
}
.p-message-enter-to,
.p-message-leave-from {
    opacity: 1;
    transform: translateY(0) scale(1);
    max-height: 500px;
    margin-top: 8px;
    margin-bottom: 8px;
    padding-top: 12px;
    padding-bottom: 12px;
}
</style>
