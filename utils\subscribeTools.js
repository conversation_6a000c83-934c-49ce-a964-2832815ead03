import { LUMEN_EXPEND_DICT, PLATFORM_TYPE, SUBSCRIBE_PARAMS, VIP_WEIGHT } from "@/utils/constant.js";

/**
 * 订阅 -- 请求订阅会话参数
 *
 * @description
 * {
 *     "platform": "piclumen",
 *     "successUrl": "http://************:3001/app/user/subscribe",
 *     "cancelUrl": "http://************:3001/app/user/subscribe",
 *     "stripeItems": [
 *         {
 *             "product": "standard", // standard，pro
 *             "price": "month", // year, month
 *             "amount": "", // long
 *             "type": "PLAN" // plan, one
 *         }
 *     ]
 * }
 *
 * @param {string} type
 * @param {object} params
 * @param (object) successCallBackParams
 * @returns {object}
 * @example
 *
 */
export const formatSubscribeParams = (type, params = {}, successCallBackParams = {}) => {
    // plan, one -> amount 不能为null
    params.stripeItems = params.stripeItems.map((item) => ({
        ...item,
        type: SUBSCRIBE_PARAMS[type],
    }));
    const isPaypal = successCallBackParams.paymentMethod === "paypal";
    const successCallBackParamsMapping = [];
    for (const successCallBackParamsStrKey in successCallBackParams) {
        successCallBackParamsMapping.push(successCallBackParamsStrKey + "=" + successCallBackParams[successCallBackParamsStrKey]);
    }
    const successCallBackParamsStr = successCallBackParamsMapping.join("&");
    const successUrl = successCallBackParamsStr ? SUBSCRIBE_PARAMS.SUCCESS_CALLBACK() + "?" + successCallBackParamsStr : SUBSCRIBE_PARAMS.SUCCESS_CALLBACK();
    if (isPaypal) {
        return {
            platform: SUBSCRIBE_PARAMS.PLATFORM,
            successUrl,
            cancelUrl: SUBSCRIBE_PARAMS.FAIL_CALLBACK(),
            items: params.stripeItems.map((item) => ({
                type: SUBSCRIBE_PARAMS[type], //订阅：plan，一次性购买Lumen：one,
                product: item.product, // 产品等级: standard pro
                priceInterval: item.price, //价格间隔：year, month
                lumen: item.lumen, // 购买的lumen规格
                qty: item.amount, // 购买的lumen数量
            })),
        };
    }
    return {
        platform: SUBSCRIBE_PARAMS.PLATFORM,
        successUrl,
        cancelUrl: SUBSCRIBE_PARAMS.FAIL_CALLBACK(),
        stripeItems: params.stripeItems,
    };
};

/**
 * 根据平台会员权重获取当前订阅信息
 *
 * @param {Array} subList 订阅列表
 * @param {String} platforms 支持的平台列表
 */
export const getPlatformVipToWeight = (subList, platforms) => {
    const platformSubList = subList.filter((item) => platforms.includes(item.vipPlatform));
    if (platformSubList.length === 0) {
        return [];
    }
    return platformSubList
        .map((item) => ({
            ...item,
            weight: VIP_WEIGHT[item.priceInterval] + VIP_WEIGHT[item.planLevel],
        }))
        .sort((a, b) => {
            if (b.weight !== a.weight) {
                return b.weight - a.weight;
            } else {
                // 权重相同时 stripe 优先（stripe 支持的能力更丰富）
                const aPlatform = a.vipPlatform === "stripe" ? 1 : 0;
                const bPlatform = b.vipPlatform === "stripe" ? 1 : 0;
                return bPlatform - aPlatform;
            }
        }); // 计算权重并排序
};

/**
 * 多平台时判断是否允许显示订阅按钮
 *
 * @param subList
 * @returns {boolean}
 */
export const multiSubAllowShow = (subList) => {
    const existOtherSub = subList.some((item) => PLATFORM_TYPE.OTHER.includes(item.vipPlatform)); // 存在IOS订阅
    const existWebSub = subList.some((item) => PLATFORM_TYPE.WEB.includes(item.vipPlatform)); // 存在WEB订阅
    // 只存在IOS订阅,不显示按钮
    if (existOtherSub && !existWebSub) {
        return false;
    }
    // IOS与WEB订阅都存在
    if (existOtherSub && existWebSub) {
        const iosSubInfo = subList.find((item) => PLATFORM_TYPE.OTHER.includes(item.vipPlatform));
        const webSubInfo = subList.find((item) => PLATFORM_TYPE.WEB.includes(item.vipPlatform));
        const iosVipWeight = VIP_WEIGHT[iosSubInfo.planLevel] + VIP_WEIGHT[iosSubInfo.priceInterval];
        const webVipWeight = VIP_WEIGHT[webSubInfo.planLevel] + VIP_WEIGHT[webSubInfo.priceInterval];
        return iosVipWeight <= webVipWeight; // ios订阅订阅等级比web小或等于显示按钮
    }
    return true;
};

// 处理金额 实现 5 舍6 入保留两位小数
export const round56 = (num) => {
    num = Number(num);
    if (isNaN(num)) return "";
    const int100 = Math.floor(num * 100); // 前两位整数
    const int1000 = Math.floor(num * 1000); // 三位整数
    const third = int1000 - int100 * 10; // 第三位小数

    if (third <= 5) {
        // 5舍
        return (int100 / 100).toFixed(2);
    } else {
        // 6入
        return ((int100 + 1) / 100).toFixed(2);
    }
};
