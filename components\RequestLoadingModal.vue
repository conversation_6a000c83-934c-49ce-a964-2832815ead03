<script setup>
import { usePurchaseSuccessModal, useSubscribeErrorModal } from "@/hook/subscribe.js";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { storeToRefs } from "pinia";
import { reqPollLumenNum, reqUserVipInfo } from "@/api";
import { useAsyncUserCount } from "@/hook/updateAccount.js";
import { I18nT } from "vue-i18n";

const emits = defineEmits(["close"]);
const props = defineProps({
    loopType: {
        type: String,
        default: "subPlan", // buyLumen, subPlan, upgradePlan
    },
    paymentMethod: {
        type: String,
        default: "stripe", // 支付方式 [stripe, paypal]
    },
});

const subscribeStore = useSubscribeStore();
const { vipInfo } = storeToRefs(subscribeStore);
// const { showErrorModal } = useSubscribeErrorModal();
const { openModal: openPurchaseSuccess } = usePurchaseSuccessModal();

/**
 * 公共轮询函数
 * @param {object} params 接口参数
 * @param {function} reqFn 接口
 * @param {function} assertFn 断言函数
 * @param {number} pollCount 轮询初始值
 * @returns {Promise<T|*>} 成功或失败
 */
const maxPollCount = 40; // 最大轮询次数 40次 * 3s = 120s
const reqPoll = async (params, reqFn = async () => {}, assertFn = () => {}, pollCount = 0) => {
    const res = await reqFn(params).catch((err) => err);
    if (res.status !== 0 || pollCount >= maxPollCount) return Promise.reject(res);
    if (assertFn(res || {})) return Promise.resolve(res);
    pollCount++;
    await new Promise((resolve) => setTimeout(() => resolve(), 3000));
    return reqPoll(params, reqFn, assertFn, pollCount);
};

/**
 * 轮询出错
 * @param {string} msg
 * @param {number} code
 */
const showError = ref(false);
const handleClose = (msg, code) => {
    emits("close");
    // showErrorModal(msg, { code });
};

/**
 * 订阅轮询
 *
 * @description 升级订阅触发此函数意味着没有跳出到支付界面，不确定升级成功还是失败，所以采用接口比对
 *
 * @param {boolean} isSub 是订阅还是升级
 */
const subOrUpgradePlanResultPoll = (isSub = true) => {
    reqPoll({ vipPlatform: props.paymentMethod }, reqUserVipInfo, ({ data }) => {
        let { vipType, priceInterval } = data || {};
        priceInterval === "" && (priceInterval = null);
        if (isSub) {
            try {
                let subscriptionTemp = sessionStorage.getItem("subscriptionTemp"); // 获取点击订阅是缓存的订阅信息
                let { plan, billing } = subscriptionTemp && JSON.parse(subscriptionTemp);
                //统一将空串 转换为null ,兼容接口返回null/""
                billing === "" && (billing = null);
                return plan !== vipType || billing !== priceInterval;
            } catch (err) {
                return false;
            }
        } else {
            try {
                let { plan, billingPeriod } = vipInfo.value;
                billingPeriod === "" && (billingPeriod = null);
                if (props.paymentMethod === "paypal") {
                    let subscriptionTemp = sessionStorage.getItem("subscriptionTemp"); // 获取点击订阅是缓存的订阅信息
                    let { plan: sessionPlan, billing } = subscriptionTemp && JSON.parse(subscriptionTemp);
                    //统一将空串 转换为null ,兼容接口返回null/""
                    billing === "" && (billing = null);
                    return sessionPlan !== vipType || billing !== billingPeriod || plan !== vipType || billingPeriod !== priceInterval;
                }
                return plan !== vipType || billingPeriod !== priceInterval;
            } catch (error) {
                return true;
            }
        }
    })
        .then(async ({ data }) => {
            try {
                // useAsyncUserCount与getSubList存在先后关系
                await useAsyncUserCount();
                await Promise.all([subscribeStore.getLumenNum(), subscribeStore.getSubList(), subscribeStore.getRefreshOn()]);
                emits("close");
                openPurchaseSuccess();
            } catch (error) {
                showError.value = true;
            }
        })
        .catch((err) => {
            showError.value = true;
        })
        .finally(() => {
            if (isSub || props.paymentMethod === "paypal") sessionStorage.removeItem("subscriptionTemp"); // 缓存数据清理
        });
};

/**
 * 购买Lumen轮询
 */
const buyLumenResultPoll = () => {
    reqPoll(null, reqPollLumenNum, ({ data }) => {
        const tempLumen = sessionStorage.getItem("lumenTemp");
        return tempLumen && Number(tempLumen) !== data;
    })
        .then(({ data }) => {
            subscribeStore.setVipInfo({ awardedLumenLeft: data });
            emits("close");
            openPurchaseSuccess();
        })
        .catch(() => {
            showError.value = true;
        })
        .finally(() => {
            sessionStorage.removeItem("lumenTemp"); // 清理缓存数据
        });
};
/**
 * 发送邮件
 */
const handleSendEmail = () => {
    const email = "<EMAIL>";
    const subject = "Payment Issue with PicLumen Subscription";
    const body = `action: ${props.loopType};\n paymentMethod: ${props.paymentMethod};\n\n`;
    const url = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = url;
};

onMounted(() => {
    if (props.loopType === "upgradePlan") subOrUpgradePlanResultPoll(false);
    if (props.loopType === "subPlan") subOrUpgradePlanResultPoll(true);
    if (props.loopType === "buyLumen") buyLumenResultPoll();
});
</script>

<template>
    <div v-if="showError" class="p-6 pt-4">
        <div class="flex items-center justify-between">
            <span>{{ $t("DIALOG_TITLE_NOTICE") }}</span>
            <div class="translate-x-2 flex items-center justify-center size-8 cursor-pointer rounded-full transition-colors text-text-2 hover:bg-fill-wd-1" @click="handleClose">
                <n-icon size="20">
                    <IconsClose />
                </n-icon>
            </div>
        </div>
        <div class="py-6">
            <i18n-t keypath="SUB_CALLBACK_REFRESH">
                <template v-slot:email>
                    <span class="text-primary-6"><EMAIL></span>
                </template>
            </i18n-t>
        </div>
        <div class="mt-6 flex flex-wrap justify-end gap-3">
            <Button type="secondary" class="min-w-32 max-md:w-full" @click="handleClose">{{ $t("COMMON_BTN_OK") }}</Button>
            <Button type="primary" class="min-w-32 max-md:w-full" @click="handleSendEmail">{{ $t("BTN_SEND_EMAIL") }}</Button>
        </div>
    </div>
    <div v-else class="px-6 py-10">
        <IconsSpinLoading class="size-8 text-primary-6 mx-auto" />
        <div class="text-text-1 font-medium text-base mt-6 text-center">{{ $t("DIALOG_TITLE_PROCESSING") }}</div>

        <div class="text-center mt-2 text-text-3 font-medium">
            <i18n-t keypath="SUB_CALLBACK_PENDING">
                <template v-slot:email>
                    <span class="text-primary-6"><EMAIL></span>
                </template>
            </i18n-t>
        </div>
    </div>
</template>
