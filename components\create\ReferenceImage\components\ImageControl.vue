<template>
    <div class="w-full h-full pb-4 lg:pb-0">
        <div class="flex flex-col h-full gap-2">
            <div class="flex">
                <div v-for="item in controlType" :key="item.name" class="flex items-center justify-center gap-2 hover:text-text-1 rounded-full cursor-pointer" @click="changeType(item.name)">
                    <span class="text-text-4 text-sm h-10 leading-10">{{ $t(item.label) }}</span>
                </div>
            </div>
            <!-- pose control 模块 -->
            <div class="flex flex-wrap gap-2">
                <Upload
                    class="!w-fit !h-fit"
                    accept=".png,.jpg,.jpeg,.jfif,.webp,.ico,.gif,.svg,.bmp"
                    :maxPixels="maxPixels"
                    :limit="sizeLimit"
                    :disabled="disableUpload"
                    @disabled-click="handleUploadDisableClick"
                    @change="handleShowDetect"
                    @check-failed="handleUploadError"
                >
                    <div
                        class="flex flex-col gap-2 px-4 size-[calc((100vw-48px)/3)] lg:w-[200px] lg:h-[96px] min-w-[96px] items-center justify-center rounded-lg border border-dashed border-border-2 bg-fill-wd-1 text-xs text-text-3"
                        :class="[disableUpload ? 'cursor-not-allowed' : 'hover:bg-fill-wd-2  hover:text-text-1']"
                    >
                        <!-- PC icon 和 文案 -->
                        <IconsUpload class="text-xl hidden lg:inline-block" />
                        <span class="hidden lg:inline-block">{{ $t("FEATURE_UPLOAD_TITLE") }}</span>
                        <!-- H5 icon 和 文案 -->
                        <IconsAdd class="text-xl inline-block lg:hidden" />
                        <span class="inline-block lg:hidden">{{ $t("ADD_BUTTON") }}</span>
                    </div>
                </Upload>
                <div
                    v-for="item in controlItems[currentType] || []"
                    :key="item.name"
                    class="aspect-square rounded-lg overflow-hidden relative bg-fill-ipt-2 control-item-box cursor-pointer min-size-24 size-[calc((100vw-48px)/3)] lg:size-[96px] group shrink-0"
                    :class="{ 'selected-border': cantDelete(item.imgUrl) }"
                    @click="handleChoose(item)"
                >
                    <span
                        class="size-8 lg:size-6 items-center justify-center rounded-sm bg-fill-t-2 absolute top-1 right-1 z-10 flex lg:hidden backdrop-blur-[8px]"
                        :class="{ 'lg:group-hover:!flex': !cantDelete(item.imgUrl), '!hidden': cantDelete(item.imgUrl) }"
                        @click.stop="handleDelControlItem(item)"
                    >
                        <IconsDele class="text-base text-white" />
                    </span>
                    <img class="flex-grow lg:size-full aspect-square object-cover rounded-lg" :src="item.imgUrl" alt="" />
                    <div class="selected-mask rounded-lg" :class="[cantDelete(item.imgUrl) ? 'opacity-100' : 'opacity-0']"></div>
                </div>
            </div>

            <!-- 弹出框 -->
            <n-modal v-model:show="showNewControl" class="!rounded-2xl border border-border-1 border-solid !bg-bg-2">
                <div class="w-full lg:w-[880px] mx-4 lg:mx-auto rounded-2xl border border-solid border-border-1 bg-bg-2 p-6">
                    <div class="w-full flex justify-between items-center text-xl font-semibold text-text-1">
                        <span class="">{{ t("POSE_CONTROL_CREATE") }}</span>
                        <n-icon size="24" class="cursor-pointer" @click="handleClose">
                            <IconsClose />
                        </n-icon>
                    </div>
                    <div>
                        <div class="px-3 py-2 rounded-lg bg-danger-1 text-danger-6 mt-6" v-show="detectFile.error">
                            <div>{{ t("POSE_CONTROL_ERR_TITLE") }}</div>
                            <div class="mt-2 text-xs">{{ t("POSE_CONTROL_ERR_DESC") }}</div>
                        </div>
                        <div class="py-6 flex items-center justify-center lg:justify-between">
                            <Upload
                                class="border-none !w-[48%] lg:!w-[388px] aspect-square"
                                accept=".jpeg,.jpg,.png,.webp"
                                :maxPixels="maxPixels"
                                :limit="sizeLimit"
                                :disabled="detectPending"
                                @change="handleShowDetect"
                                @check-failed="handleUploadError"
                            >
                                <div
                                    class="flex flex-col gap-2 w-full h-full items-center justify-center rounded-lg border border-dashed border-border-2 bg-fill-wd-1 text-xs text-text-3"
                                    :class="{ 'hover:bg-fill-wd-2  hover:text-text-1': !detectPending }"
                                >
                                    <template v-if="!detectFile.tempLink">
                                        <n-icon size="24"><IconsUpload /></n-icon>
                                        <div class="text-center mt-2 text-xs px-4">{{ $t("FEATURE_UPLOAD_TITLE") }}</div>
                                    </template>
                                    <template v-else>
                                        <img :src="detectFile.tempLink" class="w-full h-full object-contain rounded-lg" />
                                    </template>
                                </div>
                            </Upload>
                            <n-icon size="24" class="rotate-180 shrink-0 text-text-2">
                                <IconsBack />
                            </n-icon>
                            <div class="rounded-lg bg-fill-wd-1 shrink-0 w-[48%] lg:w-[388px] aspect-square overflow-hidden flex items-center justify-center">
                                <n-icon size="72" v-if="!detectPending && !detectFile.resultLink" class="text-text-5"> <IconsPose /> </n-icon>
                                <div v-else-if="detectPending" class="text-xs text-text-3 flex justify-center items-center flex-col gap-2">
                                    <n-icon size="20">
                                        <IconsSpinLoading />
                                    </n-icon>
                                    <span>{{ t("POSE_CONTROL_DETECTING") }}</span>
                                </div>
                                <img v-if="detectFile.resultLink" :src="detectFile.resultLink" class="w-full h-full object-contain" alt="" />
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-14">
                            <Button :disabled="detectPending || !detectFile.tempLink || !!detectFile.resultLink" rounded="lg" type="secondary" class="w-full" @click="handleDetected">
                                <span v-if="detectPending">{{ t("POSE_CONTROL_DETECTING") }}</span>
                                <span v-else>{{ t("POSE_CONTROL_DETECT") }}</span>
                            </Button>
                            <Button :disabled="!detectFile.resultLink || submitLoading" rounded="lg" type="primary" class="w-full" @click="handleUploadImg">
                                <n-icon size="20" v-if="submitLoading">
                                    <IconsSpinLoading />
                                </n-icon>
                                <template v-else>
                                    <span class="hidden lg:inline-block"> {{ t("POSE_CONTROL_SAVE") }} </span>
                                    <span class="inline-block lg:hidden">{{ $t("ADD_BUTTON") }}</span>
                                </template>
                            </Button>
                        </div>
                    </div>
                </div>
            </n-modal>
        </div>
    </div>
</template>

<script setup>
import { imageDetectPost, appendImageControl, delImageControl, getImageControlByType } from "@/api";
import { uploadToCos, loadImage } from "@/utils/tools";
import { compressSingleFileOrUrl } from "@/utils/compress.js";
import { REFER_TYPES, SHAPE_ALL } from "@/utils/constant";
const { t } = useI18n({ useScope: "global" });
const emits = defineEmits(["change"]);
const props = defineProps({
    selectedControlsId: {
        type: Array,
        default: () => [],
    },
});

const uploading = ref(true);
const disableUpload = computed(() => uploading.value || controlItems.value[currentType.value]?.length >= 10);
const controlType = [
    { label: "POSE", name: "openpose" },
    // { label: "Canny", name: "canny" },
    // { label: "Depth", name: "depth" },
];
const keyMap = {
    openpose: "openposeControl",
};

const controlItems = ref({
    openpose: [],
    canny: [],
    depth: [],
});

const showNewControl = ref(false);
const detectPending = ref(false);
const submitLoading = ref(false);
const detectFile = ref({});

const currentType = ref(controlType[0].name);
// watch(
//     () => props.checkedControl.mainCategory,
//     (newType) => {
//         currentType.value = newType || controlType[0].name;
//     }
// );
const changeType = (type) => {
    if (type === currentType.value) {
        return;
    }
    currentType.value = type;
    getImageControlList();
};

//上传相册相关
//上传前校验文件
const maxPixels = 5000 * 5000;
const sizeLimit = 20;
//校验失败
const handleUploadError = (data) => {
    const { type, limit, maxPixels, format } = data;
    console.log(type, "type");
    switch (type) {
        case "size":
        case "ratio":
            openToast.error(t("UPLOAD_ERROR.SIZE_PIXEL_EXCEED", { limit, maxPixels }));
            break;
        case "format":
            openToast.error(t("FEATURE_UPLOAD_TYPE_ERROR", { type: format })); //TODO 多语言
            break;
    }
};

//打开Pose检测弹窗
const handleShowDetect = async (files) => {
    console.log(files.length, "files");
    if (!files?.length) return;
    detectPending.value = false;
    detectFile.value.resultLink = "";
    showNewControl.value = true;
    const url = URL.createObjectURL(files[0]);
    detectFile.value.tempLink = url;
    const compressedInfo = await compressSingleFileOrUrl(files[0], {
        type: "webp",
        width: 1024,
        height: 1024,
        quality: 0.5,
    });
    const { compressedFile } = compressedInfo;
    detectFile.value.file = compressedFile;
    window.trackEvent("Create", { el: `reference_image_popup_image_control_upload` });
};
//移除当前文件
const handleRemoveTempLink = (isError = false) => {
    detectFile.value.tempLink && URL.revokeObjectURL(detectFile.value.tempLink);
    const conf = { tempLink: null, file: null, originLink: null, resultLink: null, error: isError };
    detectFile.value = { ...conf };
    detectPending.value = false;
    submitLoading.value = false;
};
const handleClose = () => {
    showNewControl.value = false;
    handleRemoveTempLink();
};
//开始检测
const handleDetected = async () => {
    window.trackEvent("Create", { el: "reference_image_popup_image_control_pose_detect" });
    if (detectPending.value) {
        return;
    }
    try {
        detectPending.value = true;
        const { fullPath } = await uploadToCos({ file: detectFile.value.file, originalFileName: Date.now() + "_.webp", type: "temp" });
        const { status, data, message } = await imageDetectPost({ imgUrl: fullPath, controlType: currentType.value });
        if (status !== 0 || !data) {
            handleRemoveTempLink(true);
            console.log("error");
            return;
        }
        const imgInfo = await loadImage(data);
        detectFile.value = {
            ...detectFile.value,
            resultLink: data,
            width: imgInfo.width,
            height: imgInfo.height,
        };
    } catch (error) {
        console.error(error, "error");
        handleRemoveTempLink(true);
    } finally {
        detectPending.value = false;
    }
};
//将图片添加到姿势库
const handleUploadImg = async () => {
    try {
        if (submitLoading.value) {
            return;
        }
        window.trackEvent("Create", { el: "reference_image_popup_image_control_pose_create" });
        submitLoading.value = true;
        //真实宽高用于对图片信息进行记录 近似宽高用于生图操作
        const { resultLink: imgUrl, width: realWidth, height: realHeight } = detectFile.value;
        const { width, height } = autoMatchResolution({ realWidth, realHeight }); // 获取近似宽高
        const { status, message } = await appendImageControl({ imgUrl, controlType: currentType.value, realWidth: realWidth || width, realHeight: realHeight || height, width, height });
        if (status !== 0) {
            handleToastError(message);
            return;
        }
        getImageControlList();
        handleClose();
    } catch (error) {
        handleToastError(error?.message);
    } finally {
        submitLoading.value = false;
    }
};
//选中当前图片
const handleChoose = ({ imgUrl, width, height }) => {
    emits("change", {
        style: keyMap[currentType.value],
        img_url: imgUrl,
        weight: 0.5,
        width,
        height,
        displayType: REFER_TYPES.IMAGE_CONTROL,
    });
};

//查询列表
const getImageControlList = async () => {
    uploading.value = true;
    try {
        const { status, data = [], message } = await getImageControlByType({ controlType: currentType.value });
        if (status !== 0) {
            openToast.error(message, 5e3);
            return;
        }
        window.trackEvent("Create", { el: `reference_image_popup_image_control_save_count=${data?.length}` });
        controlItems.value[currentType.value] = data;
    } catch (e) {
    } finally {
        uploading.value = false;
    }
};

//如果当前图片已经被选中作为参考 不可删除
const cantDelete = (imgUrl) => {
    return props.selectedControlsId?.includes(imgUrl);
};
//删除指定的control item
const handleDelControlItem = async ({ id, imgType, controlType, imgUrl }) => {
    if (cantDelete(imgUrl)) return;
    window.trackEvent("Create", { el: "reference_image_popup_image_control_delete" });
    const { status, message } = await delImageControl({ id, imgType });
    if (status !== 0) {
        openToast.error(message, 5e3);
        return;
    }
    controlItems.value[controlType] = controlItems.value[controlType].filter((item) => item.id !== id);
};

getImageControlList();
const handleUploadDisableClick = () => {
    if (uploading.value) return;
    handleToastError(t("UPLOAD_ERROR.REACHED_THE_MAXIMUM"));
};
const handleToastError = (message = t("UPLOAD_ERROR.UNKNOWN_ERROR")) => {
    openToast.error(message);
};
</script>

<style lang="scss" scoped></style>
