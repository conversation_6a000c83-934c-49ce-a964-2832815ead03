<template>
    <VirtualWaterfall
        v-bind="$attrs"
        v-show="showFollowList.length > 0"
        style="box-sizing: content-box; min-height: 30vh"
        class="h-full max-w-[1200px] mx-auto"
        padding="0 0 0 0"
        rowKey="id"
        :gap="16"
        :virtual="true"
        :items="showFollowList"
        :calcItemHeight="() => 96"
        :itemMinWidth="128"
        :preload-screen-count="[1, 2]"
        :columnCount="waterfallColCount"
        :maxColumnCount="waterfallColCount"
    >
        <template #default="{ item, index }">
            <div :key="item.id" class="h-24 flex items-center justify-between text-text-2 font-medium w-full" :class="isMobile ? 'gap-2' : 'rounded-2xl bg-bg-2  px-6 '">
                <template v-if="isMobile">
                    <div class="flex items-center gap-3 flex-1 min-w-0">
                        <img
                            v-if="!!item.userAccountInfo?.userAvatarUrl"
                            :src="item.userAccountInfo?.userAvatarUrl"
                            class="rounded-full bg-bg-1 size-[42px]"
                            @click="useToCommunityHome(item.userAccountInfo?.userId)"
                        />
                        <!-- 上传 -->
                        <n-icon v-else size="48" class="cursor-pointer text-text-4" @click="useToCommunityHome(item.userAccountInfo?.userId)">
                            <IconsPerson />
                        </n-icon>
                        <div class="flex-1 min-w-0">
                            <div class="text-base text-text-2 truncate" @click="useToCommunityHome(item.userAccountInfo?.userId)">
                                {{ item.userAccountInfo?.userName }}
                            </div>
                            <div class="text-base text-text-3 truncate">{{ item.introduction || $t("COMMUNITY_EMPTY_DESC") }}</div>
                        </div>
                    </div>

                    <div class="w-24 flex-shrink-0">
                        <Button v-if="item.followed || tabs === 'follow'" class="min-w-24" type="secondary" size="medium" @click="handleUnFollow(item.id, item.userAccountInfo)">UnFollow</Button>
                        <Button v-if="tabs === 'fans' && !item.followed" class="min-w-24" type="tertiary" size="medium" @click="handleFollow(item.id, item.userAccountInfo)">Follow</Button>
                    </div>
                </template>
                <template v-else>
                    <img
                        v-if="!!item.userAccountInfo?.userAvatarUrl"
                        :src="item.userAccountInfo?.userAvatarUrl"
                        class="rounded-full bg-bg-1 cursor-pointer"
                        :class="isMobile ? 'size-[42px]' : 'size-[48px]'"
                        @click="useToCommunityHome(item.userAccountInfo?.userId)"
                    />
                    <n-icon v-else size="48" class="cursor-pointer text-text-4" @click="useToCommunityHome(item.userAccountInfo?.userId)">
                        <IconsPerson />
                    </n-icon>
                    <div class="flex-1 ml-3">
                        <span class="inline-block flex-1 text-base cursor-pointer truncate hover:text-text-1" @click="useToCommunityHome(item.userAccountInfo?.userId)">
                            {{ item.userAccountInfo?.userName }}
                        </span>
                        <div class="flex items-center gap-2">
                            <div class="md:block hidden">
                                <span>{{ formatLike(item.publicImgNums) }}</span>
                                <span class="ml-1 text-text-4">{{ t("COMMUNITY_PUBLISHED") }}</span>
                            </div>
                            <div class="w-[1px] h-3 bg-text-5 md:block hidden"></div>
                            <div>
                                <span>{{ formatLike(item.fansNums) }}</span>
                                <span class="ml-1 text-text-4">{{ t("COMMUNITY_FANS") }}</span>
                            </div>
                            <div class="w-[1px] h-3 bg-text-5"></div>
                            <div>
                                <span>{{ formatLike(item.likeNums) }}</span>
                                <span class="ml-1 text-text-4">{{ t("SHORT_LIKES") }}</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <Button v-if="item.followed || tabs === 'follow'" class="min-w-24" type="secondary" size="medium" @click="handleUnFollow(item.id, item.userAccountInfo)">UnFollow</Button>
                        <Button v-if="tabs === 'fans' && !item.followed" class="min-w-24" type="tertiary" size="medium" @click="handleFollow(item.id, item.userAccountInfo)">Follow</Button>
                    </div>
                </template>
            </div>
        </template>
    </VirtualWaterfall>
    <div class="py-20 flex justify-center" v-if="pageLoading">
        <n-icon size="48" class="text-primary-6">
            <IconsSpinLoading />
        </n-icon>
    </div>
    <Empty class="py-20" v-if="!showFollowList.length && !pageLoading" text="EMPTY_DATA"></Empty>
</template>

<script setup>
import { getFansOrFollowingsV2, unfollowUserById, followUserById } from "@/api";
import { formatNumber } from "@/utils/tools";
import { NIcon } from "naive-ui";
import { Alert } from "@/icons/index.js";
import VirtualWaterfall from "@/components/waterfall/Waterfall.vue";
import useWindowResize from "@/hook/windowResize";
import { useToCommunityHome } from "@/hook/updateAccount";
import { useUserCommunity } from "@/stores";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const userCommunity = useUserCommunity();
const { t } = useI18n({ useScope: "global" });
const { isMobile } = storeToRefs(useThemeStore());

const props = defineProps({
    tabs: {
        type: String,
        default: "follow",
    },
});
const windowResize = useWindowResize();
const waterfallColCount = computed(() => {
    let base = 2;
    if (windowResize.width.value < 1200) {
        base = 1;
    }
    return base;
});

const formatLike = computed(() => {
    return (num = 0) => formatNumber(num);
});
const showFollowList = ref([]);
const pageLoading = ref(false);
const pages = ref({
    pageNum: 0,
    pageSize: 50,
    isDone: false,
});
const loadMoreData = async () => {
    if (pageLoading.value) {
        return;
    }
    let { pageNum, pageSize, isDone, lastFollowId = "" } = pages.value;
    if (isDone) {
        return;
    }
    pageNum += 1;
    pageLoading.value = true;
    //游标分页  当前数据最后一条数据ID
    if (pageNum === 1) {
        lastFollowId = "";
    }
    const { status, data, message } = await getFansOrFollowingsV2({ pageSize, lastFollowId, selectType: props.tabs });
    pageLoading.value = false;
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    pages.value.lastFollowId = data.lastId;
    const list = data?.resultList || [];
    console.log(list);
    if (pageNum === 1) {
        showFollowList.value = list;
    } else {
        showFollowList.value = [...showFollowList.value, ...list];
    }
    pages.value.isDone = list.length < pageSize;
    pages.value.pageNum = pageNum;
};
loadMoreData();
//取消关注
const handleUnFollow = (id, { userId, userName }) => {
    const { showMessage } = useModal();
    showMessage({
        style: { width: "420px" },
        cancelBtn: t("COMMON_BTN_CANCEL"),
        confirmBtn: t("COMMUNITY_UN_FOLLOW_BTN"),
        content: h("div", null, t("COMMUNITY_UN_FOLLOW_TIPS", { target: userName })),
        icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_CONFIRM"),
    }).then(async () => {
        const { status, message } = await unfollowUserById({ userId });
        if (status !== 0) {
            openToast.error(message);
            return;
        }
        const index = showFollowList.value.findIndex((item) => item.id === id);

        if (props.tabs === "follow") {
            showFollowList.value.splice(index, 1);
        } else {
            const index = showFollowList.value.findIndex((item) => item.id === id);
            showFollowList.value[index].followed = false;
        }
        userCommunity.updateCommunityUser({
            following: Math.max(0, userCommunity.communityUser.following - 1),
        });
    });
};
//关注用户
const handleFollow = async (id, { userId }) => {
    const { status, message } = await followUserById({ userId });
    if (status !== 0) {
        openToast.error(message);
        return;
    }
    const index = showFollowList.value.findIndex((item) => item.id === id);
    showFollowList.value[index].followed = true;
    userCommunity.updateCommunityUser({
        following: userCommunity.communityUser.following + 1,
    });
};

defineExpose({ loadMoreData });
</script>
