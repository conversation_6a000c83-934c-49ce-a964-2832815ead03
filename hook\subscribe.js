import { h, ref, render, getCurrentInstance, nextTick } from "vue";
import BuyLumenModal from "@/components/BuyLumenModal.vue";
import SubscribeModal from "@/components/SubscribeModal.vue";
import DialogBox from "@/components/common/dialog/index.vue";
import UpDownPlanModal from "@/components/UpDownPlanModal.vue";
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useUserProfile } from "@/stores/index.js";
import { Alert, CloseRoundFill } from "@/icons/index.js";
import { useI18n } from "vue-i18n";
import RequestLoadingModal from "@/components/RequestLoadingModal.vue";
import PurchaseSuccessModal from "@/components/PurchaseSuccessModal.vue";
import CancelAutoSubscriptionModal from "@/components/CancelAutoSubscriptionModal.vue";
import CancelUpgradeSubscriptionModal from "@/components/CancelUpgradeSubscriptionModal.vue";

import { formatSubscribeParams } from "@/utils/tools.js";
import { SUBSCRIBE_TYPE, SUBSCRIBE_CALLBACK_PARAMS, PAYMENT_METHOD } from "@/utils/constant.js";
import { reqSubscribeSession, reqSubscribeSessionByPaypal } from "@/api";

export const useMessageBox = () => {
    // Map<groupKey, { vm, container }>
    const messageInstances = new Map();

    /**
     * 打开弹窗
     * @param {object} option - 弹窗配置
     * @param {*} appContext - vue上下文
     * @param {string} groupKey - 分组key，保证同组只存在一个弹窗
     */
    const showMessage = (option, appContext, groupKey = "default") => {
        // 打开前先清理同组
        clearMessageBox(groupKey);

        return new Promise(async (resolve, reject) => {
            const container = document.createElement("div");
            container.setAttribute("data-messagebox-group", groupKey);

            const { content, icon, title, confirmBtn, cancelBtn, ...pro } = option;
            const show = ref(true);

            const onVanish = () => {
                render(null, container);
                if (container.parentNode) container.parentNode.removeChild(container);
                messageInstances.delete(groupKey);
            };

            const vnode = h(
                DialogBox,
                {
                    show: show.value,
                    ...pro,
                    "onUpdate:show": (val) => {
                        show.value = val;
                        if (!val) setTimeout(onVanish, 300);
                    },
                    onConfirm: (val) => {
                        onVanish();
                        resolve(val);
                    },
                    onCancel: (val) => {
                        onVanish();
                        reject(val);
                    },
                    onVanish,
                },
                {
                    icon: icon ? () => h(icon) : null,
                    title: title ? () => h("div", null, title) : null,
                    default: () => (typeof content === "function" || typeof content === "object" ? h(content) : h("div", null, content)),
                    "confirm-btn": () => h("span", null, confirmBtn),
                    "cancel-btn": cancelBtn && (() => h("span", null, cancelBtn)),
                }
            );
            vnode.appContext = appContext ?? useMessageBox._context;
            await nextTick();
            render(vnode, container);
            const parent = document.querySelector("#app_content") || document.body;
            parent.appendChild(container);

            // 只保留一个
            messageInstances.set(groupKey, { container });
        });
    };

    /**
     * 关闭同组所有弹窗
     * @param {string} groupKey
     */
    const clearMessageBox = (groupKey = "default") => {
        const instance = messageInstances.get(groupKey);
        if (instance) {
            render(null, instance.container);
            if (instance.container.parentNode) instance.container.parentNode.removeChild(instance.container);
            messageInstances.delete(groupKey);
        }
        // 兜底DOM清理，防止极端race情况
        document.querySelectorAll(`[data-messagebox-group="${groupKey}"]`).forEach((el) => el.parentNode && el.parentNode.removeChild(el));
    };

    return { showMessage, clearMessageBox };
};

export default {
    install: (app) => {
        useMessageBox._context = app._context;
        app.config.globalProperties.$messageBox = useMessageBox;
    },
};

/**
 * 创建弹窗工厂函数
 * @param modalComponent
 * @param option
 * @returns {function(): {openModal: function(*): *}}
 */
export const createModalHook = (modalComponent, option, groupKey = "default") => {
    return () => {
        const vueInstance = getCurrentInstance();
        const { showMessage, clearMessageBox } = useMessageBox();

        const openModal = (props) => {
            // 先清理同组（其实 showMessage 里也会清理，这里保险起见无副作用）
            clearMessageBox(groupKey);

            return new Promise((resolve, reject) => {
                const modalComponentVNode = h(modalComponent, {
                    ...props,
                    onClose: () => {
                        clearMessageBox(groupKey);
                        resolve(false);
                    },
                });
                const options = Object.assign(
                    {
                        showCancel: false,
                        showConfirm: false,
                        content: modalComponentVNode,
                    },
                    option
                );
                showMessage(options, vueInstance?.appContext, groupKey).then(resolve).catch(reject);
            });
        };
        return { openModal };
    };
};

const baseStyle = { padding: "0" };
export const useBuyLumenModal = createModalHook(BuyLumenModal, { style: { width: "880px", ...baseStyle } }, "buyLumen");
export const useSubscribeModal = createModalHook(SubscribeModal, { style: { width: "880px", ...baseStyle } }, "subscribed");
export const useUpgradePlanModal = createModalHook(UpDownPlanModal, { style: { width: "500px", ...baseStyle } }, "upgradePlan");
export const useRequestLoadingModal = createModalHook(RequestLoadingModal, { style: { width: "500px", ...baseStyle } }, "requestLoading");
export const usePurchaseSuccessModal = createModalHook(PurchaseSuccessModal, { style: { width: "500px" } }, "purchaseSuccess");
export const useCancelAutoSubscriptionModal = createModalHook(CancelAutoSubscriptionModal, { style: { width: "auto", ...baseStyle } }, "cancelPlan");
export const useCancelUpgradeSubscriptionModal = createModalHook(CancelUpgradeSubscriptionModal, { style: { width: "500px" } }, "cancelChange");

/**
 * 显示消息提示框
 * @returns {{showErrorModal: undefined}}
 */
export const useSubscribeErrorModal = () => {
    const { showMessage, clearMessageBox } = useMessageBox();
    const { t } = useI18n({ useScope: "global" });
    const showErrorModal = (msg, fill = {}) => {
        showMessage({
            style: { width: "420px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, t(msg, fill)),
            icon: h(CloseRoundFill, { class: "text-error text-2xl" }),
            title: t("TOAST_TITLE_ERROR"),
        }).then(() => {
            clearMessageBox();
        });
    };
    const showInfoModal = (msg, fill = {}) => {
        showMessage({
            style: { width: "420px" },
            showCancel: false,
            confirmBtn: t("COMMON_BTN_OK"),
            content: h("div", null, t(msg, fill)),
            icon: h(Alert, {
                class: "dark:text-dark-com-status-waring text-com-status-waring text-2xl",
            }),
            title: t("DIALOG_TITLE_NOTICE"),
        }).then(() => {
            clearMessageBox();
        });
    };
    return { showErrorModal, showInfoModal };
};

/**
 * 功能权限校验工厂函数
 */
const validatePermission = {
    vipPermission: {},
    imagesPerBatch: function (count) {
        return this.vipPermission.imagesPerBatch >= count;
    },
    upscale: function () {
        return this.vipPermission.upscale;
    },
    inpaint: function () {
        return this.vipPermission.inpaint;
    },
    expand: function () {
        return this.vipPermission.expand;
    },
    colorize: function () {
        return this.vipPermission.colorize;
    },
    removeBg: function () {
        return this.vipPermission.removeBg;
    },
    // fluxDevGen: function () {
    //     return this.vipPermission.vipType !== SUBSCRIBE_TYPE.BASIC;
    // },
    batchDownload: function () {
        return this.vipPermission.batchDownload;
    },
    promptTranslation: function () {
        return this.vipPermission.translation;
    },
    promptEnhance: function () {
        return this.vipPermission.enhance;
    },
    historyExplore: function () {
        return this.vipPermission.historyExplore;
    },
    notBasicMember: function () {
        return this.vipPermission.vipType !== SUBSCRIBE_TYPE.BASIC;
    },
    cloudStorage: function (count) {
        return this.vipPermission.collectNum >= count;
    },
};

/**
 * 校验订阅功能权限
 * @returns {{checkPermission: (function(*, {}=): *)}}
 */
export const useSubPermission = () => {
    const subscribeStore = useSubscribeStore();
    const { openModal } = useSubscribeModal(); // 引入订阅弹窗
    return {
        checkPermissionNotModal: (featureKey, options = {}) => {
            Object.assign(validatePermission.vipPermission, subscribeStore.currVipPermission);
            return validatePermission[featureKey] && validatePermission[featureKey](options);
        },
        checkPermission: (featureKey, options = {}) => {
            Object.assign(validatePermission.vipPermission, subscribeStore.currVipPermission);
            return new Promise((resolve) => {
                const isVal = validatePermission[featureKey] && validatePermission[featureKey](options);
                if (isVal) return resolve(true); // 有权限
                openModal({ triggerEl: options?.triggerEl || featureKey }).finally(() => resolve(false)); // 无权限
            });
        },
    };
};

export const useBuyLumen = () => {
    const subscribeStore = useSubscribeStore();
    const { openModal: openSubModal } = useSubscribeModal(); // 引入订阅弹窗
    const { openModal: openBuyLumenModal } = useBuyLumenModal(); // 引入Lumen弹窗
    return {
        toBuyLumen: (options) => {
            return new Promise((resolve) => {
                const isVip = subscribeStore.vipInfo.plan !== SUBSCRIBE_TYPE.BASIC;
                if (isVip) openBuyLumenModal({ triggerEl: options?.triggerEl || "" }).finally(() => resolve(false));
                else openSubModal({ triggerEl: options?.triggerEl || "" }).finally(() => resolve(false));
            });
        },
    };
};

// import { useToastBox } from "@/components/toast";

/**
 * 校验用户权限，当用户处于Vip和Vip pro时，在一些付费功能使用时弹出toast提示
 */
export const useVipNotice = () => {
    const { t } = useI18n({ useScope: "global" });
    const subscribeStore = useSubscribeStore();
    const userProfile = useUserProfile();
    return {
        /**
         * 检查vip类型 弹出对应提示，当用户曾有过弹出提示后，将不再弹出提示
         * @param {*} func_name
         * @param {*} notice_str
         */
        checkShowVipNotice: (func_name, notice_str) => {
            const planType = subscribeStore.vipInfo.plan;
            if (planType === SUBSCRIBE_TYPE.BASIC) return; // 免费用户无提示

            try {
                const storeKey = `vip_notice_${planType}_${func_name}`; // 记录信息弹出key,standard和pro各自一次,
                if (!userProfile.checkUserVipNoticeKey(storeKey)) {
                    // if (!notice_str) notice_str = "You have subscribed and are using the subscription feature.";
                    if (!notice_str) notice_str = t("TOAST_VIP_FUNC_NOTICE");
                    useToastBox().showToast(planType, notice_str, 2000);
                    userProfile.appendUserVipNoticeKey(storeKey);
                }
            } catch (e) { }
        },
    };
};

//通用快速拉起订阅计划
export const useCallSubscribePlan = ({ product, priceInterval, paymentMethod }) => {
    return new Promise(async (resolve) => {
        const subscribeStore = useSubscribeStore();
        const vipInfo = subscribeStore.vipInfo;
        const params = formatSubscribeParams("BUY_PLAN", { stripeItems: [{ product, price: priceInterval }] }, { ...SUBSCRIBE_CALLBACK_PARAMS.PLAN, paymentMethod });
        let createLink = reqSubscribeSession;
        if (PAYMENT_METHOD.PAYPAL === paymentMethod) {
            createLink = reqSubscribeSessionByPaypal;
        }
        try {
            const res = await createLink(params);
            let redirectUrl;
            if (res.status === 0) {
                if (PAYMENT_METHOD.PAYPAL === paymentMethod) {
                    redirectUrl = res.data?.approvalUrl;
                } else {
                    redirectUrl = res.data;
                }
                location.href = redirectUrl;
                const subscriptionTemp = {
                    plan: vipInfo.plan,
                    billing: vipInfo.billingPeriod,
                };
                sessionStorage.setItem("subscriptionTemp", JSON.stringify(subscriptionTemp)); // 缓存当前订阅用于轮询结果比对
            }
            resolve({ code: res.status });
        } catch (error) {
            resolve({ code: 500 });
        }
    });
};
