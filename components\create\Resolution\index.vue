<template>
    <div class="w-full">
        <div class="w-full flex items-center justify-between text-text-4 text-sm mb-2">
            <span>{{ $t("CONFIG_BASE_RESOLUTION") }}</span>
            <span v-show="!showAuto">{{ selectedItem.width }} x {{ selectedItem.height }}</span>
        </div>
        <div class="flex items-center gap-2">
            <!-- 默认1：1 或者 auto -->
            <div
                class="resolution-item"
                :class="{
                    useable: !disabled,
                    active: selectedItem.id === 'shape_0' || disabled,
                    '!cursor-not-allowed': disabled,
                }"
                @click="handleRatioChange('shape_0', true)"
            >
                <template v-if="showAuto">
                    <IconsAutoSize class="text-2xl" />
                    <span class="text-xs">Auto</span>
                </template>
                <!-- 当图像参考中 存在content refer 或者 存在 image control时 不可以选择比例 -->
                <template v-else>
                    <Aspect :width="1" :height="1" class="!h-6" />
                    <span class="text-xs">1 : 1</span>
                </template>
            </div>
            <!-- 当前选中比例形成的比例组合 -->
            <template v-if="checkedGroup.length">
                <template v-for="item in checkedGroup" :key="item.id">
                    <div
                        class="resolution-item"
                        :class="{
                            useable: !disabled,
                            active: item.id === selectedItem.id && !disabled,
                            disabled: disabled,
                        }"
                        @click="handleRatioChange(item.id, true)"
                    >
                        <Aspect :width="item.width" :height="item.height" class="!h-6" />
                        <span class="text-xs tracking-wider">{{ item.label }}</span>
                    </div>
                </template>
            </template>
            <!-- 占位符 选中的比例形成的组合未返回前确保页面渲染视觉完整 -->
            <template v-else>
                <div
                    class="resolution-item temp"
                    :class="{
                        useable: !disabled,
                        disabled: disabled,
                    }"
                    @click="handleRatioChange('shape_9', true)"
                >
                    <Aspect :width="3" :height="1" class="!h-6" />
                    <span class="text-xs">9 : 16</span>
                </div>
                <div
                    class="resolution-item temp"
                    :class="{
                        useable: !disabled,
                        disabled: disabled,
                    }"
                    @click="handleRatioChange('shape_10', true)"
                >
                    <Aspect :width="4" :height="3" class="!h-6" />
                    <span class="text-xs">16 : 9</span>
                </div>
            </template>
            <!-- more展开全部比例 -->
            <ResolutionPanel :checkedRatioId="defaultCheckRatioId" :disabled="disabled" :shapeList="myShapeList" @change="handleRatioChange($event, true)" @state-change="(e) => (isOpen = e)">
                <template #actionTrigger>
                    <div
                        class="resolution-item"
                        :class="{
                            ' useable-more': !disabled,
                            '!bg-fill-wd-2 !text-text-1': isOpen,
                            disabled: disabled,
                        }"
                    >
                        <IconsHorMore class="text-2xl" />
                        <span class="text-xs">{{ $t("MORE") }}</span>
                    </div>
                </template>
            </ResolutionPanel>
        </div>
    </div>
</template>
<script setup>
import { SHAPE_ALL, defaultRatio } from "@/utils/constant";
import ResolutionPanel from "./components/ResolutionPanel.vue";
import { getSupportResolution } from "@/utils/tools";
import { useGetModelInfo } from "@/hook/create";

const checkedGroup = ref([]);
const isOpen = ref(false);
const emit = defineEmits(["change"]);
const props = defineProps({
    defaultCheckRatioId: {
        // 默认选中的比例id
        type: String,
        default: "shape_0",
    },
    modelId: {
        type: String,
        default: "",
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    showAuto: { type: Boolean, default: false },
});
/**
 * 更新选中组合
 * @param item
 */
const handleGroupChange = (item) => {
    const { width, height, groupKey } = item;
    let targetGroupKey = "";
    if (width / height === 1) {
        targetGroupKey = "06";
    } else {
        targetGroupKey = groupKey;
    }
    checkedGroup.value = myShapeList.value?.filter((groupItem) => groupItem.groupKey === targetGroupKey);
};
const selectedItem = ref(defaultRatio);
/**
 * 更新选中比例
 * @param newId 新选中的比例的id 在constant文件中
 * @param emitChange 是否告知父组件更新
 */
const handleRatioChange = (newId, emitChange = true) => {
    if (props.disabled) return;
    let result = SHAPE_ALL.find((item) => item.id === newId) || defaultRatio;
    const finalSelect = { ...result, id: newId };
    selectedItem.value = finalSelect;
    handleGroupChange(finalSelect);
    emitChange && emit("change", result);
};
const myShapeList = computed(() => {
    const model = useGetModelInfo(props.modelId);
    const { modelType } = model;
    let list = [];
    if (modelType) {
        list = getSupportResolution(modelType);
    } else {
        list = [...SHAPE_ALL];
    }
    list.splice(1, 0, { width: 0, height: 0, id: "custom" });
    return list;
});

watch(
    () => props.defaultCheckRatioId,
    (newVal) => {
        handleRatioChange(newVal, false);
    },
    {
        immediate: true,
    }
);
</script>
<style scoped lang="scss">
.resolution-item {
    @apply w-[22vw] lg:w-[76px] lg:h-[70px] bg-fill-tab-5 text-text-tab-5 rounded-lg py-3 flex flex-col justify-end gap-2 items-center flex-grow lg:flex-grow-0 transition-all duration-[250];
    ::v-deep(.aspect-item) {
        @apply border-text-tab-5 transition-all duration-[250];
    }
}
.active {
    @apply text-text-tab-7 !bg-fill-tab-7;
    ::v-deep(.aspect-item) {
        @apply border-text-tab-7;
    }
}

.useable {
    @apply bg-fill-tab-5 hover:text-text-tab-6 hover:bg-fill-tab-6 cursor-pointer;
    &:hover {
        ::v-deep(.aspect-item) {
            @apply border-text-tab-6 transition-all duration-[250];
        }
    }
}
.useable-more {
    @apply hover:text-text-1 hover:bg-fill-wd-2 cursor-pointer;
}
.disabled {
    @apply cursor-not-allowed bg-fill-tab-5 text-text-tab-8;
    ::v-deep(.aspect-item) {
        @apply border-text-tab-8;
    }
}
</style>
