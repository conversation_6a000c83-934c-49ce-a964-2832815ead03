<template>
    <div class="grid grid-cols-2 gap-2 text-text-2 mt-4 text-sm">
        <div class="feature-item" :class="[allowRemix ? '' : 'disabled-item']" @click="commandDispense('Remix', allowRemix)">
            <n-icon size="24" class="shrink-0">
                <IconsCreate />
            </n-icon>
            <span>{{ t("TOOLBAR_REMIX") }}</span>
        </div>
        <div class="feature-item relative" :class="[item.sensitive ? 'disabled-item' : '']" @click="commandDispense('Edit', true)">
            <div class="flex items-center gap-2">
                <n-icon size="24"> <IconsChatEdit /> </n-icon>
                <span>{{ t("EDIT_IMAGE.EDIT_IMAGE") }}</span>
            </div>
            <div class="absolute new-tag flex items-center justify-center text-white font-medium text-xs px-2 py-0.5 rounded-[4px] rounded-tr-xl rounded-bl-xl z-10 top-[-30%] right-0 leading-4">
                NEW
            </div>
        </div>

        <div class="feature-item" :class="[allowUpscaleAction ? '' : 'disabled-item']" @click="commandDispense('Upscale', allowUpscaleAction, 1)">
            <n-icon size="24" class="shrink-0">
                <IconsHD />
            </n-icon>
            <div class="flex items-center gap-1">
                <span>{{ t("FEATURE_HIR_FIX_TITLE") }}</span>
            </div>
        </div>

        <!-- 更多按钮 -->
        <n-dropdown :options="moreActionList" placement="top" @select="checkMoreAction" trigger="click" :disabled="noMoreAction" content-class="text-red-500" content-style="w-40">
            <div class="feature-item cursor-pointer" :class="{ 'opacity-50 !bg-fill-btn-1 !cursor-default': noMoreAction }">
                <n-icon size="24" class="shrink-0">
                    <IconsHorMore />
                </n-icon>
                <span>{{ t("H5_BTN_MORE") }}</span>
            </div>
        </n-dropdown>

        <!-- 非上传图 非黄图 才允许post -->
        <div v-if="!isCustomUpload && !item.sensitive" class="col-span-2">
            <Button v-if="allowClickPost" type="primary" rounded="lg" class="w-full" @click="handlePublishCommunity">
                <span>{{ publicStateTxt(item.isPublic) }}</span>
            </Button>
            <Button v-if="postDisabledStatus" :disabled="true" type="primary" rounded="lg" class="w-full" @click="handlePublishCommunity">
                <span>{{ publicStateTxt(item.isPublic) }}</span>
            </Button>

            <PicPopover v-if="item.isPublic === 3" placement="top">
                <template #trigger>
                    <Button :disabled="true" type="primary" rounded="lg" class="w-full">
                        <span>{{ publicStateTxt(item.isPublic) }}</span>
                        <n-icon class="ml-1">
                            <IconsAlert />
                        </n-icon>
                    </Button>
                </template>
                <div>
                    {{ item.rejectionContent }}
                </div>
            </PicPopover>
        </div>
    </div>
</template>
<script setup>
import { handleResetParams, isMjModel, isFluxKontextModel } from "@/utils/tools";
import { SUB_EL, SUBSCRIBE_PERMISSION } from "@/utils/constant";
import { useSyncAction } from "@/stores/syncAction";
import { useImageEdit, useRemix, useDescribe, useUpscale, useRemoveBg, useColorize, useInpaint, useOutpaint, usePhotoCrop } from "@/hook/create";

import { allowUpscale, allowExpand, allowInpaint, allowReColorize, allowVary } from "@/hook/allowAction";
import { useSubPermission, useVipNotice } from "@/hook/subscribe";

import { NIcon } from "naive-ui";
// import { advanceGenerator } from "@/hook/create";
import { IconVary, Bandage, IconColorize, IconExpand, IconCrop, IconDiamond, IconDescribe, RemoveBg } from "@/icons/index.js";
import LangText from "@/components/LangText.vue";
import PublishModal from "@/components/PublishModal.vue";
const syncAction = useSyncAction();

import { withDirectives, resolveDirective, computed } from "vue";
const { t } = useI18n({ useScope: "global" });

// h 函数中需要注入指令
const showPlan = resolveDirective("showPlan");
//isPublic 当前图片审核状态  0未提交  1已公开  2审核中  3已拒绝
const stateMap = ["COMMUNITY_PUBLISH", "COMMUNITY_PUBLISHED", "COMMUNITY_PUBLIC_REVIEW", "COMMUNITY_PUBLIC_REJECT"];
const publicStateTxt = (status) => {
    return t(stateMap[status] || "COMMUNITY_PUBLISH");
};

const props = defineProps({
    item: {
        type: Object,
        required: true,
        default: () => ({}),
    },
    isCustomUpload: {
        type: Boolean,
        default: false,
    },
});
const renderIcon = (icon) => {
    return () => {
        return h(
            NIcon,
            { size: 20 },
            {
                default: () => h(icon),
            }
        );
    };
};
//允许点post
const allowClickPost = computed(() => {
    return props.item.isPublic === 0;
});
// //post数量限制
// const postDisabledLimit = computed(() => {
//     return props.item.isPublic === 0 && dailyPostPicLimit.limit <= 0;
// });
//post status 限制
const postDisabledStatus = computed(() => {
    return props.item.isPublic === 1 || props.item.isPublic === 2;
});

//允许Remix
const allowRemix = computed(() => !props.isCustomUpload);
//允许去背景
const allowRemoveBg = computed(() => {
    return !props.item.sensitive;
});
//允许局部重绘
const allowInpaintAction = computed(() => {
    return allowInpaint.value(props.item);
});
//允许上色
const allowColorize = computed(() => {
    return allowReColorize.value(props.item);
});
//允许 超分
const allowUpscaleAction = computed(() => {
    return allowUpscale.value(props.item);
});
//允许 微变
const allowVaryAction = computed(() => {
    return allowVary.value(props.item);
});
//允许扩图
const allowExpandAction = computed(() => {
    return allowExpand.value(props.item);
});

//更多操作
const moreActionList = computed(() => {
    const { model_id } = props.item;
    return [
        {
            label: () => h(LangText, { labelKey: "FEATURE_BG_REMOVER_FEATURE_TITLE_SHORT" }),
            key: "RemoveBg",
            icon: renderIcon(RemoveBg),
            disabled: !allowRemoveBg.value,
        },
        {
            label: () => h(LangText, { labelKey: "FEATURE_DESCRIBE_TITLE" }),
            key: "Describe",
            icon: renderIcon(IconDescribe),
            disabled: false,
        },
        {
            label: () => h("div", { class: "flex items-center gap-1.5" }, [h(LangText, { labelKey: "TOOLBAR_INPAINT" }), withDirectives(h("span", { class: "text-lg text-info-6" }), [[showPlan]])]),
            key: "Inpaint",
            icon: renderIcon(Bandage),
            disabled: !allowInpaintAction.value || isMjModel({ model_id }) || isFluxKontextModel({ model_id }) || isFluxKreaModel({ model_id }),
        },
        {
            label: () => h("div", { class: "flex items-center gap-1.5" }, [h(LangText, { labelKey: "LINE_ART" }), withDirectives(h("span", { class: "text-lg text-info-6" }), [[showPlan]])]),
            key: "Colorize",
            icon: renderIcon(IconColorize),
            disabled: !allowColorize.value || isMjModel({ model_id }) || isFluxKontextModel({ model_id }) || isFluxKreaModel({ model_id }),
        },
        {
            label: () =>
                h("div", { class: "flex items-center gap-1.5" }, [h(LangText, { labelKey: "FEATURE_OUTPAINT_TITLE" }), withDirectives(h("span", { class: "text-lg text-info-6" }), [[showPlan]])]),
            key: "Expand",
            icon: renderIcon(IconExpand),
            disabled: !allowExpandAction.value || isMjModel({ model_id }) || isFluxKontextModel({ model_id }) || isFluxKreaModel({ model_id }),
        },
        // {
        //     label: () => h(LangText, { labelKey: "FEATURE_CROP_TITLE" }),
        //     key: "Cropper",
        //     icon: renderIcon(IconCrop),
        // },
    ];
});

const checkMoreAction = (e) => {
    // console.log("moreAction click", e, "----------------", props.item);
    switch (e) {
        case "Vary":
            commandDispense("Vary", allowVaryAction.value);
            break;
        case "RemoveBg":
            commandDispense("RemoveBg", allowRemoveBg.value);
            break;
        case "Inpaint":
            commandDispense("Inpaint", allowInpaintAction.value);
            break;
        case "Colorize":
            commandDispense("Colorize", allowColorize.value);
            break;
        case "Expand":
            commandDispense("Expand", allowExpandAction.value, "def");
            break;
        case "Cropper":
            commandDispense("Cropper", true);
            break;
        case "Describe":
            commandDispense("describe", true);
            break;
        default:
            break;
    }
};

//事件分发控制器
const permissionFeatures = {
    Inpaint: {
        perName: SUBSCRIBE_PERMISSION.INPAINT,
        eveName: SUB_EL.INPAINT,
    },
    Colorize: {
        perName: SUBSCRIBE_PERMISSION.COLORIZE,
        eveName: SUB_EL.COLORIZE,
    },
    Expand: {
        perName: SUBSCRIBE_PERMISSION.EXPAND,
        eveName: SUB_EL.EXPAND,
    },
    Upscale: {
        perName: SUBSCRIBE_PERMISSION.UPSCALE,
        eveName: SUB_EL.UPSCALE,
    },
    RemoveBg: {
        perName: SUBSCRIBE_PERMISSION.REMOVE_BG,
        eveName: "",
    },
};

const { checkShowVipNotice } = useVipNotice();
const { checkPermission } = useSubPermission();
const commandDispense = async (action, allow, ps) => {
    console.log(action, "----------------", allow);

    window.trackEvent("APP_DETAILS", { el: `details=${action}` });
    if (!allow) {
        return false;
    }
    const enumKey = permissionFeatures[action];
    if (enumKey) {
        const res = await checkPermission(enumKey.perName, { triggerEl: enumKey.eveName });
        if (!res) {
            return;
        }
    }
    switch (action) {
        case "Remix":
            useRemix(props.item);
            window.trackEvent("create", { el: "create_details_remix" });
            break;
        case "RemoveBg":
            handleClearBackground();
            break;
        case "Inpaint":
            // checkShowVipNotice(`inpaint`);
            handleInpaint();
            break;
        case "Colorize":
            handleColorize();
            break;
        case "Upscale":
            // checkShowVipNotice(`upscale`);
            handleHireFix(ps);
            break;
        case "Vary":
            handleVaryGenerator();
            break;
        case "Expand":
            handleOutpaint();
            break;
        case "Cropper":
            handleCropperPhoto();
            break;
        case "describe":
            const { highMiniUrl, thumbnailUrl, highThumbnailUrl, imgUrl } = props.item;
            window.trackEvent("Create", { el: "create_details_describe" });
            useDescribe(highMiniUrl || thumbnailUrl || highThumbnailUrl || imgUrl);
            break;
        case "Edit":
            handleEdit();
            break;
        default:
            break;
    }
};

// useRoute
const route = useRoute();
const pageKey = computed(() => {
    if (route.path === localePath("/image/create")) {
        return "CREATE";
    }
    if (route.path === localePath("/m/collection")) {
        return "COLLECTION";
    }
    if (route.path === localePath("/image/gallery")) {
        return "HISTORY";
    }
    return "";
});
//cropper
const handleCropperPhoto = () => {
    const { item } = props;
    if (item.sensitive) return;
    usePhotoCrop({ item });
};
//inpaint
const handleInpaint = () => {
    const { item } = props;
    if (item.sensitive) return;
    window.trackEvent("Create", "create_details_inpaint");
    openInpaint({ item });
};
//Colorize
const handleColorize = () => {
    const { item } = props;
    if (item.sensitive) return;
    window.trackEvent("Create", { el: "create_details_colorize" });
    openColorize({ item });
};
//Expand
const handleOutpaint = () => {
    const { item } = props;
    if (item.sensitive) return;
    window.trackEvent("Create", { el: "create_details_outpaint" });
    openOutpaint({ item });
};
//Edit
const { openModal: openImageEdit } = useImageEdit();
const { openModal: openInpaint } = useInpaint();
const { openModal: openOutpaint } = useOutpaint();
const { openModal: openColorize } = useColorize();
const { openModal: openCroper } = usePhotoCrop();
const handleEdit = () => {
    // 判断是否用户是第一次使用edit, 第一次使用时弹出使用引导

    const { item } = props;
    //二次编辑图片
    if (item.sensitive) return;
    window.trackEvent("Create", { el: "create_details_edit" });
    openImageEdit({ item });
};

//图片微变
const handleVaryGenerator = async () => {
    const { model_id, prompt, imgUrl: img_url, negative_prompt, resolution, realWidth, realHeight, originCreate } = props.item;
    const conf = {
        model_id,
        prompt,
        negative_prompt,
        resolution: {
            width: realWidth || resolution.width,
            height: realHeight || resolution.height,
            batch_size: 1,
        },
    };
    const varyPara = { img_url, originCreate };
    // varyGeneration({ ...conf, varyPara });
};
// 去背景 调用高级生图功能
const { removeBg } = useRemoveBg();
const handleClearBackground = () => {
    const base = handleResetParams(props.item);
    const { imgUrl, promptId, model_id, loginName } = props.item;
    removeBg(base, {
        imgUrl,
        promptId: promptId,
        modelId: model_id,
        loginName: loginName,
    });
};
// 高清修复 调用高级生图功能
const { hiresFixTask } = useUpscale();
const handleHireFix = () => {
    if (props.item.sensitive) return;
    window.trackEvent("Create", { el: "create_details_upscale" });
    const base = handleResetParams(props.item);
    const { realWidth, realHeight } = props.item;

    const { imgUrl, promptId, model_id, loginName } = props.item;
    const expandConf = {
        imgUrl,
        promptId,
        model_id,
        loginName,
        realWidth,
        realHeight,
    };

    hiresFixTask(base, {
        ...expandConf,
    });
};

//唤起发布图片到社区功能弹窗
const handlePublishCommunity = async () => {
    const { showMessage } = useModal();
    const { imgName, promptId, thumbnailUrl } = props.item;
    window.trackEvent("Create", { el: "create_details_post" });
    showMessage({
        style: { width: "880px" },
        showCancel: false,
        showConfirm: false,
        zIndex: 100,
        content: h(PublishModal, {
            base: {
                imgUrl: thumbnailUrl,
                imgName,
                promptId,
            },
        }),
    })
        .then((_) => {
            syncAction.publish("updateImg", {
                updateKey: "isPublic",
                updateValue: 2,
                promptId,
                imgName,
            });
        })
        .catch((_) => _);
};

const noMoreAction = computed(() => {
    return !moreActionList.value.some((item) => !item.disabled);
});
</script>

<style lang="scss" scoped>
.feature-item {
    @apply rounded-lg flex items-center h-10 bg-fill-btn-1 hover:bg-fill-btn-2 active:bg-fill-btn-3 cursor-pointer px-4 gap-1.5 transition-colors duration-300;
    &.disabled-item {
        @apply cursor-default bg-fill-btn-4 text-text-6;
    }
}
</style>
