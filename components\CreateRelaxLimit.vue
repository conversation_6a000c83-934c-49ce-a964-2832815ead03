<!--
 * @Author: HuangQS
 * @Description: Create页 Relax模式用户生成超过限制提示组件，移动端在页面顶部，网页端在滚动组件顶部
 * @Date: 2025-05-23 10:29:50
 * @LastEditors: <PERSON><PERSON><PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-07-17 18:49:46
-->

<template>
    <div v-if="isShow" class="font-medium text-text-2 bg-bg-2 rounded-lg p-2 border border-solid border-border-1 flex items-center justify-between w-full gap-2">
        <n-icon size="24">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                    d="M7 9C5.34315 9 4 10.3431 4 12C4 13.6569 5.34315 15 7 15C7.76152 15 8.45464 14.7176 8.98409 14.2503C9.07824 14.1672 9.16716 14.0782 9.25026 13.9841C9.61573 13.57 10.2477 13.5306 10.6617 13.8961C11.0758 14.2616 11.1152 14.8935 10.7497 15.3076C10.6117 15.464 10.464 15.6117 10.3076 15.7497C9.42675 16.5272 8.26737 17 7 17C4.23858 17 2 14.7614 2 12C2 9.23858 4.23858 7 7 7C8.56107 7 9.73584 7.64019 10.6613 8.52861C11.5328 9.36527 12.2135 10.4545 12.8147 11.4167C12.8258 11.4345 12.8369 11.4523 12.848 11.47C13.4886 12.495 14.0489 13.3808 14.7238 14.0286C15.3608 14.6402 16.0611 15 17 15C18.6569 15 20 13.6569 20 12C20 10.3431 18.6569 9 17 9C16.2385 9 15.5454 9.28243 15.0159 9.74974C14.9218 9.83284 14.8328 9.92176 14.7497 10.0159C14.3843 10.43 13.7523 10.4694 13.3383 10.1039C12.9242 9.73845 12.8848 9.10651 13.2503 8.69244C13.3883 8.53602 13.536 8.38832 13.6924 8.25026C14.5733 7.47284 15.7326 7 17 7C19.7614 7 22 9.23858 22 12C22 14.7614 19.7614 17 17 17C15.4389 17 14.2642 16.3598 13.3387 15.4714C12.4672 14.6347 11.7865 13.5455 11.1853 12.5833C11.1742 12.5655 11.1631 12.5477 11.152 12.53C10.5114 11.505 9.95105 10.6192 9.27622 9.97139C8.63916 9.35981 7.93893 9 7 9Z"
                    fill="#7B57E5"
                />
            </svg>
        </n-icon>
        <span class="ml-2 line-clamp-2">{{ t("RELAX_LIMIT_ERR_BAR") }}</span>
        <NuxtLinkLocale to="/user/subscribe" @click="handleSubscribe">
            <n-button :bordered="false" class="!bg-primary-6 rounded-lg font-medium !text-white flex items-center ml-4 hover:!bg-primary-7" type="primary">
                <img src="@/assets/images/subscribe/icon_fo_all_member.webp" class="size-4 hidden lg:flex" alt="" />
                <span class="text-xs text-white ml-1.5"> {{ t("BTN_SUBSCRIBE_NOW") }}</span>
            </n-button>
        </NuxtLinkLocale>
    </div>
</template>

<script setup>
import { useSubscribeStore } from "@/stores/subscribe.js";
import { useThemeStore } from "@/stores/system-config";
import { toRefs } from "vue";
import { SUB_EL, SUBSCRIBE_PERMISSION } from "@/utils/constant.js";
import { storeToRefs } from "pinia";

const { t } = useI18n({ useScope: "global" });
const { isMobile } = storeToRefs(useThemeStore());
const props = defineProps({
    isShow: {
        type: Boolean,
        default: false,
    },
});

const { isShow } = toRefs(props);

const subscribeStore = useSubscribeStore();

//弹出订阅弹窗
const handleSubscribe = async () => {
    window.trackEvent("Create", { el: `relax_top_sub_click` });
    subscribeStore.setSubGaEvent(SUB_EL.RELAX_LIMIT_BAR);
};
</script>
<style></style>
