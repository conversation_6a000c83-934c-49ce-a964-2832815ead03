import { defineStore } from 'pinia'
import { PRELOAD_TASK_TYPE, defaultModel, defaultRatio, REFER_TYPES } from "@/utils/constant.js";
import { getCurrentISO8601Time, isPicLumenArtV1, getResolution } from "@/utils/tools";
import { useUserProfile } from './index';
import { useGetModelInfo, isModelValidForMe } from "@/hook/create.js";
import { updateUserProfile, getPreloadTasks, delPreloadTask, updateReadStatus, updateUserDailyLumenDailogControl } from "@/api";


//生图参数共享 提示词 文生图的参考项列表 模型id 生图比例 生图张数 cfg steps seed 负向提示词
//生图状态共享 创建成功 生成按钮的loading解除 可再次点击 （点击后判断当前同时进行的任务长度 阻断任务的创建）
//生图结果共享 生图轮询中、生图成功、生图失败
//生图队列共享 进行中的总数以及任务详情展示在左侧siderbar中、任务取消的处理   
export const useCreateStore = defineStore('createStore', () => {
  const userProfile = useUserProfile();
  const remixParams = ref({}) // remix的参数
  const model_id = ref() //模型id
  const batch_size = ref(2) //批量数
  const defaultCheckRatioId = ref(getResolution(defaultRatio)?.id) //生图比例 默认1024*1024 1/1
  const resolution = ref(defaultRatio) //生图比例 默认1024*1024 1/1
  const cfg = ref(null) //Guidance scale
  const steps = ref(null) //Steps
  const prompt = ref(""), negative_prompt = ref("")//提示词/负向提示词
  const seed = ref(-1)//种子
  const style_list = ref([]), image_control_list = ref([])//图生图
  const mainCategory = ref("")
  const subCategory = ref("") //prompt magic
  //采样器、调度器
  const sampler_name = ref(""), scheduler = ref("")
  const continueCreate = ref(false);//长任务等待？
  const selectedRefers = ref([]);// 共享 用于页面渲染的 图生图参考数组

  const taskIsRelaxMode = ref(false);//relax模式生图

  const supportReferList = computed(() => { //当前可选的 图生图 人像参考type
    const list = style_list.value?.map((item) => item.style);
    const model = useGetModelInfo(model_id.value);
    let supportRefTypes = model.supportStyleList;
    return supportRefTypes
      ?.filter((item) => item.value !== "multiRefer")
      .map((item) => {
        return { ...item, disabled: list.includes(item.value) };
      });
  });
  // 模型id设置
  const setModel = (value) => {
    if (!value) return
    model_id.value = value
  }
  // 生图张数设置
  const setBatchSize = (value) => {
    batch_size.value = value || 2;
  }

  // 生图比例设置
  const setResolution = (value) => {
    nextTick(() => {
      const model = useGetModelInfo(model_id.value);
      const { modelType } = model;
      let shapeList = getSupportResolution(modelType);
      // 判断当前选中的生图比例切换模型后是否支持 不支持使用默认比例
      let currRatio = shapeList.find((i) => i.width === value.width && i.height === value.height)
      if (!currRatio) {
        currRatio = defaultRatio
      }
      defaultCheckRatioId.value = currRatio?.id
      resolution.value = currRatio
    })
  }

  //Guidance scale 设置
  const setCfg = (value) => {
    cfg.value = value || null;
  }

  // steps 设置
  const setSteps = (value) => {
    steps.value = value || null;
  }
  // prompt 设置
  const setPrompt = (value) => {
    prompt.value = value || "";
  }
  // negative_prompt 设置
  const setNegativePrompt = (value) => {
    negative_prompt.value = value || "";
  }
  // seed 设置
  const setSeed = (value) => {
    seed.value = value || -1;
  }
  // style_list 图像参考 设置
  const setReferenceList = (value) => {
    style_list.value = value || [];
  }
  // image_control_list 图像控制 设置
  const setControlList = (value) => {
    image_control_list.value = value || [];
  }
  const setSampler_name = (value) => {
    sampler_name.value = value || "";
  }
  const setScheduler = (value) => {
    scheduler.value = value || "";
  }
  const setMinCategory = (value) => {
    mainCategory.value = value || ""
  }
  const setSubCategory = (value) => {
    subCategory.value = value || ""
  }
  const setContinueCreate = (value) => {
    continueCreate.value = value || false
  }


  const setSelectedRefers = (value) => {
    selectedRefers.value = value || []
  }
  // 图生图列表 参数更新 分别对一个现有的三种参考方式
  const updateReferStore = (newList = []) => {
    const style_list = [];
    const image_control_list = [];
    let mainCategory = "";
    let subCategory = "";
    newList?.forEach((item) => {
      if (item.displayType === REFER_TYPES.IMAGE_REFER) {
        const { imgUrl, style, weight } = item;
        style_list.push({
          img_url: imgUrl,
          style,
          weight,
        });
      }
      if (item.displayType === REFER_TYPES.IMAGE_CONTROL) {
        const { img_url, style, weight } = item;
        image_control_list.push({
          img_url,
          style,
          weight,
        });
      }
      if (item.displayType === REFER_TYPES.PROMPT_MAGIC) {
        const { type, name } = item;
        mainCategory = type;
        subCategory = name;
      }
    });
    setGenerateConfig({
      style_list,
      image_control_list,
      mainCategory,
      subCategory,
      selectedRefers: newList,
    });
  };

  // 恢复用户 的 use as default
  const setGenerateConfig = (params) => {
    const configKeys = [
      { key: 'batch_size', setter: setBatchSize },
      { key: 'cfg', setter: setCfg },
      { key: 'model_id', setter: setModel },
      { key: 'negative_prompt', setter: setNegativePrompt },
      { key: 'prompt', setter: setPrompt },
      { key: 'resolution', setter: setResolution },
      { key: 'sampler_name', setter: setSampler_name },
      { key: 'scheduler', setter: setScheduler },
      { key: 'seed', setter: setSeed },
      { key: 'steps', setter: setSteps },
      { key: 'style_list', setter: setReferenceList },
      { key: 'image_control_list', setter: setControlList },
      { key: 'mainCategory', setter: setMinCategory },
      { key: 'subCategory', setter: setSubCategory },
      { key: 'continueCreate', setter: setContinueCreate },
      { key: 'selectedRefers', setter: setSelectedRefers }, // 渲染页面用 不用放到生图参数中
    ];

    // 循环批量设置
    configKeys.forEach(({ key, setter }) => {
      if (params[key] !== undefined) {
        setter(params[key]);
      }
    });
  }
  //最终生图参数
  const generateConfig = ref({});
  watchEffect(() => {
    const result = {
      model_id: model_id.value,
      prompt: prompt.value,
      negative_prompt: negative_prompt.value,
      resolution: {
        ...resolution.value,
        batch_size: batch_size.value
      },
      batch_size: batch_size.value,
      seed: Number(seed.value),
      steps: steps.value,
      cfg: cfg.value,
      sampler_name: sampler_name.value,
      scheduler: scheduler.value,
      multi_img2img_info: {
        style_list: style_list.value
      },
      img_control_info: {
        style_list: image_control_list.value
      },
      mainCategory: mainCategory.value,
      subCategory: subCategory.value,
      continueCreate: continueCreate.value
    }
    generateConfig.value = result
    // console.log(result, '参数更新了——————————————————————————————++++++++++++++++')
    // return result
  });

  //设置 remix参数
  const setRemixParams = (value) => {
    remixParams.value = value
  }
  //根据用户配置 触发生图--是否 清空提示词输入框
  const clearPromptInput = () => {
    if (userProfile.userConfig.clearPrompt) {
      setGenerateConfig({
        prompt: "",
        mainCategory: null,
        subCategory: null,
        style_list: [],
        image_control_list: [],
        mainCategory: null,
        subCategory: null,
      })
      setSelectedRefers([])
    }
  };
  // 更新当前生图的方式 relax/fast
  const updateTaskState = (state = false) => {
    taskIsRelaxMode.value = state;
    state && window.trackEvent("Create", { el: `relax_top_show` });
  };

  return {
    remixParams,
    model_id,
    batch_size,
    defaultCheckRatioId,
    resolution,
    cfg,
    steps,
    prompt,
    negative_prompt,
    seed,
    style_list,
    image_control_list,
    generateConfig,
    mainCategory,
    subCategory,
    selectedRefers,
    supportReferList,
    taskIsRelaxMode,
    setGenerateConfig,
    setRemixParams,
    clearPromptInput,
    updateTaskState,
    setModel,
    setBatchSize,
    setResolution,
    setCfg,
    setSteps,
    setPrompt,
    setNegativePrompt,
    setSeed,
    setReferenceList,
    setControlList,
    setSampler_name,
    setScheduler,
    setMinCategory,
    setSubCategory,
    setContinueCreate,
    setSelectedRefers,
    updateReferStore
  }
})

//平台当前支持的模型列表
export const useSupportModelList = defineStore("supportModelList", () => {
  const modelList = ref([]);
  const setModelList = (list = []) => {
    modelList.value = list;
  };
  return { modelList, setModelList };
});

//用户当前生图任务
const elapsedTimeMap = {
  lineRecolor: 46,
  localRedraw: 26,
  enlargeImage: 40,
  removeBackground: 9,
  hiresFix: 38,
  enlargeUpscale: 10,

  multiRefer_1: 18,
  multiRefer_2: 20,
  multiRefer_3: 24,
  multiRefer_4: 30,

  characterRefer_1: 54,
  characterRefer_2: 55,
  characterRefer_3: 55,
  characterRefer_4: 63,

  contentRefer_1: 34,
  contentRefer_2: 38,
  contentRefer_3: 44,
  contentRefer_4: 56,

  styleRefer_1: 39,
  styleRefer_2: 44,
  styleRefer_3: 55,
  styleRefer_4: 57,

  art_characterRefer_1: 8,
  art_characterRefer_2: 12,
  art_characterRefer_3: 17,
  art_characterRefer_4: 22,

  create_1: 7,
  create_2: 12,
  create_3: 17,
  create_4: 22,
};
//任务队列
export const useCurrentTaskQueue = defineStore("currentTaskQueue", () => {
  const userProfile = useUserProfile();
  const createStore = useCreateStore();

  //生图任务队列
  const taskQueue = ref([]);
  //预加载任务队列
  const preloadQueue = ref([]);
  // 允许提交的任务数量
  const queueLimit = ref({
    concurrency: 0,
    queueMax: 0,
  });
  const isLoading = ref(false);
  // 当前排队最快的任务
  const fastestTask = ref({});
  //计算 预估耗时
  const computingTime = (info) => {
    //是否是多图生图
    const isMultiRefer = info.originCreate === "picCreate" && info.multi_img2img_info?.style_list?.length > 1;
    const batchSize = info.resolution?.batch_size || 1;
    if (isMultiRefer) {
      return `multiRefer_${batchSize}`;
    }
    //单生图
    if (info.originCreate === "picCreate") {
      const referType = info.multi_img2img_info?.style_list[0].style;
      if (isPicLumenArtV1({ model_id: info.model_id })) {
        return `art_${referType}_${batchSize}`;
      }
      return `${referType}_${batchSize}`;
    }
    //超分
    if (info.originCreate === "hiresFix") {
      if (info.featureName === "enlargeUpscale") {
        //自定义上传图超分
        return info.featureName;
      }
      return info.originCreate;
    }
    if (info.originCreate !== "create") {
      return info.originCreate;
    }
    return `${info.originCreate}_${batchSize}`;
  };
  const updateTaskQueue = (tasks = []) => {
    taskQueue.value = tasks
      .sort((a, b) => new Date(b.createTimestamp) - new Date(a.createTimestamp))
      .map((item) => {
        if (item.index === 0 && !item.genBeginTime && !item.elapsedTime) {
          item.genBeginTime = Date.now(); // 记录任务开始时间
          const elapsedTime = elapsedTimeMap[computingTime(item) || `create_${item.resolution?.batch_size || 1}`] || 10; // unit second
          item.elapsedTime = elapsedTime * 1000;
          // console.log(`[${computingTime(item)}] 生图任务开始${item.genBeginTime}，预估耗时 ${elapsedTime} 秒`);
        }
        return item;
      });

    if (tasks.length > 0) {
      let runningTask = null;
      let fastTask = tasks[0];
      for (const item of tasks) {
        if (item.index === 0) {
          runningTask = item;
          break; // 找到 index 为 0 的元素后立即退出循环
        }
        if (new Date(item.createTimestamp) < new Date(fastTask.createTimestamp)) {
          fastTask = item;
        }
      }
      updateFastestTask(runningTask || fastTask);
    } else {
      updateFastestTask();
    }
  };
  const updateQueueLimit = ({ concurrency = 0, queueMax = 0 } = {}) => {
    queueLimit.value = { concurrency, queueMax };
  };
  //生图接口是否在loading
  const updateReqStatus = (statue = false) => {
    isLoading.value = statue;
  };

  const updateFastestTask = (task = {}) => {
    fastestTask.value = task;
    const pendingIcon = "🟣";
    const runningIcon = "🟢";
    //relax 模式 更新 relax limit 状态
    if (task.markId) {
      createStore.updateTaskState(!task.fastHour);
    }
    let title = document.title.replace(pendingIcon, "").replace(runningIcon, "");
    if (!task.model_id || !userProfile.userConfig.changeTabStatus) {
      document.title = title;
      console.log("——————————————————————————————————————", title);
      return;
    }
    console.log(task.index)
    if (task.index === 0) {
      document.title = `${runningIcon} ${title}`;
      return;
    }
    document.title = `${pendingIcon} ${title}`;
  };

  const ORIGIN_MENU = {
    [PRELOAD_TASK_TYPE.TEXT_TO_PICTURE]: "create",
    [PRELOAD_TASK_TYPE.PICTURE_TO_PICTURE]: "picCreate",
    [PRELOAD_TASK_TYPE.UPSCALE]: "hiresFix",
    [PRELOAD_TASK_TYPE.INPAINT]: "localRedraw",
    [PRELOAD_TASK_TYPE.EXTEND]: "enlargeImage",
    [PRELOAD_TASK_TYPE.REMOVE_BG]: "removeBackground",
    [PRELOAD_TASK_TYPE.LINE_RECOLOR]: "lineRecolor",
    [PRELOAD_TASK_TYPE.EDIT_IMAGE]: "edit",
  };

  // 从服务器更新预加载任务队列
  const syncUpdatePreloadQueue = async () => {
    const { data = [], status } = await getPreloadTasks();
    if (status !== 0) {
      return Promise.resolve(false);
    }
    const list = data.map(({ genInfo, id, featuresTypeValue, createTime }) => {
      if (genInfo) {
        genInfo = JSON.parse(genInfo);
        let conf = {};
        try {
          if (genInfo.genParameters) {
            conf = JSON.parse(genInfo.genParameters);
          } else {
            conf = JSON.parse(genInfo.dealImgParams);
          }
        } catch { }
        Object.assign(genInfo, {
          createTimestamp: createTime || getCurrentISO8601Time(),
          ...conf,
          originCreate: ORIGIN_MENU[featuresTypeValue],
          status: "pending",
          index: -1,
        });
      }
      return {
        genInfo,
        id,
      };
    });
    preloadQueue.value = list.filter((item) => item.genInfo);
    return Promise.resolve(true);
  };
  // 从预载队列移除task
  const syncDelPreloadTasks = async (ids) => {
    const { status } = await delPreloadTask(ids);
    if (status === 0) {
      preloadQueue.value = preloadQueue.value.filter((item) => !ids.includes(item.id));
    }
    return Promise.resolve(status === 0);
  };
  // 手动更新本地预载队列中的数据
  const updateLocalPreloadTasks = async (tasks = []) => {
    const list = tasks.map(({ genInfo, id, featuresTypeValue, createTime }) => {
      if (genInfo) {
        genInfo = JSON.parse(genInfo);
        let conf = {};
        try {
          if (genInfo.genParameters) {
            conf = JSON.parse(genInfo.genParameters);
          } else {
            conf = JSON.parse(genInfo.dealImgParams);
          }
        } catch { }
        Object.assign(genInfo, {
          createTimestamp: createTime || getCurrentISO8601Time(),
          ...conf,
          originCreate: ORIGIN_MENU[featuresTypeValue],
          status: "pending",
          index: -1,
        });
      }
      return {
        genInfo,
        id,
      };
    });
    preloadQueue.value = list.filter((item) => item.genInfo);
    return Promise.resolve(true);
  };

  // 清除预载和并发队列
  const clearAllTasks = () => {
    taskQueue.value = [];
    preloadQueue.value = [];
    fastestTask.value = {};
    queueLimit.value = {
      concurrency: 0,
      queueMax: 0,
    };
    isLoading.value = false;
  };
  return {
    fastestTask,
    taskQueue,
    preloadQueue,
    queueLimit,
    isLoading,
    syncDelPreloadTasks,
    updateLocalPreloadTasks,
    syncUpdatePreloadQueue,
    updateFastestTask,
    updateTaskQueue,
    updateQueueLimit,
    updateReqStatus,
    clearAllTasks,
  };
});

//共享链接到图像反推
export const useShareDescribe = defineStore("shareDescribe", () => {
  const fileLink = ref("");
  const setLink = (link = "") => {
    fileLink.value = link;
  };
  return { fileLink, setLink };
});


