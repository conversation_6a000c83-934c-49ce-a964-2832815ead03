<template>
    <div class="w-full flex justify-center cursor-default" :class="[status === 'noData' ? 'w-full' : ' my-10']">
        <div v-if="status === 'noData'" class="flex flex-col items-center justify-center absolute top-0 left-0 right-0 bottom-10 h-full">
            <slot name="noData">
                <img src="@/assets/images/notice_empty.webp" class="w-32 aspect-square hidden dark:block" />
                <img src="@/assets/images/notice_empty_light.webp" class="w-36 aspect-square dark:hidden" />
                <span class="mt-4">{{ $t(noDataText) }}</span>

                <div class="text-center mt-8">
                    <NuxtLinkLocale to="/image/create" class="block">
                        <Button class="min-w-[120px]" type="primary">
                            <span>{{ $t("MENU_CREATE") }}</span>
                        </Button>
                    </NuxtLinkLocale>
                </div>
            </slot>
        </div>
        <p v-else @click="handleLoadMore">
            <n-icon size="32" v-if="status === 'loading'" class="text-primary">
                <IconsSpinLoading />
            </n-icon>

            <span v-else-if="status === 'noMore'" class="text-black/40 dark:text-dark-text/40 end-msg">{{ $t(statusText) }}</span>
        </p>
    </div>
</template>
<script setup>
const emit = defineEmits(["loadMore"]);
const props = defineProps({
    status: {
        type: String,
        default: "",
    },
    noDataText: {
        type: String,
        default: "GALLERY_EMPTY",
    },
});

const textMap = {
    // more: "Load more",
    // loading: "",
    noMore: "SHORT_NO_MORE",
    // noData: "No Data",
    // error: 'Something went wrong. Please try again later.'
};

const statusText = computed(() => textMap[props.status] || "");

const handleLoadMore = () => {
    if (props.status === "more") {
        emit("loadMore");
    }
};
</script>
