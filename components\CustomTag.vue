<template>
    <div class="relative overflow-hidden px-2 py-1 rounded-xl text-xs font-medium flex items-center justify-center h-7" :class="[`tag-${type}`]">
        <span>{{ label }}</span>
    </div>
</template>

<script setup>
const props = defineProps({
    label: { type: String, required: true },
    type: { type: String, default: "primary" }, //展示类型 primary | info
});
</script>
<style lang="scss" scoped>
.tag {
    &-primary {
        @apply bg-primary-1 text-primary-6 h-7;
    }
    &-info {
        @apply text-text-4 bg-fill-wd-1 h-6;
    }
}
</style>
