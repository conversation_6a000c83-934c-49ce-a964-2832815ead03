<!--
 * @Author: HuangQS
 * @Description: 消息中心跳转页面
 * @Date: 2025-05-14 14:13:35
 * @LastEditors: HuangQ<PERSON> huang<PERSON><EMAIL>
 * @LastEditTime: 2025-07-03 14:57:13
-->

<template>
    <div class="flex flex-col w-full h-full">
        <MobileHeader v-if="title" :title="t(title)" />

        <div class="flex flex-col px-4 flex-1 overflow-y-auto">
            <component class="flex-1 w-full" v-if="viewKey" :key="viewKey" :is="messageChannel[viewKey]" />
        </div>
    </div>
</template>
<script setup>
import { defineAsyncComponent, onMounted, toRefs, watchEffect } from "vue";
import MobileHeader from "@/components/mobile/header/MobileHeader.vue";

const route = useRoute();
const { t } = useI18n({ useScope: "global" });

const messageMap = Object.freeze({
    nlikeNums: "MESSAGE_CENTER_LIKE",
    ncommentNums: "MESSAGE_CENTER_COMMENTS",
    nsysUpdateNums: "MESSAGE_CENTER_UPDATE",
    nplatformMessageNums: "MESSAGE_CENTER_PERSONAL",
    nplatformActivityNums: "MESSAGE_CENTER_PUBLIC",
});

const messageChannel = {
    nlikeNums: defineAsyncComponent(() => import("@/components/message/LikeNotice.vue")),
    ncommentNums: defineAsyncComponent(() => import("@/components/message/CommentNotice.vue")),
    nsysUpdateNums: defineAsyncComponent(() => import("@/components/message/SystemNotice.vue")),
    nplatformMessageNums: defineAsyncComponent(() => import("@/components/message/PersonalNotice.vue")),
    nplatformActivityNums: defineAsyncComponent(() => import("@/components/message/PublicNotice.vue")),
};
const title = ref("");
const viewKey = ref("");

onMounted(() => {
    viewKey.value = route.params.viewKey;
    console.log("viewKey", viewKey.value);
    title.value = messageMap[viewKey.value];
});
</script>
<style lang="scss" scoped></style>
