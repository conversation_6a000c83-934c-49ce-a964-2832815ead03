<template>
    <div v-if="showIcon" class="absolute transition-all delay-75" :class="[isFold ? 'top-[-8px] right-[-2px] z-[20]' : 'top-0 right-3']" @click="updatePageKey('/image/create', 0)">
        <n-popover trigger="hover" placement="right" raw :show-arrow="false" ref="taskListRef">
            <template #trigger>
                <div class="relative flex items-center justify-center" :class="[isFold ? 'size-6' : 'size-10']">
                    <figure v-if="renderAnimeSvg.showIcon && renderAnimeSvg.hasPending" class="absolute size-full">
                        <img src="@/assets/pending.svg" />
                    </figure>
                    <figure v-if="renderAnimeSvg.showIcon && !renderAnimeSvg.hasPending" class="absolute size-full">
                        <img src="@/assets/gen.svg" />
                    </figure>
                    <template v-if="!isFold">
                        <span v-if="allTask.length === 1" class="relative z-10 mt-0.5 text-dark-active-text text-xs">{{ allTask.length }}</span>
                        <span v-else class="relative z-10 mt-0.5 text-dark-active-text text-xs">{{ allTask.length || taskQueueLen }}</span>
                    </template>
                </div>
            </template>
            <div class="p-2 rounded border border-solid dark:border-white/20 flex flex-col gap-2.5 card-shadow">
                <div
                    v-for="item in allTask"
                    :key="item.markId || item.id"
                    class="flex items-center justify-between p-2 text-black dark:text-dark-text min-w-60 rounded task-item"
                    :class="[itemIsGenerating(item) ? 'bg-[#22C55E]/30' : 'bg-primary/30']"
                >
                    <div class="ml-0.5">
                        <span> {{ batchSizeTxt(item.resolution?.batch_size) }}, {{ itemIsGenerating(item) ? t("MENU_JOB_STATUS_GENERATING") : t("MENU_JOB_STATUS_QUEUEING") }}</span>
                        <span v-if="item.index > 0" class="ml-0.5">. ({{ item.index }})</span>
                    </div>
                    <div class="ml-2 flex items-center gap-2 text-sm">
                        <n-icon size="24" class="hover-show" @click="handleCancel(item)">
                            <CloseRoundFill />
                        </n-icon>

                        <n-popover trigger="click" placement="right" raw :show-arrow="false">
                            <template #trigger>
                                <n-icon size="20" class="hover-show">
                                    <IconsInfo />
                                </n-icon>
                            </template>
                            <div class="p-4 w-[364px] rounded-xl border border-solid dark:border-white/20 card-shadow">
                                <div class="flex items-center gap-2.5">
                                    <span class="font-medium">{{ t("COMMON_PROMPT") }}</span>
                                    <PicPopover placement="right" trigger="click" :duration="2" v-if="!!item.prompt">
                                        <template #trigger>
                                            <n-icon size="16" class="opacity-40 cursor-pointer hover:opacity-70" @click="copyToClipboard(item.prompt)">
                                                <IconsCopyText />
                                            </n-icon>
                                        </template>
                                        <div class="gap-1 flex items-center">
                                            <n-icon size="20">
                                                <IconsSuccess />
                                            </n-icon>
                                            <span>{{ t("TOAST_COPY_SUCCESS") }}</span>
                                        </div>
                                    </PicPopover>
                                </div>
                                <div class="mt-1.5 rounded-lg bg-black/5 dark:bg-white/5">
                                    <n-input
                                        type="textarea"
                                        :bordered="false"
                                        :placeholder="t('COMMON_PROMPT')"
                                        :value="promptFilter(item).prompt"
                                        round
                                        readonly
                                        :autosize="{
                                            minRows: 2,
                                            maxRows: 2,
                                        }"
                                    />
                                </div>
                                <div class="mt-2.5 flex items-center gap-2.5">
                                    <span class="font-medium">{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>
                                    <PicPopover placement="right" trigger="click" :duration="2" v-if="!!item.negative_prompt">
                                        <template #trigger>
                                            <n-icon size="16" class="opacity-40 cursor-pointer hover:opacity-70" @click="copyToClipboard(item.negative_prompt)">
                                                <IconsCopyText />
                                            </n-icon>
                                        </template>
                                        <div class="gap-1 flex items-center">
                                            <n-icon size="20">
                                                <IconsSuccess />
                                            </n-icon>
                                            <span>{{ t("TOAST_COPY_SUCCESS") }}</span>
                                        </div>
                                    </PicPopover>
                                </div>
                                <div class="mt-1.5 rounded-lg bg-black/5 dark:bg-white/5">
                                    <n-input
                                        type="textarea"
                                        :bordered="false"
                                        :placeholder="t('CONFIG_BASE_NEGATIVE_PROMPT')"
                                        v-model:value="item.negative_prompt"
                                        readonly
                                        round
                                        :autosize="{
                                            minRows: 2,
                                            maxRows: 2,
                                        }"
                                    />
                                </div>
                                <div v-if="!!modelInfo(item).icon" class="mt-5 p-1 bg-black/5 w-max dark:bg-white/5 flex items-center gap-2.5 rounded-lg dark:text-dark-text/70">
                                    <img :src="modelInfo(item).icon" class="rounded w-8 h-8" />
                                    <span class="pr-2">{{ modelInfo(item).label }}</span>
                                </div>

                                <div class="gap-2 mt-2.5 px-2.5 py-2 bg-black/5 dark:bg-white/5 flex text-xs w-max items-center rounded dark:text-neutral-200">
                                    <div>Date created</div>
                                    <div class="font-normal opacity-45">{{ timeFormat(item) || "--" }}</div>
                                </div>

                                <div class="mt-2.5 flex gap-2.5">
                                    <div class="def-tag">
                                        <div>Resolution</div>
                                        <div class="font-normal opacity-45">{{ item.resolution.width }} x {{ item.resolution.height }}</div>
                                    </div>
                                    <div class="def-tag" v-show="!!item.cfg">
                                        <div>Guidance Scale</div>
                                        <div class="font-normal opacity-45">{{ item.cfg }}</div>
                                    </div>
                                </div>
                                <div class="mt-2.5 def-tag" v-show="!!item.seed">
                                    <div>Seed</div>
                                    <div class="font-normal opacity-45">{{ item.seed }}</div>
                                </div>
                                <div class="mt-2.5"><CreateOriginCreateTag :task="item" /></div>
                            </div>
                        </n-popover>
                    </div>
                </div>
            </div>
        </n-popover>
    </div>
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import { useForceUpdatePageState } from "@/stores";
import { useCurrentTaskQueue } from "@/stores/create";
import { useGetModelInfo, useCancelUnfinishedTask } from "@/hook/create";
import { copyToClipboard, formatDate, formatPonyV6Prompt } from "@/utils/tools";
import { renderModelIcon } from "@/utils/tools";
import { useSyncAction } from "@/stores/syncAction";
import { NIcon } from "naive-ui";
import { Alert, CloseRoundFill } from "@/icons/index.js";
const props = defineProps({
    isFold: {
        type: Boolean,
        default: true,
    },
});

const fastSvg = new URL("@/assets/pending.svg", import.meta.url).href;
const relaxSvg = new URL("@/assets/gen.svg", import.meta.url).href;

const { updatePageKey } = useForceUpdatePageState();
const syncAction = useSyncAction();

const taskListRef = ref(null); //获取dom
// const animationDom = ref(null); //获取dom
const generatingFail = ref(false);
const generatingFailMessage = ref("");

const currentTaskQueue = useCurrentTaskQueue();
const taskQueueLen = computed(() => currentTaskQueue?.taskQueue?.length);
const showIcon = computed(() => taskQueueLen.value > 0);

const allTask = computed(() => {
    const taskQueue = currentTaskQueue.taskQueue;
    const preloadedTasks = currentTaskQueue.preloadQueue.map((item) => ({ ...item, ...item.genInfo, isPreloadTask: true })).reverse();
    return [...preloadedTasks, ...taskQueue];
});

//当前最快的任务
const fastestTask = computed(() => currentTaskQueue.fastestTask);

const itemIsGenerating = computed(() => {
    return ({ status, index }) => status === "running" || index === 0;
});

const promptFilter = computed(() => {
    return (item) => formatPonyV6Prompt(item, "output");
});

const renderAnimeSvg = computed(() => {
    const isRender = taskQueueLen.value > 0;
    const index = fastestTask.value.index;
    return {
        showIcon: isRender,
        hasPending: index !== 0,
    };
});

const batchSizeTxt = (num) => {
    if (!num || num < 2) {
        return t("MENU_JOB_STATUS_BATCH_SIZE");
    }
    return t("MENU_JOB_STATUS_BATCH_SIZES", { batch_size: num });
};
//model
//模型选择组件渲染函数
const modelInfo = computed(() => {
    return ({ model_id }) => {
        const option = useGetModelInfo(model_id);
        let icon = renderModelIcon(model_id);
        return { ...option, icon };
    };
});
//time
const timeFormat = computed(() => {
    return ({ createTimestamp }) => formatDate(createTimestamp);
});

const computedReferLabel = computed(() => {
    return ({ multi_img2img_info, model_id }) => {
        const styles = (multi_img2img_info?.style_list || []).map((item) => item.style);
        if (styles.length === 0) {
            return "";
        }
        const model = useGetModelInfo(model_id);
        const refers = (model.supportStyleList || []).filter((item) => styles.includes(item.value)).map((item) => item.label);
        if (!refers) {
            return "";
        }
        return refers.join(", ");
    };
});

//任务完成通知
const taskCompletedMission = (res) => {
    if (res.status === "success") {
        generatingFail.value = false;
        openToast.success(t("TOAST_GEN_COMPLETE"));
        generatingFailMessage.value = "";
        return;
    }
};

//取消任务
let cancelTaskLoading = false;
const handleCancel = (item) => {
    const { isPreloadTask, id, markId } = item;
    console.log("handleCancel", item);
    if (cancelTaskLoading) {
        return;
    }
    cancelTaskLoading = true;
    taskListRef.value?.setShow(false);
    const { showMessage } = useModal();
    showMessage({
        style: { width: "380px" },
        cancelBtn: t("SHORT_BTN_NO"),
        confirmBtn: t("SHORT_BTN_YES"),
        content: h("div", null, t("SHORT_CANCEL_TASK")),
        icon: h(NIcon, { size: 32, class: "text-error" }, { default: () => h(Alert) }),
        title: t("DIALOG_TITLE_CONFIRM"),
    })
        .then(async () => {
            if (isPreloadTask) {
                await currentTaskQueue.syncDelPreloadTasks([id]);
            } else {
                const { status, message } = await useCancelUnfinishedTask(markId);
                !status && openToast.error(message);
            }
            cancelTaskLoading = false;
        })
        .catch(() => {
            cancelTaskLoading = false;
        });
};
onMounted(() => {
    syncAction.subscribe("latest-completed-mission", taskCompletedMission);
});
onBeforeUnmount(() => {
    // clearAnimation();
    // animationDom.value = null;
    syncAction.unsubscribe("latest-completed-mission", taskCompletedMission);
});
</script>

<style lang="scss" scoped>
.card-shadow {
    box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.1);
    background: #fff;
    overflow: hidden;
}
.dark .card-shadow {
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.5);
    background: #2f3035;
}
.job-icon {
    background: linear-gradient(46deg, #3859ff 6%, #d658ff 32%, #ff5645 70%, #ffcc87 97%);
}
.status-tag {
    border-radius: 0 40px 40px 40px;
}

.def-tag {
    @apply gap-2 px-2.5 py-2 bg-black/5 dark:bg-white/5 flex text-xs w-max items-center rounded dark:text-neutral-200;
}
.act-tag {
    @apply relative overflow-hidden mt-2.5 px-3 py-2 flex text-xs w-max items-center bg-[#6904E9]/30 border border-solid border-[#6904E9] rounded;
    & span {
        @apply dark:text-[#977EEE] text-dark-active-text/70;
    }
}
.task-item {
    cursor: pointer;
    .hover-show {
        @apply dark:text-neutral-200 text-black hover:text-primary opacity-0;
        transition: opacity 0.1s ease-in-out;
    }
    &:hover {
        .hover-show {
            opacity: 1;
        }
    }
}
</style>
