<template>
    <div v-if="showTag && !onlyOneTag" class="flex items-center flex-wrap gap-2">
        <template v-if="computedReferLabel.length > 0">
            <CustomTag v-for="item in computedReferLabel" :label="item" :type="type" />
        </template>

        <CustomTag v-if="!!promptMagic" :label="promptMagic" :type="type" />
        <template v-if="showControls">
            <CustomTag v-for="item in imageControlLabels" :label="item" :type="type" />
        </template>
        <CustomTag v-if="showLabel" :label="$t(tagMap[label])" :type="type" />
    </div>
    <!-- 只展示一个 tag -->

    <div v-else-if="showTag && onlyOneTag" class="flex items-center flex-wrap gap-2">
        <template v-if="onlyOneTagLabel">
            <CustomTag :label="onlyOneTagLabel || ''" :type="type" />
        </template>
        <!-- 三者满足其一 就代表总tag个数大于等于2 需要展示... -->
        <template v-if="[computedReferLabel.length > 1, !!promptMagic, showLabel]?.filter(Boolean)?.length">
            <CustomTag label="..." :type="type" />
        </template>
    </div>
</template>

<script setup>
import { useGetModelInfo } from "@/hook/create";
import { t } from "@/utils/i18n-util";
const props = defineProps({
    task: {
        type: Object,
        default: () => ({}),
    },
    type: {
        type: String,
        default: "primary",
    },
    onlyOneTag: {
        //是否只展示所有tag中的一个 加上... 移动端create页面生图历史兼容
        type: Boolean,
        default: false,
    },
});
const tagMap = {
    hiresFix: "FEATURE_HIR_FIX_TITLE",
    removeBackground: "TOOLBAR_REMOVE_BG",
    localRedraw: "FEATURE_INPAINT_TITLE",
    lineRecolor: "LINE_ART",
    enlargeImage: "FEATURE_OUTPAINT_TITLE",
    customUpload: "FEATURE_SELF_UPLOAD_TITLE",
    vary: "FEATURE_VARY_TITLE",
    crop: "FEATURE_CROP_TITLE",
    edit: "FEATURE_EDIT_TITLE",
};
const computedReferLabel = computed(() => {
    const style = props.task?.img2img_info?.style;
    const model = useGetModelInfo(props.task.model_id);
    if (style) {
        const refer = (model.supportStyleList || []).find((item) => item.value === style);
        if (!refer || !refer.value) {
            return [];
        }
        return [refer.label];
    }
    const style_list = props.task?.multi_img2img_info?.style_list || [];
    const styles = style_list.map((item) => item.style);
    if (styles.length === 0) {
        return [];
    }
    const refers = (model.supportStyleList || []).filter((item) => styles.includes(item.value)).map((item) => item.label);
    if (!refers) {
        return [];
    }
    return refers;
});
const label = computed(() => {
    let originLabel = props.task.originCreate;
    if (!originLabel && Array.isArray(props.task.img_urls)) {
        originLabel = props.task.img_urls[0]?.originCreate;
    }
    return originLabel;
});
const showLabel = computed(() => {
    if (!label.value) {
        return false;
    }
    return !!tagMap[label.value];
});
const promptMagic = computed(() => props.task.subCategory);
const controlMap = {
    openposeControl: "Pose Control",
    cannyControl: "Canny Control",
    depthControl: "Depth Control",
};
const imageControlLabels = computed(() => (props.task?.img_control_info?.style_list || []).map((item) => controlMap[item.style] || item.style));
const showControls = computed(() => imageControlLabels.value.length > 0);

const showTag = computed(() => {
    return showLabel.value || computedReferLabel.value.length > 0 || promptMagic.value || imageControlLabels.value.length > 0;
});

const onlyOneTagLabel = computed(() => {
    let finalLabel = "";
    if (computedReferLabel.value[0]) {
        finalLabel = computedReferLabel.value[0];
    } else if (imageControlLabels.value[0]) {
        finalLabel = imageControlLabels.value[0];
    } else {
        finalLabel = t(tagMap[label.value] || "");
    }
    console.log(finalLabel, "finalLabel");
    return finalLabel;
});
</script>
