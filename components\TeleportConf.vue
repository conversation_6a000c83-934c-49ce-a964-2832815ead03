<template>
  <Teleport to="#confExtendArea" v-if="show">
    <div ref="teleportRef">
      <div class="flex-1 absolute top-full left-0 z-10 right-16">
        <slot />
      </div>

      <div class="absolute top-0 left-0 right-0 h-screen bg-dark-bg/30" @mousewheel.prevent @click.stop="outsideClick" />
    </div>
  </Teleport>
</template>

<script setup>
import { onClickOutside } from '@vueuse/core'
import { rafThrottle } from '@/utils/tools'
const teleportRef = ref(null)
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
    default: false
  }
})
const emits = defineEmits(['hiddenTeleport'])
const outsideClick = () => {
  emits('hiddenTeleport')
}
onClickOutside(teleportRef, rafThrottle(outsideClick, 0))
</script>
 