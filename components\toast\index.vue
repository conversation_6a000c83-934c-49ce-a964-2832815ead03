<!--
 * @Author: HuangQS
 * @Description: 自定义toast组件 用于VIP状态检查 与subscribe.js中checkShowVipNotice方法关联
 * @Date: 2025-05-08 16:58:52
 * @LastEditors: <PERSON><PERSON><PERSON>@ylxz.onaliyun.com
 * @LastEditTime: 2025-07-17 17:02:09
-->
<template>
    <div v-if="visible" class="toast-wrapper" :style="{ top: 80 + top + 'px' }" :class="isMobile && 'w-[70%]'">
        <div class="flex flex-row p-3 rounded-lg gap-2 items-center border border-solid border-border-t-1 font-medium" :class="`${planType}-box`">
            <n-icon size="24">
                <IconsVipStandard v-if="planType === 'standard'" />
                <IconsVipPro v-else-if="planType === 'pro'" />
            </n-icon>
            <span class="font text-sm"> {{ message }} </span>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useThemeStore } from "@/stores/system-config";
import { storeToRefs } from "pinia";

const { isMobile } = storeToRefs(useThemeStore());

const props = defineProps({
    message: {
        type: String,
        required: true,
    },
    duration: {
        type: Number,
        default: 2000,
    },
    planType: {
        type: String, // 用户vip计划类型 standard / pro
        default: "standard",
    },
    top: {
        type: Number,
        default: 20,
    },
});

const visible = ref(true);
const planType = computed(() => props.planType);
</script>

<style lang="scss" scoped>
.toast-wrapper {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    z-index: 9999;

    .standard-box {
        background: linear-gradient(93deg, rgba(0, 193, 154, 0) 9.66%, rgba(0, 193, 154, 0.08) 100%), var(--p-bg-6);
        .font {
            @apply bg-gradient-to-r from-[rgba(0,193,154,1)] to-[rgba(32,183,97,1)] text-transparent bg-clip-text;
        }
    }
    .pro-box {
        background: linear-gradient(93deg, rgba(255, 192, 36, 0) 9.66%, rgba(255, 192, 36, 0.08) 100%), var(--p-bg-6);
        .font {
            @apply bg-gradient-to-r from-[rgba(255,170,0,1)] to-[rgba(255,108,63,1)] text-transparent bg-clip-text;
        }
    }
}
</style>
