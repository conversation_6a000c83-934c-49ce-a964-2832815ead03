<template>
    <div class="relative w-full">
        <!-- 可编辑输入框 -->
        <div
            ref="inputBox"
            contenteditable="true"
            class="input-box md:p-4 w-full outline-none whitespace-nowrap overflow-x-hidden"
            @keydown.left="handleArrowNavigation"
            @keydown.right="handleArrowNavigation"
            @compositionstart="handleCompositionStart"
            @compositionend="handleCompositionEnd"
            @keydown.enter="handleKeydown"
            @mousedown="handleMouseDown"
            @input="updateHighlights"
        ></div>

        <div v-if="noText && !isComposing" class="absolute opacity-60 pointer-events-none top-0 left-2 h-10 flex items-center">
            <span>{{ placeholder }}</span>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";

const emits = defineEmits(["update:value"]);

const props = defineProps({
    maxlength: { type: Number, default: 160 },
    placeholder: { type: String, default: "" },
});

const noText = computed(() => rawText.value === "" || rawText.value === "\n");

const inputBox = ref(null);
const rawText = ref("");

onMounted(() => inputBox.value.focus());

const mentionRegex = /#([^\s!@#$%^&*()=+.,?":{}|<>，。！？、；：“”‘’（）【】《》]+)/g;

const escapeHTML = (unsafe) => unsafe.replace(/[<>]/g, (m) => ({ "<": "&lt;", ">": "&gt;" }[m]));

const saveCursorPosition = (container) => {
    const selection = window.getSelection();
    if (!selection.rangeCount) return null;
    const range = selection.getRangeAt(0);
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(container);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString().length;
};

const restoreCursorPosition = (container, offset) => {
    const selection = window.getSelection();
    let currentOffset = 0;
    const traverse = (node) => {
        if (node.nodeType === Node.TEXT_NODE) {
            const next = currentOffset + node.textContent.length;
            if (currentOffset <= offset && offset <= next) {
                const r = document.createRange();
                r.setStart(node, offset - currentOffset);
                r.collapse(true);
                selection.removeAllRanges();
                selection.addRange(r);
                return true;
            }
            currentOffset = next;
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            for (let i = 0; i < node.childNodes.length; i++) {
                if (traverse(node.childNodes[i])) return true;
            }
        }
        return false;
    };
    traverse(container);
};

const handleMouseDown = (e) => {
    if (e.target.tagName === "SPAN") {
        e.preventDefault();
        moveCursorToClickPosition(e, e.target);
    }
};

const moveCursorToClickPosition = (e, el) => {
    nextTick(() => {
        const range = document.createRange();
        const sel = window.getSelection();
        const pos = document.caretPositionFromPoint ? document.caretPositionFromPoint(e.clientX, e.clientY) : document.caretRangeFromPoint(e.clientX, e.clientY);
        if (pos) {
            range.setStart(pos.offsetNode, pos.offset);
            range.collapse(true);
            sel.removeAllRanges();
            sel.addRange(range);
        }
    });
};

const handleArrowNavigation = (e) => {
    const sel = window.getSelection();
    if (!sel.rangeCount) return;
    const range = sel.getRangeAt(0);
    const { startContainer: sc, startOffset: so } = range;

    if (e.key === "ArrowRight") {
        if (sc.nodeType === Node.TEXT_NODE && so === sc.textContent.length) {
            let next = sc.nextSibling;
            while (next && next.nodeType === Node.TEXT_NODE && next.textContent === "") next = next.nextSibling;
            if (next && next.nodeType === Node.ELEMENT_NODE && next.contentEditable === "true") {
                e.preventDefault();
                moveCursorToStart(next);
            }
        }
    } else if (e.key === "ArrowLeft") {
        if (sc.nodeType === Node.TEXT_NODE && so === 0) {
            let prev = sc.previousSibling;
            while (prev && prev.nodeType === Node.TEXT_NODE && prev.textContent === "") prev = prev.previousSibling;
            if (prev && prev.nodeType === Node.ELEMENT_NODE && prev.contentEditable === "true") {
                e.preventDefault();
                moveCursorToEnd(prev);
            }
        }
    }
};

const moveCursorToStart = (el) =>
    nextTick(() => {
        const r = document.createRange();
        r.setStart(el, 0);
        r.collapse(true);
        const s = window.getSelection();
        s.removeAllRanges();
        s.addRange(r);
    });

const moveCursorToEnd = (el) =>
    nextTick(() => {
        const r = document.createRange();
        r.selectNodeContents(el);
        r.collapse(false);
        const s = window.getSelection();
        s.removeAllRanges();
        s.addRange(r);
    });

const isComposing = ref(false);
const handleCompositionStart = () => (isComposing.value = true);
const handleCompositionEnd = (e) => {
    isComposing.value = false;
    rawText.value = e.data;
};

const updateHighlights = () => {
    const c = inputBox.value;
    if (!c || isComposing.value) return;
    let pos = saveCursorPosition(c);
    let t = c.innerText;
    if (t.length >= props.maxlength) {
        t = t.slice(0, props.maxlength - 1);
        pos = Math.min(pos, props.maxlength - 1);
    }
    t = escapeHTML(t);
    const html = t.replace(mentionRegex, '<span class="highlight-mention">#$1</span>');
    c.innerHTML = html;
    restoreCursorPosition(c, pos);
    rawText.value = c.innerText;
    emits("update:value", rawText.value);
};

const handleKeydown = (e) => {
    if (e.key === "Enter") e.preventDefault();
};

const insertHighlightText = (text = "") => {
    const c = inputBox.value;
    if (!c) return;
    if (c.innerText.length + text.length >= props.maxlength) return;

    const sel = window.getSelection();
    if (!sel.rangeCount) {
        c.innerHTML += text;
        updateHighlights();
        return;
    }

    const range = sel.getRangeAt(0);
    const span = document.createElement("span");
    span.className = "highlight-mention";
    span.textContent = text;

    range.deleteContents();
    range.insertNode(span);
    const space = document.createTextNode("\u00A0");
    span.after(space);

    const newRange = document.createRange();
    newRange.setStartAfter(space);
    newRange.collapse(true);
    sel.removeAllRanges();
    sel.addRange(newRange);
    c.focus();
    updateHighlights();

    nextTick(() => (c.scrollLeft = c.scrollWidth));
};

defineExpose({ insertHighlightText });
</script>

<style lang="scss" scoped>
.input-box {
    padding: 8px;
    font-size: 14px;
    line-height: 24px;
    min-height: 24px;
    font-weight: 500;
    white-space: nowrap;
    overflow-x: hidden; /* 完全隐藏横向滚动条 */
    overflow-y: hidden;
}
::v-deep(.highlight-mention) {
    color: #7b57e5;
}
</style>
```
