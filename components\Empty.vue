<template>
    <div class="flex flex-col items-center justify-center gap-y-4">
        <div class="w-full flex items-center justify-center">
            <img class="dark:block hidden w-[120px]" :class="[imageClass]" src="@/assets/images/notice_empty.webp" alt="" />
            <img class="dark:hidden block w-[120px]" :class="[imageClass]" src="@/assets/images/notice_empty_light.webp" alt="" />
        </div>
        <slot name="text"
            ><p class="text-text-3 text-sm">{{ $t(text) }}</p></slot
        >
        <slot></slot>
    </div>
</template>
<script setup>
defineProps({
    text: {
        type: String,
        default: "EMPTY_DATA",
    },
    imageClass: {
        type: String,
        default: "",
    },
});
</script>
