<template>
    <div
        v-if="show && !isMobile"
        @mouseenter="autoFocus"
        @mouseleave="autoBlur"
        @wheel="scrollFn"
        @click="cancelPreView"
        tabindex="1"
        ref="autoFocusRef"
        class="fixed top-0 left-0 right-0 bottom-20 md:bottom-0 overflow-hidden flex justify-center pre-view-box bg-bg-1 p-6"
        :class="!isMobile && 'z-50'"
    >
        <!-- 最左侧上下左右按钮 -->
        <div class="w-16 shrink-0 flex flex-col justify-center relative gap-6" @click.stop>
            <div
                class="absolute top-0 left-0 gap-4 w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl hover:bg-fill-wd-2 cursor-pointer"
                @click.stop="cancelPreView"
            >
                <IconsArrowLeft />
            </div>
            <div
                class="w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl -rotate-90 hover:bg-fill-wd-2 active:bg-fill-wd-3 cursor-pointer"
                :class="{ '!text-text-drop-3 !bg-fill-wd-1 !cursor-default': disableSwitch(-1) }"
                @click.stop="handlePrevious"
            >
                <IconsArrowRight />
            </div>
            <div
                class="w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl rotate-90 hover:bg-fill-wd-2 active:bg-fill-wd-3 cursor-pointer"
                :class="{ '!text-text-drop-3 !bg-fill-wd-1 !cursor-default': disableSwitch(1) }"
                @click.stop="handleNext"
            >
                <IconsArrowRight />
            </div>
        </div>
        <div class="bg-bg-2 rounded-2xl flex gap-4 w-[1656px] max-box" @click.stop>
            <!-- 左侧缩略图 -->
            <div class="overflow-hidden relative shrink-0 pl-4">
                <div class="shrink-0 w-20 no-scroll relative hidden md:block h-full" id="wheel-slot" ref="wheelSlotRef">
                    <div
                        v-for="item in listData"
                        :key="item.imgName"
                        class="thumbnail-item"
                        :style="{ width: item.index === checkThumb ? '100%' : '64px', transform: `translateY(${item.index > checkThumb ? item.offsetY + 20 : item.offsetY}px)` }"
                        @click="chooseIndex(item.index)"
                    >
                        <img :src="viewIcons(item)" class="w-full h-full object-cover" alt="" />
                    </div>
                </div>
            </div>

            <!-- 图片区域 -->
            <div class="p-4 h-full flex-1 overflow-hidden relative flex justify-center" ref="previewBoxRef">
                <div class="overflow-hidden w-full h-full m-auto bg-no-repeat bg-center bg-contain flex justify-center items-center cursor-zoom-in" :style="itemBgStyle" @click="isPreviewMode = true">
                    <img
                        v-show="currentPreview.preUrl"
                        :src="currentPreview.preUrl"
                        class="object-contain max-w-full max-h-full"
                        :class="{
                            'w-auto h-full': previewBoxRatio >= currentPreview.realWidth / currentPreview.realHeight,
                            'h-auto w-full': previewBoxRatio < currentPreview.realWidth / currentPreview.realHeight,
                        }"
                        alt=""
                    />
                </div>
                <div v-if="!currentPreview.preUrl" class="absolute px-4 h-11 rounded-lg flex items-center justify-center gap-2 backdrop-blur-lg bottom-2 z-10 text-text-2 bg-bg-1">
                    <n-icon size="14">
                        <IconsSpinLoading />
                    </n-icon>
                    <span>{{ t("LOADING_TEXT") }}</span>
                </div>
            </div>

            <div @wheel.stop="null" class="relative h-full w-[360px] text-sm shrink-0 p-4 no-scroll text-text-4 hidden md:block border-l border-solid border-border-1">
                <div class="h-full overflow-hidden flex flex-col">
                    <div class="flex-1 overflow-y-auto no-scroll flex flex-col gap-4">
                        <div v-if="!isCustomUpload && currentPreview.prompt" class="bg-fill-wd-1 rounded-lg px-4 py-3">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">{{ t("COMMON_PROMPT") }}</span>
                                <PicPopover placement="right" trigger="click" :duration="2" v-if="!!currentPreview.prompt">
                                    <template #trigger>
                                        <div>
                                            <PicPopover placement="top" trigger="hover">
                                                <template #trigger>
                                                    <n-icon size="20" class="cursor-pointer hover:text-text-2" @click="copyToClipboard(currentPreview.prompt)">
                                                        <IconsCopyText />
                                                    </n-icon>
                                                </template>
                                                <div class="gap-1 flex items-center">
                                                    <span>{{ t("TOOLBAR_COPY_INFO") }}</span>
                                                </div>
                                            </PicPopover>
                                        </div>
                                    </template>
                                    <div class="gap-1 flex items-center">
                                        <n-icon size="20">
                                            <IconsSuccess />
                                        </n-icon>
                                        <span>{{ t("TOAST_COPY_SUCCESS") }}</span>
                                    </div>
                                </PicPopover>
                            </div>

                            <div class="mt-2 break-words max-h-40 overflow-y-auto no-scroll text-sm leading-5.5 text-text-3">
                                {{ currentPreview.prompt }}
                            </div>
                        </div>

                        <div v-if="!isCustomUpload && currentPreview.negative_prompt" class="bg-fill-wd-1 rounded-lg px-4 py-3">
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-sm">{{ t("CONFIG_BASE_NEGATIVE_PROMPT") }}</span>

                                <PicPopover placement="right" trigger="click" :duration="2" v-if="!!currentPreview.negative_prompt">
                                    <template #trigger>
                                        <div>
                                            <PicPopover placement="top" trigger="hover">
                                                <template #trigger>
                                                    <n-icon size="20" class="cursor-pointer hover:text-text-2" @click="copyToClipboard(currentPreview.negative_prompt)">
                                                        <IconsCopyText />
                                                    </n-icon>
                                                </template>
                                                <div class="gap-1 text-sm flex items-center">
                                                    <span>{{ t("TOOLBAR_COPY_INFO") }}</span>
                                                </div>
                                            </PicPopover>
                                        </div>
                                    </template>
                                    <div class="gap-1 text-sm flex items-center">
                                        <n-icon size="20">
                                            <IconsSuccess />
                                        </n-icon>
                                        <span>{{ t("TOAST_COPY_SUCCESS") }}</span>
                                    </div>
                                </PicPopover>
                            </div>
                            <div class="mt-2 break-words max-h-40 overflow-y-auto no-scroll text-sm leading-5.5 text-text-3">
                                {{ currentPreview.negative_prompt }}
                            </div>
                        </div>

                        <PictureInfo :info="currentPreview" />

                        <CreateOriginCreateTag :task="currentPreview" />

                        <img
                            v-if="currentPreview?.genLocalRedrawPara?.draw_img_url"
                            :src="currentPreview?.genLocalRedrawPara?.draw_img_url"
                            class="rounded-lg w-14 h-14 object-contain overflow-hidden"
                            alt=""
                        />

                        <!-- outpain -->
                        <img
                            v-if="currentPreview?.enlargeImagePara?.draw_img_url"
                            :src="currentPreview?.enlargeImagePara?.draw_img_url"
                            class="rounded-lg w-14 h-14 object-contain overflow-hidden"
                            alt=""
                        />

                        <div class="flex gap-2">
                            <DownLoadPopover
                                :link="currentPreview.highThumbnailUrl || currentPreview.imgUrl"
                                :thumbnail="currentPreview.thumbnailUrl || currentPreview.highThumbnailUrl || currentPreview.imgUrl"
                                :isSensitive="isSensitive"
                                class="w-8 h-8"
                                @download-end="trackEvents('create_details_download')"
                            >
                                <PicPopover placement="top">
                                    <template #trigger>
                                        <div
                                            class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                            :class="{ 'hover:!bg-transparent !cursor-default text-text-4': isSensitive }"
                                        >
                                            <n-icon size="20">
                                                <IconsDownload />
                                            </n-icon>
                                        </div>
                                    </template>
                                    <span>{{ t("TOOLBAR_DOWNLOAD") }}</span>
                                </PicPopover>
                            </DownLoadPopover>
                            <PicPopover placement="top" v-if="!isFavorite && (!currentPreview.collectNums || currentPreview.collectNums === 0)">
                                <template #trigger>
                                    <div
                                        class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                        :class="{ 'hover:!bg-transparent !cursor-default text-text-4': isSensitive }"
                                        @click="handleFavorite('Favorite')"
                                    >
                                        <n-icon size="20">
                                            <IconsFavorite />
                                        </n-icon>
                                    </div>
                                </template>
                                <span>{{ t("MENU_COLLECTION_TIPS") }}</span>
                            </PicPopover>
                            <PicPopover placement="top" v-if="isFavorite">
                                <template #trigger>
                                    <div
                                        class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                        :class="{ 'hover:!bg-transparent !cursor-default text-text-4': isSensitive }"
                                        @click="commandDispense('unFavorite')"
                                    >
                                        <n-icon size="20">
                                            <IconsUnFavorite />
                                        </n-icon>
                                    </div>
                                </template>
                                <span>{{ t("SHORT_DELETE_TIPS") }}</span>
                            </PicPopover>

                            <PicPopover placement="top">
                                <template #trigger>
                                    <div
                                        class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                        :class="{ 'hover:!bg-transparent !cursor-default text-text-4': isSensitive || isCustomUpload }"
                                        @click="commandDispense('copy_link', isSensitive || isCustomUpload)"
                                    >
                                        <n-icon size="20">
                                            <IconsShareLink />
                                        </n-icon>
                                    </div>
                                </template>
                                <span>{{ t("TOOLBAR_COPY_LINK") }}</span>
                            </PicPopover>

                            <!-- 分享 -->
                            <ShareBar
                                :url="currentPreview.thumbnailUrl"
                                :id="currentPreview.promptId"
                                :imgName="currentPreview.imgName"
                                :gId="currentPreview.loginName"
                                :isSensitive="isSensitive || isCustomUpload"
                                @share="trackEvents('create_details_share')"
                            >
                                <div
                                    class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                    :class="{ 'hover:!bg-transparent !cursor-default text-text-4': isSensitive || isCustomUpload }"
                                >
                                    <n-icon size="20">
                                        <IconsShare />
                                    </n-icon>
                                </div>
                            </ShareBar>

                            <!-- 复制 -->
                            <PicPopover placement="top">
                                <template #trigger>
                                    <div
                                        class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                        :class="{ 'hover:!bg-transparent !cursor-default text-text-4': isSensitive }"
                                        @click="commandDispense('copy_full_prompt', isSensitive)"
                                    >
                                        <n-icon size="20">
                                            <IconsCopyText />
                                        </n-icon>
                                    </div>
                                </template>
                                <span>{{ t("TOOLBAR_COPY_FULL_INFO") }}</span>
                            </PicPopover>

                            <!-- 删除 -->
                            <PicPopover placement="top" v-if="!isFavorite">
                                <template #trigger>
                                    <div
                                        class="rounded-full cursor-pointer flex items-center justify-center w-8 h-8 text-text-2 hover:bg-fill-wd-1 transition-colors"
                                        @click="commandDispense('deleteImg')"
                                    >
                                        <n-icon size="20">
                                            <IconsDele />
                                        </n-icon>
                                    </div>
                                </template>
                                <span>{{ t("SHORT_DELETE_TIPS") }}</span>
                            </PicPopover>
                        </div>
                    </div>

                    <div class="mt-auto text-xs shrink-0">
                        <FeaturePanel :item="currentPreview" :isCustomUpload="isCustomUpload" />
                    </div>
                </div>
            </div>
        </div>
        <div class="w-16 shrink-0"></div>

        <Teleport to="body">
            <div v-if="hasFull" @wheel="scrollFn" class="fixed z-50 left-0 right-0 bottom-0 top-0 bg-white/95 dark:bg-dark-bg/95 cursor-zoom-out" @click="hasFull = false">
                <img :src="currentPreview.preUrl || currentPreview.thumbnailUrl" class="w-full h-full object-contain" alt="" />
            </div>
        </Teleport>
    </div>
    <div v-if="show && isMobile" class="absolute top-0 left-0 right-0 bottom-0 w-screen h-full z-49">
        <div
            class="absolute z-[98] top-6 right-6 gap-4 w-10 h-10 rounded-full bg-fill-wd-1 flex items-center justify-center text-text-2 text-2xl hover:bg-fill-wd-2 cursor-pointer"
            @click.stop="cancelPreView"
        >
            <IconsClose />
        </div>
        <div class="absolute top-0 left-0 right-0 bottom-0 z-50 h-full w-full flex items-center justify-center bg-bg-3">
            <H5Slider :list="list" :current-index="checkThumb" class="" @swiperIndex="swiperIndex" />
        </div>
    </div>

    <PictureViewer v-if="isPreviewMode" v-model:show="isPreviewMode" :imageUrl="previewImgUrl" />
</template>

<script setup>
const { t } = useI18n({ useScope: "global" });
import useWindowResize from "@/hook/windowResize";
import { useAddCollection } from "@/hook/updateAccount";
import { useGetModelInfo } from "@/hook/create";
import { copyToClipboard, downloadImage, debounce, copyAllParams } from "@/utils/tools";
import { useSyncAction } from "@/stores/syncAction";
const syncAction = useSyncAction();

import { NIcon } from "naive-ui";
const windowResize = useWindowResize();
const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    item: {
        type: [Object, null],
        default: null,
    },
    viewHigh: {
        type: Boolean,
        default: true,
    },
    show: {
        type: Boolean,
        default: false,
    },
    total: {
        type: Number,
        default: 0,
    },
    //收藏夹过来的，不展示 删除按钮，收藏按钮修改成为取消收藏
    isFavorite: {
        type: Boolean,
        default: false,
    },
});
const hasFull = ref(false);
const currentPreview = ref({});
const previewBoxRef = ref(null); //图片容器
const previewBoxRatio = ref(1); //图片容器的宽高比
const wheelSlotRef = ref(null);
const mid = ref(0); // 滚动容器的视口中位的序号
const start = ref(0); // 滚动容器的视口中位item的Y轴起点
const domH = ref(0); // 滚动容器的视口高度
const sliceLen = ref(0); // 每次动态切割的长度
const itemGap = 8; // item间距为8px
const defItemH = 64 + itemGap; //  h:64 + gap:8 = 72
const actItemH = 80; // 选中项 宽高为80px
watch(
    () => windowResize.height.value,
    async (v) => {
        await updateBaseComputed();
        props.show && updateSliceList(checkThumb.value);
    }
);
watch(
    () => windowResize.width.value,
    async () => {
        await updateBaseComputed();
        props.show && updateSliceList(checkThumb.value);
    }
);

//预览大图的链接
const previewImgUrl = computed(() => {
    const { highThumbnailUrl, imgUrl } = currentPreview.value;
    return highThumbnailUrl || imgUrl;
});
//是否开启预览模式
const isPreviewMode = ref(false);

const itemBgStyle = computed(() => {
    const { realWidth, realHeight } = currentPreview.value;
    const itemStyle = {
        "aspect-ratio": realWidth / realHeight,
    };
    if (defaultDefault.value) {
        itemStyle.backgroundImage = `url('${defaultDefault.value}')`;
    }
    return itemStyle;
});

//是否支持上/下一张
const disableSwitch = computed(() => {
    return (direction) => {
        if (direction === -1) {
            return checkThumb.value <= 0;
        } else {
            return checkThumb.value >= props.list.length - 1;
        }
    };
});
//切换上下一张
const handlePrevious = () => {
    if (disableSwitch.value(-1)) return;
    checkThumb.value -= 1;
    updateSliceList(checkThumb.value);
};
const handleNext = () => {
    if (disableSwitch.value(1)) return;
    checkThumb.value += 1;
    updateSliceList(checkThumb.value);
};

const defaultDefault = computed(() => {
    if (props.viewHigh) {
        return currentPreview.value.highMiniUrl || currentPreview.value.thumbnailUrl;
    } else {
        return currentPreview.value.miniThumbnailUrl || currentPreview.value.highMiniUrl || currentPreview.value.thumbnailUrl;
    }
});

const viewIcons = computed(() => {
    return (item) => {
        if (props.viewHigh) {
            return item.highMiniUrl || item.thumbnailUrl;
        } else {
            return item.miniThumbnailUrl || item.highMiniUrl || item.thumbnailUrl;
        }
    };
});
//是否为自己定义上传图片
const isCustomUpload = computed(() => currentPreview.value.originCreate === "customUpload" || currentPreview.value.originCreate === "crop");
const isMobile = ref(false);

//更新基础计算(中位下标、起始Y轴、视口高度)
const updateBaseComputed = async () => {
    if (!wheelSlotRef.value) return Promise.resolve();
    isMobile.value = window.matchMedia("only screen and (max-width: 768px)").matches;

    const { width, height } = previewBoxRef.value.getBoundingClientRect();
    previewBoxRatio.value = width / height;
    domH.value = wheelSlotRef.value.getBoundingClientRect().height;
    console.log(domH.value, "domH.value");
    mid.value = Math.max(Math.ceil(domH.value / defItemH / 2), 3);
    start.value = (domH.value - actItemH) / 2;
    sliceLen.value = mid.value * 4 + 2; //至少2屏 + 2个item
    return Promise.resolve();
};
const emits = defineEmits(["update:show", "loadMore", "delItem", "changeLike", "favorite", "unFavorite"]);
//选中的索引(总数组的索引，不是切割的短数组)
const checkThumb = ref(-1);
//虚拟列表数组(动态切割)
const listData = ref([]);
const loadNextPage = () => {
    emits("loadMore");
};

const swiperIndex = (currentIndex) => {
    if (props.list.length - 1 - currentIndex <= sliceLen.value) {
        loadNextPage();
    }
    checkThumb.value = currentIndex;
};

//切割原始数组
const sliceRenderList = (startIndex = 0) => {
    let end = startIndex + sliceLen.value;
    if (end > props.total) {
        startIndex = Math.max(0, props.total - sliceLen.value);
        end = props.total;
    }
    const currentIndex = checkThumb.value - startIndex;
    let firstY = start.value - (currentIndex - 1) * defItemH;
    if (currentIndex < mid.value) {
        firstY = defItemH;
    } else if (end - mid.value <= checkThumb.value) {
        if (end <= mid.value * 2) {
            firstY = defItemH;
        } else {
            firstY = domH.value - (end - startIndex - 1) * defItemH;
        }
    }
    let list = props.list.slice(startIndex, end).map((item, index) => {
        return {
            ...item,
            index: startIndex + index,
            offsetY: firstY + (index - 1) * defItemH,
        };
    });
    listData.value = list;
};
//监听选中项变化，触发动态切割数据
watch(checkThumb, (v) => updateSliceList(v));
//更新动态切割
const updateSliceList = (currentCheckIndex) => {
    currentPreview.value.thumbnailUrl = null;
    currentPreview.value.highMiniUrl = null;
    nextTick(() => {
        currentPreview.value = {
            ...props.list[currentCheckIndex],
        };
        if (!currentPreview.value.preUrl) {
            lazyLoadImg();
        }
        const diff = currentCheckIndex - mid.value;
        let offsetIndex = diff > 0 ? diff : 0; //当前选择项 与 视口中位项的差值
        sliceRenderList(offsetIndex);
    });
};
const autoFocusRef = ref(null);
//组件打开，计算并设置起始下标
watch(
    () => props.show,
    (v) => {
        if (v) {
            nextTick(() => {
                //更新一下基础计算值
                updateBaseComputed();
                autoFocus();
            });
        }
    }
);
const autoFocus = () => {
    autoFocusRef.value && autoFocusRef.value.focus();
};
const autoBlur = () => {
    autoFocusRef.value && autoFocusRef.value.blur();
};
const forceUpdateIndex = (index) => {
    console.log(index, "index");
    nextTick(() => {
        index = Math.max(0, Math.min(props.list.length - 1, index));
        checkThumb.value = index;
        updateSliceList(index);
    });
};
const getCUrrentIndex = () => {
    return checkThumb.value;
};
defineExpose({ forceUpdateIndex, getCUrrentIndex });

//lazyLoad  img
const lazyLoadImg = debounce(() => {
    if (import.meta.client) {
        const img = new Image();
        const { highThumbnailUrl, thumbnailUrl, imgUrl, promptId, imgName } = currentPreview.value;
        const preUrl = highThumbnailUrl || imgUrl;
        img.src = preUrl;
        img.onload = () => {
            if (currentPreview.value.thumbnailUrl === img.src || currentPreview.value.highThumbnailUrl === img.src || currentPreview.value.imgUrl === img.src) {
                currentPreview.value.preUrl = preUrl;
                syncAction.publish("updateImg", {
                    updateKey: "preUrl",
                    updateValue: preUrl,
                    promptId,
                    imgName,
                });
            }
        };
    }
}, 600);

// 滚轮事件  -- 60ms 执行一次偏移
let lastScroll = 0;
const scrollFn = (event) => {
    event.preventDefault();
    event.stopPropagation();
    const now = Date.now();
    if (now - lastScroll > 60) {
        // 这里是滚动事件的处理逻辑
        lastScroll = now;
        let n = checkThumb.value;
        if (event.deltaY > 0) {
            n += 1;
        } else {
            n -= 1;
        }
        if (n < 0) {
            n = 0;
        }
        const len = props.list.length - 1;
        if (n > len) {
            n = len;
        }
        if (len - n <= sliceLen.value) {
            loadNextPage();
        }
        checkThumb.value = n;
    }
};

//点击设置为选中项目
const chooseIndex = (index) => {
    checkThumb.value = index;
};

//删除图片
const handleRemove = () => {
    let { promptId, imgName } = props.list[checkThumb.value];
    // syncAction.publish("delImg", {
    //     promptId,
    //     imgName,
    // });
    window.trackEvent("Create", { el: "create_details_delete" });
    emits("delItem", promptId, imgName);
};

//关闭预览状态
const cancelPreView = () => {
    emits("update:show", false);
};

//收藏图片
const handleFavorite = async () => {
    // gtagController("collection_img");
    window.trackEvent("Create", { el: "create_details_collect" });
    const { sensitive, promptId, imgName } = currentPreview.value;
    if (sensitive) {
        return;
    }
    // const hasTrack = true; //已触发跟踪
    // emits("favorite", { sensitive, imgName }, promptId, hasTrack);
    const hasCollected = await useAddCollection({ sensitive, promptId, imgName });
    if (hasCollected) {
        syncAction.publish("collectedItem", { promptId, imgName });
    }
};
//取消收藏图片
// const handleUnFavorite = async () => {
//     gtagController("un_collection_img");
//     emits("unfavorite", currentPreview.value);
// };

//是否为敏感图
const isSensitive = computed(() => {
    return !!currentPreview.value.sensitive;
});
//事件分发，统一拦截
const commandDispense = (actionName, disabledAction = false) => {
    gtagController(actionName);
    if (disabledAction) {
        return false;
    }
    switch (actionName) {
        case "copy_full_prompt":
            setCopyToClipboard();
            break;
        case "download":
            trackEvents("create_details_download");
            downloadImage(currentPreview.value.imgUrl);
            break;
        case "unFavorite":
            handleUnFavorite(currentPreview.value);
            break;
        case "copy_link":
            setCopyLink();
            break;
        case "deleteImg":
            handleRemove();
            break;
        default:
            break;
    }
};
//移除收藏
const handleUnFavorite = () => {
    emits("unFavorite", currentPreview.value);
};

// gtag 派发代理
const gtagController = (key) => {
    if (import.meta.client) {
        window.trackEvent("APP_DETAILS", { el: `details=${key}` });
    }
};

//全参数复制
const setCopyToClipboard = () => {
    const task = currentPreview.value;
    const modelName = useGetModelInfo(task.model_id)?.label;
    copyAllParams(task, modelName);
    openToast.success(t("TOAST_COPY_SUCCESS"));
    trackEvents("create_details_copy_prompt");
};
//复制分享链接
const setCopyLink = () => {
    const task = currentPreview.value;
    const info = `promptId=${task.promptId}&imageName=${task.imgName}&gId=${btoa(task.loginName)}`;
    let href = window.location.host + `/app/image/share?${info}`;
    copyToClipboard(href);
    openToast.success(t("TOAST_COPY_SUCCESS"));
    trackEvents("create_details_copy_link");
};

// 方向左右键
// const handleKeyDown = (event) => {
//     if (event.key === "ArrowUp") {
//         event.stopPropagation(); // 阻止事件冒泡到父页面
//         event.preventDefault(); // 阻止默认行为（如页面滚动）
//         const index = checkThumb.value - 1;
//         if (index >= 0) {
//             chooseIndex(index);
//         }
//     } else if (event.key === "ArrowDown") {
//         event.stopPropagation();
//         event.preventDefault();
//         const index = checkThumb.value + 1;
//         if (index < props.total) {
//             chooseIndex(index);
//         }
//     }
// };

//注册键盘事件，处理esc/↑↓键
useShortcutKey({
    onEsc: () => {
        if (isPreviewMode.value) {
            isPreviewMode.value = false;
            return;
        }
        cancelPreView();
    },
    onUp: () => {
        if (isPreviewMode.value) {
            return;
        }
        handlePrevious();
    },
    onDown: () => {
        if (isPreviewMode.value) {
            return;
        }
        handleNext();
    },
});

const trackEvents = (el) => {
    window.trackEvent("Create", { el });
};
</script>

<style lang="scss" scoped>
.max-box {
    max-width: calc(100vw - 64px - 64px - 24px - 24px);
}
.thumbnail-item {
    --ratio: 1;
    aspect-ratio: var(--ratio);
    transition: all 0.15s linear;
    transform-origin: right top;
    @apply dark:bg-dark-bg-2 bg-white rounded-lg last:mb-0 w-16 absolute left-0 flex items-center justify-center cursor-pointer overflow-hidden;
}
.act-tag {
    @apply relative overflow-hidden mt-2 p-2 flex w-max items-center bg-primary/30 border border-solid border-primary rounded-lg;
    & span {
        @apply dark:text-[#977EEE] text-primary;
    }
}
.check-pre:hover {
    width: 100%;
}
#wheel-slot {
    // height: calc(100vh - 24px -24px);
    overflow: hidden;
    transition: transform 0.15s linear;
}
.vignette {
    position: fixed;
    z-index: 0;
    left: 0;
    right: 0;
    bottom: 0;
    top: 80px;
    opacity: 1;
    animation: opacity-switch 0.35s cubic-bezier(0.19, -0.51, 1, 1);
    background: linear-gradient(180deg, transparent 0%, #ffffff 30%, #eee6e2 100%);
}
.dark .vignette {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, #291c18 100%);
}
@keyframes opacity-switch {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
.pre-view-box {
    animation: enter 0.1s ease-in;
}

@keyframes enter {
    0% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
    }
}
</style>
